<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联动告警实时通信测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #66b1ff;
        }
        .button.danger {
            background: #f56c6c;
        }
        .button.danger:hover {
            background: #f78989;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.connected {
            background: #67c23a;
            color: white;
        }
        .status.disconnected {
            background: #f56c6c;
            color: white;
        }
        .status.connecting {
            background: #e6a23c;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>联动告警实时通信测试</h1>
        
        <!-- SSE测试 -->
        <div class="section">
            <h2>SSE (Server-Sent Events) 测试</h2>
            <div>
                <button class="button" onclick="connectSSE('devices/status')">连接设备状态流</button>
                <button class="button" onclick="connectSSE('executions')">连接执行记录流</button>
                <button class="button" onclick="connectSSE('alerts')">连接系统告警流</button>
                <button class="button" onclick="connectSSE('statistics')">连接统计数据流</button>
                <button class="button" onclick="connectSSE('all')">连接所有事件流</button>
                <button class="button danger" onclick="disconnectAllSSE()">断开所有SSE连接</button>
            </div>
            <div style="margin-top: 10px;">
                状态: <span id="sse-status" class="status disconnected">未连接</span>
            </div>
            <div style="margin-top: 10px;">
                <strong>SSE日志:</strong>
                <div id="sse-log" class="log"></div>
            </div>
        </div>

        <!-- WebSocket测试 -->
        <div class="section">
            <h2>WebSocket 测试</h2>
            <div>
                <button class="button" onclick="connectWebSocket('control')">连接设备控制</button>
                <button class="button" onclick="connectWebSocket('management')">连接系统管理</button>
                <button class="button danger" onclick="disconnectWebSocket()">断开WebSocket</button>
            </div>
            <div style="margin-top: 10px;">
                <button class="button" onclick="sendDeviceControl()">发送设备控制命令</button>
                <button class="button" onclick="sendPing()">发送Ping</button>
            </div>
            <div style="margin-top: 10px;">
                状态: <span id="ws-status" class="status disconnected">未连接</span>
            </div>
            <div style="margin-top: 10px;">
                <strong>WebSocket日志:</strong>
                <div id="ws-log" class="log"></div>
            </div>
        </div>
    </div>

    <script>
        let eventSources = new Map();
        let websocket = null;

        // SSE功能
        function connectSSE(endpoint) {
            const url = `/api/linkage/stream/${endpoint}`;
            
            if (eventSources.has(endpoint)) {
                eventSources.get(endpoint).close();
            }

            logSSE(`连接SSE: ${url}`);
            updateSSEStatus('connecting');

            const eventSource = new EventSource(url);
            eventSources.set(endpoint, eventSource);

            eventSource.addEventListener('connected', (event) => {
                const data = JSON.parse(event.data);
                logSSE(`✅ 连接成功: ${endpoint}`, data);
                updateSSEStatus('connected');
            });

            eventSource.addEventListener('data', (event) => {
                const data = JSON.parse(event.data);
                logSSE(`📨 收到数据: ${endpoint}`, data);
            });

            eventSource.addEventListener('heartbeat', (event) => {
                const data = JSON.parse(event.data);
                logSSE(`💓 心跳: ${endpoint} - ${data.message}`);
            });

            eventSource.addEventListener('error', (event) => {
                logSSE(`❌ SSE错误: ${endpoint}`, event);
                updateSSEStatus('disconnected');
            });
        }

        function disconnectAllSSE() {
            eventSources.forEach((eventSource, endpoint) => {
                eventSource.close();
                logSSE(`断开SSE: ${endpoint}`);
            });
            eventSources.clear();
            updateSSEStatus('disconnected');
        }

        function logSSE(message, data = null) {
            const log = document.getElementById('sse-log');
            const time = new Date().toLocaleTimeString();
            const logEntry = `[${time}] ${message}`;
            
            if (data) {
                log.innerHTML += logEntry + '\n' + JSON.stringify(data, null, 2) + '\n\n';
            } else {
                log.innerHTML += logEntry + '\n';
            }
            
            log.scrollTop = log.scrollHeight;
        }

        function updateSSEStatus(status) {
            const statusEl = document.getElementById('sse-status');
            statusEl.className = `status ${status}`;
            statusEl.textContent = status === 'connected' ? '已连接' : 
                                 status === 'connecting' ? '连接中' : '未连接';
        }

        // WebSocket功能
        function connectWebSocket(type) {
            if (websocket) {
                websocket.close();
            }

            const url = `ws://localhost:3000/api/linkage/ws/${type}`;
            logWS(`连接WebSocket: ${url}`);
            updateWSStatus('connecting');

            websocket = new WebSocket(url);

            websocket.onopen = () => {
                logWS('✅ WebSocket连接成功');
                updateWSStatus('connected');
            };

            websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                logWS('📨 收到消息', data);
            };

            websocket.onclose = () => {
                logWS('🔌 WebSocket连接关闭');
                updateWSStatus('disconnected');
            };

            websocket.onerror = (error) => {
                logWS('❌ WebSocket错误', error);
                updateWSStatus('disconnected');
            };
        }

        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }

        function sendDeviceControl() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                logWS('❌ WebSocket未连接');
                return;
            }

            const message = {
                type: 'device_control',
                payload: {
                    device_id: 'test_device_001',
                    command: 'turn_on',
                    params: {
                        brightness: 80
                    }
                },
                timestamp: new Date().toISOString(),
                id: 'test_' + Date.now()
            };

            websocket.send(JSON.stringify(message));
            logWS('📤 发送设备控制命令', message);
        }

        function sendPing() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                logWS('❌ WebSocket未连接');
                return;
            }

            const message = {
                type: 'ping',
                payload: {},
                timestamp: new Date().toISOString(),
                id: 'ping_' + Date.now()
            };

            websocket.send(JSON.stringify(message));
            logWS('📤 发送Ping', message);
        }

        function logWS(message, data = null) {
            const log = document.getElementById('ws-log');
            const time = new Date().toLocaleTimeString();
            const logEntry = `[${time}] ${message}`;
            
            if (data) {
                log.innerHTML += logEntry + '\n' + JSON.stringify(data, null, 2) + '\n\n';
            } else {
                log.innerHTML += logEntry + '\n';
            }
            
            log.scrollTop = log.scrollHeight;
        }

        function updateWSStatus(status) {
            const statusEl = document.getElementById('ws-status');
            statusEl.className = `status ${status}`;
            statusEl.textContent = status === 'connected' ? '已连接' : 
                                 status === 'connecting' ? '连接中' : '未连接';
        }
    </script>
</body>
</html>
