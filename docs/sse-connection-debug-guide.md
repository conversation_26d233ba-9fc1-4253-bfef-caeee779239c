# SSE连接中断问题排查指南

## 问题现象

用户反馈网络监控页面的SSE连接在发送几条数据后就停止了，不再有新数据返回。

## 排查步骤

### 1. 检查服务器日志

启动服务器后，观察控制台输出的详细日志：

```
[SSE] 启动网络监控数据收集，客户端: 192.168.1.100
[SSE] 连接确认消息已发送
[Monitor] 开始网络监控流，PID: 12345
[Monitor] 初始数据收集成功，发送数据
[Monitor] 初始数据发送成功
[Monitor] 进入定时循环
[Monitor] 定时器触发，开始收集数据
[Monitor] 开始收集网络监控数据
[Monitor] 网络状态获取成功，接口数量: 10
[Monitor] 流量统计获取成功，接口数量: 10
[Monitor] 网络质量检测完成
[Monitor] 数据收集成功，准备发送
[SSE] 收到数据消息 #1，准备发送
[SSE] 数据消息 #1 发送完成
```

### 2. 关键日志分析

**正常情况下应该看到**：
- ✅ 每2秒一次的定时器触发
- ✅ 数据收集成功的消息
- ✅ SSE消息发送完成的确认

**异常情况可能出现**：
- ❌ 数据收集失败的错误
- ❌ 网络质量检测超时
- ❌ 客户端断开连接的消息
- ❌ Panic错误信息

### 3. 浏览器开发者工具检查

在浏览器中打开开发者工具：

1. **Network标签页**：
   - 找到EventStream类型的请求
   - 检查连接状态是否为"pending"
   - 查看是否有错误状态码

2. **Console标签页**：
   - 查看是否有JavaScript错误
   - 观察SSE消息接收日志

3. **Application标签页**：
   - 检查是否有Service Worker干扰
   - 清理缓存和存储

### 4. 常见问题和解决方案

#### 问题1：网络质量检测阻塞

**现象**：日志显示"网络质量检测超时"

**原因**：ping ******* 可能被防火墙阻止或网络不通

**解决方案**：
- 检查网络连接
- 配置防火墙允许ICMP
- 或者修改代码跳过网络质量检测

#### 问题2：数据收集失败

**现象**：日志显示"数据收集失败"

**原因**：系统API调用失败

**解决方案**：
- 检查系统权限
- 确保网络接口可访问
- 重启网络服务

#### 问题3：客户端连接断开

**现象**：日志显示"客户端断开连接"

**原因**：浏览器或网络中断

**解决方案**：
- 刷新页面重新连接
- 检查网络稳定性
- 使用有线网络测试

#### 问题4：服务器超时

**现象**：连接在固定时间后断开

**原因**：服务器或代理超时设置

**解决方案**：
```yaml
# 服务器配置
server:
  read_timeout: 300s
  write_timeout: 300s

# Nginx配置
proxy_read_timeout 300s;
proxy_send_timeout 300s;
proxy_buffering off;
```

### 5. 新增功能说明

#### 心跳机制

系统现在每30秒发送一次心跳消息，防止连接超时：

```json
{
  "type": "data",
  "content": {
    "type": "heartbeat",
    "timestamp": 1753842800,
    "message": "连接正常"
  }
}
```

#### 错误恢复

当数据收集失败时，系统会：
1. 记录错误日志
2. 发送错误消息到前端
3. 继续下次数据收集（不会停止整个流程）

#### Panic保护

添加了panic恢复机制，防止单次错误导致整个连接中断。

### 6. 测试验证

#### 手动测试步骤

1. **启动服务器**：
   ```bash
   ./ecp.exe
   ```

2. **打开浏览器**：
   - 访问网络监控页面
   - 打开开发者工具
   - 点击"开始监控"

3. **观察日志**：
   - 服务器控制台应该显示详细日志
   - 浏览器控制台应该显示接收消息
   - EventStream应该持续接收数据

4. **长时间测试**：
   - 保持连接至少5分钟
   - 观察是否有心跳消息
   - 检查数据是否持续更新

#### 自动化测试

运行集成测试：
```bash
go test -v ./test -run TestNetworkMonitorSSE -timeout 60s
```

### 7. 故障排除清单

**服务器端检查**：
- [ ] 服务器正常启动
- [ ] 端口没有被占用
- [ ] 防火墙允许连接
- [ ] 系统资源充足

**网络检查**：
- [ ] 网络连接正常
- [ ] DNS解析正常
- [ ] 没有代理干扰
- [ ] ICMP协议可用

**浏览器检查**：
- [ ] 浏览器支持SSE
- [ ] 没有扩展干扰
- [ ] 缓存已清理
- [ ] JavaScript正常执行

**代码检查**：
- [ ] 最新版本已部署
- [ ] 配置文件正确
- [ ] 权限设置正确
- [ ] 日志级别适当

### 8. 联系支持

如果问题仍然存在，请提供以下信息：

1. **服务器日志**：完整的控制台输出
2. **浏览器信息**：版本、操作系统
3. **网络环境**：是否使用代理、防火墙设置
4. **错误截图**：开发者工具的Network和Console截图
5. **复现步骤**：详细的操作步骤

## 总结

通过添加详细日志、心跳机制、错误恢复和panic保护，SSE连接的稳定性得到了显著提升。大多数连接中断问题都可以通过查看日志快速定位和解决。
