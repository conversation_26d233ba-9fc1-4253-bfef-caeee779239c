# 视频源与算法绑定关系 API 文档

## 概述

视频源与算法绑定关系功能允许用户为每个视频源配置多个算法，并为每个算法设置检测参数。这是一个多对多的关系，支持精细化的配置管理。

## 数据结构

### VideoAlgorithmBinding

```json
{
  "id": 1,
  "video_id": 1,
  "algorithm_id": 1,
  "detection_area": "[{\"x\":100,\"y\":100},{\"x\":200,\"y\":100},{\"x\":200,\"y\":200},{\"x\":100,\"y\":200}]",
  "alert_interval": 60,
  "alert_window": 300,
  "alert_threshold": 0.8,
  "voice_content": "检测到异常情况",
  "danger_level": "warning",
  "extension_fields": "{\"sensitivity\":0.7,\"min_size\":50}",
  "status": "active",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z",
  "video": {
    "id": 1,
    "name": "摄像头01"
  },
  "algorithm": {
    "id": 1,
    "name": "人员检测算法"
  }
}
```

### 字段说明

- `id`: 绑定关系ID
- `video_id`: 视频源ID
- `algorithm_id`: 算法ID
- `detection_area`: 检测区域（多边形坐标，JSON格式）
- `alert_interval`: 告警间隔（秒）
- `alert_window`: 告警窗口长度（秒）
- `alert_threshold`: 告警阈值（0-1之间的浮点数）
- `voice_content`: 浏览器语音播报内容
- `danger_level`: 危险等级（info, warning, error）
- `extension_fields`: 算法扩展字段（JSON格式）
- `status`: 绑定状态（active, inactive）

## API 接口

### 1. 查询绑定关系列表

**GET** `/api/bindings`

**查询参数:**
- `videoId`: 视频源ID（可选）
- `algorithmId`: 算法ID（可选）
- `status`: 绑定状态（可选）
- `page`: 页码（默认1）
- `pageSize`: 每页数量（默认20）

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [...],
    "paging": {
      "page": 1,
      "limit": 20,
      "total": 100
    }
  }
}
```

### 2. 获取单个绑定关系

**GET** `/api/bindings/{id}`

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {...}
}
```

### 3. 创建绑定关系

**POST** `/api/bindings`

**请求体:**
```json
{
  "video_id": 1,
  "algorithm_id": 1,
  "detection_area": "[{\"x\":100,\"y\":100},{\"x\":200,\"y\":100},{\"x\":200,\"y\":200},{\"x\":100,\"y\":200}]",
  "alert_interval": 60,
  "alert_window": 300,
  "alert_threshold": 0.8,
  "voice_content": "检测到异常情况",
  "danger_level": "warning",
  "extension_fields": "{\"sensitivity\":0.7,\"min_size\":50}"
}
```

### 4. 更新绑定关系

**PUT** `/api/bindings/{id}`

**请求体:** 同创建绑定关系

### 5. 删除绑定关系

**DELETE** `/api/bindings/{id}`

### 6. 更新绑定状态

**PUT** `/api/bindings/{id}/status`

**请求体:**
```json
{
  "status": "active"
}
```

### 7. 获取视频源的绑定关系

**GET** `/api/videos/{id}/bindings`

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": [...]
}
```

### 8. 批量创建视频源绑定关系

**POST** `/api/videos/{id}/bindings`

**请求体:**
```json
[
  {
    "algorithm_id": 1,
    "detection_area": "...",
    "alert_interval": 60,
    "alert_window": 300,
    "alert_threshold": 0.8,
    "voice_content": "检测到异常情况",
    "danger_level": "warning",
    "extension_fields": "{\"sensitivity\":0.7}"
  },
  {
    "algorithm_id": 2,
    "detection_area": "...",
    "alert_interval": 30,
    "alert_window": 180,
    "alert_threshold": 0.9,
    "voice_content": "检测到危险情况",
    "danger_level": "error",
    "extension_fields": "{\"sensitivity\":0.8}"
  }
]
```

### 9. 获取算法的绑定关系

**GET** `/api/algorithms/{id}/bindings`

## 使用场景

### 1. 创建视频源时绑定算法

1. 首先创建视频源
2. 然后使用批量创建接口为视频源绑定多个算法
3. 为每个算法设置不同的检测参数

### 2. 修改视频源的算法配置

1. 获取当前视频源的所有绑定关系
2. 修改需要的绑定关系
3. 使用批量创建接口重新设置绑定关系

### 3. 启用/禁用特定算法

使用更新绑定状态接口来启用或禁用特定的算法绑定

## 注意事项

1. 检测区域使用多边形坐标表示，需要按照顺序定义顶点
2. 告警阈值范围为0-1，值越高表示检测精度要求越高
3. 扩展字段使用JSON格式，可以存储算法特定的参数
4. 批量创建会先删除原有绑定关系，然后创建新的绑定关系
5. 同一视频源和算法只能有一个有效的绑定关系 