# 视频算法管道执行实现计划

## 概述

基于 `VideoAlgorithmBinding` 表中的绑定关系，通过调用 `/api/v1/pipelines/complete` 接口一次性创建完整的算法执行管道。管道配置分为固定内容和动态内容两部分，其中动态内容根据绑定关系生成。

## 数据模型分析

### VideoAlgorithmBinding 核心字段
```go
type VideoAlgorithmBinding struct {
    ID              int64     // 绑定关系ID
    VideoID         int64     // 视频源ID
    AlgorithmID     int64     // 算法ID
    DetectionArea   string    // 检测区域（JSON格式多边形坐标）
    AlertInterval   int       // 告警间隔（秒）
    AlertWindow     int       // 告警窗口长度（秒）
    AlertThreshold  float64   // 告警阈值（0-1）
    VoiceContent    string    // 语音播报内容
    DangerLevel     string    // 危险等级：info, warning, error
    ExtensionFields string    // 算法扩展字段（JSON格式）
    Status          string    // 绑定状态：active, inactive
    
    // 关联数据
    Video     Video     // 视频源信息
    Algorithm Algorithm // 算法信息
}
```

### Video 关键字段
- `Name`: 视频源名称
- `Type`: 类型（摄像头-1、视频流-2、视频文件-3）
- `Protocol`: 协议（rtsp、onvif、gb28181）
- `URL`: 视频流地址
- `Username/Password`: 认证信息

### Algorithm 关键字段
- `Name`: 算法名称
- `Path`: 算法模型路径
- `Status`: 算法状态

## 管道配置结构设计

### 固定内容部分（模板配置）
基于 `pipeline_sample.yaml` 格式，固定内容包括：

```yaml
# 基础信息（固定）
version: "1.0"
author: "ECP System"

# 全局变量（固定，预留扩展位置）
globals:
  model_dir: "./vp_data/models/"
  video_dir: "./vp_data/videos/"
  output_dir: "./vp_data/output/"
  resize_ratio: 0.6
  log_level: "info"
  api_endpoint: "http://localhost:8080/api/alerts"
  # 预留位置供用户补充其他全局变量

# 固定输出节点
nodes:
  # 告警处理节点（固定）
  - id: "alert_handler"
    type: "vp_alert_handler_node"
    params:
      alert_endpoint: "${globals.api_endpoint}"
      danger_level: "warning"  # 将被动态覆盖
  
  # 屏幕输出节点（固定）
  - id: "screen_output"
    type: "vp_screen_des_node"
    params:
      channel_index: 0
  
  # OSD显示节点（固定）
  - id: "osd_display"
    type: "vp_osd_node"
    params:
      show_fps: true
      show_timestamp: true
```

### 动态内容部分（基于绑定关系生成）

#### 1. 管道基础信息（动态）
```yaml
id: "binding_{binding_id}"
name: "{video.name} - {algorithm.name}"
description: "视频源 {video.name} 与算法 {algorithm.name} 的绑定管道"
```

#### 2. 视频源节点（动态，基于Video表）
```yaml
# 根据Video.Type和Protocol选择节点类型
- id: "video_src_{video_id}"
  type: "vp_rtsp_src_node|vp_file_src_node|vp_gb28181_src_node"
  params:
    channel_index: 0
    # 根据Video.Type动态设置参数
    rtsp_url: "{video.url}"        # Type=1,2时
    file_path: "{video.url}"       # Type=3时
    username: "{video.username}"   # 有认证时
    password: "{video.password}"   # 有认证时
    resize_ratio: "${globals.resize_ratio}"
```

#### 3. 算法检测节点（动态，基于Algorithm表和绑定参数）
```yaml
- id: "detector_{algorithm_id}"
  type: "vp_yunet_face_detector_node|vp_yolo_detector_node|..."  # 根据算法类型映射
  params:
    model_path: "{algorithm.path}"
    confidence_threshold: "{binding.alert_threshold}"
    # 检测区域（如果设置）
    detection_area: "{binding.detection_area}"  # JSON格式
    # 扩展字段（动态解析JSON）
    # {binding.extension_fields} 解析后的键值对
```

#### 4. 连接关系（动态生成）
```yaml
connections:
  # 视频源 → 检测器
  - id: 1
    from: "video_src_{video_id}"
    to: "detector_{algorithm_id}"
  
  # 检测器 → OSD显示
  - id: 2
    from: "detector_{algorithm_id}"
    to: "osd_display"
  
  # OSD → 告警处理
  - id: 3
    from: "osd_display"
    to: "alert_handler"
  
  # OSD → 屏幕输出
  - id: 4
    from: "osd_display"
    to: "screen_output"
```

## 实现架构设计

### 1. 目录结构
```
internal/app/pipeline/
├── pipeline.go          # 管道服务主逻辑
├── client.go           # VideoPipe API客户端
├── builder.go          # 管道配置构建器
├── mapper.go           # 数据映射器
├── template.go         # 固定模板管理
├── config.go           # 配置文件管理
└── types.go            # 数据类型定义

configs/
└── pipeline-template.yaml  # 固定模板配置文件
```

### 2. 核心接口定义
```go
// PipelineService 管道服务接口
type PipelineService interface {
    // 根据所有绑定关系创建综合管道
    CreateComprehensivePipeline(bindings []*VideoAlgorithmBinding) error

    // 更新综合管道配置
    UpdateComprehensivePipeline(bindings []*VideoAlgorithmBinding) error

    // 删除综合管道
    DeleteComprehensivePipeline() error

    // 启动/停止综合管道
    StartComprehensivePipeline() error
    StopComprehensivePipeline() error

    // 获取综合管道状态
    GetComprehensivePipelineStatus() (*PipelineStatus, error)

    // 全量绑定关系变更处理
    OnAllBindingsChanged(bindings []*VideoAlgorithmBinding) error
}

// PipelineBuilder 管道配置构建器
type PipelineBuilder interface {
    // 构建完整管道配置
    BuildCompleteConfig(binding *VideoAlgorithmBinding) (*CompletePipelineConfig, error)
    
    // 构建视频源节点
    BuildVideoSourceNode(video *Video) (*PipelineNode, error)
    
    // 构建算法检测节点
    BuildDetectorNode(algorithm *Algorithm, binding *VideoAlgorithmBinding) (*PipelineNode, error)
    
    // 构建连接关系
    BuildConnections(videoNodeID, detectorNodeID string, fixedNodeIDs []string) ([]*PipelineConnection, error)
}

// TemplateManager 模板管理器
type TemplateManager interface {
    // 加载固定模板
    LoadTemplate() (*PipelineTemplate, error)
    
    // 合并模板和动态内容
    MergeTemplate(template *PipelineTemplate, dynamic *DynamicContent) (*CompletePipelineConfig, error)
}
```

### 3. 数据类型定义
```go
// CompletePipelineConfig 完整管道配置（对应API请求格式）
type CompletePipelineConfig struct {
    ID          string                 `json:"id" yaml:"id"`
    Name        string                 `json:"name" yaml:"name"`
    Description string                 `json:"description" yaml:"description"`
    Version     string                 `json:"version" yaml:"version"`
    Author      string                 `json:"author" yaml:"author"`
    Globals     map[string]interface{} `json:"globals" yaml:"globals"`
    Nodes       []*PipelineNode        `json:"nodes" yaml:"nodes"`
    Connections []*PipelineConnection  `json:"connections" yaml:"connections"`
}

// PipelineNode 管道节点
type PipelineNode struct {
    ID     string                 `json:"id" yaml:"id"`
    Type   string                 `json:"type" yaml:"type"`
    Params map[string]interface{} `json:"params" yaml:"params"`
}

// PipelineConnection 管道连接
type PipelineConnection struct {
    ID   int         `json:"id" yaml:"id"`
    From interface{} `json:"from" yaml:"from"` // string 或 []string
    To   string      `json:"to" yaml:"to"`
}

// PipelineTemplate 固定模板
type PipelineTemplate struct {
    Version     string                 `yaml:"version"`
    Author      string                 `yaml:"author"`
    Globals     map[string]interface{} `yaml:"globals"`
    FixedNodes  []*PipelineNode        `yaml:"fixed_nodes"`
}

// DynamicContent 动态内容
type DynamicContent struct {
    ID          string
    Name        string
    Description string
    VideoNode   *PipelineNode
    DetectorNode *PipelineNode
    Connections []*PipelineConnection
}
```

## 实现步骤

### 第一阶段：基础框架搭建

#### 1.1 创建管道服务模块
- [ ] 创建 `internal/app/pipeline/` 目录结构
- [ ] 实现基础数据类型定义 (`types.go`)
- [ ] 实现 VideoPipe API 客户端 (`client.go`)
- [ ] 创建固定模板配置文件 (`configs/pipeline-template.yaml`)

#### 1.2 模板管理器实现
```go
// template.go
type TemplateManager struct {
    templatePath string
    template     *PipelineTemplate
}

func (tm *TemplateManager) LoadTemplate() (*PipelineTemplate, error) {
    data, err := ioutil.ReadFile(tm.templatePath)
    if err != nil {
        return nil, err
    }

    var template PipelineTemplate
    err = yaml.Unmarshal(data, &template)
    if err != nil {
        return nil, err
    }

    tm.template = &template
    return &template, nil
}
```

### 第二阶段：核心映射逻辑实现

#### 2.1 视频源节点映射
```go
// mapper.go
func (m *Mapper) MapVideoToSourceNode(video *Video) (*PipelineNode, error) {
    nodeType := m.getVideoSourceNodeType(video.Protocol, video.Type)
    nodeID := fmt.Sprintf("video_src_%d", video.ID)

    params := map[string]interface{}{
        "channel_index": 0,
        "resize_ratio": "${globals.resize_ratio}",
    }

    switch video.Type {
    case 1, 2: // 摄像头、视频流
        if video.Protocol == "rtsp" {
            params["rtsp_url"] = video.URL
        } else if video.Protocol == "gb28181" {
            params["device_id"] = video.CameraID
            params["channel_id"] = 1
        }

        if video.Username != "" {
            params["username"] = video.Username
            params["password"] = video.Password
        }
    case 3: // 视频文件
        params["file_path"] = video.URL
    }

    return &PipelineNode{
        ID:     nodeID,
        Type:   nodeType,
        Params: params,
    }, nil
}

func (m *Mapper) getVideoSourceNodeType(protocol string, videoType int) string {
    switch protocol {
    case "rtsp":
        return "vp_rtsp_src_node"
    case "gb28181":
        return "vp_gb28181_src_node"
    default:
        if videoType == 3 {
            return "vp_file_src_node"
        }
        return "vp_rtsp_src_node" // 默认
    }
}
```

#### 2.2 算法检测节点映射
```go
func (m *Mapper) MapAlgorithmToDetectorNode(algorithm *Algorithm, binding *VideoAlgorithmBinding) (*PipelineNode, error) {
    nodeType := m.getAlgorithmNodeType(algorithm.Name)
    nodeID := fmt.Sprintf("detector_%d", algorithm.ID)

    params := map[string]interface{}{
        "model_path": algorithm.Path,
        "confidence_threshold": binding.AlertThreshold,
    }

    // 处理检测区域
    if binding.DetectionArea != "" {
        params["detection_area"] = binding.DetectionArea
    }

    // 处理扩展字段
    if binding.ExtensionFields != "" {
        var extFields map[string]interface{}
        if err := json.Unmarshal([]byte(binding.ExtensionFields), &extFields); err == nil {
            for k, v := range extFields {
                params[k] = v
            }
        }
    }

    return &PipelineNode{
        ID:     nodeID,
        Type:   nodeType,
        Params: params,
    }, nil
}

func (m *Mapper) getAlgorithmNodeType(algorithmName string) string {
    // 根据算法名称映射到对应的节点类型
    algorithmTypeMap := map[string]string{
        "face_detection":   "vp_yunet_face_detector_node",
        "object_detection": "vp_yolo_detector_node",
        "person_detection": "vp_person_detector_node",
        // 更多算法类型映射...
    }

    if nodeType, exists := algorithmTypeMap[algorithmName]; exists {
        return nodeType
    }

    // 默认返回通用检测节点
    return "vp_generic_detector_node"
}
```

### 第三阶段：配置构建器实现

#### 3.1 完整配置构建
```go
// builder.go
func (b *Builder) BuildCompleteConfig(binding *VideoAlgorithmBinding) (*CompletePipelineConfig, error) {
    // 1. 加载固定模板
    template, err := b.templateManager.LoadTemplate()
    if err != nil {
        return nil, fmt.Errorf("加载模板失败: %v", err)
    }

    // 2. 构建动态内容
    dynamic, err := b.buildDynamicContent(binding)
    if err != nil {
        return nil, fmt.Errorf("构建动态内容失败: %v", err)
    }

    // 3. 合并模板和动态内容
    config, err := b.templateManager.MergeTemplate(template, dynamic)
    if err != nil {
        return nil, fmt.Errorf("合并配置失败: %v", err)
    }

    return config, nil
}

func (b *Builder) buildDynamicContent(binding *VideoAlgorithmBinding) (*DynamicContent, error) {
    // 构建视频源节点
    videoNode, err := b.mapper.MapVideoToSourceNode(&binding.Video)
    if err != nil {
        return nil, err
    }

    // 构建检测器节点
    detectorNode, err := b.mapper.MapAlgorithmToDetectorNode(&binding.Algorithm, binding)
    if err != nil {
        return nil, err
    }

    // 构建连接关系
    connections := b.buildConnections(videoNode.ID, detectorNode.ID)

    return &DynamicContent{
        ID:          fmt.Sprintf("binding_%d", binding.ID),
        Name:        fmt.Sprintf("%s - %s", binding.Video.Name, binding.Algorithm.Name),
        Description: fmt.Sprintf("视频源 %s 与算法 %s 的绑定管道", binding.Video.Name, binding.Algorithm.Name),
        VideoNode:   videoNode,
        DetectorNode: detectorNode,
        Connections: connections,
    }, nil
}

func (b *Builder) buildConnections(videoNodeID, detectorNodeID string) []*PipelineConnection {
    return []*PipelineConnection{
        {ID: 1, From: videoNodeID, To: detectorNodeID},
        {ID: 2, From: detectorNodeID, To: "osd_display"},
        {ID: 3, From: "osd_display", To: "alert_handler"},
        {ID: 4, From: "osd_display", To: "screen_output"},
    }
}
```

### 第四阶段：API客户端实现

#### 4.1 VideoPipe API客户端
```go
// client.go
type PipelineClient struct {
    baseURL    string
    apiKey     string
    httpClient *http.Client
}

func (c *PipelineClient) CreateCompletePipeline(config *CompletePipelineConfig) error {
    url := fmt.Sprintf("%s/api/v1/pipelines/complete", c.baseURL)

    jsonData, err := json.Marshal(config)
    if err != nil {
        return fmt.Errorf("序列化配置失败: %v", err)
    }

    req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
    if err != nil {
        return fmt.Errorf("创建请求失败: %v", err)
    }

    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-API-Key", c.apiKey)

    resp, err := c.httpClient.Do(req)
    if err != nil {
        return fmt.Errorf("发送请求失败: %v", err)
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        body, _ := ioutil.ReadAll(resp.Body)
        return fmt.Errorf("API调用失败: %d, %s", resp.StatusCode, string(body))
    }

    return nil
}
```

## 配置文件设计

### configs/pipeline-template.yaml
```yaml
# 固定模板配置文件
version: "1.0"
author: "ECP System"

globals:
  model_dir: "./vp_data/models/"
  video_dir: "./vp_data/videos/"
  output_dir: "./vp_data/output/"
  resize_ratio: 0.6
  log_level: "info"
  api_endpoint: "http://localhost:8080/api/alerts"
  # 预留位置供用户补充

fixed_nodes:
  - id: "alert_handler"
    type: "vp_alert_handler_node"
    params:
      alert_endpoint: "${globals.api_endpoint}"

  - id: "screen_output"
    type: "vp_screen_des_node"
    params:
      channel_index: 0

  - id: "osd_display"
    type: "vp_osd_node"
    params:
      show_fps: true
      show_timestamp: true
```

### configs/config.yaml (扩展)
```yaml
pipeline:
  enabled: true
  api_endpoint: "http://localhost:8080/api/v1"
  api_key: "${PIPELINE_API_KEY}"
  timeout: 30s
  retry_count: 3
  template_path: "./configs/pipeline-template.yaml"

  # 节点类型映射配置
  node_mapping:
    video_source:
      rtsp: "vp_rtsp_src_node"
      file: "vp_file_src_node"
      gb28181: "vp_gb28181_src_node"

    algorithms:
      face_detection: "vp_yunet_face_detector_node"
      object_detection: "vp_yolo_detector_node"
      person_detection: "vp_person_detector_node"
```

## 集成和生命周期管理

### 第五阶段：绑定关系监听

#### 5.1 绑定关系变更监听
```go
// pipeline.go
type PipelineService struct {
    client         *PipelineClient
    builder        *PipelineBuilder
    bindingService *binding.BindingService
}

func (ps *PipelineService) OnAllBindingsChanged(bindings []*VideoAlgorithmBinding) error {
    // 过滤出激活的绑定关系
    activeBindings := make([]*VideoAlgorithmBinding, 0)
    for _, binding := range bindings {
        if binding != nil && binding.Status == "active" {
            activeBindings = append(activeBindings, binding)
        }
    }

    if len(activeBindings) == 0 {
        // 如果没有激活的绑定关系，删除综合管道
        return ps.DeleteComprehensivePipeline()
    } else {
        // 更新综合管道
        return ps.UpdateComprehensivePipeline(activeBindings)
    }
}
```

#### 5.2 错误处理和恢复
```go
func (ps *PipelineService) CreateComprehensivePipeline(bindings []*VideoAlgorithmBinding) error {
    config, err := ps.builder.BuildComprehensiveConfig(bindings)
    if err != nil {
        return fmt.Errorf("构建综合管道配置失败: %v", err)
    }

    // 重试机制
    for i := 0; i < 3; i++ {
        err = ps.client.CreateCompletePipeline(config)
        if err == nil {
            // 更新管道状态
            ps.updateComprehensivePipelineState(config.ID, "running", "")
            return nil
        }

        log.Printf("创建综合管道失败，重试 %d/3: %v", i+1, err)
        time.Sleep(time.Duration(i+1) * time.Second)
    }

    // 更新管道状态为错误
    ps.updateComprehensivePipelineState(config.ID, "error", err.Error())
    return err
}
```

### 第六阶段：API接口扩展

#### 6.1 管道管理API
```go
// 在 server.go 中添加新的路由处理
func (s *Server) setupPipelineRoutes() {
    pipelineGroup := s.router.Group("/api/bindings/:id/pipeline")
    pipelineGroup.Use(s.authMiddleware())

    pipelineGroup.POST("/create", s.handleCreatePipeline)
    pipelineGroup.GET("/status", s.handleGetPipelineStatus)
    pipelineGroup.PUT("/start", s.handleStartPipeline)
    pipelineGroup.PUT("/stop", s.handleStopPipeline)
    pipelineGroup.POST("/rebuild", s.handleRebuildPipeline)
}

func (s *Server) handleCreateComprehensivePipeline(c *gin.Context) {
    // 获取所有激活的绑定关系
    bindings, err := s.bindingService.GetActiveBindings()
    if err != nil {
        c.JSON(http.StatusInternalServerError, web.JsonError(err))
        return
    }

    if len(bindings) == 0 {
        c.JSON(http.StatusBadRequest, web.JsonErrorMsg("没有找到激活的绑定关系"))
        return
    }

    // 转换绑定关系类型
    pipelineBindings := make([]*pipeline.VideoAlgorithmBinding, 0, len(bindings))
    for _, binding := range bindings {
        pipelineBindings = append(pipelineBindings, s.convertToPipelineBinding(binding))
    }

    err = s.pipelineService.CreateComprehensivePipeline(pipelineBindings)
    if err != nil {
        c.JSON(http.StatusInternalServerError, web.JsonError(err))
        return
    }

    c.JSON(http.StatusOK, web.JsonSuccess("综合管道创建成功"))
}
```

## 关键实现要点

### 1. 配置模板设计
- **分离关注点**: 固定内容和动态内容分离，便于维护
- **预留扩展**: 在固定模板中预留用户自定义配置位置
- **参数引用**: 支持 `${globals.key}` 语法引用全局变量

### 2. 节点类型映射策略
- **视频源映射**: 根据 `Video.Protocol` 和 `Video.Type` 确定节点类型
- **算法映射**: 根据 `Algorithm.Name` 映射到对应的检测节点类型
- **可配置映射**: 通过配置文件管理节点类型映射关系

### 3. 数据流设计
```
Video Source → Algorithm Detector → OSD Display → Alert Handler
                                                 → Screen Output
```

### 4. 错误处理机制
- **重试机制**: API调用失败时的指数退避重试
- **状态同步**: 管道状态与数据库绑定关系状态同步
- **异常恢复**: 管道异常时的自动重建机制

## 数据库扩展建议

考虑在 `VideoAlgorithmBinding` 表中添加字段：
```sql
ALTER TABLE video_algorithm_bindings ADD COLUMN pipeline_id VARCHAR(255);
ALTER TABLE video_algorithm_bindings ADD COLUMN pipeline_status VARCHAR(50) DEFAULT 'stopped';
ALTER TABLE video_algorithm_bindings ADD COLUMN last_error TEXT;
ALTER TABLE video_algorithm_bindings ADD COLUMN last_sync_at TIMESTAMP;
```

## 实施时间表

### 第1周：基础框架
- [ ] 创建目录结构和基础文件
- [ ] 实现数据类型定义
- [ ] 实现模板管理器
- [ ] 创建固定模板配置文件

### 第2周：核心功能
- [ ] 实现数据映射器
- [ ] 实现配置构建器
- [ ] 实现API客户端
- [ ] 单元测试

### 第3周：集成测试
- [ ] 实现管道服务主逻辑
- [ ] 集成绑定关系监听
- [ ] 错误处理和恢复机制
- [ ] 集成测试

### 第4周：API和优化
- [ ] 扩展API接口
- [ ] 性能优化
- [ ] 文档完善
- [ ] 部署测试

## 风险评估和缓解

### 技术风险
1. **API兼容性**: VideoPipe API版本变更风险
   - 缓解：实现API版本检查和适配层
2. **配置复杂性**: 管道配置错误导致创建失败
   - 缓解：配置验证和详细错误信息
3. **性能影响**: 大量管道并发运行的性能影响
   - 缓解：资源限制和监控机制

### 业务风险
1. **数据一致性**: 管道状态与数据库不同步
   - 缓解：定时同步和状态校验机制
2. **资源消耗**: 系统资源过度使用
   - 缓解：资源监控和限制策略

### 缓解措施
- 完善的日志记录和监控
- 详细的错误处理和恢复机制
- 配置验证和测试工具
- 性能监控和资源限制
