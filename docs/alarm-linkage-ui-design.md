# 告警联动系统用户界面设计

## 1. 设计原则

### 1.1 设计理念
- **直观易用**：界面简洁明了，操作流程清晰
- **一致性**：与现有ECP系统保持视觉和交互一致性
- **响应式**：支持多种屏幕尺寸和设备
- **可访问性**：符合无障碍设计标准

### 1.2 技术框架
- **前端框架**：Vue 3 + TypeScript
- **UI组件库**：Element Plus
- **图表库**：ECharts
- **图标库**：Element Plus Icons
- **样式方案**：SCSS + CSS Variables

### 1.3 设计规范
- **色彩方案**：继承ECP系统主题色彩
- **字体规范**：系统默认字体栈
- **间距规范**：8px基础间距单位
- **圆角规范**：4px基础圆角

## 2. 整体布局设计

### 2.1 导航结构
```
ECP系统主菜单
├── 视频管理
├── 算法管理
├── 告警记录
├── 联动管理 ← 新增模块
│   ├── 联动规则
│   ├── 联动设备
│   ├── 执行记录
│   └── 系统配置
├── 系统管理
└── 网络管理
```

### 2.2 页面布局
采用经典的左侧导航 + 右侧内容区域布局：
```
┌─────────────────────────────────────────────────────────┐
│                    系统顶部导航栏                        │
├─────────────┬───────────────────────────────────────────┤
│             │                                           │
│   左侧菜单   │              主内容区域                    │
│             │                                           │
│   - 联动规则 │  ┌─────────────────────────────────────┐  │
│   - 联动设备 │  │                                     │  │
│   - 执行记录 │  │            页面内容                  │  │
│   - 系统配置 │  │                                     │  │
│             │  └─────────────────────────────────────┘  │
└─────────────┴───────────────────────────────────────────┘
```

## 3. 联动规则管理界面

### 3.1 规则列表页面

#### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│  联动规则管理                                            │
├─────────────────────────────────────────────────────────┤
│  [搜索框] [状态筛选] [优先级筛选]           [+ 新建规则]  │
├─────────────────────────────────────────────────────────┤
│  规则列表表格                                            │
│  ┌─────┬──────────┬────────┬────────┬────────┬────────┐  │
│  │选择 │ 规则名称  │ 状态   │ 优先级 │ 更新时间│ 操作   │  │
│  ├─────┼──────────┼────────┼────────┼────────┼────────┤  │
│  │ □   │人员入侵联动│ 启用   │ 高     │2024-01-15│编辑删除│  │
│  │ □   │火灾报警联动│ 禁用   │ 紧急   │2024-01-14│编辑删除│  │
│  └─────┴──────────┴────────┴────────┴────────┴────────┘  │
├─────────────────────────────────────────────────────────┤
│  [批量启用] [批量禁用] [批量删除]        分页控件         │
└─────────────────────────────────────────────────────────┘
```

#### 功能特性
- **搜索功能**：支持规则名称模糊搜索
- **筛选功能**：按状态、优先级、创建时间筛选
- **批量操作**：支持批量启用、禁用、删除
- **状态指示**：直观的状态标识（启用/禁用）
- **优先级标识**：不同颜色表示优先级等级

### 3.2 规则创建/编辑页面

#### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│  创建联动规则                                   [保存] [取消] │
├─────────────────────────────────────────────────────────┤
│  基本信息                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 规则名称: [________________]                        │ │
│  │ 描述信息: [________________________________]        │ │
│  │ 优先级:   [高 ▼]  状态: [启用 ○] [禁用 ○]           │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  触发条件                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 视频源:   [选择视频源 ▼] [+ 添加]                   │ │
│  │ 算法:     [选择算法 ▼] [+ 添加]                     │ │
│  │ 告警等级: [□ 信息] [☑ 警告] [☑ 错误]               │ │
│  │ 告警类型: [选择类型 ▼] [+ 添加]                     │ │
│  │ 时间范围: [☑ 启用] 从 [18:00] 到 [06:00]           │ │
│  │ 工作日:   [☑一] [☑二] [☑三] [☑四] [☑五] [□六] [□日] │ │
│  │ 高级条件: [表达式编辑器]                            │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  联动动作                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 动作1: [设备选择 ▼] [命令选择 ▼] [参数配置] [删除]   │ │
│  │ 动作2: [设备选择 ▼] [命令选择 ▼] [参数配置] [删除]   │ │
│  │ [+ 添加动作]                                        │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  [测试规则] [预览配置]                                   │
└─────────────────────────────────────────────────────────┘
```

#### 关键组件设计

**条件构建器**
- 可视化的条件选择界面
- 支持多条件组合（AND/OR逻辑）
- 实时预览匹配的告警数量
- 表达式编辑器支持高级用户

**动作配置器**
- 设备选择下拉框（按协议分组）
- 命令选择（根据设备类型动态加载）
- 参数配置表单（根据命令类型动态生成）
- 延迟执行时间设置

**规则测试功能**
- 模拟告警数据测试规则匹配
- 实时显示匹配结果
- 测试联动动作执行（安全模式）

### 3.3 规则详情页面

#### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│  规则详情: 人员入侵联动                      [编辑] [删除] │
├─────────────────────────────────────────────────────────┤
│  基本信息                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 规则ID: rule_001                                    │ │
│  │ 状态: [启用]  优先级: [高]                          │ │
│  │ 创建时间: 2024-01-15 10:30:00                      │ │
│  │ 更新时间: 2024-01-15 15:20:00                      │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  触发条件                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ • 视频源: 前门摄像头, 后门摄像头                     │ │
│  │ • 算法: 人员检测算法                                │ │
│  │ • 告警等级: 警告, 错误                              │ │
│  │ • 时间范围: 18:00-06:00 (工作日)                   │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  联动动作                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 1. 警报灯控制 → 开启红色警报灯 (延迟: 0秒)           │ │
│  │ 2. 通知推送 → 发送MQTT通知消息 (延迟: 2秒)          │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  执行统计                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 今日执行: 5次  本周执行: 23次  成功率: 95.6%        │ │
│  │ [查看执行记录]                                      │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 4. 联动设备管理界面

### 4.1 设备列表页面

#### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│  联动设备管理                                            │
├─────────────────────────────────────────────────────────┤
│  [搜索框] [协议筛选] [状态筛选]             [+ 添加设备]  │
├─────────────────────────────────────────────────────────┤
│  设备列表表格                                            │
│  ┌─────┬──────────┬────────┬────────┬────────┬────────┐  │
│  │状态 │ 设备名称  │ 协议   │ 地址   │ 最后通信│ 操作   │  │
│  ├─────┼──────────┼────────┼────────┼────────┼────────┤  │
│  │ 🟢  │入口警报灯 │ Modbus │192.168.1│ 1分钟前│编辑测试│  │
│  │ 🔴  │门禁控制器 │ RS485  │COM3    │ 5分钟前│编辑测试│  │
│  │ 🟡  │MQTT网关   │ MQTT   │192.168.2│ 连接中 │编辑测试│  │
│  └─────┴──────────┴────────┴────────┴────────┴────────┘  │
├─────────────────────────────────────────────────────────┤
│  状态统计: 在线 15台 | 离线 3台 | 故障 1台    分页控件    │
└─────────────────────────────────────────────────────────┘
```

#### 功能特性
- **实时状态**：设备连接状态实时更新
- **协议分类**：按协议类型分组显示
- **连接测试**：一键测试设备连接
- **批量操作**：批量测试、重连、删除

### 4.2 设备配置页面

#### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│  添加联动设备                               [保存] [取消] │
├─────────────────────────────────────────────────────────┤
│  基本信息                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 设备名称: [________________]                        │ │
│  │ 设备类型: [警报灯 ▼]                               │ │
│  │ 协议类型: [Modbus TCP ▼]                           │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  连接配置                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ IP地址:   [*************]                          │ │
│  │ 端口:     [502]                                     │ │
│  │ 从站ID:   [1]                                       │ │
│  │ 超时时间: [5] 秒                                    │ │
│  │ 重试次数: [3] 次                                    │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  命令配置                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 支持的命令:                                         │ │
│  │ • 开启: 写入线圈地址 0x0001, 值 True               │ │
│  │ • 关闭: 写入线圈地址 0x0001, 值 False              │ │
│  │ • 设置亮度: 写入寄存器地址 0x0002, 值 0-100        │ │
│  │ [+ 添加命令]                                        │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  [测试连接] [导入配置模板]                               │
└─────────────────────────────────────────────────────────┘
```

#### 协议特定配置

**MQTT设备配置**
```
连接配置:
- Broker地址: [*************]
- 端口: [1883]
- 客户端ID: [自动生成]
- 用户名: [可选]
- 密码: [可选]
- 使用TLS: [☑]
- QoS级别: [1 ▼]

主题配置:
- 控制主题: [device/control]
- 状态主题: [device/status]
- 消息格式: [JSON ▼]
```

**RS485设备配置**
```
串口配置:
- 串口号: [COM3 ▼]
- 波特率: [9600 ▼]
- 数据位: [8 ▼]
- 停止位: [1 ▼]
- 校验位: [None ▼]
- 超时时间: [3] 秒

协议配置:
- 设备地址: [1]
- 命令格式: [自定义]
- 校验方式: [CRC16 ▼]
```

### 4.3 设备状态监控页面

#### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│  设备状态监控                                [刷新] [导出] │
├─────────────────────────────────────────────────────────┤
│  概览统计                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ [在线设备: 15] [离线设备: 3] [故障设备: 1] [总计: 19] │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  协议分布图表                                            │
│  ┌─────────────────────────────────────────────────────┐ │
│  │     MQTT: 8台 (42%)                                 │ │
│  │   Modbus: 7台 (37%)                                 │ │
│  │    RS485: 4台 (21%)                                 │ │
│  │   [饼图显示]                                        │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  实时状态列表                                            │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 设备名称    │ 状态 │ 响应时间 │ 最后通信    │ 操作   │ │
│  │ 入口警报灯  │ 🟢   │ 15ms    │ 30秒前     │ 详情   │ │
│  │ 门禁控制器  │ 🔴   │ 超时    │ 5分钟前    │ 重连   │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 5. 执行记录界面

### 5.1 执行记录列表

#### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│  联动执行记录                                            │
├─────────────────────────────────────────────────────────┤
│  [时间范围] [状态筛选] [规则筛选] [设备筛选]   [导出记录] │
├─────────────────────────────────────────────────────────┤
│  执行记录表格                                            │
│  ┌─────────────┬────────┬────────┬────────┬────────────┐ │
│  │ 执行时间     │ 规则   │ 设备   │ 状态   │ 响应时间   │ │
│  ├─────────────┼────────┼────────┼────────┼────────────┤ │
│  │2024-01-15   │人员入侵│警报灯  │ 成功   │ 150ms     │ │
│  │14:30:25     │联动    │        │        │           │ │
│  │2024-01-15   │火灾报警│MQTT网关│ 失败   │ 超时      │ │
│  │14:28:10     │联动    │        │        │           │ │
│  └─────────────┴────────┴────────┴────────┴────────────┘ │
├─────────────────────────────────────────────────────────┤
│  统计信息: 今日执行 45次 | 成功 42次 | 失败 3次 | 成功率 93.3% │
└─────────────────────────────────────────────────────────┘
```

### 5.2 执行统计页面

#### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│  联动执行统计                                            │
├─────────────────────────────────────────────────────────┤
│  时间范围: [最近7天 ▼]                      [自定义时间] │
├─────────────────────────────────────────────────────────┤
│  执行趋势图表                                            │
│  ┌─────────────────────────────────────────────────────┐ │
│  │     执行次数                                        │ │
│  │ 50 ┤                                                │ │
│  │ 40 ┤     ●                                          │ │
│  │ 30 ┤   ●   ●                                        │ │
│  │ 20 ┤ ●       ●                                      │ │
│  │ 10 ┤           ●   ●                                │ │
│  │  0 └─────────────────────────────────────────────── │ │
│  │    周一 周二 周三 周四 周五 周六 周日                │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  成功率统计                                              │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 总体成功率: 94.2%                                   │ │
│  │ MQTT设备: 96.8%                                     │ │
│  │ Modbus设备: 92.1%                                   │ │
│  │ RS485设备: 89.5%                                    │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  热门规则排行                                            │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 1. 人员入侵联动    执行 156次  成功率 95.5%          │ │
│  │ 2. 火灾报警联动    执行 89次   成功率 92.1%          │ │
│  │ 3. 车辆违停联动    执行 67次   成功率 97.0%          │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 6. 系统配置界面

### 6.1 全局配置页面

#### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│  联动系统配置                               [保存] [重置] │
├─────────────────────────────────────────────────────────┤
│  基础配置                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 系统启用: [☑ 启用联动功能]                          │ │
│  │ 最大并发执行数: [10]                                │ │
│  │ 执行超时时间: [30] 秒                               │ │
│  │ 重试次数: [3]                                       │ │
│  │ 重试间隔: [5] 秒                                    │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  协议配置                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ MQTT协议:                                           │ │
│  │   启用: [☑]  默认QoS: [1]  保持连接: [60]秒         │ │
│  │ Modbus协议:                                         │ │
│  │   启用: [☑]  默认超时: [5]秒  重试次数: [3]         │ │
│  │ RS485协议:                                          │ │
│  │   启用: [☑]  默认超时: [3]秒  缓冲区: [1024]字节    │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  日志配置                                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 日志级别: [INFO ▼]                                  │ │
│  │ 日志保留天数: [30] 天                               │ │
│  │ 执行记录保留天数: [90] 天                           │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  [测试配置] [导出配置] [导入配置] [恢复默认]             │
└─────────────────────────────────────────────────────────┘
```

## 7. 响应式设计

### 7.1 断点设计
- **桌面端**: ≥1200px - 完整功能界面
- **平板端**: 768px-1199px - 简化操作界面
- **手机端**: <768px - 移动优化界面

### 7.2 移动端适配
- **导航方式**: 底部标签导航
- **表格显示**: 卡片式布局
- **操作按钮**: 大尺寸触控友好
- **表单输入**: 优化键盘体验

## 8. 交互设计

### 8.1 状态反馈
- **加载状态**: 骨架屏 + 进度指示
- **操作反馈**: Toast消息 + 状态变化
- **错误处理**: 友好的错误提示
- **成功确认**: 明确的成功反馈

### 8.2 用户引导
- **新手引导**: 功能介绍和操作指导
- **帮助文档**: 内置帮助系统
- **示例模板**: 预置配置模板
- **操作提示**: 关键操作的确认提示

## 9. 可访问性设计

### 9.1 键盘导航
- 支持Tab键导航
- 明确的焦点指示
- 快捷键支持

### 9.2 屏幕阅读器
- 语义化HTML结构
- 适当的ARIA标签
- 图片alt文本

### 9.3 色彩对比
- 符合WCAG 2.1 AA标准
- 色盲友好的配色方案
- 不依赖颜色传达信息

## 10. 性能优化

### 10.1 加载优化
- 组件懒加载
- 图片懒加载
- 代码分割

### 10.2 渲染优化
- 虚拟滚动
- 防抖节流
- 缓存策略

### 10.3 网络优化
- API请求合并
- 数据预加载
- 离线缓存

通过以上详细的界面设计方案，可以为用户提供直观易用的告警联动管理体验，确保系统的可用性和用户满意度。
