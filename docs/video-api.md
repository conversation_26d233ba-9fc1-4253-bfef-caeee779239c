# 视频源管理 API 文档

## 概述

视频源管理功能提供视频接入设备的完整生命周期管理，支持多种协议和摄像头品牌，可以自动生成或手动配置视频流地址。

## 数据结构

### Video

```json
{
  "id": 1,
  "name": "摄像头01",
  "protocol": "rtsp",
  "camera_id": "34020000001320000001",
  "description": "大厅入口摄像头",
  "stream_type": "自动生成",
  "camera_ip": "*************",
  "username": "admin",
  "password": "123456",
  "brand": "海康威视",
  "stream_mode": "主码流",
  "url": "rtsp://admin:123456@*************:554/h264/ch1/1/av_stream",
  "port": 554,
  "status": "online",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### 字段说明

- `id`: 视频源ID
- `name`: 视频源名称
- `protocol`: 协议类型（rtsp, onvif, gb28181）
- `camera_id`: 摄像头ID（GB28181协议使用）
- `description`: 描述信息
- `stream_type`: 取流方式（自动生成, 手动输入）
- `camera_ip`: 摄像头IP地址
- `username`: 用户名
- `password`: 密码
- `brand`: 摄像头品牌（海康威视, 大华, 宇视, 华为）
- `stream_mode`: 码流类型（主码流, 子码流）
- `url`: 视频流地址
- `port`: 端口号（ONVIF协议使用）
- `status`: 状态（online, offline）

## API 接口

### 1. 查询视频源列表

**GET** `/api/videos`

**查询参数:**
- `name`: 视频源名称（模糊查询，可选）
- `protocol`: 协议类型（可选）
- `status`: 状态（可选）
- `page`: 页码（默认1）
- `pageSize`: 每页数量（默认20）

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "摄像头01",
        "protocol": "rtsp",
        "camera_ip": "*************",
        "brand": "海康威视",
        "status": "online",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "paging": {
      "page": 1,
      "limit": 20,
      "total": 1
    }
  }
}
```

### 2. 获取单个视频源

**GET** `/api/videos/{id}`

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "摄像头01",
    "protocol": "rtsp",
    "camera_ip": "*************",
    "username": "admin",
    "brand": "海康威视",
    "stream_mode": "主码流",
    "url": "rtsp://admin:123456@*************:554/h264/ch1/1/av_stream",
    "status": "online"
  }
}
```

### 3. 创建视频源

**POST** `/api/videos`

**请求体:**
```json
{
  "name": "摄像头01",
  "type": 1,
  "protocol": "rtsp",
  "description": "大厅入口摄像头",
  "stream_type": "自动生成",
  "camera_ip": "*************",
  "port": 80,
  "username": "admin",
  "password": "123456",
  "brand": "海康威视",
  "stream_mode": "主码流",
  "status": "online"
}
```

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "摄像头01",
    "url": "rtsp://admin:123456@*************:554/h264/ch1/1/av_stream",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4. 更新视频源

**PUT** `/api/videos/{id}`

**请求体:** 同创建视频源

### 5. 删除视频源

**DELETE** `/api/videos/{id}`

**响应示例:**
```json
{
  "code": 0,
  "message": "success"
}
```

### 6. 获取视频源绑定的算法

**GET** `/api/videos/{id}/bindings`

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "video_id": 1,
      "algorithm_id": 1,
      "detection_area": "[{\"x\":100,\"y\":100},{\"x\":500,\"y\":100}]",
      "alert_threshold": 0.8,
      "status": "active",
      "algorithm": {
        "id": 1,
        "name": "人员检测算法"
      }
    }
  ]
}
```

## 协议支持

### RTSP 协议
- 支持主流摄像头品牌的RTSP URL自动生成
- 海康威视: `rtsp://username:password@ip:554/h264/ch1/1/av_stream`
- 大华: `rtsp://username:password@ip:554/cam/realmonitor?channel=1&subtype=0`
- 宇视: `rtsp://username:password@ip:554/media/mainstream`
- 华为: `rtsp://username:password@ip:554/LiveMedia/ch1/0`

### ONVIF 协议
- 支持标准ONVIF设备发现和配置
- URL格式: `onvif://username:password@ip:port`

### GB28181 协议
- 支持国标GB28181设备接入
- URL格式: `gb28181://device_id`

## 使用场景

### 1. 自动生成流地址
当选择"自动生成"取流方式时，系统会根据协议类型和摄像头品牌自动生成对应的视频流地址。

### 2. 手动输入流地址
当选择"手动输入"取流方式时，用户需要手动填写完整的视频流地址。

### 3. 批量设备管理
支持批量添加同一品牌、同一网段的摄像头设备。

## 注意事项

1. 创建视频源时，如果选择"自动生成"，系统会根据协议和品牌自动生成URL
2. 密码字段在响应中会被隐藏，不会返回给客户端
3. 删除视频源会同时删除相关的算法绑定关系
4. 视频源状态会影响算法检测的执行
5. GB28181协议需要填写标准的20位设备编码 