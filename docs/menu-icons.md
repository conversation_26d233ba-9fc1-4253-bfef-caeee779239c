# 菜单图标设计说明

本文档说明了边缘计算平台中各个菜单项使用的图标设计。

## 图标库

项目使用 **Element Plus Icons** 作为主要图标库，通过 `el-icon-` 前缀引用图标。

## 菜单图标映射

### 一级菜单

| 菜单名称 | 图标名称 | 图标说明 | 设计理念 |
|---------|---------|---------|---------|
| 首页 | `el-icon-HomeFilled` | 实心房屋图标 | 代表系统主页，用户的起始点 |
| 视频接入 | `el-icon-VideoCamera` | 摄像机图标 | 直观表示视频相关功能 |
| 算法仓库 | `el-icon-Cpu` | CPU处理器图标 | 代表算法计算和处理能力 |
| 告警记录 | `el-icon-Bell` | 铃铛图标 | 传统的告警提醒符号 |
| 系统设置 | `el-icon-Setting` | 齿轮设置图标 | 通用的设置配置符号 |

### 二级菜单

#### 首页子菜单
| 菜单名称 | 图标名称 | 图标说明 | 设计理念 |
|---------|---------|---------|---------|
| 系统概览 | `el-icon-DataAnalysis` | 数据分析图标 | 表示数据统计和分析功能 |

#### 视频接入子菜单
| 菜单名称 | 图标名称 | 图标说明 | 设计理念 |
|---------|---------|---------|---------|
| 摄像头 | `el-icon-Camera` | 相机图标 | 代表摄像头设备管理 |
| 视频流 | `el-icon-VideoPlay` | 视频播放图标 | 表示实时视频流功能 |
| 视频文件 | `el-icon-Document` | 文档图标 | 代表视频文件存储管理 |

#### 算法仓库子菜单
| 菜单名称 | 图标名称 | 图标说明 | 设计理念 |
|---------|---------|---------|---------|
| 算法列表 | `el-icon-List` | 列表图标 | 表示算法的列表展示 |
| 算法状态 | `el-icon-Monitor` | 监控图标 | 表示算法运行状态监控 |

#### 告警记录子菜单
| 菜单名称 | 图标名称 | 图标说明 | 设计理念 |
|---------|---------|---------|---------|
| 告警列表 | `el-icon-Warning` | 警告图标 | 表示告警信息的展示 |

#### 系统设置子菜单
| 菜单名称 | 图标名称 | 图标说明 | 设计理念 |
|---------|---------|---------|---------|
| 用户信息 | `el-icon-User` | 用户图标 | 表示用户账户管理 |

## 图标设计原则

### 1. 一致性
- 所有图标均来自 Element Plus Icons 图标库
- 保持统一的视觉风格和设计语言
- 图标大小和颜色遵循系统主题

### 2. 直观性
- 选择与功能高度相关的图标
- 避免使用抽象或难以理解的符号
- 优先选择用户熟悉的通用图标

### 3. 层次性
- 一级菜单使用更具代表性的图标
- 二级菜单图标与父级菜单保持关联性
- 通过图标区分不同功能模块

### 4. 可扩展性
- 为未来新增功能预留图标选择空间
- 保持图标命名的规范性
- 支持主题切换时的图标适配

## 技术实现

### 图标引用方式
```typescript
// 在菜单配置中使用
meta: {
  title: '视频接入',
  icon: 'el-icon-VideoCamera'
}
```

### 图标渲染组件
项目使用 `SidebarMenuItemTitle.vue` 组件来渲染菜单图标：

```vue
<template>
  <el-icon v-if="icon && icon.startsWith('el-icon')" class="sub-el-icon">
    <component :is="icon.replace('el-icon-', '')" />
  </el-icon>
  <svg-icon v-else-if="icon" :icon-class="icon" />
  <svg-icon v-else icon-class="menu" />
  <span v-if="title" class="ml-1">{{ translateRouteTitle(title) }}</span>
</template>
```

### 自动导入配置
项目配置了 Element Plus 图标的自动导入：

```typescript
// vite.config.ts
IconsResolver({
  enabledCollections: ["ep"], // element-plus图标库
})
```

## 图标预览

所有使用的图标都可以在 [Element Plus Icons](https://element-plus.org/zh-CN/component/icon.html) 官方文档中查看。

## 维护说明

### 添加新图标
1. 在 `web/app/src/api/menu.ts` 中配置新的菜单项
2. 选择合适的 Element Plus 图标
3. 使用 `el-icon-` 前缀 + 图标名称的格式
4. 更新本文档的图标映射表

### 图标替换
1. 在菜单配置中修改 `icon` 字段
2. 确保新图标与功能语义匹配
3. 测试图标在不同主题下的显示效果
4. 更新相关文档

## 注意事项

1. **图标命名**: 必须使用 `el-icon-` 前缀，后跟 Element Plus 图标的确切名称
2. **大小写敏感**: 图标名称严格区分大小写，如 `VideoCamera` 不能写成 `videocamera`
3. **主题适配**: 图标会自动适配系统的深色/浅色主题
4. **性能优化**: 图标通过自动导入机制按需加载，无需手动导入
5. **兼容性**: 确保选择的图标在 Element Plus 当前版本中可用

## 更新历史

- **2024-01-27**: 初始版本，为所有菜单添加 Element Plus 图标
- 统一使用 `el-icon-` 前缀的图标引用方式
- 建立图标设计原则和维护规范
