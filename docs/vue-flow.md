# 使用Vue Flow展示VideoPipe管道流程图

本文档详细介绍如何使用Vue Flow库结合VideoPipe HTTP API来创建一个交互式的管道流程图可视化界面。

## 目录

- [介绍](#介绍)
- [前提条件](#前提条件)
- [基本步骤](#基本步骤)
- [详细实现](#详细实现)
  - [1. 准备工作](#1-准备工作)
  - [2. API数据获取](#2-api数据获取)
  - [3. 数据转换](#3-数据转换)
  - [4. Vue Flow组件实现](#4-vue-flow组件实现)
  - [5. 自定义节点实现](#5-自定义节点实现)
  - [6. 实时状态更新](#6-实时状态更新)
  - [7. 事件处理与交互](#7-事件处理与交互)
- [完整代码示例](#完整代码示例)
- [进阶功能](#进阶功能)

## 介绍

Vue Flow是一个用于构建交互式节点图的Vue 3组件库，它提供了强大的功能来创建复杂的流程图、思维导图等图形界面。结合VideoPipe的HTTP API，我们可以创建一个实时的管道流程可视化工具，展示管道结构、节点状态和运行时指标。

## 前提条件

- Vue 3项目环境
- Vue Flow库安装: `npm install @vue-flow/core`
- VideoPipe HTTP API服务可访问
- API密钥配置

## 基本步骤

1. 从VideoPipe API获取管道数据（节点、连接、状态）
2. 将API数据转换为Vue Flow格式（节点和边）
3. 创建Vue Flow组件并应用数据
4. 实现自定义节点和边的样式
5. 实现实时状态更新

## 详细实现

### 1. 准备工作

#### 1.1 安装依赖

```bash
# 安装Vue Flow库
npm install @vue-flow/core

# 如果需要使用其他工具库
npm install axios vue-toastification
```

#### 1.2 引入样式

在项目的主CSS文件或`main.js`中引入Vue Flow样式：

```javascript
import '@vue-flow/core/dist/style.css';
import '@vue-flow/core/dist/theme-default.css';
```

#### 1.3 配置API请求工具

创建API请求工具函数：

```javascript
// api.js
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8080/api/v1';
const API_KEY = 'your-api-key'; // 实际开发中应从环境变量或配置中获取

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': API_KEY
  }
});

export const pipelineApi = {
  // 获取特定管道信息
  getPipeline: (id) => apiClient.get(`/pipelines/${id}`),
  
  // 获取管道中的所有节点
  getNodes: (id) => apiClient.get(`/pipelines/${id}/nodes`),
  
  // 获取管道中的所有连接
  getConnections: (id) => apiClient.get(`/pipelines/${id}/connections`),
  
  // 获取管道运行状态
  getStatus: (id) => apiClient.get(`/pipelines/${id}/status`)
};
```

### 2. API数据获取

创建一个函数来获取所需的所有管道数据：

```javascript
// pipelineService.js
import { pipelineApi } from './api';

export async function fetchPipelineData(pipelineId) {
  try {
    // 并行请求多个API以提高性能
    const [nodesResponse, connectionsResponse, statusResponse] = await Promise.all([
      pipelineApi.getNodes(pipelineId),
      pipelineApi.getConnections(pipelineId),
      pipelineApi.getStatus(pipelineId).catch(() => ({ data: { status: 'error' } }))
    ]);
    
    // 提取API响应中的数据
    const nodes = nodesResponse.data.status === 'success' ? nodesResponse.data.data : [];
    const connections = connectionsResponse.data.status === 'success' ? connectionsResponse.data.data : [];
    const status = statusResponse.data.status === 'success' ? statusResponse.data.data : null;
    
    return {
      nodes,
      connections,
      status
    };
  } catch (error) {
    console.error('获取管道数据失败:', error);
    throw new Error('无法获取管道数据');
  }
}
```

### 3. 数据转换

#### 3.1 节点转换函数

将API节点数据转换为Vue Flow节点格式：

```javascript
// flowDataTransformer.js

/**
 * 将API节点数据转换为Vue Flow节点格式
 * @param {Array} apiNodes API返回的节点数据
 * @param {Object} statusData 节点状态数据（可选）
 * @returns {Array} Vue Flow格式的节点数组
 */
export function convertToVueFlowNodes(apiNodes, statusData = null) {
  // 生成节点布局
  const layout = generateNodesLayout(apiNodes);
  
  return apiNodes.map((node, index) => {
    // 查找节点状态信息
    let nodeStatus = null;
    
    if (statusData && statusData.nodes) {
      try {
        // 解析状态数据中的节点状态JSON字符串
        const parsedNodes = JSON.parse(statusData.nodes);
        nodeStatus = parsedNodes.find(n => n.id === node.id);
      } catch (e) {
        console.error('解析节点状态数据失败', e);
      }
    }
    
    // 创建Vue Flow节点对象
    return {
      id: node.id,
      // 根据节点类型决定使用哪种节点组件
      type: getNodeType(node.type),
      // 显示的文本标签
      label: node.id,
      // 节点的位置（从布局中获取或使用默认位置）
      position: layout[node.id] || { x: 100 * index, y: 100 * (index % 3) },
      // 传递给节点组件的数据
      data: {
        ...node,
        // 节点状态，默认为idle
        state: nodeStatus?.state || 'idle',
        // 节点指标数据
        metrics: nodeStatus?.metrics || {},
        // 添加类型显示名称
        typeName: formatNodeTypeName(node.type)
      },
      // 节点样式类名，根据状态设置不同样式
      class: `node-${nodeStatus?.state || 'idle'}`
    };
  });
}

/**
 * 格式化节点类型名称
 * @param {string} nodeType 原始节点类型名
 * @returns {string} 格式化后的名称
 */
function formatNodeTypeName(nodeType) {
  // 移除前缀并将下划线替换为空格
  return nodeType
    .replace(/^vp_/, '')
    .replace(/_/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * 根据节点类型确定使用哪种Vue Flow节点组件
 * @param {string} apiNodeType API节点类型
 * @returns {string} Vue Flow节点类型名称
 */
function getNodeType(apiNodeType) {
  // 根据节点类型返回不同的组件
  if (apiNodeType.includes('src') || apiNodeType.includes('input')) {
    return 'sourceNode'; // 源节点类型
  }
  if (apiNodeType.includes('detector') || apiNodeType.includes('tracker')) {
    return 'processorNode'; // 处理器节点类型
  }
  if (apiNodeType.includes('output') || apiNodeType.includes('sink')) {
    return 'sinkNode'; // 输出节点类型
  }
  // 默认节点类型
  return 'defaultNode';
}

/**
 * 生成节点的布局位置
 * @param {Array} nodes 节点数组
 * @returns {Object} 包含每个节点ID和其位置的对象
 */
function generateNodesLayout(nodes) {
  const layout = {};
  
  // 这是一个简单的层次布局算法示例
  // 实际应用中可以使用更复杂的布局算法
  
  // 识别节点类型和依赖关系
  const nodeTypes = {
    source: [],
    processor: [],
    sink: []
  };
  
  // 将节点按类型分类
  nodes.forEach(node => {
    if (node.type.includes('src') || node.type.includes('input')) {
      nodeTypes.source.push(node.id);
    } else if (node.type.includes('output') || node.type.includes('sink')) {
      nodeTypes.sink.push(node.id);
    } else {
      nodeTypes.processor.push(node.id);
    }
  });
  
  // 为每种类型的节点分配位置
  const xSpacing = 300;
  const ySpacing = 150;
  
  // 源节点放在最左边
  nodeTypes.source.forEach((nodeId, index) => {
    layout[nodeId] = {
      x: 100,
      y: 100 + index * ySpacing
    };
  });
  
  // 处理节点放在中间
  nodeTypes.processor.forEach((nodeId, index) => {
    layout[nodeId] = {
      x: 100 + xSpacing,
      y: 100 + index * ySpacing
    };
  });
  
  // 输出节点放在最右边
  nodeTypes.sink.forEach((nodeId, index) => {
    layout[nodeId] = {
      x: 100 + xSpacing * 2,
      y: 100 + index * ySpacing
    };
  });
  
  return layout;
}
```

#### 3.2 连接转换函数

将API连接数据转换为Vue Flow边格式：

```javascript
// flowDataTransformer.js

/**
 * 将API连接数据转换为Vue Flow边格式
 * @param {Array} apiConnections API返回的连接数据
 * @returns {Array} Vue Flow格式的边数组
 */
export function convertToVueFlowEdges(apiConnections) {
  const edges = [];
  
  apiConnections.forEach(conn => {
    // 处理单一源连接
    if (conn.from.is_single) {
      edges.push({
        id: `edge-${conn.id}`,
        source: conn.from.source,
        target: conn.to,
        // 可以设置为true以显示动画效果
        animated: true,
        // 边的样式
        style: { stroke: '#b1b1b7', strokeWidth: 2 },
        // 标签（可选）
        label: `连接 ${conn.id}`,
        // 类型（可选，如果有自定义边组件）
        type: 'default'
      });
    } 
    // 处理多源连接
    else {
      conn.from.sources.forEach((source, idx) => {
        edges.push({
          id: `edge-${conn.id}-${idx}`,
          source: source,
          target: conn.to,
          animated: true,
          style: { stroke: '#b1b1b7', strokeWidth: 2 },
          label: `连接 ${conn.id}-${idx}`,
          type: 'default'
        });
      });
    }
  });
  
  return edges;
}
``` 

### 4. Vue Flow组件实现

创建主要的Vue Flow管道图组件：

```vue
<!-- PipelineFlowChart.vue -->
<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { VueFlow, Background, MiniMap, Controls } from '@vue-flow/core';
import { fetchPipelineData } from './pipelineService';
import { convertToVueFlowNodes, convertToVueFlowEdges } from './flowDataTransformer';

// 引入自定义节点组件
import SourceNode from './nodes/SourceNode.vue';
import ProcessorNode from './nodes/ProcessorNode.vue';
import SinkNode from './nodes/SinkNode.vue';
import DefaultNode from './nodes/DefaultNode.vue';

// 接收属性
const props = defineProps({
  pipelineId: {
    type: String,
    required: true
  },
  autoRefresh: {
    type: Boolean,
    default: true
  },
  refreshInterval: {
    type: Number,
    default: 5000
  }
});

// 事件
const emit = defineEmits(['nodeClick', 'edgeClick', 'error', 'dataLoaded']);

// 状态
const nodes = ref([]);
const edges = ref([]);
const isLoading = ref(true);
const error = ref(null);

// 注册自定义节点类型
const nodeTypes = {
  sourceNode: SourceNode,
  processorNode: ProcessorNode,
  sinkNode: SinkNode,
  defaultNode: DefaultNode
};

// 默认选项
const defaultOptions = {
  fitView: true,
  minZoom: 0.2,
  maxZoom: 4,
  snapToGrid: true,
  snapGrid: [20, 20]
};

// 加载数据
async function loadPipelineData() {
  try {
    isLoading.value = true;
    error.value = null;
    
    const data = await fetchPipelineData(props.pipelineId);
    
    // 转换数据为Vue Flow格式
    nodes.value = convertToVueFlowNodes(data.nodes, data.status);
    edges.value = convertToVueFlowEdges(data.connections);
    
    // 通知父组件数据加载完成
    emit('dataLoaded', { nodes: nodes.value, edges: edges.value });
  } catch (err) {
    console.error('加载管道数据失败:', err);
    error.value = err.message || '加载数据失败';
    emit('error', error.value);
  } finally {
    isLoading.value = false;
  }
}

// 更新节点状态
async function updateNodesStatus() {
  try {
    const { status } = await fetchPipelineData(props.pipelineId);
    
    if (status && status.nodes) {
      try {
        const parsedNodes = JSON.parse(status.nodes);
        
        // 更新节点状态但不改变位置
        nodes.value = nodes.value.map(node => {
          const nodeStatus = parsedNodes.find(n => n.id === node.id);
          if (nodeStatus) {
            return {
              ...node,
              data: {
                ...node.data,
                state: nodeStatus.state,
                metrics: nodeStatus.metrics
              },
              class: `node-${nodeStatus.state || 'idle'}`
            };
          }
          return node;
        });
      } catch (e) {
        console.error('解析节点状态数据失败', e);
      }
    }
  } catch (err) {
    console.warn('更新节点状态失败', err);
  }
}

// 事件处理
function onNodeClick(_, node) {
  emit('nodeClick', node);
}

function onEdgeClick(_, edge) {
  emit('edgeClick', edge);
}

// 自动刷新状态
let refreshTimer;

function startStatusRefresh() {
  if (props.autoRefresh) {
    refreshTimer = setInterval(updateNodesStatus, props.refreshInterval);
  }
}

function stopStatusRefresh() {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
}

// 监听属性变化
watch(() => props.pipelineId, (newId) => {
  if (newId) {
    loadPipelineData();
  }
});

watch(() => props.autoRefresh, (newVal) => {
  if (newVal) {
    startStatusRefresh();
  } else {
    stopStatusRefresh();
  }
});

// 生命周期钩子
onMounted(() => {
  loadPipelineData();
  startStatusRefresh();
});

onBeforeUnmount(() => {
  stopStatusRefresh();
});
</script>

<template>
  <div class="pipeline-flow-chart">
    <div v-if="isLoading" class="loading-overlay">
      <div class="spinner"></div>
      <p>加载管道图...</p>
    </div>
    
    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="loadPipelineData" class="retry-button">重试</button>
    </div>
    
    <div v-else class="flow-container">
      <VueFlow
        v-model:nodes="nodes"
        v-model:edges="edges"
        :node-types="nodeTypes"
        :default-viewport="{ x: 0, y: 0, zoom: 1 }"
        :default-options="defaultOptions"
        @node-click="onNodeClick"
        @edge-click="onEdgeClick"
      >
        <Background pattern-color="#aaa" gap="8" />
        <MiniMap />
        <Controls />
        
        <template #node-context-menu="{ node }">
          <div class="context-menu">
            <button @click="$emit('editNode', node)">编辑节点</button>
            <button @click="$emit('deleteNode', node)">删除节点</button>
          </div>
        </template>
      </VueFlow>
    </div>
  </div>
</template>

<style>
.pipeline-flow-chart {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 500px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #09f;
  animation: spin 1s ease infinite;
}

.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.error-message {
  color: #ef4444;
  margin-bottom: 20px;
}

.retry-button {
  background-color: #3b82f6;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}

.flow-container {
  width: 100%;
  height: 100%;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 节点状态样式 */
.node-running {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3) !important;
}

.node-error {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3) !important;
}

.node-stopped {
  border-color: #6b7280 !important;
}

.node-idle {
  border-color: #9ca3af !important;
}
</style>

### 5. 自定义节点实现

#### 5.1 基础节点组件

创建一个基础节点组件，其他特定类型节点可以继承它：

```vue
<!-- nodes/BaseNode.vue -->
<script setup>
import { computed } from 'vue';
import { Handle, Position } from '@vue-flow/core';

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  selected: Boolean,
  id: String
});

// 根据节点状态计算样式类名
const nodeStateClass = computed(() => `node-state-${props.data.state || 'idle'}`);

// 根据节点状态计算状态文本
const stateText = computed(() => {
  switch (props.data.state) {
    case 'running': return '运行中';
    case 'error': return '错误';
    case 'stopped': return '已停止';
    default: return '空闲';
  }
});

// 根据节点状态计算状态颜色
const stateColor = computed(() => {
  switch (props.data.state) {
    case 'running': return '#10b981';
    case 'error': return '#ef4444';
    case 'stopped': return '#6b7280';
    default: return '#9ca3af';
  }
});

// 是否显示指标数据
const showMetrics = computed(() => 
  props.data.state === 'running' && 
  props.data.metrics && 
  Object.keys(props.data.metrics).length > 0
);
</script>

<template>
  <div 
    class="custom-node" 
    :class="[nodeStateClass, { selected }]"
  >
    <Handle type="target" position="top" />
    
    <div class="node-header" :style="{ backgroundColor: stateColor }">
      <div class="node-title">{{ data.id }}</div>
      <div class="node-state">{{ stateText }}</div>
    </div>
    
    <div class="node-content">
      <div class="node-type">{{ data.typeName || data.type }}</div>
      
      <slot name="params">
        <!-- 参数插槽 -->
      </slot>
      
      <div v-if="showMetrics" class="node-metrics">
        <div class="metrics-title">运行指标</div>
        <div 
          v-for="(value, key) in data.metrics" 
          :key="key" 
          class="metric-item"
        >
          <span class="metric-key">{{ key }}:</span>
          <span class="metric-value">{{ value }}</span>
        </div>
      </div>
    </div>
    
    <Handle type="source" position="bottom" />
  </div>
</template>

<style scoped>
.custom-node {
  background: white;
  border: 2px solid #ccc;
  border-radius: 8px;
  width: 220px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  font-family: sans-serif;
  overflow: hidden;
}

.custom-node.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

.node-header {
  padding: 8px 12px;
  background-color: #9ca3af;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.node-title {
  font-weight: bold;
  font-size: 14px;
}

.node-state {
  font-size: 12px;
  padding: 2px 6px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.node-content {
  padding: 12px;
}

.node-type {
  font-size: 13px;
  color: #4b5563;
  margin-bottom: 8px;
}

.node-metrics {
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px dashed #e5e7eb;
}

.metrics-title {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #4b5563;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin: 2px 0;
}

.metric-key {
  color: #6b7280;
}

.metric-value {
  font-weight: 600;
  color: #1f2937;
}

/* 节点状态特定样式 */
.node-state-running .node-header {
  background-color: #10b981;
}

.node-state-error .node-header {
  background-color: #ef4444;
}

.node-state-stopped .node-header {
  background-color: #6b7280;
}

.node-state-idle .node-header {
  background-color: #9ca3af;
}
</style>

#### 5.2 源节点组件

创建源节点组件（如文件输入源等）：

```vue
<!-- nodes/SourceNode.vue -->
<script setup>
import BaseNode from './BaseNode.vue';

defineProps({
  data: {
    type: Object,
    required: true
  },
  selected: Boolean,
  id: String
});
</script>

<template>
  <BaseNode :data="data" :selected="selected" :id="id">
    <template #params>
      <div class="node-params">
        <div v-for="(value, key) in data.params" :key="key" class="param-item">
          <span class="param-key">{{ key }}:</span>
          <span class="param-value">{{ value }}</span>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<style scoped>
.node-params {
  font-size: 12px;
  background-color: #f9fafb;
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
}

.param-item {
  display: flex;
  margin-bottom: 4px;
}

.param-key {
  min-width: 100px;
  color: #4b5563;
}

.param-value {
  font-weight: 500;
  word-break: break-all;
}
</style>
```

#### 5.3 处理节点组件

创建处理节点组件（如检测器、跟踪器等）：

```vue
<!-- nodes/ProcessorNode.vue -->
<script setup>
import BaseNode from './BaseNode.vue';

defineProps({
  data: {
    type: Object,
    required: true
  },
  selected: Boolean,
  id: String
});
</script>

<template>
  <BaseNode :data="data" :selected="selected" :id="id">
    <template #params>
      <div class="processor-params">
        <div v-for="(value, key) in data.params" :key="key" class="param-item">
          <span class="param-key">{{ key }}:</span>
          <span class="param-value">{{ value }}</span>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<style scoped>
.processor-params {
  font-size: 12px;
  background-color: #f0f9ff;
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
  border-left: 3px solid #3b82f6;
}

.param-item {
  display: flex;
  margin-bottom: 4px;
}

.param-key {
  min-width: 100px;
  color: #1e40af;
}

.param-value {
  font-weight: 500;
  word-break: break-all;
}
</style>
```

#### 5.4 输出节点组件

类似地，创建输出节点组件：

```vue
<!-- nodes/SinkNode.vue -->
<script setup>
import BaseNode from './BaseNode.vue';

defineProps({
  data: {
    type: Object,
    required: true
  },
  selected: Boolean,
  id: String
});
</script>

<template>
  <BaseNode :data="data" :selected="selected" :id="id">
    <template #params>
      <div class="sink-params">
        <div v-for="(value, key) in data.params" :key="key" class="param-item">
          <span class="param-key">{{ key }}:</span>
          <span class="param-value">{{ value }}</span>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<style scoped>
.sink-params {
  font-size: 12px;
  background-color: #f0fdf4;
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
  border-left: 3px solid #10b981;
}

.param-item {
  display: flex;
  margin-bottom: 4px;
}

.param-key {
  min-width: 100px;
  color: #065f46;
}

.param-value {
  font-weight: 500;
  word-break: break-all;
}
</style> 
```
</template>

### 6. 实时状态更新

实时更新管道状态是显示实时运行信息的关键部分。以下代码展示了如何实现：

```javascript
// statusUpdater.js

/**
 * 用于实时更新管道状态的服务
 */
export class PipelineStatusUpdater {
  constructor(pipelineId, apiClient, updateInterval = 5000) {
    this.pipelineId = pipelineId;
    this.apiClient = apiClient;
    this.updateInterval = updateInterval;
    this.timerId = null;
    this.onUpdate = null;
    this.onError = null;
  }
  
  /**
   * 启动状态更新
   * @param {Function} onUpdate 状态更新回调
   * @param {Function} onError 错误回调
   */
  start(onUpdate, onError) {
    if (this.timerId) {
      this.stop();
    }
    
    this.onUpdate = onUpdate;
    this.onError = onError;
    
    // 立即获取一次状态
    this.fetchStatus();
    
    // 设置定时器定期获取状态
    this.timerId = setInterval(() => {
      this.fetchStatus();
    }, this.updateInterval);
  }
  
  /**
   * 停止状态更新
   */
  stop() {
    if (this.timerId) {
      clearInterval(this.timerId);
      this.timerId = null;
    }
  }
  
  /**
   * 获取当前管道状态
   */
  async fetchStatus() {
    try {
      const response = await this.apiClient.getStatus(this.pipelineId);
      
      if (response.data.status === 'success' && this.onUpdate) {
        this.onUpdate(response.data.data);
      }
    } catch (error) {
      if (this.onError) {
        this.onError(error);
      } else {
        console.warn('获取管道状态失败:', error);
      }
    }
  }
}
```

### 7. 事件处理与交互

为流程图添加交互功能，使用Vue Flow提供的事件：

```vue
<!-- PipelineInteractionDemo.vue -->
<script setup>
import { ref } from 'vue';
import PipelineFlowChart from './PipelineFlowChart.vue';
import NodeDetailsPanel from './NodeDetailsPanel.vue';

const pipelineId = ref('face_detection_pipeline');
const selectedNode = ref(null);
const showNodeDetails = ref(false);

// 节点点击事件处理
function handleNodeClick(node) {
  selectedNode.value = node;
  showNodeDetails.value = true;
}

// 关闭节点详情面板
function closeNodeDetails() {
  showNodeDetails.value = false;
}

// 处理节点编辑
function handleEditNode(node) {
  // 实现编辑节点逻辑...
  console.log('编辑节点:', node);
}

// 处理节点删除
function handleDeleteNode(node) {
  // 实现删除节点逻辑...
  console.log('删除节点:', node);
  
  // 这里应该调用API删除节点
  // 例如：await pipelineApi.deleteNode(pipelineId.value, node.id);
  // 然后重新加载管道数据
}
</script>

<template>
  <div class="pipeline-demo">
    <div class="pipeline-header">
      <h2>管道流程图: {{ pipelineId }}</h2>
      <div class="pipeline-controls">
        <button @click="$emit('refreshData')">刷新</button>
      </div>
    </div>
    
    <div class="pipeline-content">
      <PipelineFlowChart 
        :pipelineId="pipelineId"
        :autoRefresh="true"
        :refreshInterval="5000"
        @nodeClick="handleNodeClick"
        @editNode="handleEditNode"
        @deleteNode="handleDeleteNode"
        class="pipeline-chart"
      />
    </div>
    
    <NodeDetailsPanel 
      v-if="showNodeDetails && selectedNode"
      :node="selectedNode"
      @close="closeNodeDetails"
    />
  </div>
</template>

<style scoped>
.pipeline-demo {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pipeline-header {
  padding: 16px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pipeline-content {
  flex: 1;
  position: relative;
}

.pipeline-chart {
  width: 100%;
  height: 100%;
}

.pipeline-controls button {
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
}
</style>
```

## 完整代码示例

以下是如何在Vue项目中整合以上所有组件来显示VideoPipe管道的流程图：

```vue
<!-- PipelineViewer.vue -->
<script setup>
import { ref, onMounted } from 'vue';
import PipelineFlowChart from './components/PipelineFlowChart.vue';
import { pipelineApi } from './api';

const pipelineId = ref('face_detection_pipeline');
const isLoading = ref(true);
const error = ref(null);
const pipelines = ref([]);

// 获取所有管道
async function loadPipelines() {
  try {
    isLoading.value = true;
    error.value = null;
    
    const response = await pipelineApi.getPipelines();
    
    if (response.data.status === 'success') {
      pipelines.value = response.data.data.pipelines || [];
      
      // 如果有管道，默认选中第一个
      if (pipelines.value.length > 0 && !pipelineId.value) {
        pipelineId.value = pipelines.value[0].id;
      }
    } else {
      error.value = '无法加载管道列表';
    }
  } catch (err) {
    console.error('加载管道列表失败:', err);
    error.value = err.message || '加载数据失败';
  } finally {
    isLoading.value = false;
  }
}

// 切换选中的管道
function changePipeline(id) {
  pipelineId.value = id;
}

// 管道图数据加载完成事件
function handleDataLoaded(data) {
  console.log('管道数据加载完成:', data);
  // 可以在这里执行数据加载后的逻辑
}

// 处理错误
function handleError(errorMsg) {
  error.value = errorMsg;
}

// 生命周期钩子
onMounted(() => {
  loadPipelines();
});
</script>

<template>
  <div class="pipeline-viewer">
    <!-- 管道选择器 -->
    <div class="pipeline-selector">
      <h3>选择管道</h3>
      <div v-if="isLoading">加载中...</div>
      <div v-else-if="error" class="error-message">{{ error }}</div>
      <div v-else-if="pipelines.length === 0" class="empty-message">
        没有可用的管道
      </div>
      <div v-else class="pipeline-list">
        <div 
          v-for="pipeline in pipelines" 
          :key="pipeline.id"
          class="pipeline-item"
          :class="{ active: pipelineId === pipeline.id }"
          @click="changePipeline(pipeline.id)"
        >
          {{ pipeline.name || pipeline.id }}
        </div>
      </div>
    </div>
    
    <!-- 管道流程图 -->
    <div class="pipeline-container">
      <div v-if="!pipelineId" class="empty-state">
        请选择一个管道以查看流程图
      </div>
      <PipelineFlowChart 
        v-else
        :pipelineId="pipelineId"
        :autoRefresh="true"
        :refreshInterval="5000"
        @dataLoaded="handleDataLoaded"
        @error="handleError"
        class="flow-chart"
      />
    </div>
  </div>
</template>

<style scoped>
.pipeline-viewer {
  display: flex;
  height: 100vh;
}

.pipeline-selector {
  width: 250px;
  padding: 16px;
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
  overflow-y: auto;
}

.pipeline-container {
  flex: 1;
  position: relative;
}

.flow-chart {
  width: 100%;
  height: 100%;
}

.pipeline-list {
  margin-top: 16px;
}

.pipeline-item {
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 4px;
}

.pipeline-item:hover {
  background-color: #e2e8f0;
}

.pipeline-item.active {
  background-color: #dbeafe;
  color: #1e40af;
  font-weight: 500;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #64748b;
  font-size: 16px;
}

.error-message {
  color: #ef4444;
  padding: 10px;
  margin-top: 10px;
  background-color: #fee2e2;
  border-radius: 4px;
}

.empty-message {
  color: #64748b;
  padding: 10px;
}
</style>
```

## 进阶功能

### 节点参数编辑

创建一个节点参数编辑对话框：

```vue
<!-- NodeEditDialog.vue -->
<script setup>
import { ref, computed, watch } from 'vue';
import { pipelineApi } from '../api';

const props = defineProps({
  node: {
    type: Object,
    required: true
  },
  pipelineId: {
    type: String,
    required: true
  },
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'updated']);

// 创建本地参数副本，以便编辑
const localParams = ref({});
const isSaving = ref(false);
const error = ref(null);

// 计算参数类型
const paramTypes = computed(() => {
  if (!props.node || !props.node.data || !props.node.data.params) {
    return {};
  }
  
  // 实际项目中应该从节点类型信息中获取参数类型
  const types = {};
  Object.keys(props.node.data.params).forEach(key => {
    // 根据值的格式推断类型
    const value = props.node.data.params[key];
    if (value === 'true' || value === 'false') {
      types[key] = 'boolean';
    } else if (!isNaN(parseFloat(value)) && isFinite(value)) {
      if (value.includes('.')) {
        types[key] = 'float';
      } else {
        types[key] = 'int';
      }
    } else {
      types[key] = 'string';
    }
  });
  
  return types;
});

// 观察属性变化
watch(
  () => props.visible,
  (newValue) => {
    if (newValue && props.node && props.node.data && props.node.data.params) {
      // 复制参数以便编辑
      localParams.value = { ...props.node.data.params };
      error.value = null;
    }
  }
);

// 保存更改
async function saveChanges() {
  if (!props.node || !props.pipelineId) {
    return;
  }
  
  try {
    isSaving.value = true;
    error.value = null;
    
    // 构建参数对象
    const updateData = {
      params: localParams.value
    };
    
    // 调用API更新节点
    const response = await pipelineApi.updateNode(
      props.pipelineId, 
      props.node.id, 
      updateData
    );
    
    if (response.data.status === 'success') {
      emit('updated', {
        id: props.node.id,
        params: localParams.value
      });
      emit('close');
    } else {
      error.value = '保存参数失败';
    }
  } catch (err) {
    console.error('更新节点参数失败:', err);
    error.value = err.message || '保存参数失败';
  } finally {
    isSaving.value = false;
  }
}
</script>

<template>
  <div v-if="visible" class="dialog-backdrop">
    <div class="dialog-container">
      <div class="dialog-header">
        <h3>编辑节点: {{ node.data.id }}</h3>
        <button @click="$emit('close')" class="close-button">&times;</button>
      </div>
      
      <div class="dialog-content">
        <div v-if="error" class="error-message">{{ error }}</div>
        
        <div class="params-form">
          <div 
            v-for="(value, key) in localParams" 
            :key="key" 
            class="param-field"
          >
            <label :for="`param-${key}`">{{ key }}</label>
            
            <!-- 根据参数类型渲染不同的输入控件 -->
            <input 
              v-if="paramTypes[key] === 'boolean'"
              type="checkbox"
              :id="`param-${key}`"
              v-model="localParams[key]"
              true-value="true"
              false-value="false"
            />
            
            <input 
              v-else-if="paramTypes[key] === 'int'"
              type="number"
              :id="`param-${key}`"
              v-model="localParams[key]"
              step="1"
            />
            
            <input 
              v-else-if="paramTypes[key] === 'float'"
              type="number"
              :id="`param-${key}`"
              v-model="localParams[key]"
              step="0.1"
            />
            
            <input 
              v-else
              type="text"
              :id="`param-${key}`"
              v-model="localParams[key]"
            />
          </div>
        </div>
      </div>
      
      <div class="dialog-footer">
        <button 
          @click="$emit('close')" 
          class="cancel-button" 
          :disabled="isSaving"
        >
          取消
        </button>
        
        <button 
          @click="saveChanges" 
          class="save-button" 
          :disabled="isSaving"
        >
          {{ isSaving ? '保存中...' : '保存' }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dialog-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-container {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 95%;
  max-height: 95%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.dialog-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-content {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
}

.dialog-footer {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #6b7280;
}

.cancel-button {
  padding: 8px 16px;
  background-color: #e5e7eb;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #1f2937;
}

.save-button {
  padding: 8px 16px;
  background-color: #3b82f6;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: white;
}

.save-button:disabled, .cancel-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.error-message {
  color: #ef4444;
  padding: 10px;
  margin-bottom: 16px;
  background-color: #fee2e2;
  border-radius: 4px;
}

.params-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.param-field {
  display: flex;
  flex-direction: column;
}

.param-field label {
  font-size: 14px;
  color: #4b5563;
  margin-bottom: 4px;
}

.param-field input[type="text"],
.param-field input[type="number"] {
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}

.param-field input[type="checkbox"] {
  width: 20px;
  height: 20px;
}
</style>
```

### 使用Vue Router整合到应用中

如何将管道流程图整合到一个使用Vue Router的应用中：

```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router';
import Dashboard from '../views/Dashboard.vue';
import PipelineList from '../views/PipelineList.vue';
import PipelineViewer from '../views/PipelineViewer.vue';

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard
  },
  {
    path: '/pipelines',
    name: 'Pipelines',
    component: PipelineList
  },
  {
    path: '/pipelines/:id',
    name: 'PipelineViewer',
    component: PipelineViewer,
    props: true
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

export default router;
```

```vue
<!-- views/PipelineViewer.vue -->
<script setup>
import { ref, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import PipelineFlowChart from '../components/PipelineFlowChart.vue';
import NodeEditDialog from '../components/NodeEditDialog.vue';

const route = useRoute();
const router = useRouter();
const pipelineId = ref(route.params.id);
const selectedNode = ref(null);
const showEditDialog = ref(false);

// 监听路由参数变化
watch(() => route.params.id, (newId) => {
  pipelineId.value = newId;
});

function handleNodeClick(node) {
  selectedNode.value = node;
}

function handleEditNode() {
  if (selectedNode.value) {
    showEditDialog.value = true;
  }
}

function handleNodeUpdated() {
  // 重新加载管道数据
  // 可以通过ref调用子组件的方法
}
</script>

<template>
  <div class="pipeline-viewer-page">
    <h1>管道: {{ pipelineId }}</h1>
    
    <div class="pipeline-actions">
      <button @click="router.push('/pipelines')">返回列表</button>
      <button v-if="selectedNode" @click="handleEditNode">编辑选中节点</button>
    </div>
    
    <div class="flow-chart-container">
      <PipelineFlowChart 
        :pipelineId="pipelineId"
        @nodeClick="handleNodeClick"
        class="flow-chart"
      />
    </div>
    
    <NodeEditDialog
      v-if="selectedNode"
      :node="selectedNode"
      :pipelineId="pipelineId"
      :visible="showEditDialog"
      @close="showEditDialog = false"
      @updated="handleNodeUpdated"
    />
  </div>
</template>

<style scoped>
.pipeline-viewer-page {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.pipeline-actions {
  margin-bottom: 16px;
  display: flex;
  gap: 10px;
}

.flow-chart-container {
  flex: 1;
  position: relative;
}

.flow-chart {
  height: 100%;
  width: 100%;
}
</style>
```

### 8. 总结与最佳实践

通过以上步骤，我们已经实现了一个基于Vue Flow和VideoPipe HTTP API的管道流程可视化系统。以下是一些最佳实践和建议：

#### 性能优化

1. **避免频繁更新状态**：
   - 通过合理设置刷新间隔（如5秒）避免API请求过于频繁
   - 使用局部更新而非完全重绘，只更新状态变化的部分

2. **大型管道处理**：
   - 对于大型管道（节点数 > 50），考虑使用分层显示或按区域折叠
   - 实现自定义视图缓存机制，避免重复计算布局

3. **懒加载组件**：
   - 对于不常用的复杂组件，如编辑对话框，使用Vue的动态导入实现懒加载

#### 扩展功能

1. **管道状态历史记录**：
   - 添加状态历史记录功能，记录并可视化管道运行过程中的关键状态变化

2. **节点搜索与过滤**：
   - 实现按节点ID、类型或状态搜索的功能
   - 添加过滤器，如"只显示错误节点"或"只显示活动节点"

3. **管道创建与编辑**：
   - 实现拖拽式的节点创建
   - 通过拖拽连接点创建连接
   - 实现撤销/重做功能

4. **导出与共享**：
   - 添加流程图导出为图片或PDF的功能
   - 实现管道配置导入/导出

#### 错误处理

1. **优雅的错误恢复**：
   - 实现自动重试机制，特别是对于网络错误
   - 保存本地状态，以便在出现错误时恢复

2. **用户友好的错误提示**：
   - 使用上下文相关的错误消息
   - 为常见错误提供解决步骤

## 结语

通过集成Vue Flow和VideoPipe HTTP API，我们创建了一个功能强大、交互丰富的管道流程可视化工具。该工具不仅展示了管道的静态结构，还提供了实时状态监控、节点交互等功能，使用户可以直观地了解和管理视频处理管道的运行状况。

根据具体需求，这个基础实现可以进一步扩展，添加更多功能和优化，如节点分组、性能监控图表、历史数据分析等，构建更加全面的管道管理系统。
