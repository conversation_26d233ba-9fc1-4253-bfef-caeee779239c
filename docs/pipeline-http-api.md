# VideoPipe HTTP API 文档

VideoPipe HTTP API 提供了一组RESTful接口，用于管理视频处理管道的配置和运行时状态。本文档介绍了API的使用方法和示例。

## 认证

所有API请求都需要在HTTP头部包含API密钥：

```
X-API-Key: your-api-key
```

未提供有效API密钥的请求将收到401错误响应。

## 响应格式

### 成功响应

成功的响应采用以下格式：

```json
{
  "status": "success",
  "data": {
    // 响应数据，根据API端点不同而不同
  }
}
```

### 错误响应

错误响应采用以下格式：

```json
{
  "status": "error",
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息（可选）"
  }
}
```

## API 端点

### 管道管理

#### 获取所有管道

- **URL**: `/api/v1/pipelines`
- **方法**: `GET`
- **描述**: 获取所有可用的管道配置

**请求示例**:

```bash
curl -X GET \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "pipelines": [
      {
        "id": "face_detection_pipeline",
        "name": "人脸检测管道",
        "description": "用于检测视频中的人脸",
        "version": "1.0",
        "author": "VideoPipe Team",
        "created_at": "2023-06-15T10:30:00Z",
        "updated_at": "2023-06-15T10:30:00Z"
      },
      {
        "id": "object_tracking_pipeline",
        "name": "物体跟踪管道",
        "description": "用于跟踪视频中的物体",
        "version": "1.0",
        "author": "VideoPipe Team",
        "created_at": "2023-06-16T09:15:00Z",
        "updated_at": "2023-06-16T09:15:00Z"
      }
    ]
  }
}
```

#### 获取特定管道

- **URL**: `/api/v1/pipelines/:id`
- **方法**: `GET`
- **描述**: 获取特定管道的配置详情

**请求示例**:

```bash
curl -X GET \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "id": "face_detection_pipeline",
    "name": "人脸检测管道",
    "description": "用于检测视频中的人脸",
    "version": "1.0",
    "author": "VideoPipe Team",
    "globals": {
      "model_dir": "./models/",
      "resize_ratio": "0.5"
    },
    "created_at": "2023-06-15T10:30:00Z",
    "updated_at": "2023-06-15T10:30:00Z"
  }
}
```

#### 创建管道

- **URL**: `/api/v1/pipelines`
- **方法**: `POST`
- **描述**: 创建新的管道配置

**请求示例**:

```bash
curl -X POST \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "new_pipeline",
    "name": "新管道",
    "description": "测试用管道",
    "version": "1.0",
    "author": "API用户",
    "globals": {
      "model_dir": "./models/",
      "resize_ratio": "0.5"
    }
  }' \
  http://localhost:8080/api/v1/pipelines
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "id": "new_pipeline",
    "name": "新管道",
    "created_at": "2023-06-20T15:45:30Z"
  }
}
```

#### 更新管道

- **URL**: `/api/v1/pipelines/:id`
- **方法**: `PUT`
- **描述**: 更新现有管道的配置

**请求示例**:

```bash
curl -X PUT \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的管道名称",
    "description": "更新后的描述"
  }' \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "id": "face_detection_pipeline",
    "name": "更新后的管道名称",
    "updated_at": "2023-06-20T16:10:45Z"
  }
}
```

#### 删除管道

- **URL**: `/api/v1/pipelines/:id`
- **方法**: `DELETE`
- **描述**: 删除指定的管道配置

**请求示例**:

```bash
curl -X DELETE \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/new_pipeline
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "message": "管道配置已成功删除"
  }
}
```

#### 导出管道配置

- **URL**: `/api/v1/pipelines/:id/export`
- **方法**: `GET`
- **描述**: 将管道配置导出为YAML格式

**请求示例**:

```bash
curl -X GET \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/export
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "yaml_content": "id: face_detection_pipeline\nname: 人脸检测管道\ndescription: 用于检测视频中的人脸\nversion: 1.0\nauthor: VideoPipe Team\nglobals:\n  model_dir: ./models/\n  resize_ratio: 0.5\nnodes:\n  - id: file_src_0\n    type: vp_file_src_node\n    params:\n      channel_index: 0\n      file_path: ./video.mp4\nconnections:\n  - id: 1\n    from: file_src_0\n    to: face_detector"
  }
}
```

#### 导入管道配置

- **URL**: `/api/v1/pipelines/import`
- **方法**: `POST`
- **描述**: 从YAML导入管道配置

**请求示例**:

```bash
curl -X POST \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "yaml_content": "id: imported_pipeline\nname: 导入的管道\ndescription: 从YAML导入的管道\nversion: 1.0\nauthor: API用户\nglobals:\n  key1: value1\nnodes:\n  - id: node1\n    type: vp_file_src_node\n    params:\n      channel_index: 0\n      file_path: ./video.mp4\nconnections:\n  - id: 1\n    from: node1\n    to: node2"
  }' \
  http://localhost:8080/api/v1/pipelines/import
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "id": "imported_pipeline",
    "name": "导入的管道",
    "created_at": "2023-06-20T16:30:15Z"
  }
}
```

### 节点类型管理

#### 获取所有节点类型

- **URL**: `/api/v1/node-types`
- **方法**: `GET`
- **描述**: 获取系统支持的所有节点类型

**请求示例**:

```bash
curl -X GET \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/node-types
```

**响应示例**:

```json
{
  "status": "success",
  "data": [
    {
      "type_name": "vp_yunet_face_detector_node",
      "description": "YuNet人脸检测节点",
      "params": [
        [
          {
            "key": "description",
            "value": "模型路径"
          },
          {
            "key": "default_value",
            "value": ""
          },
          {
            "key": "required",
            "value": "true"
          },
          {
            "key": "name",
            "value": "model_path"
          },
          {
            "key": "type",
            "value": "string"
          }
        ],
        [
          {
            "key": "description",
            "value": "置信度阈值"
          },
          {
            "key": "default_value",
            "value": "0.9"
          },
          {
            "key": "required",
            "value": "false"
          },
          {
            "key": "name",
            "value": "confidence_threshold"
          },
          {
            "key": "type",
            "value": "float"
          }
        ]
      ]
    },
    {
      "type_name": "vp_file_src_node",
      "description": "文件源节点，用于从视频文件读取帧",
      "params": [
        [
          {
            "key": "description",
            "value": "通道索引"
          },
          {
            "key": "default_value",
            "value": ""
          },
          {
            "key": "required",
            "value": "true"
          },
          {
            "key": "name",
            "value": "channel_index"
          },
          {
            "key": "type",
            "value": "int"
          }
        ],
        [
          {
            "key": "description",
            "value": "视频文件路径"
          },
          {
            "key": "default_value",
            "value": ""
          },
          {
            "key": "required",
            "value": "true"
          },
          {
            "key": "name",
            "value": "file_path"
          },
          {
            "key": "type",
            "value": "string"
          }
        ],
        [
          {
            "key": "description",
            "value": "缩放比例"
          },
          {
            "key": "default_value",
            "value": "1.0"
          },
          {
            "key": "required",
            "value": "false"
          },
          {
            "key": "name",
            "value": "resize_ratio"
          },
          {
            "key": "type",
            "value": "float"
          }
        ]
      ]
    }
  ]
}
```

### 节点管理

#### 获取管道中的所有节点

- **URL**: `/api/v1/pipelines/:id/nodes`
- **方法**: `GET`
- **描述**: 获取指定管道中的所有节点

**请求示例**:

```bash
curl -X GET \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/nodes
```

**响应示例**:

```json
{
  "status": "success",
  "data": [
    {
      "id": "file_src_0",
      "type": "vp_file_src_node",
      "params": {
        "channel_index": "0",
        "file_path": "./video.mp4",
        "resize_ratio": "0.5"
      }
    },
    {
      "id": "face_detector",
      "type": "vp_yunet_face_detector_node",
      "params": {
        "model_path": "./models/face_detection.onnx",
        "confidence_threshold": "0.9"
      }
    }
  ]
}
```

#### 获取管道中的特定节点

- **URL**: `/api/v1/pipelines/:id/nodes/:node_id`
- **方法**: `GET`
- **描述**: 获取指定管道中的特定节点

**请求示例**:

```bash
curl -X GET \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/nodes/file_src_0
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "id": "file_src_0",
    "type": "vp_file_src_node",
    "params": {
      "channel_index": "0",
      "file_path": "./video.mp4",
      "resize_ratio": "0.5"
    }
  }
}
```

#### 创建节点

- **URL**: `/api/v1/pipelines/:id/nodes`
- **方法**: `POST`
- **描述**: 在指定管道中创建新节点

**请求示例**:

```bash
curl -X POST \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "new_node",
    "type": "vp_file_src_node",
    "params": {
      "channel_index": "0",
      "file_path": "./video.mp4",
      "resize_ratio": "0.5"
    }
  }' \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/nodes
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "id": "new_node",
    "type": "vp_file_src_node"
  }
}
```

#### 更新节点

- **URL**: `/api/v1/pipelines/:id/nodes/:node_id`
- **方法**: `PUT`
- **描述**: 更新指定管道中的特定节点

**请求示例**:

```bash
curl -X PUT \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "params": {
      "resize_ratio": "0.8"
    },
    "test1": "aa"
  }' \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/nodes/file_src_0
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "id": "file_src_0",
    "type": "vp_file_src_node"
  }
}
```

#### 删除节点

- **URL**: `/api/v1/pipelines/:id/nodes/:node_id`
- **方法**: `DELETE`
- **描述**: 删除指定管道中的特定节点

**请求示例**:

```bash
curl -X DELETE \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/nodes/file_src_0
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "message": "节点已成功删除"
  }
}
```

#### 批量创建节点

- **URL**: `/api/v1/pipelines/:id/nodes/batch`
- **方法**: `POST`
- **描述**: 在指定管道中批量创建节点

**请求示例**:

```bash
curl -X POST \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "nodes": [
      {
        "id": "node1",
        "type": "vp_file_src_node",
        "params": {
          "channel_index": "0",
          "file_path": "./video1.mp4"
        }
      },
      {
        "id": "node2",
        "type": "vp_yunet_face_detector_node",
        "params": {
          "model_path": "./models/face_detection.onnx",
          "confidence_threshold": "0.9"
        }
      }
    ]
  }' \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/nodes/batch
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "created_count": "2",
    "nodes": "[\"node1\",\"node2\"]"
  }
}
```

### 连接管理

#### 获取管道中的所有连接

- **URL**: `/api/v1/pipelines/:id/connections`
- **方法**: `GET`
- **描述**: 获取指定管道中的所有连接

**请求示例**:

```bash
curl -X GET \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/connections
```

**响应示例**:

```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "from": {
        "is_single": true,
        "source": "file_src_0"
      },
      "to": "face_detector"
    },
    {
      "id": 2,
      "from": {
        "is_single": false,
        "sources": ["node1", "node2"]
      },
      "to": "node3"
    }
  ]
}
```

#### 获取管道中的特定连接

- **URL**: `/api/v1/pipelines/:id/connections/:connection_id`
- **方法**: `GET`
- **描述**: 获取指定管道中的特定连接

**请求示例**:

```bash
curl -X GET \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/connections/1
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "id": 1,
    "from": {
      "is_single": true,
      "source": "file_src_0"
    },
    "to": "face_detector"
  }
}
```

#### 创建连接

- **URL**: `/api/v1/pipelines/:id/connections`
- **方法**: `POST`
- **描述**: 在指定管道中创建新连接

**请求示例** (单个源):

```bash
curl -X POST \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "from": "file_src_0",
    "to": "face_detector"
  }' \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/connections
```

**请求示例** (多个源):

```bash
curl -X POST \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "from": ["node1", "node2"],
    "to": "node3"
  }' \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/connections
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "message": "连接已成功创建",
    "id": "1"
  }
}
```

#### 删除连接

- **URL**: `/api/v1/pipelines/:id/connections/:connection_id`
- **方法**: `DELETE`
- **描述**: 删除指定管道中的特定连接

**请求示例**:

```bash
curl -X DELETE \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/connections/1
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "message": "连接已成功删除"
  }
}
```

#### 批量创建连接

- **URL**: `/api/v1/pipelines/:id/connections/batch`
- **方法**: `POST`
- **描述**: 在指定管道中批量创建连接

**请求示例**:

```bash
curl -X POST \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "connections": [
      {
        "from": "node1",
        "to": "node2"
      },
      {
        "from": "node2",
        "to": "node3"
      }
    ]
  }' \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/connections/batch
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "created_count": "2",
    "connections": "[1,2]"
  }
}
```

### 管道运行时管理

#### 启动管道

- **URL**: `/api/v1/pipelines/:id/start`
- **方法**: `POST`
- **描述**: 启动指定的管道

**请求示例**:

```bash
curl -X POST \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/start
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "pipeline_id": "face_detection_pipeline",
    "message": "管道已成功启动",
    "started_at": "2023-06-20T17:15:30Z"
  }
}
```

#### 停止管道

- **URL**: `/api/v1/pipelines/:id/stop`
- **方法**: `POST`
- **描述**: 停止指定的管道

**请求示例**:

```bash
curl -X POST \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/stop
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "pipeline_id": "face_detection_pipeline",
    "message": "管道已成功停止",
    "stopped_at": "2023-06-20T17:30:45Z",
    "run_duration": "00:15:15"
  }
}
```

#### 获取管道状态

- **URL**: `/api/v1/pipelines/:id/status`
- **方法**: `GET`
- **描述**: 获取指定管道的运行状态

**请求示例**:

```bash
curl -X GET \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/status
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "state": "running",
    "started_at": "2023-06-20T17:15:30Z",
    "uptime": "300s",
    "nodes": "[{\"id\":\"file_src_0\",\"state\":\"running\",\"metrics\":{\"frames_processed\":\"150\",\"fps\":\"30\"}},{\"id\":\"face_detector\",\"state\":\"running\",\"metrics\":{\"faces_detected\":\"25\",\"processing_time_ms\":\"15\"}}]"
  }
}
```

#### 验证管道配置

- **URL**: `/api/v1/pipelines/:id/validate`
- **方法**: `POST`
- **描述**: 验证指定管道的配置是否有效

**请求示例**:

```bash
curl -X POST \
  -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/pipelines/face_detection_pipeline/validate
```

**响应示例** (有效配置):

```json
{
  "status": "success",
  "data": {
    "valid": "true"
  }
}
```

**响应示例** (无效配置):

```json
{
  "status": "success",
  "data": {
    "valid": "false",
    "errors": "[{\"type\":\"missing_connection\",\"message\":\"节点 'node1' 没有输入连接\"},{\"type\":\"invalid_parameter\",\"message\":\"节点 'face_detector' 缺少必要参数 'model_path'\"}]",
    "warnings": "[\"节点 'output_node' 没有输出连接\"]"
  }
}
```

## 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|----------|------|
| UNAUTHORIZED | 401 | API密钥无效或未提供 |
| NOT_FOUND | 404 | 请求的资源不存在 |
| VALIDATION_ERROR | 400 | 请求参数验证失败 |
| INVALID_JSON | 400 | 请求体不是有效的JSON格式 |
| INVALID_YAML | 400 | 提供的YAML内容无效 |
| INTERNAL_ERROR | 500 | 服务器内部错误 | 