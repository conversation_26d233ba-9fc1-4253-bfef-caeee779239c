# 网络配置功能实现方案

## 概述

本文档详细描述了网络配置功能中网络监控、防火墙管理和网络诊断模块的完整实现方案。这些模块现已从框架代码升级为功能完整的实现。

## 1. 网络监控模块实现

### 1.1 技术选型

**主要第三方库**：
- `github.com/shirou/gopsutil/v3` - 系统信息获取，提供跨平台的网络统计API
- `github.com/google/gopacket` - 网络包捕获和分析（预留扩展）

### 1.2 核心功能实现

#### 网络状态监控
- **实时接口状态**：获取网络接口的启用状态、IP地址、MAC地址等
- **默认网关检测**：解析`/proc/net/route`文件获取默认网关
- **DNS服务器获取**：解析`/etc/resolv.conf`文件获取DNS配置
- **互联网连接检测**：通过TCP连接测试互联网可达性

#### 流量统计
- **实时流量数据**：使用gopsutil获取接收/发送字节数、包数、错误数、丢包数
- **速度计算**：通过时间差计算实时网络速度
- **历史数据缓存**：维护上次统计数据用于速度计算
- **多接口支持**：支持所有网络接口的统计

#### 网络质量检测
- **延迟测试**：通过TCP连接测试网络延迟
- **丢包率计算**：基于连接成功率计算丢包率
- **抖动估算**：基于延迟变化估算网络抖动
- **带宽测试**：预留带宽测试接口

#### 连接统计
- **TCP/UDP连接数**：解析`/proc/net/tcp`和`/proc/net/udp`获取连接数
- **监听端口**：获取系统当前监听的端口列表
- **连接状态统计**：统计ESTABLISHED、TIME_WAIT等状态的连接数

### 1.3 监控循环机制
- **后台监控**：支持启动/停止后台监控循环
- **可配置间隔**：支持自定义监控间隔时间
- **线程安全**：使用读写锁保证并发安全

## 2. 防火墙管理模块实现

### 2.1 技术方案

**基于iptables**：
- 直接调用系统iptables命令
- 解析iptables输出获取规则信息
- 支持规则的增删改查操作

### 2.2 核心功能实现

#### 规则管理
- **规则获取**：执行`iptables -L -n --line-numbers`获取所有规则
- **规则解析**：解析iptables输出，提取规则详细信息
- **规则添加**：构建iptables命令参数，添加新规则
- **规则删除**：根据规则ID删除指定规则
- **规则缓存**：维护规则缓存提高查询性能

#### 状态管理
- **防火墙状态**：检测iptables是否可用和启用状态
- **默认策略**：获取INPUT链的默认策略（ACCEPT/DROP）
- **规则统计**：统计当前规则数量和最后更新时间

#### 命令构建
- **参数构建**：根据规则对象构建完整的iptables命令参数
- **协议支持**：支持TCP、UDP、ICMP等协议过滤
- **地址过滤**：支持源地址和目标地址过滤
- **端口过滤**：支持源端口和目标端口过滤
- **接口过滤**：支持网络接口过滤
- **注释支持**：支持规则注释功能

### 2.3 安全特性
- **操作验证**：添加规则前进行参数验证
- **错误处理**：完善的错误处理和回滚机制
- **权限检查**：需要root权限执行iptables命令

## 3. 网络诊断模块实现

### 3.1 技术方案

**系统命令集成**：
- 跨平台ping命令支持（Windows/Linux）
- traceroute/tracert命令集成
- 自实现端口扫描功能
- 综合诊断逻辑

### 3.2 核心功能实现

#### Ping测试
- **跨平台支持**：自动检测操作系统，使用对应的ping命令
- **输出解析**：解析ping命令输出，提取延迟、丢包率等统计信息
- **统计计算**：计算最小、最大、平均延迟和标准差
- **超时控制**：支持自定义ping超时时间

#### 路由跟踪
- **命令执行**：执行traceroute/tracert命令
- **跳数解析**：解析每一跳的IP地址、主机名和延迟
- **路径分析**：提供完整的数据包传输路径信息

#### 端口扫描
- **并发扫描**：使用协程池并发扫描多个端口
- **超时控制**：每个端口连接都有独立的超时控制
- **结果收集**：收集所有开放端口的列表
- **性能优化**：限制并发数避免系统资源过度消耗

#### 综合诊断
- **互联网连接检查**：测试到公共DNS服务器的连接
- **DNS解析检查**：测试域名解析功能
- **默认网关检查**：测试到常见网关地址的连接
- **问题建议**：根据检查结果提供相应的解决建议

### 3.3 解析能力
- **多格式支持**：支持中英文ping输出格式
- **正则表达式**：使用正则表达式精确提取数据
- **错误容忍**：对解析失败的情况有良好的容错处理

## 4. 依赖管理和配置优化

### 4.1 第三方依赖
```go
// 新增依赖
github.com/shirou/gopsutil/v3 v3.24.5  // 系统信息获取
github.com/google/gopacket v1.1.19     // 网络包分析（预留）
```

### 4.2 配置文件优化
`configs/network.yaml`中包含了所有网络模块的配置参数：
- 监控间隔和采样率
- 防火墙默认策略和预定义规则
- 诊断工具超时参数
- 系统路径配置

### 4.3 模块协调
- **统一接口**：所有模块都实现了统一的服务接口
- **错误处理**：统一的错误处理和日志记录
- **并发安全**：所有模块都使用读写锁保证线程安全
- **资源管理**：合理的资源分配和释放机制

## 5. 性能和安全考虑

### 5.1 性能优化
- **缓存机制**：网络统计数据和防火墙规则缓存
- **并发控制**：合理的并发数限制
- **资源复用**：复用网络连接和系统调用
- **定时更新**：可配置的数据更新间隔

### 5.2 安全措施
- **权限验证**：需要适当权限执行系统命令
- **输入验证**：严格验证所有输入参数
- **命令注入防护**：防止命令注入攻击
- **错误信息过滤**：避免敏感信息泄露

## 6. 使用示例

### 6.1 网络监控
```go
// 启动监控
manager.StartMonitoring()

// 获取网络状态
status, err := manager.GetNetworkStatus()

// 获取流量统计
stats, err := manager.GetTrafficStats()
```

### 6.2 防火墙管理
```go
// 添加规则
rule := &models.FirewallRule{
    Chain:    "INPUT",
    Target:   "ACCEPT",
    Protocol: "tcp",
    DestPort: "80",
    Comment:  "允许HTTP访问",
}
err := manager.AddRule(rule)
```

### 6.3 网络诊断
```go
// Ping测试
result, err := manager.Ping("8.8.8.8", 4)

// 端口扫描
ports := []int{80, 443, 8080}
scanResult, err := manager.PortScan("192.168.1.1", ports)
```

## 7. 总结

通过以上实现，网络配置功能现在具备了完整的：
- **实时监控能力**：网络状态、流量统计、连接监控
- **防火墙管理能力**：规则管理、状态控制、安全策略
- **诊断分析能力**：连通性测试、路径分析、端口扫描

所有模块都经过了精心设计，具有良好的性能、安全性和可扩展性，为用户提供了专业级的网络管理功能。
