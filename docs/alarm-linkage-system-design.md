# 告警联动系统设计方案

## 1. 系统概述

### 1.1 功能目标
基于现有ECP边缘计算平台，增加告警联动功能，当算法产生告警时，自动通过设备协议发起联动控制，实现智能化的告警响应机制。

### 1.2 核心特性
- **告警数据源**：基于现有Alert表的算法告警数据
- **多协议支持**：MQTT、RS485、Modbus协议联动控制
- **规则引擎**：灵活的联动规则配置和匹配
- **直观界面**：易用的Web配置界面
- **数据一致性**：使用SQLite3保持与现有系统一致

### 1.3 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   算法告警源    │───▶│  告警联动引擎   │───▶│   协议适配层    │
│  (现有Alert)    │    │                 │    │ MQTT/RS485/     │
└─────────────────┘    └─────────────────┘    │ Modbus          │
                              │                └─────────────────┘
                              ▼                        │
                       ┌─────────────────┐             ▼
                       │   规则配置管理   │    ┌─────────────────┐
                       │                 │    │   设备控制层    │
                       └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │  Web配置界面    │    │   执行结果记录   │
                       └─────────────────┘    └─────────────────┘
```

## 2. 技术选型

### 2.1 开源库推荐

#### MQTT协议
- **Eclipse Paho MQTT Go Client**
  - 仓库：`github.com/eclipse/paho.mqtt.golang`
  - 优势：成熟稳定、功能完整、社区活跃
  - 支持：QoS、SSL/TLS、重连机制、消息持久化

#### Modbus协议
- **GoModbus**
  - 仓库：`github.com/goburrow/modbus`
  - 优势：轻量级、支持TCP/RTU/ASCII
  - 特性：线程安全、连接池管理、错误重试

#### RS485/串口通信
- **Go Serial**
  - 仓库：`go.bug.st/serial`
  - 优势：跨平台、配置灵活
  - 支持：波特率、数据位、停止位、校验位配置

#### 规则引擎
- **Govaluate**
  - 仓库：`github.com/Knetic/govaluate`
  - 优势：表达式求值、动态规则执行
  - 特性：安全沙箱、丰富的操作符

### 2.2 数据库选型
- **SQLite3**：与ECP系统保持一致，轻量级、嵌入式、适合边缘计算

## 3. 数据模型设计

### 3.1 扩展现有Alert表
```sql
-- 为现有alert表添加联动相关字段
ALTER TABLE alert ADD COLUMN linkage_triggered BOOLEAN DEFAULT 0;
ALTER TABLE alert ADD COLUMN linkage_results TEXT; -- JSON格式存储联动结果
ALTER TABLE alert ADD COLUMN linkage_error TEXT;   -- 联动执行错误信息
```

### 3.2 新增数据表

#### 联动规则表 (linkage_rules)
```sql
CREATE TABLE linkage_rules (
    id VARCHAR(64) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT 1,
    priority INTEGER DEFAULT 0,
    conditions TEXT, -- JSON格式存储匹配条件
    actions TEXT,    -- JSON格式存储联动动作
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 联动设备表 (linkage_devices)
```sql
CREATE TABLE linkage_devices (
    id VARCHAR(64) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100), -- 设备类型：灯光、门禁、报警器等
    protocol VARCHAR(50), -- mqtt/modbus/rs485
    address VARCHAR(255), -- IP地址或串口地址
    port INTEGER,
    config TEXT, -- JSON格式存储协议配置
    status VARCHAR(50) DEFAULT 'offline',
    last_seen DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 联动执行记录表 (linkage_executions)
```sql
CREATE TABLE linkage_executions (
    id VARCHAR(64) PRIMARY KEY,
    alert_id INTEGER, -- 关联的告警ID
    rule_id VARCHAR(64), -- 执行的规则ID
    device_id VARCHAR(64), -- 目标设备ID
    action VARCHAR(255), -- 执行的动作
    status VARCHAR(50), -- success/failed/timeout
    error_message TEXT,
    execution_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    response_data TEXT -- 设备响应数据
);
```

## 4. 核心模块设计

### 4.1 告警联动引擎 (LinkageEngine)
```go
type LinkageEngine struct {
    ruleManager    *RuleManager
    deviceManager  *DeviceManager
    protocolAdapters map[string]ProtocolAdapter
    executionQueue chan LinkageTask
    db            *database.DB
}

type LinkageTask struct {
    AlertID   int64
    RuleID    string
    DeviceID  string
    Action    string
    Params    map[string]interface{}
    Timestamp time.Time
}
```

### 4.2 规则管理器 (RuleManager)
```go
type RuleManager struct {
    rules   map[string]*LinkageRule
    mutex   sync.RWMutex
    db      *database.DB
    evaluator *govaluate.EvaluableExpression
}

type LinkageRule struct {
    ID          string                 `json:"id"`
    Name        string                 `json:"name"`
    Description string                 `json:"description"`
    Enabled     bool                   `json:"enabled"`
    Priority    int                    `json:"priority"`
    Conditions  RuleConditions         `json:"conditions"`
    Actions     []LinkageAction        `json:"actions"`
}

type RuleConditions struct {
    VideoID       []int64  `json:"video_id,omitempty"`
    AlgorithmID   []int64  `json:"algorithm_id,omitempty"`
    Level         []string `json:"level,omitempty"`
    Type          []string `json:"type,omitempty"`
    TimeRange     *TimeRange `json:"time_range,omitempty"`
    Expression    string   `json:"expression,omitempty"` // 自定义表达式
}

type LinkageAction struct {
    DeviceID string                 `json:"device_id"`
    Command  string                 `json:"command"`
    Params   map[string]interface{} `json:"params"`
    Delay    int                    `json:"delay"` // 延迟执行秒数
}
```

### 4.3 协议适配器接口
```go
type ProtocolAdapter interface {
    Connect(device *LinkageDevice) error
    Disconnect(deviceID string) error
    SendCommand(deviceID string, command string, params map[string]interface{}) error
    IsConnected(deviceID string) bool
    GetStatus(deviceID string) (string, error)
    Close() error
}
```

## 5. 协议适配器实现

### 5.1 MQTT适配器
```go
type MQTTAdapter struct {
    clients map[string]mqtt.Client
    mutex   sync.RWMutex
    config  MQTTConfig
}

type MQTTConfig struct {
    DefaultQoS    int           `json:"default_qos"`
    KeepAlive     time.Duration `json:"keep_alive"`
    CleanSession  bool          `json:"clean_session"`
    ConnectTimeout time.Duration `json:"connect_timeout"`
}
```

### 5.2 Modbus适配器
```go
type ModbusAdapter struct {
    handlers map[string]modbus.Client
    mutex    sync.RWMutex
    config   ModbusConfig
}

type ModbusConfig struct {
    Timeout    time.Duration `json:"timeout"`
    RetryCount int           `json:"retry_count"`
    SlaveID    byte          `json:"slave_id"`
}
```

### 5.3 RS485适配器
```go
type RS485Adapter struct {
    ports map[string]serial.Port
    mutex sync.RWMutex
    config RS485Config
}

type RS485Config struct {
    BaudRate int    `json:"baud_rate"`
    DataBits int    `json:"data_bits"`
    StopBits int    `json:"stop_bits"`
    Parity   string `json:"parity"`
    Timeout  time.Duration `json:"timeout"`
}
```

## 6. API接口设计

### 6.1 联动规则管理API
```
GET    /api/linkage/rules          - 获取联动规则列表
POST   /api/linkage/rules          - 创建联动规则
GET    /api/linkage/rules/{id}     - 获取联动规则详情
PUT    /api/linkage/rules/{id}     - 更新联动规则
DELETE /api/linkage/rules/{id}     - 删除联动规则
PUT    /api/linkage/rules/{id}/enable  - 启用联动规则
PUT    /api/linkage/rules/{id}/disable - 禁用联动规则
```

### 6.2 联动设备管理API
```
GET    /api/linkage/devices        - 获取联动设备列表
POST   /api/linkage/devices        - 添加联动设备
GET    /api/linkage/devices/{id}   - 获取联动设备详情
PUT    /api/linkage/devices/{id}   - 更新联动设备
DELETE /api/linkage/devices/{id}   - 删除联动设备
POST   /api/linkage/devices/{id}/test - 测试设备连接
GET    /api/linkage/devices/{id}/status - 获取设备状态
```

### 6.3 联动执行记录API
```
GET    /api/linkage/executions     - 获取联动执行记录
GET    /api/linkage/executions/{id} - 获取执行记录详情
POST   /api/linkage/executions/manual - 手动执行联动
GET    /api/linkage/statistics     - 获取联动统计信息
```

## 7. Web界面设计

### 7.1 页面结构
```
联动管理
├── 联动规则
│   ├── 规则列表
│   ├── 规则创建/编辑
│   └── 规则测试
├── 联动设备
│   ├── 设备列表
│   ├── 设备配置
│   └── 设备状态监控
├── 执行记录
│   ├── 执行历史
│   ├── 执行统计
│   └── 错误日志
└── 系统配置
    ├── 协议配置
    └── 全局设置
```

### 7.2 关键界面设计

#### 规则配置界面
- **条件配置**：可视化的条件构建器，支持多条件组合
- **动作配置**：设备选择、命令配置、参数设置
- **规则测试**：模拟告警测试规则匹配和执行
- **优先级设置**：规则执行优先级配置

#### 设备管理界面
- **设备列表**：显示设备状态、协议类型、连接状态
- **设备配置**：协议参数配置、连接测试
- **状态监控**：实时显示设备连接状态和响应时间

## 8. 配置文件设计

### 8.1 联动系统配置 (configs/linkage.yaml)
```yaml
linkage:
  enabled: true
  max_concurrent_executions: 10
  execution_timeout: 30s
  retry_count: 3
  retry_interval: 5s
  
  protocols:
    mqtt:
      enabled: true
      default_qos: 1
      keep_alive: 60s
      connect_timeout: 10s
    
    modbus:
      enabled: true
      timeout: 5s
      retry_count: 3
      
    rs485:
      enabled: true
      timeout: 3s
      buffer_size: 1024
```

## 9. 实现计划

### 9.1 第一阶段：基础框架 (2周)
**目标**：搭建联动系统基础架构

**任务清单**：
- [ ] 设计并实现数据库表结构
- [ ] 创建基础数据模型 (LinkageRule、LinkageDevice等)
- [ ] 实现告警联动引擎基本框架
- [ ] 创建协议适配器接口定义
- [ ] 搭建基础API路由结构

**交付物**：
- 数据库迁移脚本
- 基础数据模型代码
- 联动引擎框架代码
- API接口定义文档

### 9.2 第二阶段：协议适配器 (3周)
**目标**：实现MQTT、Modbus、RS485协议适配器

**任务清单**：
- [ ] 实现MQTT协议适配器
  - [ ] 连接管理和重连机制
  - [ ] 消息发布功能
  - [ ] 连接状态监控
- [ ] 实现Modbus协议适配器
  - [ ] TCP和RTU模式支持
  - [ ] 读写寄存器和线圈功能
  - [ ] 错误处理和重试机制
- [ ] 实现RS485串口适配器
  - [ ] 串口配置和连接
  - [ ] 数据发送和接收
  - [ ] 超时和错误处理
- [ ] 编写协议适配器单元测试

**交付物**：
- 三种协议适配器完整实现
- 协议适配器测试用例
- 协议配置文档

### 9.3 第三阶段：规则引擎 (2周)
**目标**：实现灵活的规则匹配和执行引擎

**任务清单**：
- [ ] 实现规则管理器
  - [ ] 规则加载和缓存
  - [ ] 规则匹配算法
  - [ ] 优先级排序
- [ ] 集成Govaluate表达式引擎
  - [ ] 条件表达式构建
  - [ ] 表达式求值
  - [ ] 安全性验证
- [ ] 实现规则执行引擎
  - [ ] 异步执行机制
  - [ ] 错误处理和重试
  - [ ] 执行结果记录

**交付物**：
- 规则引擎完整实现
- 表达式语法文档
- 规则配置示例

### 9.4 第四阶段：API接口 (2周)
**目标**：实现完整的REST API接口

**任务清单**：
- [ ] 实现联动规则管理API
- [ ] 实现联动设备管理API
- [ ] 实现联动执行记录API
- [ ] 添加API认证和权限控制
- [ ] 编写API文档和测试用例

**交付物**：
- 完整的API接口实现
- API文档和测试用例
- Postman测试集合

### 9.5 第五阶段：Web界面 (3周)
**目标**：实现直观易用的Web配置界面

**任务清单**：
- [ ] 实现联动规则配置界面
  - [ ] 规则列表和搜索
  - [ ] 可视化规则编辑器
  - [ ] 规则测试功能
- [ ] 实现联动设备管理界面
  - [ ] 设备列表和状态监控
  - [ ] 设备配置表单
  - [ ] 连接测试功能
- [ ] 实现执行记录查看界面
  - [ ] 执行历史列表
  - [ ] 统计图表展示
  - [ ] 错误日志查看

**交付物**：
- 完整的Web界面实现
- 用户操作手册
- 界面测试报告

### 9.6 第六阶段：集成测试 (1周)
**目标**：系统集成测试和优化

**任务清单**：
- [ ] 端到端功能测试
- [ ] 性能测试和优化
- [ ] 错误处理测试
- [ ] 文档完善和更新

**交付物**：
- 系统测试报告
- 性能优化报告
- 完整的用户文档

## 10. 风险评估与应对

### 10.1 技术风险
- **协议兼容性**：不同厂商设备协议差异
  - 应对：提供协议配置模板，支持自定义协议参数
- **并发处理**：大量告警同时触发联动
  - 应对：实现任务队列和限流机制
- **网络稳定性**：网络中断影响设备控制
  - 应对：实现重连机制和离线缓存

### 10.2 业务风险
- **误触发**：错误的规则配置导致误操作
  - 应对：提供规则测试功能，支持规则回滚
- **设备故障**：联动设备故障影响系统
  - 应对：实现设备健康检查和故障转移

## 11. 总结

本设计方案基于现有ECP系统架构，充分利用现有的告警数据和技术栈，通过模块化设计实现告警联动功能。方案具有以下特点：

1. **技术成熟**：采用成熟的开源库，降低开发风险
2. **架构清晰**：模块化设计，便于维护和扩展
3. **界面友好**：直观的Web界面，降低使用门槛
4. **性能可靠**：异步处理机制，支持高并发场景
5. **扩展性强**：支持新协议和设备类型的扩展

通过分阶段实施，可以逐步完善系统功能，确保项目成功交付。

## 12. 详细技术实现

### 12.1 告警联动引擎核心实现

#### 引擎初始化
```go
func NewLinkageEngine(db *database.DB) *LinkageEngine {
    engine := &LinkageEngine{
        ruleManager:      NewRuleManager(db),
        deviceManager:    NewDeviceManager(db),
        protocolAdapters: make(map[string]ProtocolAdapter),
        executionQueue:   make(chan LinkageTask, 1000),
        db:              db,
    }

    // 初始化协议适配器
    engine.protocolAdapters["mqtt"] = NewMQTTAdapter()
    engine.protocolAdapters["modbus"] = NewModbusAdapter()
    engine.protocolAdapters["rs485"] = NewRS485Adapter()

    // 启动执行协程
    go engine.processExecutionQueue()

    return engine
}
```

#### 告警处理流程
```go
func (e *LinkageEngine) ProcessAlert(alert *database.Alert) error {
    // 1. 查找匹配的规则
    matchedRules, err := e.ruleManager.FindMatchingRules(alert)
    if err != nil {
        return fmt.Errorf("查找匹配规则失败: %v", err)
    }

    // 2. 按优先级排序
    sort.Slice(matchedRules, func(i, j int) bool {
        return matchedRules[i].Priority > matchedRules[j].Priority
    })

    // 3. 执行联动动作
    for _, rule := range matchedRules {
        for _, action := range rule.Actions {
            task := LinkageTask{
                AlertID:   alert.ID,
                RuleID:    rule.ID,
                DeviceID:  action.DeviceID,
                Action:    action.Command,
                Params:    action.Params,
                Timestamp: time.Now(),
            }

            // 添加到执行队列
            select {
            case e.executionQueue <- task:
                log.Printf("联动任务已加入队列: 告警ID=%d, 规则ID=%s", alert.ID, rule.ID)
            default:
                log.Warn("执行队列已满，丢弃联动任务")
            }
        }
    }

    // 4. 更新告警联动状态
    return e.updateAlertLinkageStatus(alert.ID, true, nil)
}
```

### 12.2 规则匹配算法实现

```go
func (rm *RuleManager) FindMatchingRules(alert *database.Alert) ([]*LinkageRule, error) {
    rm.mutex.RLock()
    defer rm.mutex.RUnlock()

    var matchedRules []*LinkageRule

    for _, rule := range rm.rules {
        if !rule.Enabled {
            continue
        }

        // 基础条件匹配
        if !rm.matchBasicConditions(rule.Conditions, alert) {
            continue
        }

        // 时间范围匹配
        if !rm.matchTimeRange(rule.Conditions.TimeRange, alert.CreatedAt) {
            continue
        }

        // 自定义表达式匹配
        if rule.Conditions.Expression != "" {
            if !rm.evaluateExpression(rule.Conditions.Expression, alert) {
                continue
            }
        }

        matchedRules = append(matchedRules, rule)
    }

    return matchedRules, nil
}

func (rm *RuleManager) matchBasicConditions(conditions RuleConditions, alert *database.Alert) bool {
    // 视频源ID匹配
    if len(conditions.VideoID) > 0 {
        found := false
        for _, id := range conditions.VideoID {
            if id == alert.VideoID {
                found = true
                break
            }
        }
        if !found {
            return false
        }
    }

    // 算法ID匹配
    if len(conditions.AlgorithmID) > 0 {
        found := false
        for _, id := range conditions.AlgorithmID {
            if id == alert.AlgorithmID {
                found = true
                break
            }
        }
        if !found {
            return false
        }
    }

    // 告警等级匹配
    if len(conditions.Level) > 0 {
        found := false
        for _, level := range conditions.Level {
            if level == alert.Level {
                found = true
                break
            }
        }
        if !found {
            return false
        }
    }

    // 告警类型匹配
    if len(conditions.Type) > 0 {
        found := false
        for _, alertType := range conditions.Type {
            if alertType == alert.Type {
                found = true
                break
            }
        }
        if !found {
            return false
        }
    }

    return true
}
```

### 12.3 MQTT协议适配器详细实现

```go
type MQTTAdapter struct {
    clients map[string]mqtt.Client
    mutex   sync.RWMutex
    config  MQTTConfig
}

func NewMQTTAdapter() *MQTTAdapter {
    return &MQTTAdapter{
        clients: make(map[string]mqtt.Client),
        config: MQTTConfig{
            DefaultQoS:     1,
            KeepAlive:      60 * time.Second,
            CleanSession:   true,
            ConnectTimeout: 10 * time.Second,
        },
    }
}

func (a *MQTTAdapter) Connect(device *LinkageDevice) error {
    opts := mqtt.NewClientOptions()

    // 基础连接配置
    brokerURL := fmt.Sprintf("tcp://%s:%d", device.Address, device.Port)
    opts.AddBroker(brokerURL)
    opts.SetClientID(fmt.Sprintf("ecp_linkage_%s", device.ID))
    opts.SetKeepAlive(a.config.KeepAlive)
    opts.SetCleanSession(a.config.CleanSession)
    opts.SetConnectTimeout(a.config.ConnectTimeout)
    opts.SetAutoReconnect(true)
    opts.SetMaxReconnectInterval(30 * time.Second)

    // 解析设备配置
    var mqttConfig struct {
        Username string `json:"username"`
        Password string `json:"password"`
        UseTLS   bool   `json:"use_tls"`
        CertFile string `json:"cert_file"`
        KeyFile  string `json:"key_file"`
        CAFile   string `json:"ca_file"`
    }

    if err := json.Unmarshal([]byte(device.Config), &mqttConfig); err != nil {
        return fmt.Errorf("解析MQTT配置失败: %v", err)
    }

    // 认证配置
    if mqttConfig.Username != "" {
        opts.SetUsername(mqttConfig.Username)
        opts.SetPassword(mqttConfig.Password)
    }

    // TLS配置
    if mqttConfig.UseTLS {
        tlsConfig := &tls.Config{
            InsecureSkipVerify: false,
        }

        if mqttConfig.CertFile != "" && mqttConfig.KeyFile != "" {
            cert, err := tls.LoadX509KeyPair(mqttConfig.CertFile, mqttConfig.KeyFile)
            if err != nil {
                return fmt.Errorf("加载客户端证书失败: %v", err)
            }
            tlsConfig.Certificates = []tls.Certificate{cert}
        }

        if mqttConfig.CAFile != "" {
            caCert, err := ioutil.ReadFile(mqttConfig.CAFile)
            if err != nil {
                return fmt.Errorf("读取CA证书失败: %v", err)
            }
            caCertPool := x509.NewCertPool()
            caCertPool.AppendCertsFromPEM(caCert)
            tlsConfig.RootCAs = caCertPool
        }

        opts.SetTLSConfig(tlsConfig)
        brokerURL = strings.Replace(brokerURL, "tcp://", "ssl://", 1)
        opts.AddBroker(brokerURL)
    }

    // 连接回调
    opts.SetOnConnectHandler(func(client mqtt.Client) {
        log.Printf("MQTT设备 %s 连接成功", device.ID)
        a.updateDeviceStatus(device.ID, "online")
    })

    opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
        log.Printf("MQTT设备 %s 连接丢失: %v", device.ID, err)
        a.updateDeviceStatus(device.ID, "offline")
    })

    // 创建客户端并连接
    client := mqtt.NewClient(opts)
    if token := client.Connect(); token.Wait() && token.Error() != nil {
        return fmt.Errorf("MQTT连接失败: %v", token.Error())
    }

    a.mutex.Lock()
    a.clients[device.ID] = client
    a.mutex.Unlock()

    return nil
}

func (a *MQTTAdapter) SendCommand(deviceID string, command string, params map[string]interface{}) error {
    a.mutex.RLock()
    client, exists := a.clients[deviceID]
    a.mutex.RUnlock()

    if !exists {
        return fmt.Errorf("设备 %s 未连接", deviceID)
    }

    if !client.IsConnected() {
        return fmt.Errorf("设备 %s MQTT连接已断开", deviceID)
    }

    // 构建消息负载
    payload := map[string]interface{}{
        "command":   command,
        "params":    params,
        "timestamp": time.Now().Unix(),
        "device_id": deviceID,
    }

    payloadBytes, err := json.Marshal(payload)
    if err != nil {
        return fmt.Errorf("序列化消息负载失败: %v", err)
    }

    // 获取QoS设置
    qos := a.config.DefaultQoS
    if qosValue, ok := params["qos"]; ok {
        if qosInt, ok := qosValue.(int); ok {
            qos = qosInt
        }
    }

    // 获取主题
    topic := command
    if topicValue, ok := params["topic"]; ok {
        if topicStr, ok := topicValue.(string); ok {
            topic = topicStr
        }
    }

    // 发布消息
    token := client.Publish(topic, byte(qos), false, payloadBytes)
    if token.Wait() && token.Error() != nil {
        return fmt.Errorf("发布MQTT消息失败: %v", token.Error())
    }

    log.Printf("MQTT命令发送成功: 设备=%s, 主题=%s, 命令=%s", deviceID, topic, command)
    return nil
}
```

### 12.4 Modbus协议适配器详细实现

```go
type ModbusAdapter struct {
    handlers map[string]modbus.Client
    mutex    sync.RWMutex
    config   ModbusConfig
}

func NewModbusAdapter() *ModbusAdapter {
    return &ModbusAdapter{
        handlers: make(map[string]modbus.Client),
        config: ModbusConfig{
            Timeout:    5 * time.Second,
            RetryCount: 3,
            SlaveID:    1,
        },
    }
}

func (a *ModbusAdapter) Connect(device *LinkageDevice) error {
    var handler modbus.Client

    // 解析设备配置
    var modbusConfig struct {
        SlaveID    byte   `json:"slave_id"`
        BaudRate   int    `json:"baud_rate"`
        DataBits   int    `json:"data_bits"`
        StopBits   int    `json:"stop_bits"`
        Parity     string `json:"parity"`
        Timeout    int    `json:"timeout"`
        RetryCount int    `json:"retry_count"`
    }

    if err := json.Unmarshal([]byte(device.Config), &modbusConfig); err != nil {
        return fmt.Errorf("解析Modbus配置失败: %v", err)
    }

    // 根据协议类型创建客户端
    switch device.Protocol {
    case "modbus-tcp":
        address := fmt.Sprintf("%s:%d", device.Address, device.Port)
        handler = modbus.TCPClient(address)

    case "modbus-rtu":
        // RTU over TCP
        if device.Port > 0 {
            address := fmt.Sprintf("%s:%d", device.Address, device.Port)
            handler = modbus.RTUClientFromURL(fmt.Sprintf("tcp://%s", address))
        } else {
            // RTU over Serial
            handler = modbus.RTUClientFromURL(device.Address)
        }

    case "modbus-ascii":
        handler = modbus.ASCIIClientFromURL(device.Address)

    default:
        return fmt.Errorf("不支持的Modbus协议类型: %s", device.Protocol)
    }

    // 设置从站ID
    slaveID := modbusConfig.SlaveID
    if slaveID == 0 {
        slaveID = a.config.SlaveID
    }
    handler.SetSlave(slaveID)

    // 设置超时
    timeout := time.Duration(modbusConfig.Timeout) * time.Second
    if timeout == 0 {
        timeout = a.config.Timeout
    }
    handler.SetTimeout(timeout)

    a.mutex.Lock()
    a.handlers[device.ID] = handler
    a.mutex.Unlock()

    // 测试连接
    _, err := handler.ReadCoils(0, 1)
    if err != nil {
        log.Printf("Modbus设备 %s 连接测试失败: %v", device.ID, err)
        // 不返回错误，因为某些设备可能不支持读取线圈
    }

    log.Printf("Modbus设备 %s 连接成功", device.ID)
    return nil
}

func (a *ModbusAdapter) SendCommand(deviceID string, command string, params map[string]interface{}) error {
    a.mutex.RLock()
    handler, exists := a.handlers[deviceID]
    a.mutex.RUnlock()

    if !exists {
        return fmt.Errorf("设备 %s 未连接", deviceID)
    }

    // 执行重试逻辑
    var lastErr error
    for i := 0; i < a.config.RetryCount; i++ {
        err := a.executeModbusCommand(handler, command, params)
        if err == nil {
            log.Printf("Modbus命令执行成功: 设备=%s, 命令=%s", deviceID, command)
            return nil
        }
        lastErr = err

        if i < a.config.RetryCount-1 {
            time.Sleep(time.Second)
        }
    }

    return fmt.Errorf("Modbus命令执行失败 (重试%d次): %v", a.config.RetryCount, lastErr)
}

func (a *ModbusAdapter) executeModbusCommand(handler modbus.Client, command string, params map[string]interface{}) error {
    switch command {
    case "write_single_coil":
        address := uint16(params["address"].(float64))
        value := params["value"].(bool)
        _, err := handler.WriteSingleCoil(address, value)
        return err

    case "write_single_register":
        address := uint16(params["address"].(float64))
        value := uint16(params["value"].(float64))
        _, err := handler.WriteSingleRegister(address, value)
        return err

    case "write_multiple_coils":
        address := uint16(params["address"].(float64))
        values := params["values"].([]interface{})
        boolValues := make([]bool, len(values))
        for i, v := range values {
            boolValues[i] = v.(bool)
        }
        _, err := handler.WriteMultipleCoils(address, boolValues)
        return err

    case "write_multiple_registers":
        address := uint16(params["address"].(float64))
        values := params["values"].([]interface{})
        uint16Values := make([]uint16, len(values))
        for i, v := range values {
            uint16Values[i] = uint16(v.(float64))
        }
        _, err := handler.WriteMultipleRegisters(address, uint16Values)
        return err

    case "read_coils":
        address := uint16(params["address"].(float64))
        quantity := uint16(params["quantity"].(float64))
        results, err := handler.ReadCoils(address, quantity)
        if err == nil {
            log.Printf("读取线圈结果: %v", results)
        }
        return err

    case "read_holding_registers":
        address := uint16(params["address"].(float64))
        quantity := uint16(params["quantity"].(float64))
        results, err := handler.ReadHoldingRegisters(address, quantity)
        if err == nil {
            log.Printf("读取保持寄存器结果: %v", results)
        }
        return err

    default:
        return fmt.Errorf("不支持的Modbus命令: %s", command)
    }
}
```

### 12.5 配置示例

#### 联动规则配置示例
```json
{
  "id": "rule_001",
  "name": "人员入侵联动",
  "description": "检测到人员入侵时开启警报灯并发送通知",
  "enabled": true,
  "priority": 10,
  "conditions": {
    "video_id": [1, 2, 3],
    "algorithm_id": [1],
    "level": ["warning", "error"],
    "type": ["人员检测"],
    "time_range": {
      "start_time": "18:00:00",
      "end_time": "06:00:00",
      "weekdays": [1, 2, 3, 4, 5, 6, 7]
    },
    "expression": "alert.confidence > 0.8"
  },
  "actions": [
    {
      "device_id": "alarm_light_001",
      "command": "turn_on",
      "params": {
        "brightness": 100,
        "color": "red",
        "duration": 30
      },
      "delay": 0
    },
    {
      "device_id": "mqtt_notification",
      "command": "security/alert",
      "params": {
        "topic": "security/alert",
        "qos": 1,
        "message": "检测到人员入侵",
        "location": "{{alert.video_name}}"
      },
      "delay": 2
    }
  ]
}
```

#### 设备配置示例
```json
{
  "id": "alarm_light_001",
  "name": "入口警报灯",
  "type": "警报灯",
  "protocol": "modbus-tcp",
  "address": "*************",
  "port": 502,
  "config": {
    "slave_id": 1,
    "timeout": 5,
    "retry_count": 3
  }
}
```

## 13. 监控和运维

### 13.1 系统监控指标
- **联动执行成功率**：成功执行的联动任务比例
- **设备连接状态**：各协议设备的在线状态
- **响应时间**：从告警产生到联动执行的时间
- **错误率**：联动执行失败的比例
- **队列长度**：待执行任务队列的长度

### 13.2 日志记录
```go
type LinkageLogger struct {
    logger *logrus.Logger
}

func (l *LinkageLogger) LogExecution(execution *LinkageExecution) {
    l.logger.WithFields(logrus.Fields{
        "alert_id":   execution.AlertID,
        "rule_id":    execution.RuleID,
        "device_id":  execution.DeviceID,
        "action":     execution.Action,
        "status":     execution.Status,
        "duration":   execution.Duration,
    }).Info("联动执行记录")
}
```

### 13.3 健康检查
```go
func (e *LinkageEngine) HealthCheck() map[string]interface{} {
    status := map[string]interface{}{
        "engine_status": "running",
        "queue_length":  len(e.executionQueue),
        "active_rules":  e.ruleManager.GetActiveRuleCount(),
        "device_status": e.deviceManager.GetDeviceStatusSummary(),
        "protocol_adapters": make(map[string]string),
    }

    for protocol, adapter := range e.protocolAdapters {
        if adapter.IsHealthy() {
            status["protocol_adapters"].(map[string]string)[protocol] = "healthy"
        } else {
            status["protocol_adapters"].(map[string]string)[protocol] = "unhealthy"
        }
    }

    return status
}
```

通过以上详细的技术实现方案，可以构建一个功能完整、性能可靠的告警联动系统。
