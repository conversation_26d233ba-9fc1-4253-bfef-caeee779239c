# 算法仓库管理 API 文档

## 概述

算法仓库管理功能提供算法的导入、删除、激活、停用等完整生命周期管理。支持算法版本管理和状态控制，为视频分析提供核心算法支持。

## 数据结构

### Algorithm

```json
{
  "id": 1,
  "name": "人员检测算法",
  "description": "基于YOLO的人员检测算法，可以识别视频中的人员目标",
  "version": "v1.0.2",
  "path": "./data/algorithms/algorithm_1640995200000000000",
  "status": "active",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### 字段说明

- `id`: 算法ID
- `name`: 算法名称
- `description`: 算法描述
- `version`: 算法版本号
- `path`: 算法文件存储路径
- `status`: 算法状态（active=激活, inactive=停用）
- `created_at`: 创建时间
- `updated_at`: 更新时间

## API 接口

### 1. 查询算法列表

**GET** `/api/algorithms`

**查询参数:**
- `name`: 算法名称（模糊查询，可选）
- `status`: 算法状态（可选）
- `page`: 页码（默认1）
- `pageSize`: 每页数量（默认20）

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "人员检测算法",
        "description": "基于YOLO的人员检测算法",
        "version": "v1.0.2",
        "status": "active",
        "created_at": "2024-01-01T00:00:00Z"
      },
      {
        "id": 2,
        "name": "车辆检测算法",
        "description": "车辆目标检测和识别",
        "version": "v2.1.0",
        "status": "inactive",
        "created_at": "2024-01-02T00:00:00Z"
      }
    ],
    "paging": {
      "page": 1,
      "limit": 20,
      "total": 2
    }
  }
}
```

### 2. 获取单个算法

**GET** `/api/algorithms/{id}`

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "人员检测算法",
    "description": "基于YOLO的人员检测算法，可以识别视频中的人员目标",
    "version": "v1.0.2",
    "path": "./data/algorithms/algorithm_1640995200000000000",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 导入算法

**POST** `/api/algorithms`

**请求方式:** `multipart/form-data`

**请求参数:**
- `name`: 算法名称（表单字段）
- `description`: 算法描述（表单字段）
- `version`: 算法版本（表单字段）
- `file`: 算法文件（文件上传）

**请求示例:**
```bash
curl -X POST http://localhost:8080/api/algorithms \
  -F "name=人员检测算法" \
  -F "description=基于YOLO的人员检测算法" \
  -F "version=v1.0.2" \
  -F "file=@algorithm.zip"
```

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "人员检测算法",
    "description": "基于YOLO的人员检测算法",
    "version": "v1.0.2",
    "path": "./data/algorithms/algorithm_1640995200000000000",
    "status": "inactive",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4. 删除算法

**DELETE** `/api/algorithms/{id}`

**响应示例:**
```json
{
  "code": 0,
  "message": "success"
}
```

### 5. 激活算法

**PUT** `/api/algorithms/{id}/activate`

**响应示例:**
```json
{
  "code": 0,
  "message": "success"
}
```

### 6. 停用算法

**PUT** `/api/algorithms/{id}/deactivate`

**响应示例:**
```json
{
  "code": 0,
  "message": "success"
}
```

### 7. 获取算法绑定的视频源

**GET** `/api/algorithms/{id}/bindings`

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "video_id": 1,
      "algorithm_id": 1,
      "detection_area": "[{\"x\":100,\"y\":100},{\"x\":500,\"y\":100}]",
      "alert_threshold": 0.8,
      "status": "active",
      "video": {
        "id": 1,
        "name": "摄像头01",
        "camera_ip": "*************"
      }
    }
  ]
}
```

## 算法文件格式

### 支持的文件格式
- `.zip` - 算法包（推荐）
- `.tar.gz` - 压缩包
- `.onnx` - ONNX模型文件
- `.pt` - PyTorch模型文件
- `.pb` - TensorFlow模型文件

### 算法包结构
```
algorithm.zip
├── model/              # 模型文件目录
│   ├── model.onnx     # 主模型文件
│   └── config.json    # 模型配置
├── src/               # 源代码目录（可选）
│   └── inference.py   # 推理脚本
└── README.md          # 说明文档
```

### 算法配置文件示例
```json
{
  "name": "人员检测算法",
  "version": "v1.0.2",
  "model_type": "onnx",
  "input_shape": [1, 3, 640, 640],
  "output_shape": [1, 25200, 85],
  "classes": ["person"],
  "preprocessing": {
    "resize": [640, 640],
    "normalize": true,
    "mean": [0.485, 0.456, 0.406],
    "std": [0.229, 0.224, 0.225]
  },
  "postprocessing": {
    "confidence_threshold": 0.5,
    "nms_threshold": 0.4
  }
}
```

## 算法状态管理

### 状态说明
- `active`: 激活状态，可以被视频源绑定和使用
- `inactive`: 停用状态，不能被新的视频源绑定，但已有绑定关系仍然保留

### 状态转换规则
1. 新导入的算法默认为`inactive`状态
2. 只有`active`状态的算法才能被视频源绑定
3. 停用算法不会影响已有的绑定关系
4. 删除算法会同时删除所有相关的绑定关系

## 使用场景

### 1. 算法导入流程
1. 准备算法文件（推荐使用zip格式）
2. 使用导入接口上传算法
3. 算法导入后默认为停用状态
4. 测试算法正常后激活算法
5. 将算法绑定到视频源

### 2. 算法版本管理
1. 导入新版本算法时，建议使用不同的版本号
2. 可以同时保留多个版本的算法
3. 通过激活/停用控制算法的使用

### 3. 算法维护
1. 定期检查算法运行状态
2. 及时停用有问题的算法
3. 清理不再使用的旧版本算法

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `404`: 算法不存在
- `409`: 算法名称已存在
- `413`: 文件过大
- `415`: 不支持的文件格式
- `500`: 服务器内部错误

### 错误响应示例
```json
{
  "code": 400,
  "message": "算法文件格式不支持"
}
```

## 注意事项

1. 算法文件建议使用zip格式打包，文件大小不超过100MB
2. 算法名称在系统中必须唯一
3. 删除算法会同时删除算法文件和所有绑定关系
4. 停用算法不会影响正在运行的检测任务
5. 建议在算法包中包含详细的README文档说明算法用途和配置方法 