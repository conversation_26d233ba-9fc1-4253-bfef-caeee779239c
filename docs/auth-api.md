# 认证 API 文档

本文档描述了边缘计算平台的认证 API，包括用户登录、注册、令牌刷新和用户管理等功能。

## 认证流程

1. 用户通过 `/api/auth/login` 或 `/api/auth/register` 获取访问令牌和刷新令牌
2. 在后续请求中，将访问令牌添加到 `Authorization` 头部：`Bearer {access_token}`
3. 当访问令牌过期时，使用刷新令牌通过 `/api/auth/refresh` 获取新的访问令牌
4. 如果刷新令牌也过期，用户需要重新登录

## API 接口

### 登录

**请求**

```
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6",
    "expires_at": "2024-05-10T15:30:45Z",
    "token_type": "Bearer",
    "user_id": 1,
    "username": "admin",
    "name": "系统管理员",
    "role": "admin"
  }
}
```

### 注册

**请求**

```
POST /api/auth/register
Content-Type: application/json

{
  "username": "user1",
  "password": "password123",
  "name": "测试用户",
  "email": "<EMAIL>",
  "mobile": "13800138000"
}
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 2,
    "username": "user1",
    "name": "测试用户",
    "email": "<EMAIL>",
    "mobile": "13800138000",
    "role": "user",
    "status": "active",
    "created_at": "2024-05-09T10:30:45Z",
    "updated_at": "2024-05-09T10:30:45Z"
  }
}
```

### 刷新令牌

**请求**

```
POST /api/auth/refresh
Content-Type: application/json

{
  "refresh_token": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6"
}
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "q7r8s9t0-u1v2-w3x4-y5z6-a7b8c9d0e1f2",
    "expires_at": "2024-05-10T15:30:45Z",
    "token_type": "Bearer",
    "user_id": 1,
    "username": "admin",
    "name": "系统管理员",
    "role": "admin"
  }
}
```

### 获取当前用户信息

**请求**

```
GET /api/users/me
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "username": "admin",
    "name": "系统管理员",
    "email": "<EMAIL>",
    "mobile": "13900139000",
    "role": "admin",
    "status": "active",
    "last_login_at": "2024-05-09T10:15:30Z",
    "last_login_ip": "*************",
    "created_at": "2024-05-01T00:00:00Z",
    "updated_at": "2024-05-09T10:15:30Z"
  }
}
```

### 更新当前用户信息

**请求**

```
PUT /api/users/me
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "新名称",
  "email": "<EMAIL>",
  "mobile": "13800138001"
}
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "username": "admin",
    "name": "新名称",
    "email": "<EMAIL>",
    "mobile": "13800138001",
    "role": "admin",
    "status": "active",
    "last_login_at": "2024-05-09T10:15:30Z",
    "last_login_ip": "*************",
    "created_at": "2024-05-01T00:00:00Z",
    "updated_at": "2024-05-09T10:45:20Z"
  }
}
```

### 修改当前用户密码

**请求**

```
POST /api/users/me/change-password
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "old_password": "admin123",
  "new_password": "newpassword123"
}
```

**响应**

```json
{
  "code": 0,
  "message": "success"
}
```

## 管理员用户管理 API

### 获取用户列表

**请求**

```
GET /api/admin/users?page=1&pageSize=20&username=user&role=user&status=active
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 2,
      "username": "user1",
      "name": "测试用户",
      "email": "<EMAIL>",
      "mobile": "13800138000",
      "role": "user",
      "status": "active",
      "last_login_at": "2024-05-09T11:20:15Z",
      "last_login_ip": "*************",
      "created_at": "2024-05-09T10:30:45Z",
      "updated_at": "2024-05-09T11:20:15Z"
    }
  ],
  "paging": {
    "page": 1,
    "pageSize": 20,
    "totalCount": 1
  }
}
```

### 获取用户详情

**请求**

```
GET /api/admin/users/2
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 2,
    "username": "user1",
    "name": "测试用户",
    "email": "<EMAIL>",
    "mobile": "13800138000",
    "role": "user",
    "status": "active",
    "last_login_at": "2024-05-09T11:20:15Z",
    "last_login_ip": "*************",
    "created_at": "2024-05-09T10:30:45Z",
    "updated_at": "2024-05-09T11:20:15Z"
  }
}
```

### 创建用户

**请求**

```
POST /api/admin/users
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "username": "user2",
  "password": "password123",
  "name": "测试用户2",
  "email": "<EMAIL>",
  "mobile": "13800138002"
}
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 3,
    "username": "user2",
    "name": "测试用户2",
    "email": "<EMAIL>",
    "mobile": "13800138002",
    "role": "user",
    "status": "active",
    "created_at": "2024-05-09T14:25:10Z",
    "updated_at": "2024-05-09T14:25:10Z"
  }
}
```

### 更新用户

**请求**

```
PUT /api/admin/users/2
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "更新的用户名",
  "email": "<EMAIL>",
  "mobile": "13800138003",
  "role": "admin",
  "status": "inactive"
}
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 2,
    "username": "user1",
    "name": "更新的用户名",
    "email": "<EMAIL>",
    "mobile": "13800138003",
    "role": "admin",
    "status": "inactive",
    "last_login_at": "2024-05-09T11:20:15Z",
    "last_login_ip": "*************",
    "created_at": "2024-05-09T10:30:45Z",
    "updated_at": "2024-05-09T14:35:22Z"
  }
}
```

### 删除用户

**请求**

```
DELETE /api/admin/users/3
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**

```json
{
  "code": 0,
  "message": "success"
}
```

## 错误码

| 状态码 | 描述 |
|-------|------|
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有需要认证的API都必须在请求头中包含有效的访问令牌
2. 访问令牌有效期默认为24小时，过期后需要使用刷新令牌获取新的访问令牌
3. 刷新令牌有效期默认为7天，过期后需要重新登录
4. 管理员API只能由具有管理员权限的用户访问 