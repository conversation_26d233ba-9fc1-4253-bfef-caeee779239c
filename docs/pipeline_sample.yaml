id: "face_detection_pipeline"  # 添加顶层id字段
version: "1.0"
name: "人脸检测与识别管道"
description: "用于检测和识别视频中的人脸"
author: "VideoPipe Team"

globals:
  model_dir: "./vp_data/models/face/"
  video_dir: "./vp_data/test_video/"
  resize_ratio: 0.6

nodes:
  - id: "file_src_0"
    type: "vp_file_src_node"
    params:
      channel_index: 0
      file_path: "${globals.video_dir}face.mp4"
      resize_ratio: ${globals.resize_ratio}
  
  - id: "face_detector"
    type: "vp_yunet_face_detector_node"
    params:
      model_path: "${globals.model_dir}face_detection_yunet_2022mar.onnx"
      confidence_threshold: 0.9
      nms_threshold: 0.3
      top_k: 5000
  
  - id: "face_encoder"
    type: "vp_sface_feature_encoder_node"
    params:
      model_path: "${globals.model_dir}face_recognition_sface_2021dec.onnx"
  
  - id: "osd"
    type: "vp_face_osd_node_v2"
    params: {}
  
  - id: "screen_des_0"
    type: "vp_screen_des_node"
    params:
      channel_index: 0

connections:
  - id: 1  # 添加id字段
    from: "file_src_0"
    to: "face_detector"
  
  - id: 2  # 添加id字段
    from: "face_detector"
    to: "face_encoder"
  
  - id: 3  # 添加id字段
    from: "face_encoder"
    to: "osd"
  
  - id: 4  # 添加id字段
    from: "osd"
    to: "screen_des_0"