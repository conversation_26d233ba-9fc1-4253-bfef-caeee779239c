# VideoPipe HTTP API 返回示例

本文档提供了VideoPipe HTTP API的返回示例，可用于前端开发时模拟API响应，特别是用于生成Vue Flow图表。

## 1. 获取特定管道信息 (GET /pipelines/{id})

```json
{
  "status": "success",
  "data": {
    "id": "face_detection_pipeline",
    "name": "人脸检测与识别管道",
    "description": "用于检测和识别视频中的人脸",
    "version": "1.0",
    "author": "VideoPipe Team",
    "globals": {
      "model_dir": "./vp_data/models/face/",
      "video_dir": "./vp_data/test_video/",
      "resize_ratio": 0.6
    },
    "created_at": "2023-10-15T08:30:00Z",
    "updated_at": "2023-10-16T14:20:00Z"
  }
}
```

## 2. 获取管道中的所有节点 (GET /pipelines/{id}/nodes)

```json
{
  "status": "success",
  "data": [
    {
      "id": "file_src_0",
      "type": "vp_file_src_node",
      "params": {
        "channel_id": 0,
        "file_path": "./vp_data/test_video/10.mp4",
        "resize_ratio": 0.6,
        "loop": true
      }
    },
    {
      "id": "yunet_face_detector_0",
      "type": "vp_yunet_face_detector_node",
      "params": {
        "model_path": "./vp_data/models/face/face_detection_yunet_2022mar.onnx",
        "confidence_threshold": 0.7,
        "nms_threshold": 0.3,
        "top_k": 5000
      }
    },
    {
      "id": "sface_face_encoder_0",
      "type": "vp_sface_feature_encoder_node",
      "params": {
        "model_path": "./vp_data/models/face/face_recognition_sface_2021dec.onnx",
        "feature_dim": 128
      }
    },
    {
      "id": "osd_0",
      "type": "vp_face_osd_node_v2",
      "params": {
        "show_fps": true,
        "show_timestamp": true,
        "font_scale": 0.5
      }
    },
    {
      "id": "screen_des_0",
      "type": "vp_screen_des_node",
      "params": {
        "channel_id": 0,
        "window_name": "Face Detection"
      }
    },
    {
      "id": "rtmp_des_0",
      "type": "vp_rtmp_des_node",
      "params": {
        "channel_id": 0,
        "rtmp_url": "rtmp://localhost/live/face_detection"
      }
    }
  ]
}
```

## 3. 获取管道中的所有连接 (GET /pipelines/{id}/connections)

```json
{
  "status": "success",
  "data": [
    {
      "id": "conn_1",
      "from": {
        "is_single": true,
        "source": "file_src_0"
      },
      "to": "yunet_face_detector_0"
    },
    {
      "id": "conn_2",
      "from": {
        "is_single": true,
        "source": "yunet_face_detector_0"
      },
      "to": "sface_face_encoder_0"
    },
    {
      "id": "conn_3",
      "from": {
        "is_single": true,
        "source": "sface_face_encoder_0"
      },
      "to": "osd_0"
    },
    {
      "id": "conn_4",
      "from": {
        "is_single": true,
        "source": "osd_0"
      },
      "to": "screen_des_0"
    },
    {
      "id": "conn_5",
      "from": {
        "is_single": true,
        "source": "osd_0"
      },
      "to": "rtmp_des_0"
    }
  ]
}
```

## 4. 获取管道运行状态 (GET /pipelines/{id}/status)

```json
{
  "status": "success",
  "data": {
    "pipeline_id": "face_detection_pipeline",
    "state": "running",
    "start_time": "2023-10-20T09:15:30Z",
    "uptime_seconds": 3625,
    "nodes": "[{\"id\":\"file_src_0\",\"state\":\"running\",\"metrics\":{\"fps\":25.3,\"frames_processed\":91825,\"queue_size\":2}},{\"id\":\"yunet_face_detector_0\",\"state\":\"running\",\"metrics\":{\"detection_time_ms\":12.5,\"faces_detected\":3,\"inference_fps\":24.8}},{\"id\":\"sface_face_encoder_0\",\"state\":\"running\",\"metrics\":{\"encoding_time_ms\":8.2,\"features_extracted\":3}},{\"id\":\"osd_0\",\"state\":\"running\",\"metrics\":{\"render_time_ms\":3.1}},{\"id\":\"screen_des_0\",\"state\":\"running\",\"metrics\":{\"display_fps\":24.9}},{\"id\":\"rtmp_des_0\",\"state\":\"running\",\"metrics\":{\"streaming_fps\":24.8,\"bitrate_kbps\":2150}}]",
    "global_metrics": {
      "total_fps": 24.8,
      "memory_usage_mb": 285.6,
      "cpu_usage_percent": 18.5,
      "gpu_usage_percent": 22.3
    },
    "errors": []
  }
}
```

## 使用说明

这些JSON示例可以用于前端开发时模拟API响应。在实际开发Vue Flow图表时，需要使用数据转换函数将这些API数据转换为Vue Flow所需的节点和边格式。

主要的转换函数包括：

1. `convertToVueFlowNodes` - 将API节点数据转换为Vue Flow节点格式
2. `convertToVueFlowEdges` - 将API连接数据转换为Vue Flow边格式

示例代码可参考 `vue-flow.md` 文档中的详细实现。 