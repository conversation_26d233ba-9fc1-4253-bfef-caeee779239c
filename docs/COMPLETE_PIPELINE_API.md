# 完整管道配置API

本文档介绍了新增的完整管道配置API，该API允许用户一次性创建包含基础信息、globals、nodes和connections的完整管道配置。

## 接口概述

- **URL**: `/api/v1/pipelines/complete`
- **方法**: `POST`
- **描述**: 一次性创建包含基础信息、globals、nodes和connections的完整管道配置

## 功能特点

1. **一次性创建**: 无需分别调用多个API，一次请求即可创建完整的管道配置
2. **完整配置**: 支持管道的所有配置项，包括基础信息、全局变量、节点和连接
3. **类型安全**: 自动处理不同数据类型的参数转换
4. **错误处理**: 提供详细的错误信息和验证

## 请求格式

### 请求头
```
X-API-Key: your-api-key
Content-Type: application/json
```

### 请求体结构

```json
{
  "id": "pipeline_id",                    // 可选，管道唯一标识符
  "name": "管道名称",                      // 必需，管道名称
  "description": "管道描述",               // 可选，管道描述
  "version": "1.0",                       // 可选，版本号，默认为"1.0"
  "author": "作者名称",                    // 可选，作者，默认为"API User"
  "globals": {                            // 可选，全局变量
    "key1": "value1",
    "key2": 123,
    "key3": true
  },
  "nodes": [                              // 可选，节点配置数组
    {
      "id": "node_id",                    // 必需，节点唯一标识符
      "type": "node_type",                // 必需，节点类型
      "params": {                         // 可选，节点参数
        "param1": "value1",
        "param2": 123,
        "param3": true
      }
    }
  ],
  "connections": [                        // 可选，连接配置数组
    {
      "id": 1,                           // 可选，连接ID，默认自动分配
      "from": "source_node_id",          // 必需，源节点ID（单个源）
      "to": "target_node_id"             // 必需，目标节点ID
    },
    {
      "id": 2,
      "from": ["node1", "node2"],        // 多个源节点ID（多个源）
      "to": "target_node_id"
    }
  ]
}
```

## 响应格式

### 成功响应
```json
{
  "status": "success",
  "data": {
    "id": "created_pipeline_id",
    "name": "管道名称",
    "created_at": "2023-06-20T17:45:30Z",
    "nodes_count": "5",
    "connections_count": "4"
  }
}
```

### 错误响应
```json
{
  "status": "error",
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息"
  }
}
```

## 使用示例

### 创建人脸检测管道

```bash
curl -X POST \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "face_detection_complete",
    "name": "人脸检测与识别管道",
    "description": "用于检测和识别视频中的人脸",
    "version": "1.0",
    "author": "VideoPipe Team",
    "globals": {
      "model_dir": "./vp_data/models/face/",
      "video_dir": "./vp_data/test_video/",
      "resize_ratio": 0.6
    },
    "nodes": [
      {
        "id": "file_src_0",
        "type": "vp_file_src_node",
        "params": {
          "channel_index": 0,
          "file_path": "${globals.video_dir}face.mp4",
          "resize_ratio": "${globals.resize_ratio}"
        }
      },
      {
        "id": "face_detector",
        "type": "vp_yunet_face_detector_node",
        "params": {
          "model_path": "${globals.model_dir}face_detection_yunet_2022mar.onnx",
          "confidence_threshold": 0.9,
          "nms_threshold": 0.3,
          "top_k": 5000
        }
      },
      {
        "id": "screen_des_0",
        "type": "vp_screen_des_node",
        "params": {
          "channel_index": 0
        }
      }
    ],
    "connections": [
      {
        "id": 1,
        "from": "file_src_0",
        "to": "face_detector"
      },
      {
        "id": 2,
        "from": "face_detector",
        "to": "screen_des_0"
      }
    ]
  }' \
  http://localhost:8080/api/v1/pipelines/complete
```

## 测试脚本

项目提供了测试脚本 `test_complete_pipeline_api.sh` 来验证API功能：

```bash
# 使用默认参数测试
./utils/http_api/test_complete_pipeline_api.sh

# 指定服务器参数测试
./utils/http_api/test_complete_pipeline_api.sh localhost 8080 your-api-key
```

## 注意事项

1. **参数类型**: API会自动处理字符串、数字和布尔类型的参数转换
2. **变量引用**: 支持在节点参数中使用 `${globals.key}` 语法引用全局变量
3. **ID分配**: 如果未指定管道ID，系统会自动生成唯一ID
4. **连接ID**: 如果未指定连接ID，系统会自动分配
5. **验证**: 创建后建议使用验证API检查配置的有效性

## 相关API

- `GET /api/v1/pipelines/{id}` - 获取管道配置
- `GET /api/v1/pipelines/{id}/export` - 导出管道配置为YAML
- `POST /api/v1/pipelines/{id}/validate` - 验证管道配置
- `DELETE /api/v1/pipelines/{id}` - 删除管道配置
