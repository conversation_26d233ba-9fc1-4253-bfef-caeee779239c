# 球机相机控制 API 文档

## 概述

球机相机控制API提供了对支持云台控制的摄像头进行PTZ（Pan-Tilt-Zoom，即平移-倾斜-缩放）操作的能力，支持ONVIF和GB28181两种协议，可以实现上、下、左、右、放大、缩小等基本控制功能，以及预置点的设置、调用和删除操作。

## API 接口

### 1. PTZ控制

**接口描述**: 控制球机摄像头的云台移动方向和速度

**请求路径**: `/api/videos/{id}/ptz`

**请求方法**: POST

**路径参数**:
- `id`: 视频源ID，必填，整数类型

**请求体**:
```json
{
  "direction": "up",  // 控制方向: up, down, left, right, zoomin, zoomout, stop
  "speed": 50         // 速度: 1-100
}
```

**请求参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| direction | string | 是 | 控制方向，可选值：up(上)、down(下)、left(左)、right(右)、zoomin(放大)、zoomout(缩小)、stop(停止) |
| speed | int | 是 | 控制速度，范围1-100，值越大速度越快 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success"
}
```

**错误响应**:
```json
{
  "code": 400,
  "message": "无效的视频源ID"
}
```

```json
{
  "code": 400,
  "message": "缺少方向参数"
}
```

```json
{
  "code": 400,
  "message": "不支持的摄像头协议: 不支持的协议类型"
}
```

```json
{
  "code": 500,
  "message": "PTZ控制失败: 连接设备超时"
}
```

### 2. 预置点控制

**接口描述**: 控制球机摄像头的预置点，包括设置、调用和删除预置点

**请求路径**: `/api/videos/{id}/preset`

**请求方法**: POST

**路径参数**:
- `id`: 视频源ID，必填，整数类型

**请求体**:
```json
{
  "action": "set",   // 操作类型: set(设置), goto(调用), remove(删除)
  "presetId": 1      // 预置点ID: 1-255
}
```

**请求参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| action | string | 是 | 操作类型，可选值：set(设置预置点)、goto(调用预置点)、remove(删除预置点) |
| presetId | int | 是 | 预置点ID，范围1-255 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success"
}
```

**错误响应**:
```json
{
  "code": 400,
  "message": "无效的视频源ID"
}
```

```json
{
  "code": 400,
  "message": "无效的预置点ID"
}
```

```json
{
  "code": 400,
  "message": "不支持的预置点操作: 无效的操作"
}
```

```json
{
  "code": 500,
  "message": "预置点控制失败: 设备拒绝操作"
}
```

## 协议支持

### ONVIF协议

对于ONVIF协议的摄像头，系统会将API中的参数转换为ONVIF标准的格式：

- 速度值(speed)会被映射到ONVIF标准的-1.0到1.0范围
- 根据方向调用ONVIF的`ContinuousMove`或`Stop`方法
- 预置点操作会调用ONVIF的对应方法

### GB28181协议

对于GB28181协议的摄像头，系统会将API中的参数转换为GB28181标准的格式：

- 速度值(speed)会被映射到GB28181标准的1-255范围
- 通过国标协议命令控制云台
- 预置点操作会转换为GB28181协议中的对应命令

## 使用场景

### 1. 实时监控场景

在实时监控界面中，用户可以通过方向按钮控制摄像头，查看不同角度的画面。

### 2. 预置点巡航

设置多个预置点后，可以按照预定的顺序调用这些预置点，实现自动巡航功能。

### 3. 智能跟踪

结合算法检测结果，可以控制摄像头自动跟踪目标对象。

## 注意事项

1. 不同品牌和型号的摄像头对PTZ控制的支持程度可能不同，部分功能可能不可用
2. 控制速度的实际效果与摄像头硬件性能有关
3. 预置点的数量上限取决于摄像头型号，通常在8-255之间
4. 对于不支持云台控制的固定摄像头，调用PTZ控制API会返回错误
5. 连续发送控制命令可能会导致摄像头反应延迟，建议适当控制命令发送频率 