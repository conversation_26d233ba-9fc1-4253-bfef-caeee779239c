# 告警记录管理 API 文档

## 概述

告警记录管理功能提供算法检测结果的完整生命周期管理，包括告警记录的查询、状态更新、图片管理等功能。支持多种查询条件和分页显示。

## 数据结构

### Alert

```json
{
  "id": 1,
  "source": "大厅入口摄像头",
  "ip": "*************",
  "level": "warning",
  "type": "人员检测",
  "video_id": 1,
  "video_name": "摄像头01",
  "algorithm_id": 1,
  "algorithm_name": "人员检测算法",
  "content": "检测到人员入侵，位置：大厅入口",
  "original_image": "./data/images/original/image_1640995200000000000",
  "image_path": "./data/images/alert/image_1640995200000000001",
  "status": "new",
  "created_at": "2024-01-01T12:30:45Z",
  "updated_at": "2024-01-01T12:30:45Z"
}
```

### 字段说明

- `id`: 告警记录ID
- `source`: 告警来源（摄像头描述）
- `ip`: 摄像头IP地址
- `level`: 危险等级（info=信息, warning=警告, error=错误）
- `type`: 告警类型（算法类型）
- `video_id`: 视频源ID
- `video_name`: 视频源名称
- `algorithm_id`: 算法ID
- `algorithm_name`: 算法名称
- `content`: 告警内容描述
- `original_image`: 原图地址
- `image_path`: 告警图地址（标注后的图片）
- `status`: 处理状态（new=新告警, processed=已处理, ignored=已忽略）
- `created_at`: 检测时间
- `updated_at`: 更新时间

## API 接口

### 1. 查询告警记录列表

**GET** `/api/alerts`

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "source": "大厅入口摄像头",
      "ip": "*************",
      "level": "warning",
      "type": "人员检测",
      "video_name": "摄像头01",
      "algorithm_name": "人员检测算法",
      "content": "检测到人员入侵",
      "status": "new",
      "created_at": "2024-01-01T12:30:45Z"
    }
  ]
}
```

### 2. 获取单个告警记录

**GET** `/api/alerts/{id}`

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "source": "大厅入口摄像头",
    "ip": "*************",
    "level": "warning",
    "type": "人员检测",
    "video_id": 1,
    "video_name": "摄像头01",
    "algorithm_id": 1,
    "algorithm_name": "人员检测算法",
    "content": "检测到人员入侵，位置：大厅入口",
    "original_image": "./data/images/original/image_1640995200000000000",
    "image_path": "./data/images/alert/image_1640995200000000001",
    "status": "new",
    "created_at": "2024-01-01T12:30:45Z",
    "updated_at": "2024-01-01T12:30:45Z"
  }
}
```

### 3. 条件查询告警记录

**GET** `/api/alerts/query`

**查询参数:**
- `videoId`: 视频源ID（可选）
- `algorithmId`: 算法ID（可选）
- `videoName`: 视频源名称（可选，支持模糊查询）
- `level`: 危险等级（可选）
- `type`: 告警类型（可选）
- `status`: 处理状态（可选）
- `startTime`: 开始时间（格式：2006-01-02 15:04:05）
- `endTime`: 结束时间（格式：2006-01-02 15:04:05）
- `page`: 页码（可选，默认为1）
- `limit`: 每页数量（可选，默认为20，最大100）

**请求示例:**
```bash
# 按视频源ID和危险等级查询
curl "http://localhost:8080/api/alerts/query?videoId=1&level=warning&startTime=2024-01-01%2000:00:00&endTime=2024-01-31%2023:59:59"

# 按视频源名称模糊查询
curl "http://localhost:8080/api/alerts/query?videoName=摄像头&level=warning&page=1&limit=20"

# 分页查询所有告警
curl "http://localhost:8080/api/alerts/query?page=1&limit=50"
```

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "source": "大厅入口摄像头",
        "level": "warning",
        "type": "人员检测",
        "content": "检测到人员入侵",
        "status": "new",
        "created_at": "2024-01-01T12:30:45Z"
      }
    ],
    "paging": {
      "page": 1,
      "limit": 100,
      "total": 1
    }
  }
}
```

### 4. 更新告警状态

**PUT** `/api/alerts/{id}/status`

**请求体:**
```json
{
  "status": "processed"
}
```

**响应示例:**
```json
{
  "code": 0,
  "message": "success"
}
```

### 5. 删除告警记录

**DELETE** `/api/alerts/{id}`

**响应示例:**
```json
{
  "code": 0,
  "message": "success"
}
```

### 6. 上传告警图片

**POST** `/api/alerts/{id}/images`

**请求方式:** `multipart/form-data`

**请求参数:**
- `originalImage`: 原图文件（可选）
- `alertImage`: 告警图文件（可选）

**请求示例:**
```bash
curl -X POST http://localhost:8080/api/alerts/1/images \
  -F "originalImage=@original.jpg" \
  -F "alertImage=@alert.jpg"
```

**响应示例:**
```json
{
  "code": 0,
  "message": "success"
}
```

## 告警等级说明

### info (信息)
- 一般性信息提示
- 不需要立即处理
- 用于记录正常的检测事件

### warning (警告)
- 需要关注的异常情况
- 建议及时处理
- 可能影响安全或运行效率

### error (错误)
- 严重的安全威胁或系统故障
- 需要立即处理
- 可能造成重大损失或安全事故

## 告警状态说明

### new (新告警)
- 刚生成的告警记录
- 等待处理
- 系统默认状态

### processed (已处理)
- 已经查看和处理的告警
- 处理措施已完成
- 可以归档

### ignored (已忽略)
- 确认为误报或不需要处理的告警
- 不再显示在待处理列表中
- 保留记录用于统计分析

## 图片管理

### 图片类型
- **原图 (original_image)**: 算法检测时的原始图片
- **告警图 (image_path)**: 经过算法标注的图片，包含检测框和标签

### 图片存储
- 原图存储路径: `./data/images/original/`
- 告警图存储路径: `./data/images/alert/`
- 支持格式: JPG, PNG, BMP
- 文件命名: `image_{timestamp}`

### 图片访问
```bash
# 获取原图
GET /api/files/original/{filename}

# 获取告警图
GET /api/files/alert/{filename}
```

## 统计分析

### 按时间统计
```bash
# 查询某个时间段的告警数量
GET /api/alerts/stats/time?startTime=2024-01-01&endTime=2024-01-31
```

### 按等级统计
```bash
# 查询各等级告警数量分布
GET /api/alerts/stats/level
```

### 按类型统计
```bash
# 查询各类型告警数量分布
GET /api/alerts/stats/type
```

## 使用场景

### 1. 实时监控
- 实时查询新产生的告警记录
- 根据告警等级进行优先级处理
- 快速响应高等级告警

### 2. 历史分析
- 查询指定时间段的历史告警
- 分析告警趋势和规律
- 生成统计报表

### 3. 告警处理
- 批量更新告警状态
- 添加处理备注
- 删除误报告警

### 4. 图片管理
- 上传检测图片
- 查看告警现场图片
- 下载图片用于取证

## 性能优化

### 查询优化
- 使用时间范围查询时建议指定具体的开始和结束时间
- 大数据量查询时使用分页功能
- 合理使用索引字段进行过滤

### 存储优化
- 定期清理过期的告警记录和图片
- 使用压缩格式存储图片
- 考虑使用对象存储服务存储大量图片

## 注意事项

1. 删除告警记录会同时删除相关的图片文件
2. 图片文件名使用时间戳生成，确保唯一性
3. 查询历史数据时建议设置合理的时间范围
4. 告警状态更新后不可回退，请谨慎操作
5. 大批量操作建议分批进行，避免系统压力过大
6. 图片上传支持多种格式，但建议使用JPG格式以节省存储空间 