# 网络配置功能使用示例

## 概述

本文档提供了网络配置功能的详细使用示例，包括网络监控、防火墙管理和网络诊断三个核心模块的实际应用。

## 1. 网络监控模块使用示例

### 1.1 基础监控功能

```go
package main

import (
    "fmt"
    "time"
    
    "ecp/internal/app/network/monitor"
)

func main() {
    // 创建网络监控管理器
    // 参数：监控间隔5秒，采样率100%
    manager := monitor.NewManager(5*time.Second, 100)
    
    // 获取网络状态
    status, err := manager.GetNetworkStatus()
    if err != nil {
        fmt.Printf("获取网络状态失败: %v\n", err)
        return
    }
    
    fmt.Printf("网络状态概览:\n")
    fmt.Printf("- 网络接口数量: %d\n", len(status.Interfaces))
    fmt.Printf("- 默认网关: %s\n", status.DefaultGateway)
    fmt.Printf("- DNS服务器: %v\n", status.DNSServers)
    fmt.Printf("- 互联网访问: %v\n", status.InternetAccess)
    
    // 显示每个接口的详细信息
    for _, iface := range status.Interfaces {
        fmt.Printf("\n接口: %s\n", iface.Name)
        fmt.Printf("  类型: %s\n", iface.Type)
        fmt.Printf("  状态: %s\n", iface.Status)
        fmt.Printf("  IP地址: %s\n", iface.IPAddress)
        fmt.Printf("  MAC地址: %s\n", iface.MACAddress)
        fmt.Printf("  MTU: %d\n", iface.MTU)
    }
}
```

### 1.2 流量统计监控

```go
func monitorTraffic() {
    manager := monitor.NewManager(1*time.Second, 100)
    
    // 启动后台监控
    manager.StartMonitoring()
    defer manager.StopMonitoring()
    
    // 等待一段时间让监控收集数据
    time.Sleep(3 * time.Second)
    
    // 获取流量统计
    stats, err := manager.GetTrafficStats()
    if err != nil {
        fmt.Printf("获取流量统计失败: %v\n", err)
        return
    }
    
    fmt.Printf("流量统计:\n")
    for _, stat := range stats {
        fmt.Printf("\n接口: %s\n", stat.Interface)
        fmt.Printf("  接收: %d bytes (%d packets)\n", stat.RxBytes, stat.RxPackets)
        fmt.Printf("  发送: %d bytes (%d packets)\n", stat.TxBytes, stat.TxPackets)
        fmt.Printf("  接收速度: %.2f bytes/s\n", stat.RxSpeed)
        fmt.Printf("  发送速度: %.2f bytes/s\n", stat.TxSpeed)
        fmt.Printf("  错误: RX=%d, TX=%d\n", stat.RxErrors, stat.TxErrors)
        fmt.Printf("  丢包: RX=%d, TX=%d\n", stat.RxDropped, stat.TxDropped)
    }
}
```

### 1.3 网络质量检测

```go
func checkNetworkQuality() {
    manager := monitor.NewManager(5*time.Second, 100)
    
    targets := []string{"8.8.8.8", "1.1.1.1", "114.114.114.114"}
    
    for _, target := range targets {
        quality, err := manager.GetNetworkQuality(target)
        if err != nil {
            fmt.Printf("检测 %s 失败: %v\n", target, err)
            continue
        }
        
        fmt.Printf("\n目标: %s\n", quality.Target)
        fmt.Printf("  延迟: %v\n", quality.Latency)
        fmt.Printf("  丢包率: %.2f%%\n", quality.PacketLoss)
        fmt.Printf("  抖动: %v\n", quality.Jitter)
        fmt.Printf("  带宽: %.2f Mbps\n", quality.Bandwidth)
    }
}
```

## 2. 防火墙管理模块使用示例

### 2.1 防火墙状态查询

```go
package main

import (
    "fmt"
    
    "ecp/internal/app/network/firewall"
    "ecp/internal/app/network/models"
)

func main() {
    // 创建防火墙管理器
    manager := firewall.NewManager("/sbin/iptables", "/etc/ecp/firewall/backup")
    
    // 获取防火墙状态
    status, err := manager.GetStatus()
    if err != nil {
        fmt.Printf("获取防火墙状态失败: %v\n", err)
        return
    }
    
    fmt.Printf("防火墙状态:\n")
    fmt.Printf("- 启用状态: %v\n", status.Enabled)
    fmt.Printf("- 默认策略: %s\n", status.DefaultPolicy)
    fmt.Printf("- 规则数量: %d\n", status.RuleCount)
    fmt.Printf("- 最后更新: %v\n", status.LastUpdated)
}
```

### 2.2 防火墙规则管理

```go
func manageFirewallRules() {
    manager := firewall.NewManager("/sbin/iptables", "/etc/ecp/firewall/backup")
    
    // 添加允许HTTP访问的规则
    httpRule := &models.FirewallRule{
        Chain:    "INPUT",
        Target:   "ACCEPT",
        Protocol: "tcp",
        DestPort: "80",
        Comment:  "允许HTTP访问",
        Enabled:  true,
    }
    
    err := manager.AddRule(httpRule)
    if err != nil {
        fmt.Printf("添加HTTP规则失败: %v\n", err)
    } else {
        fmt.Printf("HTTP规则添加成功，ID: %s\n", httpRule.ID)
    }
    
    // 添加允许HTTPS访问的规则
    httpsRule := &models.FirewallRule{
        Chain:       "INPUT",
        Target:      "ACCEPT",
        Protocol:    "tcp",
        DestPort:    "443",
        Source:      "***********/24", // 限制来源网段
        Comment:     "允许内网HTTPS访问",
        Enabled:     true,
    }
    
    err = manager.AddRule(httpsRule)
    if err != nil {
        fmt.Printf("添加HTTPS规则失败: %v\n", err)
    } else {
        fmt.Printf("HTTPS规则添加成功，ID: %s\n", httpsRule.ID)
    }
    
    // 获取所有规则
    rules, err := manager.GetRules()
    if err != nil {
        fmt.Printf("获取规则失败: %v\n", err)
        return
    }
    
    fmt.Printf("\n当前防火墙规则:\n")
    for i, rule := range rules {
        fmt.Printf("%d. [%s] %s %s:%s -> %s:%s (%s)\n",
            i+1, rule.Chain, rule.Protocol,
            rule.Source, rule.SourcePort,
            rule.Destination, rule.DestPort,
            rule.Target)
        if rule.Comment != "" {
            fmt.Printf("   注释: %s\n", rule.Comment)
        }
    }
}
```

## 3. 网络诊断模块使用示例

### 3.1 Ping连通性测试

```go
package main

import (
    "fmt"
    "time"
    
    "ecp/internal/app/network/diagnostic"
)

func main() {
    // 创建诊断管理器
    manager := diagnostic.NewManager(5*time.Second, 30, 3*time.Second)
    
    targets := []string{"8.8.8.8", "baidu.com", "github.com"}
    
    for _, target := range targets {
        fmt.Printf("\nPing测试: %s\n", target)
        
        result, err := manager.Ping(target, 4)
        if err != nil {
            fmt.Printf("  错误: %v\n", err)
            continue
        }
        
        fmt.Printf("  发送: %d 包\n", result.PacketsSent)
        fmt.Printf("  接收: %d 包\n", result.PacketsRecv)
        fmt.Printf("  丢包率: %.2f%%\n", result.PacketLoss)
        
        if result.PacketsRecv > 0 {
            fmt.Printf("  最小延迟: %v\n", result.MinLatency)
            fmt.Printf("  最大延迟: %v\n", result.MaxLatency)
            fmt.Printf("  平均延迟: %v\n", result.AvgLatency)
            fmt.Printf("  标准差: %v\n", result.StdDev)
        }
    }
}
```

### 3.2 路由跟踪测试

```go
func traceRoute() {
    manager := diagnostic.NewManager(5*time.Second, 30, 3*time.Second)
    
    target := "baidu.com"
    fmt.Printf("路由跟踪: %s\n", target)
    
    result, err := manager.Traceroute(target)
    if err != nil {
        fmt.Printf("路由跟踪失败: %v\n", err)
        return
    }
    
    fmt.Printf("跳数\tIP地址\t\t延迟\n")
    fmt.Printf("----\t------\t\t----\n")
    
    for _, hop := range result.Hops {
        fmt.Printf("%d\t%s\t\t%v\n", hop.Hop, hop.IP, hop.Latency)
    }
}
```

### 3.3 端口扫描测试

```go
func scanPorts() {
    manager := diagnostic.NewManager(5*time.Second, 30, 3*time.Second)
    
    target := "***********"
    ports := []int{21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 8080}
    
    fmt.Printf("端口扫描: %s\n", target)
    
    result, err := manager.PortScan(target, ports)
    if err != nil {
        fmt.Printf("端口扫描失败: %v\n", err)
        return
    }
    
    fmt.Printf("开放端口: %v\n", result.OpenPorts)
    
    // 显示端口服务信息
    serviceMap := map[int]string{
        21:   "FTP",
        22:   "SSH",
        23:   "Telnet",
        25:   "SMTP",
        53:   "DNS",
        80:   "HTTP",
        110:  "POP3",
        143:  "IMAP",
        443:  "HTTPS",
        993:  "IMAPS",
        995:  "POP3S",
        8080: "HTTP-Alt",
    }
    
    for _, port := range result.OpenPorts {
        service := serviceMap[port]
        if service == "" {
            service = "Unknown"
        }
        fmt.Printf("  端口 %d: %s\n", port, service)
    }
}
```

### 3.4 综合网络诊断

```go
func diagnoseNetwork() {
    manager := diagnostic.NewManager(5*time.Second, 30, 3*time.Second)
    
    fmt.Printf("执行综合网络诊断...\n")
    
    result, err := manager.DiagnoseNetwork()
    if err != nil {
        fmt.Printf("网络诊断失败: %v\n", err)
        return
    }
    
    if len(result.Issues) == 0 {
        fmt.Printf("✅ 网络状态良好，未发现问题\n")
    } else {
        fmt.Printf("⚠️  发现 %d 个网络问题:\n", len(result.Issues))
        for i, issue := range result.Issues {
            fmt.Printf("  %d. %s\n", i+1, issue)
        }
        
        if len(result.Suggestions) > 0 {
            fmt.Printf("\n💡 建议解决方案:\n")
            for i, suggestion := range result.Suggestions {
                fmt.Printf("  %d. %s\n", i+1, suggestion)
            }
        }
    }
    
    fmt.Printf("\n诊断时间: %v\n", result.TestTime)
}
```

## 4. 集成使用示例

### 4.1 网络健康检查脚本

```go
func networkHealthCheck() {
    fmt.Printf("=== 网络健康检查 ===\n")
    
    // 1. 检查网络状态
    monitor := monitor.NewManager(5*time.Second, 100)
    status, err := monitor.GetNetworkStatus()
    if err == nil {
        fmt.Printf("✅ 网络接口: %d 个活跃\n", len(status.Interfaces))
        fmt.Printf("✅ 互联网连接: %v\n", status.InternetAccess)
    }
    
    // 2. 检查防火墙状态
    firewall := firewall.NewManager("/sbin/iptables", "/tmp/backup")
    fwStatus, err := firewall.GetStatus()
    if err == nil {
        fmt.Printf("✅ 防火墙状态: %v\n", fwStatus.Enabled)
        fmt.Printf("✅ 防火墙规则: %d 条\n", fwStatus.RuleCount)
    }
    
    // 3. 执行网络诊断
    diagnostic := diagnostic.NewManager(3*time.Second, 30, 2*time.Second)
    diagResult, err := diagnostic.DiagnoseNetwork()
    if err == nil {
        if len(diagResult.Issues) == 0 {
            fmt.Printf("✅ 网络诊断: 无问题\n")
        } else {
            fmt.Printf("⚠️  网络诊断: %d 个问题\n", len(diagResult.Issues))
        }
    }
    
    fmt.Printf("=== 检查完成 ===\n")
}
```

## 5. 最佳实践

### 5.1 错误处理
- 始终检查函数返回的错误
- 对于需要权限的操作（如防火墙管理），提供友好的错误提示
- 实现重试机制处理临时网络问题

### 5.2 性能优化
- 使用适当的监控间隔，避免过于频繁的系统调用
- 对于批量操作，考虑使用协程池限制并发数
- 缓存不经常变化的数据（如网络接口信息）

### 5.3 安全考虑
- 防火墙操作需要管理员权限
- 验证所有输入参数，防止命令注入
- 记录重要的网络配置变更操作

这些示例展示了网络配置功能的完整使用方法，可以根据实际需求进行调整和扩展。
