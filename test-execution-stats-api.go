package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// StatsResponse 定义统计API响应结构
type StatsResponse struct {
	SuccessCount int64   `json:"success_count"`
	FailedCount  int64   `json:"failed_count"`
	AvgDuration  float64 `json:"avg_duration"`
	SuccessRate  float64 `json:"success_rate"`
}

// APIResponse 定义通用API响应结构
type APIResponse struct {
	ErrorCode int         `json:"errorCode"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data"`
	Success   bool        `json:"success"`
}

func main() {
	// 测试执行记录统计API
	fmt.Println("测试执行记录统计API...")
	
	// 构建请求URL
	baseURL := "http://localhost:3000/api/linkage/executions/stats"
	
	// 测试不带参数的请求
	fmt.Println("\n1. 测试不带参数的请求:")
	testAPI(baseURL)
	
	// 测试带时间范围参数的请求
	fmt.Println("\n2. 测试带时间范围参数的请求:")
	endTime := time.Now().Format("2006-01-02 15:04:05")
	startTime := time.Now().AddDate(0, 0, -7).Format("2006-01-02 15:04:05")
	
	urlWithParams := fmt.Sprintf("%s?start_time=%s&end_time=%s", baseURL, startTime, endTime)
	testAPI(urlWithParams)
}

func testAPI(url string) {
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	
	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return
	}
	
	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}
	
	// 格式化JSON响应
	var prettyJSON bytes.Buffer
	if err := json.Indent(&prettyJSON, body, "", "  "); err != nil {
		fmt.Printf("格式化JSON失败: %v\n", err)
		fmt.Printf("原始响应: %s\n", string(body))
		return
	}
	
	// 输出结果
	fmt.Printf("状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应内容:\n%s\n", prettyJSON.String())
	
	// 解析响应数据
	if resp.StatusCode == http.StatusOK {
		var apiResp APIResponse
		if err := json.Unmarshal(body, &apiResp); err != nil {
			fmt.Printf("解析API响应失败: %v\n", err)
			return
		}
		
		if apiResp.Success {
			// 解析统计数据
			dataBytes, err := json.Marshal(apiResp.Data)
			if err != nil {
				fmt.Printf("序列化数据失败: %v\n", err)
				return
			}
			
			var stats StatsResponse
			if err := json.Unmarshal(dataBytes, &stats); err != nil {
				fmt.Printf("解析统计数据失败: %v\n", err)
				return
			}
			
			// 输出统计信息
			fmt.Printf("统计结果:\n")
			fmt.Printf("  成功执行次数: %d\n", stats.SuccessCount)
			fmt.Printf("  失败执行次数: %d\n", stats.FailedCount)
			fmt.Printf("  平均执行耗时: %.2fms\n", stats.AvgDuration)
			fmt.Printf("  成功率: %.2f%%\n", stats.SuccessRate)
		} else {
			fmt.Printf("API调用失败: %s\n", apiResp.Message)
		}
	} else {
		fmt.Printf("HTTP请求失败，状态码: %d\n", resp.StatusCode)
	}
}
