@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

rem GB28181服务启动脚本 (Windows版本)
rem 用于独立启动GB28181服务

set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set GB28181_DIR=%PROJECT_ROOT%\third_party\gb28181

rem 日志函数
:log_info
echo [INFO] %~1
goto :eof

:log_warn
echo [WARN] %~1
goto :eof

:log_error
echo [ERROR] %~1
goto :eof

rem 检查依赖
:check_dependencies
call :log_info "检查依赖..."

rem 检查Go是否安装
go version >nul 2>&1
if errorlevel 1 (
    call :log_error "Go未安装，请先安装Go 1.19或更高版本"
    exit /b 1
)

rem 获取Go版本
for /f "tokens=3" %%i in ('go version 2^>nul') do set GO_VERSION=%%i
call :log_info "当前Go版本: !GO_VERSION!"

rem 检查ZLMediaKit是否运行（可选）
tasklist /FI "IMAGENAME eq MediaServer.exe" 2>nul | find /I "MediaServer.exe" >nul
if errorlevel 1 (
    call :log_warn "ZLMediaKit流媒体服务未运行，请确保已启动"
    call :log_warn "可以从 https://github.com/ZLMediaKit/ZLMediaKit 下载并启动"
)
goto :eof

rem 准备环境
:prepare_environment
call :log_info "准备环境..."

rem 创建必要的目录
if not exist "%PROJECT_ROOT%\data\gb28181" mkdir "%PROJECT_ROOT%\data\gb28181"
if not exist "%PROJECT_ROOT%\logs\gb28181" mkdir "%PROJECT_ROOT%\logs\gb28181"

rem 复制配置文件
if not exist "%GB28181_DIR%\config\gbserver.yml" (
    call :log_warn "配置文件不存在，使用默认配置"
    if exist "%PROJECT_ROOT%\configs\gb28181.yml" (
        copy "%PROJECT_ROOT%\configs\gb28181.yml" "%GB28181_DIR%\config\gbserver.yml" >nul
    )
)
goto :eof

rem 构建GB28181服务
:build_gb28181
call :log_info "构建GB28181服务..."

cd /d "%GB28181_DIR%"

rem 下载依赖
call :log_info "下载Go模块依赖..."
go mod tidy
if errorlevel 1 (
    call :log_error "下载依赖失败"
    exit /b 1
)

rem 构建服务
call :log_info "编译GB28181服务..."
if not exist "bin" mkdir bin
go build -o bin\gbserver.exe .\cmd\gbserver

if errorlevel 1 (
    call :log_error "GB28181服务构建失败"
    exit /b 1
) else (
    call :log_info "GB28181服务构建成功"
)
goto :eof

rem 启动GB28181服务
:start_gb28181
call :log_info "启动GB28181服务..."

cd /d "%GB28181_DIR%"

rem 检查是否已经在运行
tasklist /FI "IMAGENAME eq gbserver.exe" 2>nul | find /I "gbserver.exe" >nul
if not errorlevel 1 (
    call :log_warn "GB28181服务已在运行"
    set /p restart="是否重启服务? (y/n): "
    if /i "!restart!"=="y" (
        call :log_info "停止现有服务..."
        taskkill /F /IM gbserver.exe >nul 2>&1
        timeout /t 2 >nul
    ) else (
        call :log_info "保持现有服务运行"
        goto :eof
    )
)

rem 启动服务
call :log_info "启动GB28181服务进程..."
start /B "" bin\gbserver.exe -config=config\gbserver.yml > "%PROJECT_ROOT%\logs\gb28181\gbserver.log" 2>&1

rem 等待服务启动
timeout /t 3 >nul

rem 检查服务是否成功启动
tasklist /FI "IMAGENAME eq gbserver.exe" 2>nul | find /I "gbserver.exe" >nul
if not errorlevel 1 (
    call :log_info "GB28181服务启动成功"
    call :log_info "日志文件: %PROJECT_ROOT%\logs\gb28181\gbserver.log"
    call :log_info "服务地址: http://localhost:18080"
) else (
    call :log_error "GB28181服务启动失败"
    call :log_error "请查看日志文件: %PROJECT_ROOT%\logs\gb28181\gbserver.log"
    exit /b 1
)
goto :eof

rem 检查服务状态
:check_status
call :log_info "检查GB28181服务状态..."

tasklist /FI "IMAGENAME eq gbserver.exe" 2>nul | find /I "gbserver.exe" >nul
if not errorlevel 1 (
    call :log_info "GB28181服务正在运行"
    
    rem 尝试访问健康检查接口
    where curl >nul 2>&1
    if not errorlevel 1 (
        curl -s http://localhost:18080/api/health >nul 2>&1
        if not errorlevel 1 (
            call :log_info "GB28181服务响应正常"
        ) else (
            call :log_warn "GB28181服务未响应健康检查"
        )
    )
) else (
    call :log_warn "GB28181服务未运行"
)
goto :eof

rem 停止服务
:stop_gb28181
call :log_info "停止GB28181服务..."
taskkill /F /IM gbserver.exe >nul 2>&1
call :log_info "GB28181服务已停止"
goto :eof

rem 主函数
:main
call :log_info "开始启动GB28181服务..."

set ACTION=%1
if "%ACTION%"=="" set ACTION=start

if "%ACTION%"=="start" (
    call :check_dependencies
    call :prepare_environment
    call :build_gb28181
    call :start_gb28181
    call :check_status
) else if "%ACTION%"=="status" (
    call :check_status
) else if "%ACTION%"=="stop" (
    call :stop_gb28181
) else if "%ACTION%"=="restart" (
    call :stop_gb28181
    timeout /t 2 >nul
    call :prepare_environment
    call :build_gb28181
    call :start_gb28181
    call :check_status
) else if "%ACTION%"=="build" (
    call :check_dependencies
    call :prepare_environment
    call :build_gb28181
) else (
    echo 用法: %0 {start^|stop^|restart^|status^|build}
    echo   start   - 启动GB28181服务
    echo   stop    - 停止GB28181服务
    echo   restart - 重启GB28181服务
    echo   status  - 检查服务状态
    echo   build   - 仅构建服务
    exit /b 1
)

goto :eof

rem 执行主函数
call :main %* 