#!/bin/bash

# GB28181服务启动脚本
# 用于独立启动GB28181服务

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
GB28181_DIR="$PROJECT_ROOT/third_party/gb28181"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Go是否安装
    if ! command -v go &> /dev/null; then
        log_error "Go未安装，请先安装Go 1.19或更高版本"
        exit 1
    fi
    
    # 检查Go版本
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "当前Go版本: $GO_VERSION"
    
    # 检查ZLMediaKit是否运行（可选）
    if ! pgrep -f "MediaServer" > /dev/null; then
        log_warn "ZLMediaKit流媒体服务未运行，请确保已启动"
        log_warn "可以从 https://github.com/ZLMediaKit/ZLMediaKit 下载并启动"
    fi
}

# 准备环境
prepare_environment() {
    log_info "准备环境..."
    
    # 创建必要的目录
    mkdir -p "$PROJECT_ROOT/data/gb28181"
    mkdir -p "$PROJECT_ROOT/logs/gb28181"
    
    # 复制配置文件
    if [ ! -f "$GB28181_DIR/config/gbserver.yml" ]; then
        log_warn "配置文件不存在，使用默认配置"
        cp "$PROJECT_ROOT/configs/gb28181.yml" "$GB28181_DIR/config/gbserver.yml" || true
    fi
}

# 构建GB28181服务
build_gb28181() {
    log_info "构建GB28181服务..."
    
    cd "$GB28181_DIR"
    
    # 下载依赖
    log_info "下载Go模块依赖..."
    go mod tidy
    
    # 构建服务
    log_info "编译GB28181服务..."
    go build -o bin/gbserver ./cmd/gbserver
    
    if [ $? -eq 0 ]; then
        log_info "GB28181服务构建成功"
    else
        log_error "GB28181服务构建失败"
        exit 1
    fi
}

# 启动GB28181服务
start_gb28181() {
    log_info "启动GB28181服务..."
    
    cd "$GB28181_DIR"
    
    # 检查是否已经在运行
    if pgrep -f "gbserver" > /dev/null; then
        log_warn "GB28181服务已在运行"
        read -p "是否重启服务? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "停止现有服务..."
            pkill -f "gbserver" || true
            sleep 2
        else
            log_info "保持现有服务运行"
            return 0
        fi
    fi
    
    # 启动服务
    log_info "启动GB28181服务进程..."
    nohup ./bin/gbserver -config=config/gbserver.yml > "$PROJECT_ROOT/logs/gb28181/gbserver.log" 2>&1 &
    
    # 等待服务启动
    sleep 3
    
    # 检查服务是否成功启动
    if pgrep -f "gbserver" > /dev/null; then
        log_info "GB28181服务启动成功"
        log_info "日志文件: $PROJECT_ROOT/logs/gb28181/gbserver.log"
        log_info "服务地址: http://localhost:18080"
    else
        log_error "GB28181服务启动失败"
        log_error "请查看日志文件: $PROJECT_ROOT/logs/gb28181/gbserver.log"
        exit 1
    fi
}

# 检查服务状态
check_status() {
    log_info "检查GB28181服务状态..."
    
    if pgrep -f "gbserver" > /dev/null; then
        log_info "GB28181服务正在运行"
        
        # 尝试访问健康检查接口
        if command -v curl &> /dev/null; then
            if curl -s http://localhost:18080/api/health > /dev/null; then
                log_info "GB28181服务响应正常"
            else
                log_warn "GB28181服务未响应健康检查"
            fi
        fi
    else
        log_warn "GB28181服务未运行"
    fi
}

# 主函数
main() {
    log_info "开始启动GB28181服务..."
    
    # 检查参数
    case "${1:-start}" in
        "start")
            check_dependencies
            prepare_environment
            build_gb28181
            start_gb28181
            check_status
            ;;
        "status")
            check_status
            ;;
        "stop")
            log_info "停止GB28181服务..."
            pkill -f "gbserver" || true
            log_info "GB28181服务已停止"
            ;;
        "restart")
            log_info "重启GB28181服务..."
            pkill -f "gbserver" || true
            sleep 2
            prepare_environment
            build_gb28181
            start_gb28181
            check_status
            ;;
        "build")
            check_dependencies
            prepare_environment
            build_gb28181
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status|build}"
            echo "  start   - 启动GB28181服务"
            echo "  stop    - 停止GB28181服务"
            echo "  restart - 重启GB28181服务"
            echo "  status  - 检查服务状态"
            echo "  build   - 仅构建服务"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 