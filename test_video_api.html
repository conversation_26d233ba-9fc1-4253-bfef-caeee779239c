<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #66b1ff;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
        }
        .video-item {
            border: 1px solid #ddd;
            margin: 5px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>视频API测试</h1>
        
        <div>
            <button class="button" onclick="testVideoAPI()">测试视频API</button>
            <button class="button" onclick="clearLog()">清空日志</button>
        </div>

        <div>
            <strong>API响应日志:</strong>
            <div id="log" class="log"></div>
        </div>

        <div>
            <strong>解析后的视频源:</strong>
            <div id="video-sources"></div>
        </div>
    </div>

    <script>
        function log(message, data = null) {
            const logEl = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const logEntry = `[${time}] ${message}`;
            
            if (data) {
                logEl.innerHTML += logEntry + '\n' + JSON.stringify(data, null, 2) + '\n\n';
            } else {
                logEl.innerHTML += logEntry + '\n';
            }
            
            logEl.scrollTop = logEl.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('video-sources').innerHTML = '';
        }

        async function testVideoAPI() {
            try {
                log('开始测试视频API...');
                
                const response = await fetch('/api/videos?page=1&limit=1000');
                const data = await response.json();
                
                log('API响应:', data);
                
                // 模拟前端的数据处理逻辑
                let videoSources = [];
                
                if (data && data.data && data.data.results && Array.isArray(data.data.results)) {
                    // 处理ECP API格式的响应: {data: {results: [...]}}
                    videoSources = data.data.results.map((video) => ({
                        id: video.id,
                        name: video.name,
                        location: video.description || `${video.protocol}://${video.camera_ip}`,
                        protocol: video.protocol,
                        camera_ip: video.camera_ip,
                        camera_id: video.camera_id,
                        status: video.status,
                        brand: video.brand,
                        url: video.url
                    }));
                    
                    log('✅ 成功解析视频源数据');
                } else if (data && data.data && Array.isArray(data.data)) {
                    // 处理直接数组格式的响应: {data: [...]}
                    videoSources = data.data.map((video) => ({
                        id: video.id,
                        name: video.name,
                        location: video.description || `${video.protocol}://${video.camera_ip}`,
                        protocol: video.protocol,
                        camera_ip: video.camera_ip,
                        camera_id: video.camera_id,
                        status: video.status,
                        brand: video.brand,
                        url: video.url
                    }));
                    
                    log('✅ 成功解析视频源数据（直接数组格式）');
                } else {
                    log('❌ 无法解析视频源数据');
                    videoSources = [];
                }
                
                log('解析后的视频源:', videoSources);
                
                // 显示视频源列表
                displayVideoSources(videoSources);
                
            } catch (error) {
                log('❌ API调用失败:', error);
            }
        }

        function displayVideoSources(videoSources) {
            const container = document.getElementById('video-sources');
            
            if (videoSources.length === 0) {
                container.innerHTML = '<p>没有找到视频源数据</p>';
                return;
            }
            
            let html = `<p>找到 ${videoSources.length} 个视频源:</p>`;
            
            videoSources.forEach(video => {
                html += `
                    <div class="video-item">
                        <strong>ID:</strong> ${video.id}<br>
                        <strong>名称:</strong> ${video.name}<br>
                        <strong>位置:</strong> ${video.location}<br>
                        <strong>协议:</strong> ${video.protocol}<br>
                        <strong>IP:</strong> ${video.camera_ip}<br>
                        <strong>状态:</strong> ${video.status}<br>
                        <strong>品牌:</strong> ${video.brand || 'N/A'}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
    </script>
</body>
</html>
