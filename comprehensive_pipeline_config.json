{"id": "comprehensive_pipeline", "name": "综合管道 (2个视频源, 3个算法)", "description": "包含3个绑定关系的综合算法执行管道", "version": "1.0", "author": "ECP System", "globals": {"api_endpoint": "http://localhost:8080/api/alerts", "log_level": "info", "model_dir": "./vp_data/models/", "output_dir": "./vp_data/output/", "resize_ratio": 0.6, "video_dir": "./vp_data/videos/"}, "nodes": [{"id": "alert_handler", "type": "vp_alert_handler_node", "params": {"alert_endpoint": "${globals.api_endpoint}", "danger_level": "warning"}}, {"id": "screen_output", "type": "vp_screen_des_node", "params": {"channel_index": 0}}, {"id": "osd_display", "type": "vp_osd_node", "params": {"font_color": "white", "font_size": 16, "show_fps": true, "show_timestamp": true}}, {"id": "video_src_1", "type": "vp_rtsp_src_node", "params": {"channel_index": 0, "password": "password", "resize_ratio": "${globals.resize_ratio}", "rtsp_url": "rtsp://admin:password@*************:554/stream1", "username": "admin"}}, {"id": "video_src_2", "type": "vp_rtsp_src_node", "params": {"channel_index": 0, "resize_ratio": "${globals.resize_ratio}", "rtsp_url": "rtsp://admin:password@*************:554/stream1"}}, {"id": "detector_1", "type": "vp_yunet_face_detector_node", "params": {"alert_interval": 60, "confidence_threshold": 0.8, "detection_area": "{\"points\": [[100,100], [200,100], [200,200], [100,200]]}", "model_path": "./models/face_detection.onnx"}}, {"id": "detector_2", "type": "vp_yolo_detector_node", "params": {"alert_interval": 30, "confidence_threshold": 0.7, "detection_area": "{\"points\": [[50,50], [300,50], [300,250], [50,250]]}", "model_path": "./models/yolov5s.onnx"}}, {"id": "detector_3", "type": "vp_person_detector_node", "params": {"alert_interval": 45, "confidence_threshold": 0.75, "model_path": "./models/person_detector.onnx"}}], "connections": [{"id": 1, "from": "video_src_1", "to": "detector_1"}, {"id": 2, "from": "video_src_1", "to": "detector_2"}, {"id": 3, "from": "video_src_2", "to": "detector_3"}, {"id": 4, "from": "detector_1", "to": "osd_display"}, {"id": 5, "from": "detector_2", "to": "osd_display"}, {"id": 6, "from": "detector_3", "to": "osd_display"}, {"id": 7, "from": "osd_display", "to": "alert_handler"}, {"id": 8, "from": "osd_display", "to": "screen_output"}]}