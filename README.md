# 边缘计算平台 (ECP)

边缘计算平台是一个用于视频分析和智能识别的系统，可以在边缘设备上部署和运行各种算法，对视频流进行实时分析并生成告警记录。

## 主要功能

- **视频接入管理**：支持多种协议（RTSP、ONVIF、GB28181）的视频源接入，支持多种摄像头品牌
- **算法仓库**：支持算法的导入、删除和管理
- **算法运行状态**：基于Vue Flow的算法执行流程图可视化，实时展示管道节点状态和运行指标
- **算法绑定**：支持视频源与算法的多对多绑定关系，可配置检测区域、告警参数等
- **管道执行**：基于绑定关系自动创建和管理算法执行管道，支持管道的启动、停止、重建等操作
- **告警记录**：查看和管理算法识别的结果，支持原图和告警图管理
- **告警联动**：基于告警自动触发设备控制，支持MQTT、RS485、Modbus协议联动
- **数据持久化**：使用SQLite3数据库存储系统数据

## 核心特性

### 1. 视频接入
- 支持协议：RTSP、ONVIF、GB28181
- 支持摄像头品牌：海康威视、大华、宇视、华为
- 支持码流类型：主码流、子码流
- 自动生成和手动输入视频流地址
- GB28181国标协议支持（设备注册、视频流、云台控制）

### 2. 算法管理
- 算法导入和删除
- 算法激活和停用
- 版本管理

### 3. 绑定关系管理
- 视频源与算法多对多绑定
- 检测区域配置（多边形）
- 告警参数配置（间隔、窗口、阈值）
- 语音播报内容配置
- 危险等级设置
- 算法扩展字段

### 4. 管道执行系统
- **自动管道创建**：基于绑定关系自动生成完整的算法执行管道配置
- **模板化配置**：使用固定模板和动态内容分离的设计，便于维护和扩展
- **节点类型映射**：支持多种视频源节点（RTSP、文件、GB28181）和算法检测节点

#### 4.1 综合管道系统
- **多对多绑定关系处理**：根据所有激活的绑定关系创建一个综合管道
- **智能节点去重**：自动识别和合并相同的视频源和算法节点
- **复杂连接关系**：支持多个视频源连接到多个算法检测器的复杂网络
- **绑定关系矩阵分析**：分析所有绑定关系，构建最优的管道结构
- **API接口**：
  - `POST /api/comprehensive-pipeline/create` - 创建综合管道
  - `GET /api/comprehensive-pipeline/status` - 获取综合管道状态
  - `PUT /api/comprehensive-pipeline/start` - 启动综合管道
  - `PUT /api/comprehensive-pipeline/stop` - 停止综合管道
  - `POST /api/comprehensive-pipeline/rebuild` - 重建综合管道
  - `DELETE /api/comprehensive-pipeline` - 删除综合管道

- **错误处理**：支持重试机制、状态同步、异常恢复等

### 5. 告警管理
- 告警记录查询和管理（支持分页、视频源名称、危险等级筛选）
- 原图和告警图存储和查看
- 告警状态管理（新告警、已处理、已忽略）
- 告警详情查看和图片预览
- 告警记录删除和批量操作

### 6. 告警联动
- **联动规则管理**：灵活的规则配置，支持多条件匹配和优先级设置
- **多协议支持**：支持MQTT、RS485、Modbus协议的设备控制
- **设备管理**：联动设备的添加、配置、状态监控和连接测试
- **执行记录**：联动执行历史记录、统计分析和错误日志
- **实时监控**：设备状态实时监控、执行成功率统计
- **规则引擎**：基于Govaluate的表达式引擎，支持复杂条件判断

### 7. 网络管理
- **网络接口管理**：查看和配置网络接口状态、IP地址、子网掩码等
- **路由管理**：查看、添加、编辑、删除系统路由表，支持静态路由和默认路由
- **防火墙管理**：配置iptables防火墙规则，支持端口、协议、IP地址过滤
- **网络监控**：实时监控网络状态、流量统计、连接质量等
- **网络诊断**：提供ping、traceroute、端口扫描等网络诊断工具

### 8. 系统时间管理
- **系统时间显示**：简洁的左侧蓝色竖线设计，只显示系统时间和时区信息
- **手动校时**：支持手动设置系统时间和时区，日期时间和时区选择器并排显示
- **NTP自动校时**：支持配置多个NTP服务器，自动时间同步，控制选项紧凑排列
- **NTP配置持久化**：支持chrony、ntp、systemd-timesyncd等多种NTP服务的配置文件更新
- **NTP服务器管理**：添加、删除NTP服务器，简化界面去除状态监控表格
- **时区管理**：支持常用时区选择和设置
- **界面设计**：采用左侧蓝色竖线的现代化设计风格，信息展示更加简洁明了
- **配置文件备份**：修改配置前自动备份原配置文件，确保系统安全

### 9. 用户界面
- 响应式设计，支持多种屏幕尺寸
- Element Plus UI组件库
- 统一的菜单图标设计（使用Element Plus图标）
- 深色/浅色主题切换

## 项目结构

```
.
├── api                 # API定义和文档
├── cmd                 # 应用程序入口
│   └── ecp             # 主应用程序
├── configs             # 配置文件
├── docs                # 文档
│   └── binding-api.md  # 绑定关系API文档
├── internal            # 私有应用代码
│   ├── app             # 应用程序核心逻辑
│   │   ├── algorithm   # 算法仓库管理
│   │   ├── alert       # 告警记录管理
│   │   ├── binding     # 绑定关系管理
│   │   ├── linkage     # 告警联动管理
│   │   ├── video       # 视频接入管理
│   │   ├── gb28181     # GB28181协议适配层
│   │   └── server      # 服务器实现
│   └── pkg             # 内部共享库
│       ├── database    # 数据库操作
│       └── utils       # 工具函数
├── pkg                 # 可被外部引用的库
├── third_party         # 第三方组件
│   └── gb28181         # GB28181服务（独立进程）
├── web                 # Web前端
│   ├── app             # 前端应用
│   │   └── src         # 源代码
│   │       ├── api     # API接口定义
│   │       ├── views   # 页面组件
│   │       │   ├── alert  # 告警记录页面
│   │       │   ├── video  # 视频管理页面
│   │       │   ├── algorithm  # 算法管理页面
│   │       │   └── system  # 系统管理页面
│   │       │       └── network  # 网络管理页面
│   │       └── components  # 公共组件
│   ├── dist            # 构建输出
│   └── static          # 静态资源
└── scripts             # 脚本文件
```

## 数据库表结构

### 视频源表 (video)
- 基本信息：名称、协议、摄像头ID、描述
- 连接信息：IP、账号、密码、端口、品牌
- 流信息：取流方式、码流类型、视频流地址

### 算法表 (algorithm)
- 基本信息：名称、描述、版本
- 文件信息：路径、状态

### 告警记录表 (alert)
- 告警信息：来源、IP、等级、类型
- 关联信息：视频源、算法
- 图片信息：原图地址、告警图地址

### 绑定关系表 (video_algorithm_binding)
- 关联信息：视频源ID、算法ID
- 检测参数：检测区域、告警间隔、告警窗口、告警阈值
- 扩展配置：语音内容、危险等级、扩展字段

### 联动规则表 (linkage_rules)
- 规则信息：规则名称、描述、优先级、状态
- 触发条件：视频源、算法、告警等级、时间范围
- 联动动作：设备控制命令、参数配置、延迟设置

### 联动设备表 (linkage_devices)
- 设备信息：设备名称、类型、协议类型
- 连接配置：地址、端口、认证信息、协议参数
- 状态信息：连接状态、最后通信时间

### 联动执行记录表 (linkage_executions)
- 执行信息：告警ID、规则ID、设备ID、执行动作
- 结果信息：执行状态、错误信息、响应时间
- 时间信息：执行时间、完成时间

## API 接口

### 认证 API
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/refresh` - 刷新令牌
- `GET /api/users/me` - 获取当前用户信息
- `PUT /api/users/me` - 更新当前用户信息
- `POST /api/users/me/change-password` - 修改当前用户密码

### 用户管理 API (需要管理员权限)
- `GET /api/admin/users` - 获取用户列表
- `GET /api/admin/users/{id}` - 获取用户详情
- `POST /api/admin/users` - 创建用户
- `PUT /api/admin/users/{id}` - 更新用户
- `DELETE /api/admin/users/{id}` - 删除用户

### 视频接入 API
- `GET /api/videos` - 查询视频源列表
- `POST /api/videos` - 创建视频源
- `PUT /api/videos/{id}` - 更新视频源
- `DELETE /api/videos/{id}` - 删除视频源
- `GET /api/videos/{id}/bindings` - 获取视频源绑定的算法
- `POST /api/videos/{id}/bindings` - 批量设置视频源算法绑定

### 摄像头控制 API
- `GET /api/videos/{id}/snapshot` - 获取摄像头当前快照
- `POST /api/videos/{id}/ptz` - 摄像头PTZ控制
- `POST /api/videos/{id}/preset` - 摄像头预置点控制

### 算法仓库 API
- `GET /api/algorithms` - 查询算法列表
- `POST /api/algorithms` - 导入算法
- `DELETE /api/algorithms/{id}` - 删除算法
- `PUT /api/algorithms/{id}/activate` - 激活算法
- `PUT /api/algorithms/{id}/deactivate` - 停用算法

### 绑定关系 API
- `GET /api/bindings` - 查询绑定关系列表
- `POST /api/bindings` - 创建绑定关系
- `PUT /api/bindings/{id}` - 更新绑定关系
- `DELETE /api/bindings/{id}` - 删除绑定关系

### 告警记录 API
- `GET /api/alerts` - 查询告警记录
- `GET /api/alerts/query` - 条件查询告警记录（支持分页、视频源名称、危险等级查询）
- `PUT /api/alerts/{id}/status` - 更新告警状态
- `POST /api/alerts/{id}/images` - 上传告警图片

### 告警联动 API
- `GET /api/linkage/rules` - 获取联动规则列表
- `POST /api/linkage/rules` - 创建联动规则
- `PUT /api/linkage/rules/{id}` - 更新联动规则
- `DELETE /api/linkage/rules/{id}` - 删除联动规则
- `GET /api/linkage/devices` - 获取联动设备列表
- `POST /api/linkage/devices` - 添加联动设备
- `PUT /api/linkage/devices/{id}` - 更新联动设备
- `DELETE /api/linkage/devices/{id}` - 删除联动设备
- `POST /api/linkage/devices/{id}/test` - 测试设备连接
- `GET /api/linkage/executions` - 获取联动执行记录
- `POST /api/linkage/executions/manual` - 手动执行联动

### 网络管理 API
- `GET /api/network/interfaces` - 获取网络接口列表
- `POST /api/network/interfaces/{name}/config` - 配置网络接口
- `GET /api/network/dns` - 获取DNS配置
- `POST /api/network/dns` - 设置DNS配置
- `GET /api/network/routes` - 获取路由表
- `POST /api/network/routes` - 添加路由
- `PUT /api/network/routes` - 更新路由
- `DELETE /api/network/routes` - 删除路由
- `GET /api/network/status` - 获取网络状态
- `GET /api/network/traffic` - 获取流量统计
- `GET /api/network/firewall/rules` - 获取防火墙规则
- `POST /api/network/firewall/rules` - 添加防火墙规则
- `DELETE /api/network/firewall/rules/{id}` - 删除防火墙规则
- `POST /api/network/diagnostic/ping` - 执行ping测试
- `POST /api/network/diagnostic/traceroute` - 执行路由跟踪
- `POST /api/network/diagnostic/portscan` - 执行端口扫描

### 系统时间管理 API
- `GET /api/system/time` - 获取系统时间信息
- `POST /api/system/time/set` - 设置系统时间
- `GET /api/system/time/status` - 获取完整时间状态
- `GET /api/system/time/ntp` - 获取NTP配置
- `POST /api/system/time/ntp` - 设置NTP配置
- `POST /api/system/time/ntp/sync` - 立即执行NTP同步
- `GET /api/system/time/ntp/servers` - 获取NTP服务器状态
- `GET /api/system/time/timezone` - 获取当前时区
- `POST /api/system/time/timezone` - 设置时区
- `GET /api/system/time/timezones` - 获取可用时区列表

详细API文档请参考：
- [视频源管理API文档](docs/video-api.md)
- [算法仓库管理API文档](docs/algorithm-api.md)
- [告警记录管理API文档](docs/alert-api.md)
- [绑定关系API文档](docs/binding-api.md)
- [告警联动系统设计文档](docs/alarm-linkage-system-design.md)
- [告警联动实现计划](docs/alarm-linkage-implementation-plan.md)
- [告警联动界面设计](docs/alarm-linkage-ui-design.md)

## 快速开始

### 安装

```bash
# 克隆仓库
git clone https://github.com/YOUR-USER-OR-ORG-NAME/ecp.git
cd ecp

# 安装依赖
go mod tidy

# 构建项目
go build -o ecp.exe ./cmd/ecp
```

### 运行

```bash
# 启动服务
./ecp.exe
```

默认情况下，服务将在 `http://localhost:8080` 上运行。

## 使用示例

### 1. 创建视频源并绑定算法

```bash
# 1. 创建视频源
curl -X POST http://localhost:8080/api/videos \
  -H "Content-Type: application/json" \
  -d '{
    "name": "摄像头01",
    "protocol": "rtsp",
    "camera_ip": "*************",
    "username": "admin",
    "password": "123456",
    "brand": "海康威视",
    "stream_mode": "主码流",
    "stream_type": "自动生成"
  }'

# 2. 为视频源绑定算法
curl -X POST http://localhost:8080/api/videos/1/bindings \
  -H "Content-Type: application/json" \
  -d '[{
    "algorithm_id": 1,
    "detection_area": "[{\"x\":100,\"y\":100},{\"x\":500,\"y\":100},{\"x\":500,\"y\":400},{\"x\":100,\"y\":400}]",
    "alert_interval": 60,
    "alert_threshold": 0.8,
    "voice_content": "检测到人员入侵",
    "danger_level": "warning"
  }]'
```

### 2. 管道管理操作

#### 2.1 单一绑定管道操作
```bash
# 手动创建管道（绑定关系激活时会自动创建）
curl -X POST http://localhost:8080/api/bindings/1/pipeline/create \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取管道状态
curl -X GET http://localhost:8080/api/bindings/1/pipeline/status \
  -H "Authorization: Bearer YOUR_TOKEN"

# 启动管道
curl -X PUT http://localhost:8080/api/bindings/1/pipeline/start \
  -H "Authorization: Bearer YOUR_TOKEN"

# 停止管道
curl -X PUT http://localhost:8080/api/bindings/1/pipeline/stop \
  -H "Authorization: Bearer YOUR_TOKEN"

# 重建管道（配置变更后）
curl -X POST http://localhost:8080/api/bindings/1/pipeline/rebuild \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 2.2 综合管道操作
```bash
# 创建综合管道（基于所有激活的绑定关系）
curl -X POST http://localhost:8080/api/comprehensive-pipeline/create \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取综合管道状态
curl -X GET http://localhost:8080/api/comprehensive-pipeline/status \
  -H "Authorization: Bearer YOUR_TOKEN"

# 启动综合管道
curl -X PUT http://localhost:8080/api/comprehensive-pipeline/start \
  -H "Authorization: Bearer YOUR_TOKEN"

# 停止综合管道
curl -X PUT http://localhost:8080/api/comprehensive-pipeline/stop \
  -H "Authorization: Bearer YOUR_TOKEN"

# 重建综合管道（绑定关系变更后）
curl -X POST http://localhost:8080/api/comprehensive-pipeline/rebuild \
  -H "Authorization: Bearer YOUR_TOKEN"

# 删除综合管道
curl -X DELETE http://localhost:8080/api/comprehensive-pipeline \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 查询告警记录

```bash
# 按视频源ID和危险等级查询
curl "http://localhost:8080/api/alerts/query?videoId=1&level=warning&startTime=2024-01-01%2000:00:00&endTime=2024-01-31%2023:59:59"

# 按视频源名称模糊查询（新功能）
curl "http://localhost:8080/api/alerts/query?videoName=摄像头&level=warning&page=1&limit=20"

# 分页查询所有告警
curl "http://localhost:8080/api/alerts/query?page=1&limit=50"
```

## 配置

配置文件位于 `configs` 目录中，可以根据需要修改。

### 管道配置

管道功能的配置位于 `configs/config.yaml` 中的 `pipeline` 部分：

```yaml
pipeline:
  enabled: true                                    # 是否启用管道功能
  api_endpoint: "http://localhost:8080/api/v1"    # VideoPipe API端点
  api_key: "${PIPELINE_API_KEY}"                  # API密钥（支持环境变量）
  timeout: 30s                                    # API调用超时时间
  retry_count: 3                                  # 失败重试次数
  template_path: "./configs/pipeline-template.yaml" # 固定模板路径

  # 节点类型映射配置
  node_mapping:
    video_source:
      rtsp: "vp_rtsp_src_node"
      file: "vp_file_src_node"
      gb28181: "vp_gb28181_src_node"
      onvif: "vp_onvif_src_node"

    algorithms:
      face_detection: "vp_yunet_face_detector_node"
      object_detection: "vp_yolo_detector_node"
      person_detection: "vp_person_detector_node"
      vehicle_detection: "vp_vehicle_detector_node"
      helmet_detection: "vp_helmet_detector_node"
      safety_detection: "vp_safety_detector_node"
```

### 管道模板配置

固定模板配置位于 `configs/pipeline-template.yaml`：

```yaml
version: "1.0"
author: "ECP System"

globals:
  model_dir: "./vp_data/models/"
  video_dir: "./vp_data/videos/"
  output_dir: "./vp_data/output/"
  resize_ratio: 0.6
  log_level: "info"
  api_endpoint: "http://localhost:8080/api/alerts"

fixed_nodes:
  - id: "alert_handler"
    type: "vp_alert_handler_node"
    params:
      alert_endpoint: "${globals.api_endpoint}"

  - id: "screen_output"
    type: "vp_screen_des_node"
    params:
      channel_index: 0

  - id: "osd_display"
    type: "vp_osd_node"
    params:
      show_fps: true
      show_timestamp: true
```

## GB28181国标协议支持

项目集成了GB28181-2016标准的视频监控协议，支持国标设备的接入和管理。

### GB28181功能特性

- **设备注册管理**：自动接收和管理GB28181设备注册
- **实时视频流**：支持GB28181设备的实时视频点播
- **云台控制**：支持PTZ云台控制（上下左右、缩放等）
- **设备状态监控**：实时监控设备在线状态和心跳
- **设备目录查询**：获取设备通道信息
- **自动同步**：设备信息自动同步到视频源表

### 启动GB28181服务

#### Windows用户：
```bash
# 启动GB28181服务
scripts\start_gb28181.bat start

# 检查服务状态
scripts\start_gb28181.bat status

# 停止服务
scripts\start_gb28181.bat stop
```

#### Linux/Mac用户：
```bash
# 启动GB28181服务
./scripts/start_gb28181.sh start

# 检查服务状态
./scripts/start_gb28181.sh status

# 停止服务
./scripts/start_gb28181.sh stop
```

### GB28181配置

修改 `configs/gb28181.yml` 配置文件：

```yaml
gb28181:
  server_url: "http://localhost:18080"  # GB28181服务地址
  enabled: true                         # 是否启用GB28181功能
  
gb28181_service:
  sip:
    ip: "*************"                 # 本机IP地址（重要：需要修改为实际IP）
    port: 5060                          # SIP监听端口
    domain: "4401020049"                # 域名编码
    id: "44010200492000000001"          # 服务器ID
    password: "admin123"                # 设备认证密码
```

### 使用示例

```go
// 创建带GB28181支持的视频服务
gb28181Adapter := gb28181.NewAdapter("http://localhost:18080", db)
videoService := video.NewVideoServiceWithGB28181(db.DB, gb28181Adapter)

// 同步GB28181设备到视频源表
err := videoService.SyncGB28181Devices()

// 获取GB28181设备列表
devices, err := videoService.GetGB28181Devices()

// 启动GB28181视频流
streamInfo, err := videoService.StartGB28181Stream(video)

// 云台控制
err := videoService.PTZControl(video, "up", 50, 50, 0)
```

### 设备接入流程

1. **启动GB28181服务**：使用提供的脚本启动独立的GB28181服务
2. **配置网络**：确保GB28181设备能访问服务器的SIP端口（默认5060）
3. **设备配置**：在GB28181设备中配置服务器IP、端口、域名、密码等
4. **自动注册**：设备会自动注册到GB28181服务器
5. **同步设备**：调用同步接口将设备信息同步到ECP主系统
6. **视频流控制**：通过ECP主系统管理设备和视频流

## 开发

### 依赖

- Go 1.23+
- SQLite3
- github.com/mlogclub/simple
- ZLMediaKit流媒体服务器（GB28181功能需要）

### 构建

```bash
go build -o ecp.exe ./cmd/ecp
```

### 测试

```bash
go test ./...
```

## 许可证

[LICENSE](LICENSE.md)
