# note: call scripts from /scripts

# 边缘计算平台Makefile

# 变量定义
BINARY_NAME=ecp
MAIN_PATH=cmd/ecp/main.go
BUILD_DIR=bin
GO=go

# 默认目标
.PHONY: all
all: build

# 构建
.PHONY: build
build:
	@echo "构建边缘计算平台..."
	@mkdir -p $(BUILD_DIR)
	$(GO) build -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 运行
.PHONY: run
run:
	@echo "运行边缘计算平台..."
	$(GO) run $(MAIN_PATH)

# 测试
.PHONY: test
test:
	@echo "运行测试..."
	$(GO) test ./...

# 清理
.PHONY: clean
clean:
	@echo "清理构建文件..."
	@rm -rf $(BUILD_DIR)
	@echo "清理完成"

# 依赖
.PHONY: deps
deps:
	@echo "安装依赖..."
	$(GO) mod tidy
	@echo "依赖安装完成"

# 生成Swagger文档
.PHONY: swagger
swagger:
	@echo "生成Swagger文档..."
	@echo "Swagger文档生成完成"
