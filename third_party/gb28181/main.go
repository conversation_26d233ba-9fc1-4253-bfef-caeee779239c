package main

import (
	_ "github.com/chenjianhao66/go-GB28181/api/swagger"
)

// @title           Go-GB28181项目前端APi接口
// @version         1.0
// @description     Go-GB28181是一个基于GB28181-2016标准实现的网络视频平台，用 Go 语言实现，实现了 SIP 协议和信令服务器。
// @termsOfService  http://swagger.io/terms/

// @contact.name   github homepage
// @contact.url    https://github.com/chenjianhao66/go-GB28181
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:18080
// @BasePath  /

// @securityDefinitions.basic  BasicAuth

// @externalDocs.description  OpenAPI
// @externalDocs.url          https://swagger.io/resources/open-api/
func main() {
	println(banner)
	//ListenAndServeWithSignal()
}

var banner = `
  _____              _____ ____ ___   ___  __  ___  __ 
 / ____|            / ____|  _ \__ \ / _ \/_ |/ _ \/_ |
| |  __  ___ ______| |  __| |_) | ) | (_) || | (_) || |
| | |_ |/ _ \______| | |_ |  _ < / / > _ < | |> _ < | |
| |__| | (_) |     | |__| | |_) / /_| (_) || | (_) || |
 \_____|\___/       \_____|____/____|\___/ |_|\___/ |_|
`
