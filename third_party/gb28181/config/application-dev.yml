#[可选] WVP监听的HTTP端口, 网页和接口调用都是这个端口
server:
    port: 18080

mysql:
    host: 127.0.0.1:3306
    port: 3306
    username: root
    password: 123456
    database: gb
    max-idle-connections: 100
    max-open-connections: 100
    log-level: 1

redis:
    host: 127.0.0.1
    port: 6379
    database: 0
    username: root

# 作为 28181 服务器的配置
sip:
    # [必须修改] 本机的IP
    ip: *************

    # [可选] 28181服务监听的端口
    port: 5060

    # 根据国标6.1.2中规定，domain宜采用ID统一编码的前十位编码。国标附录D中定义前8位为中心编码（由省级、市级、区级、基层编号组成，参照GB/T 2260-2007）
    # 后两位为行业编码，定义参照附录D.3
    # 3701020049标识山东济南历下区 信息行业接入
    # [可选]
    domain: 4401020049

    # [可选]
    id: 44010200492000000001

    # [可选] 默认设备认证密码，后续扩展使用设备单独密码, 移除密码将不进行校验
    password: admin123

# [可选] 日志配置, 一般不需要改
logging:
    config: classpath:logback-spring-local.xml
