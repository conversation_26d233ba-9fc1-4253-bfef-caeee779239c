# 编译输出
/bin/
/dist/

# 依赖目录
/vendor/

# 数据目录
/data/

# 日志文件
/logs/
*.log

# 配置文件（除了示例和模板）
/configs/*.local.yaml
/configs/*.local.json

# IDE和编辑器文件
.idea/
.vscode/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db

# 测试覆盖率报告
coverage.txt
profile.out

# 二进制文件
*.exe
*.dll
*.so
*.dylib

# 调试文件
__debug_bin

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Project-local glide cache, RE: https://github.com/Masterminds/glide/issues/736
.glide/

node_modules/

dist