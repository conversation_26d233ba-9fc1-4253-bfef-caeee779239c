package main

import (
	"fmt"
	"log"

	"ecp/internal/app/pipeline"
)

func main() {
	fmt.Println("=== 测试本地客户端节点生成 ===")

	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建本地客户端
	fmt.Println("📦 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 创建测试管道配置
	testConfig := &pipeline.CompletePipelineConfig{
		ID:          "test_nodes_pipeline",
		Name:        "测试节点生成管道",
		Description: "用于测试节点生成的管道",
		Version:     "1.0",
		Author:      "ECP System",
		Globals: map[string]interface{}{
			"model_dir": "./models/",
		},
		Nodes: []*pipeline.PipelineNode{
			{
				ID:   "rtsp_src_0",
				Type: "vp_rtsp_src_node",
				Params: map[string]interface{}{
					"rtsp_url": "rtsp://test",
				},
			},
		},
		Connections: []*pipeline.PipelineConnection{},
	}

	// 测试1: 创建管道
	fmt.Println("🏗️ 创建管道...")
	if err := client.CreateCompletePipeline(testConfig); err != nil {
		log.Fatalf("创建管道失败: %v", err)
	}
	fmt.Println("✅ 管道创建成功")

	// 测试2: 立即检查状态
	fmt.Println("📊 检查创建后的状态...")
	status, err := client.GetPipelineStatus(testConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s\n", status.State)
	fmt.Printf("📊 节点数据长度: %d\n", len(status.Nodes))
	fmt.Printf("📊 节点数据内容: %s\n", status.Nodes)

	// 测试3: 启动管道
	fmt.Println("🚀 启动管道...")
	if err := client.StartPipeline(testConfig.ID); err != nil {
		log.Fatalf("启动管道失败: %v", err)
	}
	fmt.Println("✅ 管道启动成功")

	// 测试4: 检查启动后的状态
	fmt.Println("📊 检查启动后的状态...")
	status, err = client.GetPipelineStatus(testConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s\n", status.State)
	fmt.Printf("📊 节点数据长度: %d\n", len(status.Nodes))
	fmt.Printf("📊 节点数据内容: %s\n", status.Nodes)

	// 清理
	fmt.Println("🗑️ 删除测试管道...")
	if err := client.DeletePipeline(testConfig.ID); err != nil {
		log.Fatalf("删除管道失败: %v", err)
	}
	fmt.Println("✅ 管道删除成功")

	fmt.Println("\n🎉 测试完成！")
}
