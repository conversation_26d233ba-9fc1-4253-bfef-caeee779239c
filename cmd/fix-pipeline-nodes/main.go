package main

import (
	"encoding/json"
	"fmt"
	"log"

	"ecp/internal/app/pipeline"
)

func main() {
	fmt.Println("=== 修复管道节点显示问题 ===")

	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建本地客户端
	fmt.Println("📦 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 使用与配置文件一致的真实管道配置
	realConfig := &pipeline.CompletePipelineConfig{
		ID:          "comprehensive_pipeline",
		Name:        "综合管道 (1个视频源, 1个算法)",
		Description: "包含1个绑定关系的综合算法执行管道",
		Version:     "1.0",
		Author:      "ECP System",
		Globals: map[string]interface{}{
			"api_endpoint": "http://localhost:8080/api/alerts",
			"log_level":    "info",
			"model_dir":    "./vp_data/models/",
			"output_dir":   "./vp_data/output/",
			"resize_ratio": 0.6,
			"video_dir":    "./vp_data/videos/",
		},
		Nodes: []*pipeline.PipelineNode{
			{
				ID:   "alert_handler",
				Type: "vp_alert_handler_node",
				Params: map[string]interface{}{
					"alert_endpoint": "${globals.api_endpoint}",
					"danger_level":   "warning",
				},
			},
			{
				ID:   "screen_output",
				Type: "vp_screen_des_node",
				Params: map[string]interface{}{
					"channel_index": 0,
				},
			},
			{
				ID:   "osd_display",
				Type: "vp_osd_node",
				Params: map[string]interface{}{
					"font_color":     "white",
					"font_size":      16,
					"show_fps":       true,
					"show_timestamp": true,
				},
			},
			{
				ID:   "video_src_1",
				Type: "vp_rtsp_src_node",
				Params: map[string]interface{}{
					"brand":        "海康威视",
					"channel_index": 0,
					"password":     "fnic1234",
					"resize_ratio": "${globals.resize_ratio}",
					"rtsp_url":     "rtsp://admin:fnic1234@*************:554/h264/ch1/2/av_stream",
					"stream_mode":  "子码流",
					"username":     "admin",
				},
			},
			{
				ID:   "detector_2",
				Type: "vp_generic_detector_node",
				Params: map[string]interface{}{
					"alert_interval":       10,
					"alert_window":         60,
					"algorithm_version":    "v1.0.1",
					"confidence_threshold": 0.5,
					"danger_level":         "medium",
					"detection_area":       "[]",
					"model_path":           "data\\algorithms\\algorithm_1753155785987351800",
					"voice_content":        "检测到异常情况",
				},
			},
		},
		Connections: []*pipeline.PipelineConnection{
			{
				ID:   1,
				From: "video_src_1",
				To:   "detector_2",
			},
			{
				ID:   2,
				From: "detector_2",
				To:   "osd_display",
			},
			{
				ID:   3,
				From: "osd_display",
				To:   "alert_handler",
			},
			{
				ID:   4,
				From: "osd_display",
				To:   "screen_output",
			},
		},
	}

	// 步骤1: 创建管道（这会使用新的节点生成逻辑）
	fmt.Println("🏗️ 重新创建综合管道...")
	if err := client.CreateCompletePipeline(realConfig); err != nil {
		log.Fatalf("创建管道失败: %v", err)
	}
	fmt.Println("✅ 管道创建成功")

	// 步骤2: 检查创建后的状态
	fmt.Println("📊 检查创建后的状态...")
	status, err := client.GetPipelineStatus(realConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s\n", status.State)
	fmt.Printf("📊 节点数据长度: %d\n", len(status.Nodes))

	// 解析并显示节点信息
	if status.Nodes != "" && status.Nodes != "[]" {
		var nodes []interface{}
		if err := json.Unmarshal([]byte(status.Nodes), &nodes); err != nil {
			fmt.Printf("❌ 解析节点数据失败: %v\n", err)
		} else {
			fmt.Printf("✅ 解析后的节点数量: %d\n", len(nodes))
			fmt.Println("📋 创建后的节点列表:")
			for i, node := range nodes {
				if nodeMap, ok := node.(map[string]interface{}); ok {
					fmt.Printf("  %d. %s (%s) - %s\n",
						i+1, nodeMap["id"], nodeMap["type"], nodeMap["state"])
				}
			}
		}
	}

	// 步骤3: 启动管道
	fmt.Println("\n🚀 启动管道...")
	if err := client.StartPipeline(realConfig.ID); err != nil {
		log.Fatalf("启动管道失败: %v", err)
	}
	fmt.Println("✅ 管道启动成功")

	// 步骤4: 检查启动后的状态
	fmt.Println("📊 检查启动后的状态...")
	status, err = client.GetPipelineStatus(realConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s\n", status.State)

	// 解析并显示启动后的节点信息
	if status.Nodes != "" && status.Nodes != "[]" {
		var nodes []interface{}
		if err := json.Unmarshal([]byte(status.Nodes), &nodes); err != nil {
			fmt.Printf("❌ 解析节点数据失败: %v\n", err)
		} else {
			fmt.Printf("✅ 启动后节点数量: %d\n", len(nodes))
			fmt.Println("📋 运行中的节点列表:")
			for i, node := range nodes {
				if nodeMap, ok := node.(map[string]interface{}); ok {
					fmt.Printf("  %d. %s (%s) - %s\n",
						i+1, nodeMap["id"], nodeMap["type"], nodeMap["state"])
				}
			}
		}
	}

	// 步骤5: 显示API响应格式
	fmt.Println("\n🌐 API响应格式:")
	apiResponse := map[string]interface{}{
		"errorCode": 0,
		"message":   "",
		"data":      status,
		"success":   true,
	}

	// 只显示关键信息
	fmt.Printf("📡 状态: %s\n", status.State)
	fmt.Printf("📡 节点数量: %d\n", len(status.Nodes))
	fmt.Printf("📡 成功标志: %v\n", apiResponse["success"])

	fmt.Println("\n🎉 管道节点修复完成！")
	fmt.Println("💡 现在前端应该显示正确的5个节点了")
	fmt.Println("🔄 请刷新前端页面查看效果")
}
