package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"ecp/internal/app/server"
	"ecp/internal/pkg/config"
)

func main() {
	fmt.Println("启动边缘计算平台...")

	// 解析命令行参数
	configPath := flag.String("config", "./configs/config.yaml", "配置文件路径")
	flag.Parse()

	// 加载配置文件
	_, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}
	fmt.Println("配置文件加载成功")

	// 初始化并启动服务器
	srv, err := server.NewServer()
	if err != nil {
		log.Fatalf("初始化服务器失败: %v", err)
	}

	// 在后台启动服务器
	go func() {
		if err := srv.Start(); err != nil {
			log.Fatalf("启动服务器失败: %v", err)
		}
	}()

	fmt.Println("边缘计算平台已启动")

	// 等待中断信号优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("正在关闭边缘计算平台...")
	if err := srv.Stop(); err != nil {
		log.Fatalf("关闭服务器失败: %v", err)
	}
	fmt.Println("边缘计算平台已关闭")
}
