package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"time"
)

func main() {
	fmt.Println("=== 测试新的配置API接口 ===")

	baseURL := "http://localhost:8080"

	// 等待服务器启动
	fmt.Println("⏳ 等待服务器启动...")
	time.Sleep(3 * time.Second)

	// 步骤1: 测试获取配置API（可能失败，因为管道可能不存在）
	fmt.Println("📊 测试获取综合管道配置API...")
	configResp, err := callAPI("GET", baseURL+"/api/comprehensive-pipeline/config", nil)
	if err != nil {
		fmt.Printf("⚠️ 获取配置失败（预期的，如果管道不存在）: %v\n", err)
	} else {
		fmt.Printf("✅ 配置API响应: %s\n", configResp)
	}

	// 步骤2: 创建综合管道
	fmt.Println("\n🚀 创建综合管道...")
	createResp, err := callAPI("POST", baseURL+"/api/comprehensive-pipeline/create", nil)
	if err != nil {
		log.Fatalf("创建综合管道失败: %v", err)
	}
	fmt.Printf("✅ 创建API响应: %s\n", createResp)

	// 等待一下
	time.Sleep(2 * time.Second)

	// 步骤3: 再次测试获取配置API
	fmt.Println("\n📊 再次测试获取综合管道配置API...")
	configResp, err = callAPI("GET", baseURL+"/api/comprehensive-pipeline/config", nil)
	if err != nil {
		fmt.Printf("❌ 获取配置失败: %v\n", err)
	} else {
		fmt.Printf("✅ 配置API响应: %s\n", configResp)

		// 解析配置响应
		var configResponse map[string]interface{}
		if err := json.Unmarshal([]byte(configResp), &configResponse); err == nil {
			if data, ok := configResponse["data"].(map[string]interface{}); ok {
				fmt.Println("\n📊 配置详情:")
				fmt.Printf("  管道ID: %v\n", data["id"])
				fmt.Printf("  管道名称: %v\n", data["name"])
				fmt.Printf("  管道描述: %v\n", data["description"])

				if nodes, ok := data["nodes"].([]interface{}); ok {
					fmt.Printf("  节点数量: %d\n", len(nodes))
					fmt.Println("  节点列表:")
					for i, node := range nodes {
						if nodeMap, ok := node.(map[string]interface{}); ok {
							fmt.Printf("    %d. %s (%s)\n", i+1, nodeMap["id"], nodeMap["type"])
						}
					}
				}

				if connections, ok := data["connections"].([]interface{}); ok {
					fmt.Printf("  连接数量: %d\n", len(connections))
					fmt.Println("  连接列表:")
					for i, conn := range connections {
						if connMap, ok := conn.(map[string]interface{}); ok {
							fmt.Printf("    %d. %s → %s (ID: %.0f)\n",
								i+1, connMap["from"], connMap["to"], connMap["id"])
						}
					}
				}
			}
		}
	}

	// 步骤4: 对比状态API
	fmt.Println("\n📊 对比状态API...")
	statusResp, err := callAPI("GET", baseURL+"/api/comprehensive-pipeline/status", nil)
	if err != nil {
		fmt.Printf("❌ 获取状态失败: %v\n", err)
	} else {
		fmt.Printf("✅ 状态API响应: %s\n", statusResp)

		// 解析状态响应
		var statusResponse map[string]interface{}
		if err := json.Unmarshal([]byte(statusResp), &statusResponse); err == nil {
			if data, ok := statusResponse["data"].(map[string]interface{}); ok {
				fmt.Println("\n📊 状态详情:")
				fmt.Printf("  管道状态: %v\n", data["state"])
				fmt.Printf("  管道ID: %v\n", data["pipeline_id"])

				if nodes, ok := data["nodes"].(string); ok && nodes != "" && nodes != "[]" {
					var nodeList []interface{}
					if err := json.Unmarshal([]byte(nodes), &nodeList); err == nil {
						fmt.Printf("  状态中的节点数量: %d\n", len(nodeList))
					}
				}

				if globalMetrics, ok := data["global_metrics"].(map[string]interface{}); ok {
					if connections, ok := globalMetrics["connections"].(string); ok && connections != "" {
						var connList []interface{}
						if err := json.Unmarshal([]byte(connections), &connList); err == nil {
							fmt.Printf("  状态中的连接数量: %d\n", len(connList))
						}
					}
				}
			}
		}
	}

	fmt.Println("\n🎉 API测试完成！")
	fmt.Println("💡 现在前端应该优先使用配置API来显示流程图")
	fmt.Println("🔄 请刷新前端页面查看效果")
}

// callAPI 调用API接口
func callAPI(method, url string, body interface{}) (string, error) {
	var reqBody []byte
	var err error

	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return "", fmt.Errorf("序列化请求体失败: %v", err)
		}
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API调用失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	return string(respBody), nil
}
