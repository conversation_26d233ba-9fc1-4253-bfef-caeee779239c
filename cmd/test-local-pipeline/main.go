package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"ecp/internal/app/pipeline"
)

func main() {
	fmt.Println("=== 测试本地管道客户端 ===")

	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
				"file": "vp_file_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建本地客户端
	fmt.Println("📦 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 创建测试管道配置
	testConfig := &pipeline.CompletePipelineConfig{
		ID:          "test_local_pipeline",
		Name:        "测试本地管道",
		Description: "用于测试本地部署模式的管道",
		Version:     "1.0",
		Author:      "ECP System",
		Globals: map[string]interface{}{
			"model_dir":    "./models/",
			"resize_ratio": 0.6,
		},
		Nodes: []*pipeline.PipelineNode{
			{
				ID:   "rtsp_src_0",
				Type: "vp_rtsp_src_node",
				Params: map[string]interface{}{
					"rtsp_url":     "rtsp://admin:password@*************:554/stream1",
					"channel_id":   0,
					"resize_ratio": 0.6,
				},
			},
			{
				ID:   "face_detector_0",
				Type: "vp_yunet_face_detector_node",
				Params: map[string]interface{}{
					"model_path":           "./models/face_detection_yunet_2022mar.onnx",
					"confidence_threshold": 0.7,
					"nms_threshold":        0.3,
				},
			},
			{
				ID:   "osd_0",
				Type: "vp_face_osd_node_v2",
				Params: map[string]interface{}{
					"show_fps":       true,
					"show_timestamp": true,
					"font_scale":     0.5,
				},
			},
		},
		Connections: []*pipeline.PipelineConnection{
			{
				ID:   1,
				From: "rtsp_src_0",
				To:   "face_detector_0",
			},
			{
				ID:   2,
				From: "face_detector_0",
				To:   "osd_0",
			},
		},
	}

	// 测试1: 验证配置
	fmt.Println("🔍 验证管道配置...")
	if err := client.ValidatePipelineConfig(testConfig); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}
	fmt.Println("✅ 配置验证通过")

	// 测试2: 创建管道
	fmt.Println("🏗️ 创建管道...")
	if err := client.CreateCompletePipeline(testConfig); err != nil {
		log.Fatalf("创建管道失败: %v", err)
	}
	fmt.Println("✅ 管道创建成功")

	// 测试3: 获取初始状态
	fmt.Println("📊 获取管道状态...")
	status, err := client.GetPipelineStatus(testConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s\n", status.State)

	// 测试4: 启动管道
	fmt.Println("🚀 启动管道...")
	if err := client.StartPipeline(testConfig.ID); err != nil {
		log.Fatalf("启动管道失败: %v", err)
	}
	fmt.Println("✅ 管道启动成功")

	// 测试5: 获取运行状态
	fmt.Println("📈 获取运行状态...")
	time.Sleep(1 * time.Second) // 等待状态更新
	status, err = client.GetPipelineStatus(testConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s, 运行时间: %d秒\n", status.State, status.UptimeSeconds)

	// 显示节点状态
	if status.Nodes != "" && status.Nodes != "[]" {
		fmt.Println("🔧 节点状态:")
		var nodes []map[string]interface{}
		if err := json.Unmarshal([]byte(status.Nodes), &nodes); err == nil {
			for _, node := range nodes {
				fmt.Printf("   - %s (%s): %s\n",
					node["id"], node["type"], node["state"])
			}
		}
	}

	// 测试6: 停止管道
	fmt.Println("⏹️ 停止管道...")
	if err := client.StopPipeline(testConfig.ID); err != nil {
		log.Fatalf("停止管道失败: %v", err)
	}
	fmt.Println("✅ 管道停止成功")

	// 测试7: 获取停止状态
	fmt.Println("📊 获取停止状态...")
	status, err = client.GetPipelineStatus(testConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s\n", status.State)

	// 测试8: 删除管道（注释掉以查看生成的文件）
	fmt.Println("🗑️ 跳过删除管道（保留文件以供查看）...")
	// if err := client.DeletePipeline(testConfig.ID); err != nil {
	// 	log.Fatalf("删除管道失败: %v", err)
	// }
	// fmt.Println("✅ 管道删除成功")

	fmt.Println("\n🎉 所有测试通过！本地管道客户端工作正常。")
	fmt.Println("\n📁 文件存储位置:")
	fmt.Println("   - 配置文件: ./data/pipelines/")
	fmt.Println("   - 状态文件: ./data/pipeline-status/")
	fmt.Println("   - 工作目录: ./data/videopipe/")
}
