package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

func main() {
	fmt.Println("=== 简单API测试 ===")

	baseURL := "http://localhost:8080"

	// 等待服务器启动
	fmt.Println("⏳ 等待服务器启动...")
	time.Sleep(2 * time.Second)

	// 测试状态接口
	fmt.Println("📊 测试状态接口...")
	statusResp, err := callAPI("GET", baseURL+"/api/comprehensive-pipeline/status")
	if err != nil {
		fmt.Printf("❌ 状态接口失败: %v\n", err)
	} else {
		fmt.Printf("✅ 状态接口响应: %s\n", statusResp)
	}

	// 测试配置接口
	fmt.Println("\n📊 测试配置接口...")
	configResp, err := callAPI("GET", baseURL+"/api/comprehensive-pipeline/config")
	if err != nil {
		fmt.Printf("❌ 配置接口失败: %v\n", err)
	} else {
		fmt.Printf("✅ 配置接口响应: %s\n", configResp)
	}

	fmt.Println("\n🎉 API测试完成！")
}

// callAPI 调用API接口
func callAPI(method, url string) (string, error) {
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API调用失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	// 格式化JSON响应
	var jsonData interface{}
	if err := json.Unmarshal(respBody, &jsonData); err == nil {
		formatted, _ := json.MarshalIndent(jsonData, "", "  ")
		return string(formatted), nil
	}

	return string(respBody), nil
}
