package main

import (
	"fmt"
	"log"

	"ecp/internal/app/pipeline"
)

func main() {
	fmt.Println("=== 语法修复后测试 ===")

	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建本地客户端
	fmt.Println("📦 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 获取管道状态
	fmt.Println("📊 获取综合管道状态...")
	status, err := client.GetPipelineStatus("comprehensive_pipeline")
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}

	fmt.Printf("✅ 管道状态: %s\n", status.State)
	fmt.Printf("📊 管道ID: %s\n", status.PipelineID)

	// 检查连接信息
	if connectionsData, ok := status.GlobalMetrics["connections"].(string); ok {
		fmt.Printf("✅ 连接数据存在，长度: %d\n", len(connectionsData))
		fmt.Printf("📊 连接数据预览: %.100s...\n", connectionsData)
	} else {
		fmt.Println("❌ 连接数据不存在")
	}

	fmt.Println("\n🎉 后端数据验证完成！")
	fmt.Println("💡 现在前端应该能正确编译并显示连接线")
	fmt.Println("🔄 请刷新前端页面查看效果")
}
