package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"time"

	"ecp/internal/app/pipeline"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法:")
		fmt.Println("  go run cmd/test-comprehensive-pipeline/main.go [command]")
		fmt.Println("")
		fmt.Println("可用命令:")
		fmt.Println("  build    - 测试构建综合管道配置")
		fmt.Println("  api      - 测试API接口调用")
		fmt.Println("  mock     - 模拟完整的管道创建流程")
		fmt.Println("  local    - 测试本地部署模式")
		return
	}

	command := os.Args[1]
	switch command {
	case "build":
		testBuildConfig()
	case "api":
		testAPICall()
	case "mock":
		testMockPipeline()
	case "local":
		testLocalMode()
	default:
		fmt.Printf("未知命令: %s\n", command)
	}
}

// testBuildConfig 测试构建综合管道配置
func testBuildConfig() {
	fmt.Println("=== 测试构建综合管道配置 ===")

	// 创建配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "remote", // 使用远程模式进行测试
		TemplatePath:   "./configs/pipeline-template.yaml",
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp":    "vp_rtsp_src_node",
				"file":    "vp_file_src_node",
				"gb28181": "vp_gb28181_src_node",
			},
			Algorithms: map[string]string{
				"face_detection":   "vp_yunet_face_detector_node",
				"object_detection": "vp_yolo_detector_node",
				"person_detection": "vp_person_detector_node",
			},
		},
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		Remote: pipeline.RemoteConfig{
			APIEndpoint: "http://localhost:8080/api/v1",
			APIKey:      "test-api-key",
			Timeout:     30 * time.Second,
			RetryCount:  3,
		},
	}

	// 创建组件
	templateManager := pipeline.NewTemplateManager(config.TemplatePath)
	dataMapper := pipeline.NewDataMapper(config.NodeMapping)
	builder := pipeline.NewPipelineBuilder(templateManager, dataMapper)

	// 创建测试绑定关系
	bindings := createTestBindings()

	// 构建配置
	pipelineConfig, err := builder.BuildComprehensiveConfig(bindings)
	if err != nil {
		log.Fatalf("构建综合管道配置失败: %v", err)
	}

	// 输出结果
	configJSON, err := json.MarshalIndent(pipelineConfig, "", "  ")
	if err != nil {
		log.Fatalf("序列化配置失败: %v", err)
	}

	fmt.Printf("✅ 成功构建综合管道配置\n")
	fmt.Printf("📊 统计信息:\n")
	fmt.Printf("   - 管道ID: %s\n", pipelineConfig.ID)
	fmt.Printf("   - 总节点数: %d\n", len(pipelineConfig.Nodes))
	fmt.Printf("   - 连接关系数: %d\n", len(pipelineConfig.Connections))
	fmt.Printf("   - 全局变量数: %d\n", len(pipelineConfig.Globals))

	// 保存配置到文件
	err = ioutil.WriteFile("comprehensive_pipeline_config.json", configJSON, 0644)
	if err != nil {
		log.Printf("保存配置文件失败: %v", err)
	} else {
		fmt.Printf("📁 配置已保存到: comprehensive_pipeline_config.json\n")
	}

	fmt.Println("\n✅ 测试完成")
}

// testAPICall 测试API接口调用
func testAPICall() {
	fmt.Println("=== 测试API接口调用 ===")

	baseURL := "http://localhost:8080"
	token := "your-auth-token" // 需要替换为实际的认证令牌

	// 测试创建综合管道
	fmt.Println("🚀 测试创建综合管道...")
	err := callAPI("POST", baseURL+"/api/comprehensive-pipeline/create", token, nil)
	if err != nil {
		fmt.Printf("❌ 创建综合管道失败: %v\n", err)
	} else {
		fmt.Println("✅ 创建综合管道成功")
	}

	// 等待一下
	time.Sleep(2 * time.Second)

	// 测试获取状态
	fmt.Println("📊 测试获取管道状态...")
	err = callAPI("GET", baseURL+"/api/comprehensive-pipeline/status", token, nil)
	if err != nil {
		fmt.Printf("❌ 获取管道状态失败: %v\n", err)
	} else {
		fmt.Println("✅ 获取管道状态成功")
	}

	// 测试启动管道
	fmt.Println("▶️ 测试启动管道...")
	err = callAPI("PUT", baseURL+"/api/comprehensive-pipeline/start", token, nil)
	if err != nil {
		fmt.Printf("❌ 启动管道失败: %v\n", err)
	} else {
		fmt.Println("✅ 启动管道成功")
	}

	fmt.Println("\n✅ API测试完成")
}

// testMockPipeline 模拟完整的管道创建流程
func testMockPipeline() {
	fmt.Println("=== 模拟完整的管道创建流程 ===")

	// 1. 构建配置
	fmt.Println("1️⃣ 构建管道配置...")
	testBuildConfig()

	// 2. 模拟API调用
	fmt.Println("\n2️⃣ 模拟API调用...")
	fmt.Println("   📤 发送配置到VideoPipe执行引擎...")
	fmt.Println("   ⏳ 等待管道创建...")
	time.Sleep(1 * time.Second)
	fmt.Println("   ✅ 管道创建成功")

	// 3. 模拟状态检查
	fmt.Println("\n3️⃣ 检查管道状态...")
	fmt.Println("   📊 管道状态: running")
	fmt.Println("   🎯 节点状态: 所有节点正常运行")
	fmt.Println("   🔗 连接状态: 所有连接正常")

	fmt.Println("\n🎉 模拟流程完成")
}

// testLocalMode 测试本地部署模式
func testLocalMode() {
	fmt.Println("=== 测试本地部署模式 ===")

	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local", // 使用本地模式
		TemplatePath:   "./configs/pipeline-template.yaml",
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp":    "vp_rtsp_src_node",
				"file":    "vp_file_src_node",
				"gb28181": "vp_gb28181_src_node",
			},
			Algorithms: map[string]string{
				"face_detection":   "vp_yunet_face_detector_node",
				"object_detection": "vp_yolo_detector_node",
				"person_detection": "vp_person_detector_node",
			},
		},
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		Remote: pipeline.RemoteConfig{
			APIEndpoint: "http://localhost:8080/api/v1",
			APIKey:      "test-api-key",
			Timeout:     30 * time.Second,
			RetryCount:  3,
		},
	}

	// 1. 构建配置
	fmt.Println("1️⃣ 构建综合管道配置...")
	templateManager := pipeline.NewTemplateManager(config.TemplatePath)
	dataMapper := pipeline.NewDataMapper(config.NodeMapping)
	builder := pipeline.NewPipelineBuilder(templateManager, dataMapper)

	bindings := createTestBindings()
	pipelineConfig, err := builder.BuildComprehensiveConfig(bindings)
	if err != nil {
		log.Fatalf("构建综合管道配置失败: %v", err)
	}
	fmt.Println("✅ 配置构建成功")

	// 2. 创建本地客户端
	fmt.Println("2️⃣ 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)
	fmt.Println("✅ 本地客户端创建成功")

	// 3. 创建管道
	fmt.Println("3️⃣ 创建综合管道...")
	if err := client.CreateCompletePipeline(pipelineConfig); err != nil {
		log.Fatalf("创建管道失败: %v", err)
	}
	fmt.Println("✅ 管道创建成功")

	// 4. 启动管道
	fmt.Println("4️⃣ 启动管道...")
	if err := client.StartPipeline(pipelineConfig.ID); err != nil {
		log.Fatalf("启动管道失败: %v", err)
	}
	fmt.Println("✅ 管道启动成功")

	// 5. 检查状态
	fmt.Println("5️⃣ 检查管道状态...")
	status, err := client.GetPipelineStatus(pipelineConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s\n", status.State)

	// 6. 停止管道
	fmt.Println("6️⃣ 停止管道...")
	if err := client.StopPipeline(pipelineConfig.ID); err != nil {
		log.Fatalf("停止管道失败: %v", err)
	}
	fmt.Println("✅ 管道停止成功")

	fmt.Println("\n🎉 本地模式测试完成")
	fmt.Println("📁 文件存储位置:")
	fmt.Println("   - 配置文件: ./data/pipelines/")
	fmt.Println("   - 状态文件: ./data/pipeline-status/")
}

// callAPI 调用API接口
func callAPI(method, url, token string, body interface{}) error {
	var reqBody []byte
	var err error

	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return fmt.Errorf("序列化请求体失败: %v", err)
		}
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode >= 400 {
		return fmt.Errorf("API调用失败 [%d]: %s", resp.StatusCode, string(respBody))
	}

	fmt.Printf("   📥 响应: %s\n", string(respBody))
	return nil
}

// createTestBindings 创建测试绑定关系
func createTestBindings() []*pipeline.VideoAlgorithmBinding {
	return []*pipeline.VideoAlgorithmBinding{
		{
			ID:             1,
			VideoID:        1,
			AlgorithmID:    1,
			DetectionArea:  `{"points": [[100,100], [200,100], [200,200], [100,200]]}`,
			AlertInterval:  60,
			AlertThreshold: 0.8,
			Status:         "active",
			Video: pipeline.Video{
				ID:       1,
				Name:     "前门摄像头",
				Type:     1,
				Protocol: "rtsp",
				URL:      "rtsp://admin:password@*************:554/stream1",
				Username: "admin",
				Password: "password",
			},
			Algorithm: pipeline.Algorithm{
				ID:   1,
				Name: "face_detection",
				Path: "./models/face_detection.onnx",
			},
		},
		{
			ID:             2,
			VideoID:        1,
			AlgorithmID:    2,
			DetectionArea:  `{"points": [[50,50], [300,50], [300,250], [50,250]]}`,
			AlertInterval:  30,
			AlertThreshold: 0.7,
			Status:         "active",
			Video: pipeline.Video{
				ID:       1,
				Name:     "前门摄像头",
				Type:     1,
				Protocol: "rtsp",
				URL:      "rtsp://admin:password@*************:554/stream1",
				Username: "admin",
				Password: "password",
			},
			Algorithm: pipeline.Algorithm{
				ID:   2,
				Name: "object_detection",
				Path: "./models/yolov5s.onnx",
			},
		},
		{
			ID:             3,
			VideoID:        2,
			AlgorithmID:    3,
			AlertInterval:  45,
			AlertThreshold: 0.75,
			Status:         "active",
			Video: pipeline.Video{
				ID:       2,
				Name:     "后门摄像头",
				Type:     1,
				Protocol: "rtsp",
				URL:      "rtsp://admin:password@*************:554/stream1",
			},
			Algorithm: pipeline.Algorithm{
				ID:   3,
				Name: "person_detection",
				Path: "./models/person_detector.onnx",
			},
		},
	}
}
