package main

import (
	"encoding/json"
	"fmt"
	"log"

	"ecp/internal/app/pipeline"
)

func main() {
	fmt.Println("=== 测试真实配置文件的节点生成 ===")

	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建本地客户端
	fmt.Println("📦 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 使用真实的综合管道配置
	realConfig := &pipeline.CompletePipelineConfig{
		ID:          "real_comprehensive_pipeline",
		Name:        "真实综合管道",
		Description: "基于真实配置文件的综合管道",
		Version:     "1.0",
		Author:      "ECP System",
		Globals: map[string]interface{}{
			"api_endpoint": "http://localhost:8080/api/alerts",
			"model_dir":    "./vp_data/models/",
		},
		Nodes: []*pipeline.PipelineNode{
			{
				ID:   "video_src_1",
				Type: "vp_rtsp_src_node",
				Params: map[string]interface{}{
					"rtsp_url": "rtsp://admin:fnic1234@*************:554/h264/ch1/2/av_stream",
					"username": "admin",
					"password": "fnic1234",
				},
			},
			{
				ID:   "detector_2",
				Type: "vp_generic_detector_node",
				Params: map[string]interface{}{
					"confidence_threshold": 0.5,
					"model_path":           "data\\algorithms\\algorithm_1753155785987351800",
				},
			},
			{
				ID:   "osd_display",
				Type: "vp_osd_node",
				Params: map[string]interface{}{
					"font_color": "white",
					"font_size":  16,
				},
			},
			{
				ID:   "alert_handler",
				Type: "vp_alert_handler_node",
				Params: map[string]interface{}{
					"alert_endpoint": "http://localhost:8080/api/alerts",
					"danger_level":   "warning",
				},
			},
			{
				ID:   "screen_output",
				Type: "vp_screen_des_node",
				Params: map[string]interface{}{
					"channel_index": 0,
				},
			},
		},
		Connections: []*pipeline.PipelineConnection{
			{
				ID:   1,
				From: "video_src_1",
				To:   "detector_2",
			},
			{
				ID:   2,
				From: "detector_2",
				To:   "osd_display",
			},
			{
				ID:   3,
				From: "osd_display",
				To:   "alert_handler",
			},
			{
				ID:   4,
				From: "osd_display",
				To:   "screen_output",
			},
		},
	}

	// 测试1: 创建管道
	fmt.Println("🏗️ 创建真实配置管道...")
	if err := client.CreateCompletePipeline(realConfig); err != nil {
		log.Fatalf("创建管道失败: %v", err)
	}
	fmt.Println("✅ 管道创建成功")

	// 测试2: 检查创建后的状态
	fmt.Println("📊 检查创建后的状态...")
	status, err := client.GetPipelineStatus(realConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s\n", status.State)
	fmt.Printf("📊 节点数据长度: %d\n", len(status.Nodes))

	// 解析并显示节点信息
	if status.Nodes != "" && status.Nodes != "[]" {
		var nodes []interface{}
		if err := json.Unmarshal([]byte(status.Nodes), &nodes); err != nil {
			fmt.Printf("❌ 解析节点数据失败: %v\n", err)
		} else {
			fmt.Printf("✅ 解析后的节点数量: %d\n", len(nodes))
			for i, node := range nodes {
				nodeData, _ := json.MarshalIndent(node, "", "  ")
				fmt.Printf("📊 节点 %d: %s\n", i+1, string(nodeData))
			}
		}
	}

	// 测试3: 启动管道
	fmt.Println("\n🚀 启动管道...")
	if err := client.StartPipeline(realConfig.ID); err != nil {
		log.Fatalf("启动管道失败: %v", err)
	}
	fmt.Println("✅ 管道启动成功")

	// 测试4: 检查启动后的状态
	fmt.Println("📊 检查启动后的状态...")
	status, err = client.GetPipelineStatus(realConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s\n", status.State)

	// 解析并显示启动后的节点信息
	if status.Nodes != "" && status.Nodes != "[]" {
		var nodes []interface{}
		if err := json.Unmarshal([]byte(status.Nodes), &nodes); err != nil {
			fmt.Printf("❌ 解析节点数据失败: %v\n", err)
		} else {
			fmt.Printf("✅ 启动后节点数量: %d\n", len(nodes))
			for i, node := range nodes {
				if nodeMap, ok := node.(map[string]interface{}); ok {
					fmt.Printf("📊 节点 %d: ID=%s, Type=%s, State=%s\n",
						i+1, nodeMap["id"], nodeMap["type"], nodeMap["state"])
				}
			}
		}
	}

	// 清理
	fmt.Println("\n🗑️ 删除测试管道...")
	if err := client.DeletePipeline(realConfig.ID); err != nil {
		log.Fatalf("删除管道失败: %v", err)
	}
	fmt.Println("✅ 管道删除成功")

	fmt.Println("\n🎉 测试完成！")
}
