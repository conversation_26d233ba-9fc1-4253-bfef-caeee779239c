package main

import (
	"fmt"
	"time"

	"ecp/internal/app/pipeline"
)

func main() {
	fmt.Println("=== 测试本地和远程部署模式的配置获取 ===")

	// 测试本地模式
	fmt.Println("\n🏠 测试本地模式...")
	testLocalMode()

	// 测试远程模式（如果有远程服务）
	fmt.Println("\n🌐 测试远程模式...")
	testRemoteMode()
}

func testLocalMode() {
	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建本地客户端
	fmt.Println("📦 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 测试获取配置（直接通过客户端）
	fmt.Println("📊 测试直接获取管道配置...")
	pipelineConfig, err := client.GetPipelineConfig("comprehensive_pipeline")
	if err != nil {
		fmt.Printf("❌ 获取配置失败: %v\n", err)
	} else {
		fmt.Printf("✅ 成功获取配置: %s\n", pipelineConfig.Name)
		fmt.Printf("📊 节点数量: %d\n", len(pipelineConfig.Nodes))
		fmt.Printf("📊 连接数量: %d\n", len(pipelineConfig.Connections))
	}

	fmt.Println("✅ 本地模式客户端测试完成")
}

func testRemoteMode() {
	// 创建远程模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "remote",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Remote: pipeline.RemoteConfig{
			APIEndpoint: "http://localhost:8080/api/v1",
			APIKey:      "test-api-key",
			Timeout:     30 * time.Second,
			RetryCount:  3,
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建远程客户端
	fmt.Println("📦 创建远程管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 测试获取配置（直接通过客户端）
	fmt.Println("📊 测试直接获取管道配置...")
	pipelineConfig, err := client.GetPipelineConfig("comprehensive_pipeline")
	if err != nil {
		fmt.Printf("❌ 获取配置失败（可能远程服务不可用）: %v\n", err)
	} else {
		fmt.Printf("✅ 成功获取配置: %s\n", pipelineConfig.Name)
		fmt.Printf("📊 节点数量: %d\n", len(pipelineConfig.Nodes))
		fmt.Printf("📊 连接数量: %d\n", len(pipelineConfig.Connections))
	}

	fmt.Println("✅ 远程模式客户端测试完成")
}

func printConfig(config *pipeline.CompletePipelineConfig) {
	fmt.Printf("📋 管道配置详情:\n")
	fmt.Printf("  ID: %s\n", config.ID)
	fmt.Printf("  名称: %s\n", config.Name)
	fmt.Printf("  描述: %s\n", config.Description)
	fmt.Printf("  版本: %s\n", config.Version)
	fmt.Printf("  作者: %s\n", config.Author)

	fmt.Printf("  节点列表:\n")
	for i, node := range config.Nodes {
		fmt.Printf("    %d. %s (%s)\n", i+1, node.ID, node.Type)
	}

	fmt.Printf("  连接列表:\n")
	for i, conn := range config.Connections {
		fmt.Printf("    %d. %s → %s (ID: %d)\n", i+1, conn.From, conn.To, conn.ID)
	}

	if config.Globals != nil {
		fmt.Printf("  全局变量数量: %d\n", len(config.Globals))
	}
}
