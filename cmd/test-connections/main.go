package main

import (
	"encoding/json"
	"fmt"
	"log"

	"ecp/internal/app/pipeline"
)

func main() {
	fmt.Println("=== 测试连接信息生成 ===")

	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建本地客户端
	fmt.Println("📦 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 获取管道状态
	fmt.Println("📊 获取综合管道状态...")
	status, err := client.GetPipelineStatus("comprehensive_pipeline")
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}

	fmt.Printf("✅ 管道状态: %s\n", status.State)
	fmt.Printf("📊 管道ID: %s\n", status.PipelineID)

	// 检查全局指标中的连接信息
	if connectionsData, ok := status.GlobalMetrics["connections"].(string); ok {
		fmt.Printf("📊 连接数据: %s\n", connectionsData)
		
		// 解析连接数据
		var connections []interface{}
		if err := json.Unmarshal([]byte(connectionsData), &connections); err != nil {
			fmt.Printf("❌ 解析连接数据失败: %v\n", err)
		} else {
			fmt.Printf("✅ 连接数量: %d\n", len(connections))
			fmt.Println("📋 连接详情:")
			for i, conn := range connections {
				if connMap, ok := conn.(map[string]interface{}); ok {
					fmt.Printf("  %d. ID: %.0f, From: %s → To: %s\n", 
						i+1, connMap["id"], connMap["from"], connMap["to"])
				}
			}
		}
	} else {
		fmt.Println("❌ 未找到连接信息")
	}

	// 模拟前端API响应
	fmt.Println("\n🌐 模拟前端API响应:")
	apiResponse := map[string]interface{}{
		"errorCode": 0,
		"message":   "",
		"data":      status,
		"success":   true,
	}

	fmt.Printf("📡 errorCode: %v\n", apiResponse["errorCode"])
	fmt.Printf("📡 success: %v\n", apiResponse["success"])
	fmt.Printf("📡 data.state: %s\n", status.State)
	
	// 检查前端能否正确获取连接信息
	if globalMetrics, ok := status.GlobalMetrics["connections"]; ok {
		fmt.Printf("📡 data.global_metrics.connections: %v\n", globalMetrics)
	}

	fmt.Println("\n🎉 连接信息测试完成！")
	fmt.Println("💡 前端应该能正确解析连接信息并显示连接线")
}
