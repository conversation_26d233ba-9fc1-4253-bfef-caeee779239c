package main

import (
	"encoding/json"
	"fmt"
	"log"

	"ecp/internal/app/pipeline"
)

func main() {
	fmt.Println("=== 最终API测试 ===")

	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建本地客户端
	fmt.Println("📦 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 获取管道状态
	fmt.Println("📊 获取综合管道状态...")
	status, err := client.GetPipelineStatus("comprehensive_pipeline")
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}

	fmt.Printf("✅ 管道状态: %s\n", status.State)
	fmt.Printf("📊 管道ID: %s\n", status.PipelineID)

	// 解析节点数据
	if status.Nodes != "" && status.Nodes != "[]" {
		var nodes []interface{}
		if err := json.Unmarshal([]byte(status.Nodes), &nodes); err != nil {
			fmt.Printf("❌ 解析节点数据失败: %v\n", err)
		} else {
			fmt.Printf("✅ 节点数量: %d\n", len(nodes))
			fmt.Println("📋 节点详情:")
			for i, node := range nodes {
				if nodeMap, ok := node.(map[string]interface{}); ok {
					fmt.Printf("  %d. ID: %s\n", i+1, nodeMap["id"])
					fmt.Printf("     类型: %s\n", nodeMap["type"])
					fmt.Printf("     状态: %s\n", nodeMap["state"])
					if params, ok := nodeMap["params"].(map[string]interface{}); ok {
						fmt.Printf("     参数数量: %d\n", len(params))
					}
					if metrics, ok := nodeMap["metrics"].(map[string]interface{}); ok {
						fmt.Printf("     指标数量: %d\n", len(metrics))
					}
					fmt.Println()
				}
			}
		}
	}

	// 模拟API响应
	fmt.Println("🌐 模拟API响应:")
	apiResponse := map[string]interface{}{
		"errorCode": 0,
		"message":   "",
		"data":      status,
		"success":   true,
	}

	fmt.Printf("📡 errorCode: %v\n", apiResponse["errorCode"])
	fmt.Printf("📡 success: %v\n", apiResponse["success"])
	fmt.Printf("📡 data.state: %s\n", status.State)
	fmt.Printf("📡 data.pipeline_id: %s\n", status.PipelineID)

	fmt.Println("\n🎉 API测试完成！")
	fmt.Println("💡 前端应该能正确解析这些数据并显示5个节点的流程图")
}
