package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"ecp/internal/app/binding"
	"ecp/internal/app/pipeline"
	"ecp/internal/pkg/config"
	"ecp/internal/pkg/database"
)

func main() {
	fmt.Println("=== 基于数据库数据测试综合管道创建 ===")

	// 1. 加载配置
	cfg, err := config.LoadConfig("configs/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 连接数据库
	dbConfig := &database.Config{
		Path: cfg.Database.Path,
	}
	db, err := database.NewDB(dbConfig)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 3. 创建绑定服务
	bindingService := binding.NewBindingService(db.DB)

	// 4. 获取所有激活的绑定关系
	fmt.Println("📊 获取数据库中的绑定关系...")
	bindings, err := bindingService.GetActiveBindings()
	if err != nil {
		log.Fatalf("获取绑定关系失败: %v", err)
	}

	if len(bindings) == 0 {
		fmt.Println("⚠️ 数据库中没有激活的绑定关系")
		fmt.Println("💡 请先在系统中创建一些绑定关系，并将状态设置为 'active'")
		return
	}

	fmt.Printf("✅ 找到 %d 个激活的绑定关系\n", len(bindings))

	// 5. 显示绑定关系信息
	fmt.Println("\n📋 绑定关系详情:")
	for i, binding := range bindings {
		fmt.Printf("  %d. 绑定ID: %d\n", i+1, binding.ID)
		fmt.Printf("     视频源: %s (ID: %d, 协议: %s)\n", binding.Video.Name, binding.VideoID, binding.Video.Protocol)
		fmt.Printf("     算法: %s (ID: %d)\n", binding.Algorithm.Name, binding.AlgorithmID)
		fmt.Printf("     状态: %s\n", binding.Status)
		fmt.Printf("     阈值: %.2f\n", binding.AlertThreshold)
		if binding.DetectionArea != "" {
			fmt.Printf("     检测区域: %s\n", binding.DetectionArea)
		}
		fmt.Println()
	}

	// 6. 创建管道服务组件
	fmt.Println("🔧 初始化管道服务组件...")

	// 解析超时时间
	timeout, err := time.ParseDuration(cfg.Pipeline.Remote.Timeout)
	if err != nil {
		timeout = 30 * time.Second
	}

	pipelineConfig := pipeline.PipelineConfig{
		DeploymentMode: cfg.Pipeline.DeploymentMode,
		TemplatePath:   cfg.Pipeline.TemplatePath,
		NodeMapping: pipeline.NodeMapping{
			VideoSource: cfg.Pipeline.NodeMapping.VideoSource,
			Algorithms:  cfg.Pipeline.NodeMapping.Algorithms,
		},
		Local: pipeline.LocalConfig{
			ConfigStoragePath: cfg.Pipeline.Local.ConfigStoragePath,
			StatusStoragePath: cfg.Pipeline.Local.StatusStoragePath,
		},
		Remote: pipeline.RemoteConfig{
			APIEndpoint: cfg.Pipeline.Remote.APIEndpoint,
			APIKey:      cfg.Pipeline.Remote.APIKey,
			Timeout:     timeout,
			RetryCount:  cfg.Pipeline.Remote.RetryCount,
		},
	}

	// 创建组件
	templateManager := pipeline.NewTemplateManager(pipelineConfig.TemplatePath)
	dataMapper := pipeline.NewDataMapper(pipelineConfig.NodeMapping)
	builder := pipeline.NewPipelineBuilder(templateManager, dataMapper)

	// 7. 转换绑定关系类型
	fmt.Println("🔄 转换绑定关系数据格式...")
	pipelineBindings := make([]*pipeline.VideoAlgorithmBinding, 0, len(bindings))
	for _, binding := range bindings {
		pipelineBinding := convertToPipelineBinding(binding)
		pipelineBindings = append(pipelineBindings, pipelineBinding)
	}

	// 8. 构建综合管道配置
	fmt.Println("🏗️ 构建综合管道配置...")
	comprehensiveConfig, err := builder.BuildComprehensiveConfig(pipelineBindings)
	if err != nil {
		log.Fatalf("构建综合管道配置失败: %v", err)
	}

	// 9. 分析结果
	fmt.Println("\n📊 综合管道分析结果:")
	analysis, err := builder.AnalyzeBindings(pipelineBindings)
	if err != nil {
		log.Fatalf("分析绑定关系失败: %v", err)
	}

	fmt.Printf("   🎥 唯一视频源数量: %d\n", len(analysis.UniqueVideos))
	for id, video := range analysis.UniqueVideos {
		fmt.Printf("      - 视频源 %d: %s (%s)\n", id, video.Name, video.Protocol)
	}

	fmt.Printf("   🤖 唯一算法数量: %d\n", len(analysis.UniqueAlgorithms))
	for id, algorithm := range analysis.UniqueAlgorithms {
		fmt.Printf("      - 算法 %d: %s\n", id, algorithm.Name)
	}

	fmt.Printf("   🔗 绑定关系矩阵:\n")
	for videoID, algorithmMap := range analysis.BindingMatrix {
		for algorithmID := range algorithmMap {
			videoName := analysis.UniqueVideos[videoID].Name
			algorithmName := analysis.UniqueAlgorithms[algorithmID].Name
			fmt.Printf("      - %s → %s\n", videoName, algorithmName)
		}
	}

	// 10. 输出最终配置
	fmt.Printf("\n🎯 最终管道配置:\n")
	fmt.Printf("   - 管道ID: %s\n", comprehensiveConfig.ID)
	fmt.Printf("   - 管道名称: %s\n", comprehensiveConfig.Name)
	fmt.Printf("   - 总节点数: %d\n", len(comprehensiveConfig.Nodes))
	fmt.Printf("   - 连接关系数: %d\n", len(comprehensiveConfig.Connections))

	// 11. 保存配置到文件
	configJSON, err := json.MarshalIndent(comprehensiveConfig, "", "  ")
	if err != nil {
		log.Fatalf("序列化配置失败: %v", err)
	}

	filename := "db_comprehensive_pipeline_config.json"
	err = os.WriteFile(filename, configJSON, 0644)
	if err != nil {
		log.Printf("保存配置文件失败: %v", err)
	} else {
		fmt.Printf("📁 配置已保存到: %s\n", filename)
	}

	fmt.Println("\n✅ 测试完成！")
	fmt.Println("💡 提示: 可以使用生成的配置文件来验证管道结构是否正确")
}

// convertToPipelineBinding 将database包的绑定关系转换为pipeline包的绑定关系
func convertToPipelineBinding(dbBinding *binding.VideoAlgorithmBinding) *pipeline.VideoAlgorithmBinding {
	return &pipeline.VideoAlgorithmBinding{
		ID:              dbBinding.ID,
		VideoID:         dbBinding.VideoID,
		AlgorithmID:     dbBinding.AlgorithmID,
		DetectionArea:   dbBinding.DetectionArea,
		AlertInterval:   dbBinding.AlertInterval,
		AlertWindow:     dbBinding.AlertWindow,
		AlertThreshold:  dbBinding.AlertThreshold,
		VoiceContent:    dbBinding.VoiceContent,
		DangerLevel:     dbBinding.DangerLevel,
		ExtensionFields: dbBinding.ExtensionFields,
		Status:          dbBinding.Status,
		Video: pipeline.Video{
			ID:          dbBinding.Video.ID,
			Name:        dbBinding.Video.Name,
			Type:        dbBinding.Video.Type,
			Protocol:    dbBinding.Video.Protocol,
			CameraID:    dbBinding.Video.CameraID,
			CameraType:  dbBinding.Video.CameraType,
			Description: dbBinding.Video.Description,
			StreamType:  dbBinding.Video.StreamType,
			CameraIP:    dbBinding.Video.CameraIP,
			Username:    dbBinding.Video.Username,
			Password:    dbBinding.Video.Password,
			Brand:       dbBinding.Video.Brand,
			StreamMode:  dbBinding.Video.StreamMode,
			URL:         dbBinding.Video.URL,
			Port:        dbBinding.Video.Port,
			Status:      dbBinding.Video.Status,
		},
		Algorithm: pipeline.Algorithm{
			ID:          dbBinding.Algorithm.ID,
			Name:        dbBinding.Algorithm.Name,
			Description: dbBinding.Algorithm.Description,
			Version:     dbBinding.Algorithm.Version,
			Path:        dbBinding.Algorithm.Path,
			Status:      dbBinding.Algorithm.Status,
		},
	}
}
