package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"ecp/internal/app/pipeline"
)

func main() {
	fmt.Println("=== 测试API响应格式 ===")

	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建本地客户端
	fmt.Println("📦 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 创建测试管道配置
	testConfig := &pipeline.CompletePipelineConfig{
		ID:          "api_test_pipeline",
		Name:        "API测试管道",
		Description: "用于测试API响应的管道",
		Version:     "1.0",
		Author:      "ECP System",
		Globals: map[string]interface{}{
			"model_dir": "./models/",
		},
		Nodes: []*pipeline.PipelineNode{
			{
				ID:   "rtsp_src_0",
				Type: "vp_rtsp_src_node",
				Params: map[string]interface{}{
					"rtsp_url": "rtsp://test",
				},
			},
		},
		Connections: []*pipeline.PipelineConnection{},
	}

	// 创建管道
	fmt.Println("🏗️ 创建管道...")
	if err := client.CreateCompletePipeline(testConfig); err != nil {
		log.Fatalf("创建管道失败: %v", err)
	}
	fmt.Println("✅ 管道创建成功")

	// 启动管道
	fmt.Println("🚀 启动管道...")
	if err := client.StartPipeline(testConfig.ID); err != nil {
		log.Fatalf("启动管道失败: %v", err)
	}
	fmt.Println("✅ 管道启动成功")

	// 等待一下
	time.Sleep(1 * time.Second)

	// 获取状态
	fmt.Println("📊 获取管道状态...")
	status, err := client.GetPipelineStatus(testConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}

	// 打印详细状态信息
	fmt.Printf("✅ 管道状态: %s\n", status.State)
	fmt.Printf("📊 管道ID: %s\n", status.PipelineID)
	fmt.Printf("📊 开始时间: %s\n", status.StartTime)
	fmt.Printf("📊 运行时间: %d秒\n", status.UptimeSeconds)
	fmt.Printf("📊 节点数据长度: %d\n", len(status.Nodes))
	fmt.Printf("📊 全局指标: %+v\n", status.GlobalMetrics)

	// 解析节点数据
	if status.Nodes != "" && status.Nodes != "[]" {
		var nodes []interface{}
		if err := json.Unmarshal([]byte(status.Nodes), &nodes); err != nil {
			fmt.Printf("❌ 解析节点数据失败: %v\n", err)
		} else {
			fmt.Printf("✅ 解析后的节点数量: %d\n", len(nodes))
			for i, node := range nodes {
				nodeData, _ := json.MarshalIndent(node, "", "  ")
				fmt.Printf("📊 节点 %d: %s\n", i+1, string(nodeData))
			}
		}
	}

	// 模拟API响应格式
	fmt.Println("\n🌐 模拟API响应格式:")
	apiResponse := map[string]interface{}{
		"errorCode": 0,
		"message":   "",
		"data":      status,
		"success":   true,
	}

	responseData, _ := json.MarshalIndent(apiResponse, "", "  ")
	fmt.Printf("📡 API响应: %s\n", string(responseData))

	// 测试HTTP请求
	fmt.Println("\n🔗 测试HTTP请求...")
	resp, err := http.Get("http://localhost:8080/api/comprehensive-pipeline/status")
	if err != nil {
		fmt.Printf("❌ HTTP请求失败: %v\n", err)
	} else {
		defer resp.Body.Close()
		fmt.Printf("✅ HTTP状态码: %d\n", resp.StatusCode)
		
		var httpResponse map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&httpResponse); err != nil {
			fmt.Printf("❌ 解析HTTP响应失败: %v\n", err)
		} else {
			httpData, _ := json.MarshalIndent(httpResponse, "", "  ")
			fmt.Printf("📡 HTTP响应: %s\n", string(httpData))
		}
	}

	// 清理
	fmt.Println("\n🗑️ 删除测试管道...")
	if err := client.DeletePipeline(testConfig.ID); err != nil {
		log.Fatalf("删除管道失败: %v", err)
	}
	fmt.Println("✅ 管道删除成功")

	fmt.Println("\n🎉 测试完成！")
}
