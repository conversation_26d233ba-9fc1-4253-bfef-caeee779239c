package main

import (
	"encoding/json"
	"fmt"
	"log"

	"ecp/internal/app/pipeline"
)

func main() {
	fmt.Println("=== 测试流水线布局 ===")

	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建本地客户端
	fmt.Println("📦 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 获取管道状态
	fmt.Println("📊 获取综合管道状态...")
	status, err := client.GetPipelineStatus("comprehensive_pipeline")
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}

	fmt.Printf("✅ 管道状态: %s\n", status.State)

	// 解析节点数据
	var nodes []interface{}
	if status.Nodes != "" && status.Nodes != "[]" {
		if err := json.Unmarshal([]byte(status.Nodes), &nodes); err != nil {
			fmt.Printf("❌ 解析节点数据失败: %v\n", err)
		} else {
			fmt.Printf("✅ 节点数量: %d\n", len(nodes))
			fmt.Println("📋 节点列表:")
			for i, node := range nodes {
				if nodeMap, ok := node.(map[string]interface{}); ok {
					fmt.Printf("  %d. %s (%s)\n", i+1, nodeMap["id"], nodeMap["type"])
				}
			}
		}
	}

	// 解析连接数据
	var connections []interface{}
	if connectionsData, ok := status.GlobalMetrics["connections"].(string); ok {
		if err := json.Unmarshal([]byte(connectionsData), &connections); err != nil {
			fmt.Printf("❌ 解析连接数据失败: %v\n", err)
		} else {
			fmt.Printf("✅ 连接数量: %d\n", len(connections))
			fmt.Println("📋 连接关系:")
			for i, conn := range connections {
				if connMap, ok := conn.(map[string]interface{}); ok {
					fmt.Printf("  %d. %s → %s (ID: %.0f)\n", 
						i+1, connMap["from"], connMap["to"], connMap["id"])
				}
			}
		}
	}

	// 分析流水线顺序
	fmt.Println("\n🔄 分析流水线顺序:")
	
	// 构建邻接表
	adjacencyList := make(map[string][]string)
	inDegree := make(map[string]int)
	
	// 初始化所有节点
	for _, node := range nodes {
		if nodeMap, ok := node.(map[string]interface{}); ok {
			nodeId := nodeMap["id"].(string)
			adjacencyList[nodeId] = []string{}
			inDegree[nodeId] = 0
		}
	}
	
	// 构建图
	for _, conn := range connections {
		if connMap, ok := conn.(map[string]interface{}); ok {
			from := connMap["from"].(string)
			to := connMap["to"].(string)
			adjacencyList[from] = append(adjacencyList[from], to)
			inDegree[to]++
		}
	}
	
	// 拓扑排序
	queue := []string{}
	for nodeId, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, nodeId)
		}
	}
	
	level := 0
	for len(queue) > 0 {
		levelSize := len(queue)
		fmt.Printf("📊 第 %d 层:", level)
		
		for i := 0; i < levelSize; i++ {
			nodeId := queue[0]
			queue = queue[1:]
			fmt.Printf(" %s", nodeId)
			
			// 更新相邻节点的入度
			for _, neighbor := range adjacencyList[nodeId] {
				inDegree[neighbor]--
				if inDegree[neighbor] == 0 {
					queue = append(queue, neighbor)
				}
			}
		}
		fmt.Println()
		level++
	}

	fmt.Println("\n🎯 预期的流水线布局:")
	fmt.Println("第0层: video_src_1 (视频源)")
	fmt.Println("第1层: detector_2 (检测器)")
	fmt.Println("第2层: osd_display (OSD显示)")
	fmt.Println("第3层: alert_handler, screen_output (输出节点)")

	fmt.Println("\n🎉 流水线布局分析完成！")
	fmt.Println("💡 前端应该按照上述层级顺序排列节点")
}
