package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"

	"ecp/internal/app/pipeline"
)

func main() {
	fmt.Println("=== 模拟前端部署流程 ===")

	// 创建本地模式配置
	config := pipeline.PipelineConfig{
		DeploymentMode: "local",
		TemplatePath:   "./configs/pipeline-template.yaml",
		Local: pipeline.LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		NodeMapping: pipeline.NodeMapping{
			VideoSource: map[string]string{
				"rtsp": "vp_rtsp_src_node",
			},
			Algorithms: map[string]string{
				"face_detection": "vp_yunet_face_detector_node",
			},
		},
	}

	// 创建本地客户端
	fmt.Println("📦 创建本地管道客户端...")
	client := pipeline.CreatePipelineClient(config)

	// 步骤1: 读取已存在的配置文件
	fmt.Println("📖 读取现有的综合管道配置文件...")
	configPath := "./data/pipelines/comprehensive_pipeline.json"

	// 检查配置文件是否存在
	configData, err := ioutil.ReadFile(configPath)
	if err != nil {
		log.Fatalf("读取配置文件失败: %v", err)
	}

	var pipelineConfig pipeline.CompletePipelineConfig
	if err := json.Unmarshal(configData, &pipelineConfig); err != nil {
		log.Fatalf("解析配置文件失败: %v", err)
	}

	fmt.Printf("✅ 配置文件读取成功: %s\n", pipelineConfig.Name)
	fmt.Printf("📊 节点数量: %d\n", len(pipelineConfig.Nodes))
	fmt.Printf("📊 连接数量: %d\n", len(pipelineConfig.Connections))

	// 显示节点信息
	fmt.Println("📋 节点列表:")
	for i, node := range pipelineConfig.Nodes {
		fmt.Printf("  %d. %s (%s)\n", i+1, node.ID, node.Type)
	}

	// 步骤2: 通过本地客户端创建管道（这会生成状态文件）
	fmt.Println("\n🏗️ 通过本地客户端创建管道...")
	if err := client.CreateCompletePipeline(&pipelineConfig); err != nil {
		log.Fatalf("创建管道失败: %v", err)
	}
	fmt.Println("✅ 管道创建成功")

	// 步骤3: 检查创建后的状态
	fmt.Println("📊 检查创建后的状态...")
	status, err := client.GetPipelineStatus(pipelineConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s\n", status.State)
	fmt.Printf("📊 节点数据长度: %d\n", len(status.Nodes))

	// 解析并显示节点信息
	if status.Nodes != "" && status.Nodes != "[]" {
		var nodes []interface{}
		if err := json.Unmarshal([]byte(status.Nodes), &nodes); err != nil {
			fmt.Printf("❌ 解析节点数据失败: %v\n", err)
		} else {
			fmt.Printf("✅ 解析后的节点数量: %d\n", len(nodes))
			fmt.Println("📋 节点状态:")
			for i, node := range nodes {
				if nodeMap, ok := node.(map[string]interface{}); ok {
					fmt.Printf("  %d. %s (%s) - %s\n",
						i+1, nodeMap["id"], nodeMap["type"], nodeMap["state"])
				}
			}
		}
	}

	// 步骤4: 启动管道
	fmt.Println("\n🚀 启动管道...")
	if err := client.StartPipeline(pipelineConfig.ID); err != nil {
		log.Fatalf("启动管道失败: %v", err)
	}
	fmt.Println("✅ 管道启动成功")

	// 步骤5: 检查启动后的状态
	fmt.Println("📊 检查启动后的状态...")
	status, err = client.GetPipelineStatus(pipelineConfig.ID)
	if err != nil {
		log.Fatalf("获取管道状态失败: %v", err)
	}
	fmt.Printf("✅ 管道状态: %s\n", status.State)

	// 解析并显示启动后的节点信息
	if status.Nodes != "" && status.Nodes != "[]" {
		var nodes []interface{}
		if err := json.Unmarshal([]byte(status.Nodes), &nodes); err != nil {
			fmt.Printf("❌ 解析节点数据失败: %v\n", err)
		} else {
			fmt.Printf("✅ 启动后节点数量: %d\n", len(nodes))
			fmt.Println("📋 运行中的节点:")
			for i, node := range nodes {
				if nodeMap, ok := node.(map[string]interface{}); ok {
					fmt.Printf("  %d. %s (%s) - %s\n",
						i+1, nodeMap["id"], nodeMap["type"], nodeMap["state"])
				}
			}
		}
	}

	// 步骤6: 模拟API响应格式
	fmt.Println("\n🌐 模拟API响应格式:")
	apiResponse := map[string]interface{}{
		"errorCode": 0,
		"message":   "",
		"data":      status,
		"success":   true,
	}

	responseData, _ := json.MarshalIndent(apiResponse, "", "  ")
	fmt.Printf("📡 API响应预览: %s\n", string(responseData)[:500])
	fmt.Println("...")

	fmt.Println("\n🎉 前端部署流程模拟完成！")
	fmt.Println("💡 现在前端应该能看到正确的管道流程图了")
}
