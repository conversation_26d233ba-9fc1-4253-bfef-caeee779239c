package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

// Config 表示应用程序的全局配置
type Config struct {
	Server    ServerConfig    `mapstructure:"server"`
	Database  DatabaseConfig  `mapstructure:"database"`
	Video     VideoConfig     `mapstructure:"video"`
	Algorithm AlgorithmConfig `mapstructure:"algorithm"`
	Alert     AlertConfig     `mapstructure:"alert"`
	GB28181   GB28181Config   `mapstructure:"gb28181"`
	Log       LogConfig       `mapstructure:"log"`
	Auth      AuthConfig      `mapstructure:"auth"`
	Pipeline  PipelineConfig  `mapstructure:"pipeline"`
}

// ServerConfig 表示服务器配置
type ServerConfig struct {
	Port         int    `mapstructure:"port"`
	ReadTimeout  string `mapstructure:"read_timeout"`
	WriteTimeout string `mapstructure:"write_timeout"`
}

// DatabaseConfig 表示数据库配置
type DatabaseConfig struct {
	Path string `mapstructure:"path"`
}

// VideoConfig 表示视频接入配置
type VideoConfig struct {
	StoragePath   string `mapstructure:"storage_path"`
	ThumbnailPath string `mapstructure:"thumbnail_path"`
}

// AlgorithmConfig 表示算法仓库配置
type AlgorithmConfig struct {
	StoragePath string `mapstructure:"storage_path"`
}

// AlertConfig 表示告警记录配置
type AlertConfig struct {
	StoragePath string `mapstructure:"storage_path"`
	ImagePath   string `mapstructure:"image_path"`
}

// GB28181Config 表示GB28181服务配置
type GB28181Config struct {
	ServerURL           string `mapstructure:"server_url"`
	Enabled             bool   `mapstructure:"enabled"`
	HealthCheckInterval int    `mapstructure:"health_check_interval"`
	SyncInterval        int    `mapstructure:"sync_interval"`
	Timeout             int    `mapstructure:"timeout"`
}

// LogConfig 表示日志配置
type LogConfig struct {
	Level string `mapstructure:"level"`
	Path  string `mapstructure:"path"`
}

// AuthConfig 表示认证配置
type AuthConfig struct {
	JWTSecret     string `mapstructure:"jwt_secret"`
	TokenExpiry   int    `mapstructure:"token_expiry"`   // 过期时间（小时）
	RefreshExpiry int    `mapstructure:"refresh_expiry"` // 刷新令牌过期时间（天）
}

// PipelineConfig 表示管道配置
type PipelineConfig struct {
	Enabled        bool                 `mapstructure:"enabled"`
	DeploymentMode string               `mapstructure:"deployment_mode"` // local 或 remote
	Local          PipelineLocalConfig  `mapstructure:"local"`
	Remote         PipelineRemoteConfig `mapstructure:"remote"`
	TemplatePath   string               `mapstructure:"template_path"`
	NodeMapping    PipelineNodeMapping  `mapstructure:"node_mapping"`
}

// PipelineLocalConfig 表示本地部署配置
type PipelineLocalConfig struct {
	ConfigStoragePath   string `mapstructure:"config_storage_path"`
	StatusStoragePath   string `mapstructure:"status_storage_path"`
	VideoPipeExecutable string `mapstructure:"videopipe_executable"`
	VideoPipeWorkdir    string `mapstructure:"videopipe_workdir"`
	ProcessTimeout      string `mapstructure:"process_timeout"`
	StatusCheckInterval string `mapstructure:"status_check_interval"`
}

// PipelineRemoteConfig 表示远程部署配置
type PipelineRemoteConfig struct {
	APIEndpoint string `mapstructure:"api_endpoint"`
	APIKey      string `mapstructure:"api_key"`
	Timeout     string `mapstructure:"timeout"`
	RetryCount  int    `mapstructure:"retry_count"`
}

// PipelineNodeMapping 表示节点类型映射配置
type PipelineNodeMapping struct {
	VideoSource map[string]string `mapstructure:"video_source"`
	Algorithms  map[string]string `mapstructure:"algorithms"`
}

// 全局配置实例
var globalConfig *Config

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	if configPath == "" {
		configPath = "configs/config.yaml" // 默认配置文件路径
	}

	// 确保配置文件存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 设置Viper配置
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析配置到结构体
	config := &Config{}
	if err := viper.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 处理路径
	if !filepath.IsAbs(config.Database.Path) {
		config.Database.Path = filepath.Clean(config.Database.Path)
	}

	// 保存全局配置
	globalConfig = config

	return config, nil
}

// GetConfig 获取全局配置实例
func GetConfig() *Config {
	if globalConfig == nil {
		// 如果全局配置未初始化，尝试加载默认配置
		config, err := LoadConfig("")
		if err != nil {
			// 加载失败时返回默认配置
			return &Config{
				Database: DatabaseConfig{
					Path: "./data/ecp.db",
				},
				GB28181: GB28181Config{
					ServerURL: "http://localhost:18080",
					Enabled:   true,
				},
				Auth: AuthConfig{
					JWTSecret:     "ecp_default_jwt_secret_key",
					TokenExpiry:   24, // 默认24小时
					RefreshExpiry: 7,  // 默认7天
				},
			}
		}
		return config
	}
	return globalConfig
}
