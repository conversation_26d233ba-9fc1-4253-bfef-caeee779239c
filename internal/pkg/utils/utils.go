package utils

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"
)

// GenerateID 生成唯一ID
func GenerateID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// GenerateFilePath 生成文件存储路径
func GenerateFilePath(baseDir, prefix string) (string, error) {
	// 确保目录存在
	if err := os.MkdirAll(baseDir, 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %w", err)
	}

	// 生成唯一文件名
	fileName := fmt.Sprintf("%s_%s", prefix, GenerateID())
	return filepath.Join(baseDir, fileName), nil
}

// SaveFile 保存文件
func SaveFile(data []byte, filePath string) error {
	return os.WriteFile(filePath, data, 0644)
}

// DeleteFile 删除文件
func DeleteFile(filePath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil // 文件不存在，视为删除成功
	}

	// 删除文件
	return os.Remove(filePath)
}

// CalculateMD5 计算文件的MD5值
func CalculateMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", fmt.Errorf("计算MD5失败: %w", err)
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// FormatTime 格式化时间
func FormatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

// ParseTime 解析时间字符串
func ParseTime(s string) (time.Time, error) {
	return time.Parse("2006-01-02 15:04:05", s)
}
