# 工具函数模块

这个模块提供边缘计算平台的通用工具函数，包括文件操作、时间处理等功能。

## 功能

- 生成唯一ID
- 文件路径生成
- 文件保存
- MD5计算
- 时间格式化和解析

## 使用方法

```go
// 生成唯一ID
id := utils.GenerateID()

// 生成文件存储路径
filePath, err := utils.GenerateFilePath("./data/images", "alert")
if err != nil {
    log.Fatalf("生成文件路径失败: %v", err)
}

// 保存文件
err := utils.SaveFile(data, filePath)
if err != nil {
    log.Fatalf("保存文件失败: %v", err)
}

// 计算MD5值
md5, err := utils.CalculateMD5(filePath)
if err != nil {
    log.Fatalf("计算MD5失败: %v", err)
}

// 格式化时间
timeStr := utils.FormatTime(time.Now())

// 解析时间字符串
t, err := utils.ParseTime("2023-01-01 12:00:00")
if err != nil {
    log.Fatalf("解析时间失败: %v", err)
}
``` 