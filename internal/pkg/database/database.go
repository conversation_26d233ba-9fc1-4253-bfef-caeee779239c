package database

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/mlogclub/simple/sqls"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

// DB 表示数据库连接
type DB struct {
	*gorm.DB
}

// Config 表示数据库配置
type Config struct {
	Path string
}

// Video 表示一个视频源
type Video struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	Name        string    `gorm:"not null" json:"name"`
	Type        int       `gorm:"not null" json:"type"`     // 摄像头-1、视频流-2、视频文件-3
	Protocol    string    `gorm:"not null" json:"protocol"` // rtsp、onvif、gb28181
	CameraID    string    `json:"camera_id"`
	CameraType  string    `json:"camera_type"` // 枪机-1、球机-2
	Description string    `json:"description"`
	StreamType  string    `gorm:"not null" json:"stream_type"` // 自动生成、手动输入
	CameraIP    string    `json:"camera_ip"`
	Username    string    `json:"username"`
	Password    string    `json:"password"`
	Brand       string    `json:"brand"`       // 海康威视、大华、宇视、华为
	StreamMode  string    `json:"stream_mode"` // 主码流、子码流
	URL         string    `gorm:"not null" json:"url"`
	Port        int       `json:"port"`
	Status      string    `gorm:"not null" json:"status"` // online, offline
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// Algorithm 表示一个算法
type Algorithm struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	Name        string    `gorm:"not null" json:"name"`
	Description string    `json:"description"`
	Version     string    `gorm:"not null" json:"version"`
	Path        string    `gorm:"not null" json:"path"`
	Status      string    `gorm:"not null" json:"status"` // active, inactive
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// Alert 表示一条告警记录
type Alert struct {
	ID               int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	Source           string    `gorm:"not null" json:"source"` // 告警来源（摄像头中的描述）
	IP               string    `json:"ip"`                     // 摄像头IP
	Level            string    `gorm:"not null" json:"level"`  // 危险等级：info, warning, error
	Type             string    `gorm:"not null" json:"type"`   // 告警类型（算法类型）
	VideoID          int64     `gorm:"not null" json:"video_id"`
	VideoName        string    `gorm:"not null" json:"video_name"`
	AlgorithmID      int64     `gorm:"not null" json:"algorithm_id"`
	AlgorithmName    string    `gorm:"not null" json:"algorithm_name"`
	Content          string    `json:"content"`
	OriginalImage    string    `json:"original_image"`                         // 原图地址
	ImagePath        string    `json:"image_path"`                             // 告警图地址
	Status           string    `gorm:"not null" json:"status"`                 // new, processed, ignored
	LinkageTriggered bool      `gorm:"default:false" json:"linkage_triggered"` // 是否已触发联动
	LinkageResults   string    `json:"linkage_results"`                        // 联动执行结果（JSON格式）
	LinkageError     string    `json:"linkage_error"`                          // 联动执行错误信息
	CreatedAt        time.Time `gorm:"autoCreateTime" json:"created_at"`       // 检测时间
	UpdatedAt        time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// VideoAlgorithmBinding 表示视频源和算法的绑定关系
type VideoAlgorithmBinding struct {
	ID              int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	VideoID         int64     `gorm:"not null;index" json:"video_id"`                 // 视频源ID
	AlgorithmID     int64     `gorm:"not null;index" json:"algorithm_id"`             // 算法ID
	DetectionArea   string    `json:"detection_area"`                                 // 检测区域（多边形坐标，JSON格式存储）
	AlertInterval   int       `gorm:"default:60" json:"alert_interval"`               // 告警间隔（秒）
	AlertWindow     int       `gorm:"default:300" json:"alert_window"`                // 告警窗口长度（秒）
	AlertThreshold  float64   `gorm:"default:0.5" json:"alert_threshold"`             // 告警阈值（0-1）
	VoiceContent    string    `json:"voice_content"`                                  // 浏览器语言播报内容
	DangerLevel     string    `gorm:"not null;default:'warning'" json:"danger_level"` // 危险等级：info, warning, error
	ExtensionFields string    `json:"extension_fields"`                               // 算法扩展字段（JSON格式存储）
	Status          string    `gorm:"not null;default:'active'" json:"status"`        // 绑定状态：active, inactive
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// 关联关系
	Video     Video     `gorm:"foreignKey:VideoID" json:"video,omitempty"`
	Algorithm Algorithm `gorm:"foreignKey:AlgorithmID" json:"algorithm,omitempty"`
}

// LinkageRule 表示联动规则
type LinkageRule struct {
	ID          string    `gorm:"primaryKey" json:"id"`        // 规则ID
	Name        string    `gorm:"not null" json:"name"`        // 规则名称
	Description string    `json:"description"`                 // 规则描述
	Enabled     bool      `gorm:"default:true" json:"enabled"` // 是否启用
	Priority    int       `gorm:"default:0" json:"priority"`   // 优先级（数值越大优先级越高）
	Conditions  string    `json:"conditions"`                  // 触发条件（JSON格式）
	Actions     string    `json:"actions"`                     // 联动动作（JSON格式）
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// LinkageDevice 表示联动设备
type LinkageDevice struct {
	ID        string    `gorm:"primaryKey" json:"id"`            // 设备ID
	Name      string    `gorm:"not null" json:"name"`            // 设备名称
	Type      string    `json:"type"`                            // 设备类型（警报灯、门禁、报警器等）
	Protocol  string    `gorm:"not null" json:"protocol"`        // 协议类型（mqtt、modbus、rs485）
	Address   string    `gorm:"not null" json:"address"`         // 设备地址（IP地址或串口地址）
	Port      int       `json:"port"`                            // 端口号
	Config    string    `json:"config"`                          // 协议配置（JSON格式）
	Status    string    `gorm:"default:'offline'" json:"status"` // 设备状态（online、offline、error）
	LastSeen  time.Time `json:"last_seen"`                       // 最后通信时间
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// LinkageExecution 表示联动执行记录
type LinkageExecution struct {
	ID           string    `gorm:"primaryKey" json:"id"`              // 执行记录ID
	AlertID      int64     `gorm:"index" json:"alert_id"`             // 关联的告警ID
	RuleID       string    `gorm:"index" json:"rule_id"`              // 执行的规则ID
	DeviceID     string    `gorm:"index" json:"device_id"`            // 目标设备ID
	Action       string    `gorm:"not null" json:"action"`            // 执行的动作
	Status       string    `gorm:"not null" json:"status"`            // 执行状态（success、failed、timeout）
	ErrorMessage string    `json:"error_message"`                     // 错误信息
	ResponseData string    `json:"response_data"`                     // 设备响应数据
	Duration     int64     `json:"duration"`                          // 执行耗时（毫秒）
	ExecutedAt   time.Time `gorm:"autoCreateTime" json:"executed_at"` // 执行时间
}

// User 表示系统用户
type User struct {
	ID           int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	Username     string    `gorm:"not null;uniqueIndex" json:"username"`
	Password     string    `gorm:"not null" json:"-"`
	Name         string    `json:"name"`
	Email        string    `json:"email"`
	Mobile       string    `json:"mobile"`
	Role         string    `gorm:"not null;default:'user'" json:"role"`     // admin, user
	Status       string    `gorm:"not null;default:'active'" json:"status"` // active, inactive
	LastLoginAt  time.Time `json:"last_login_at"`
	LastLoginIP  string    `json:"last_login_ip"`
	RefreshToken string    `json:"-"`
	CreatedAt    time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// HashPassword 对密码进行哈希处理
func (u *User) HashPassword() error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(u.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	u.Password = string(hashedPassword)
	return nil
}

// CheckPassword 验证密码是否正确
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

// NewDB 创建一个新的数据库连接
func NewDB(config *Config) (*DB, error) {
	if config.Path == "" {
		// 默认数据库路径
		dbDir := "./data"
		if err := os.MkdirAll(dbDir, 0755); err != nil {
			return nil, fmt.Errorf("创建数据库目录失败: %w", err)
		}
		config.Path = filepath.Join(dbDir, "ecp.db")
	}

	// 配置GORM
	gormConfig := &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
	}

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(config.Path), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("打开数据库连接失败: %w", err)
	}

	// 设置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库连接失败: %w", err)
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 设置simple库的DB
	sqls.SetDB(db)

	return &DB{DB: db}, nil
}

// InitSchema 初始化数据库表结构
func (db *DB) InitSchema() error {
	// 自动迁移表结构
	err := db.AutoMigrate(
		&Video{},
		&Algorithm{},
		&Alert{},
		&VideoAlgorithmBinding{},
		&LinkageRule{},
		&LinkageDevice{},
		&LinkageExecution{},
		&User{},
	)
	if err != nil {
		return fmt.Errorf("自动迁移表结构失败: %w", err)
	}

	// 检查是否需要创建默认管理员用户
	var count int64
	if err := db.Model(&User{}).Count(&count).Error; err != nil {
		return fmt.Errorf("检查用户数量失败: %w", err)
	}

	// 如果没有用户，创建默认管理员
	if count == 0 {
		adminUser := &User{
			Username: "admin",
			Password: "admin123",
			Name:     "系统管理员",
			Role:     "admin",
			Status:   "active",
		}

		// 密码加密
		if err := adminUser.HashPassword(); err != nil {
			return fmt.Errorf("加密管理员密码失败: %w", err)
		}

		if err := db.Create(adminUser).Error; err != nil {
			return fmt.Errorf("创建默认管理员失败: %w", err)
		}
	}

	return nil
}

// Close 关闭数据库连接
func (db *DB) Close() error {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}
	return sqlDB.Close()
}
