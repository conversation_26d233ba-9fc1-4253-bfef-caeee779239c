# 数据库模块

这个模块负责边缘计算平台的数据库操作，包括数据库连接、表结构初始化等功能。

## 功能

- 数据库连接管理
- 表结构初始化
- 提供数据库操作接口

## 数据库表结构

### 视频源表 (videos)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| name | TEXT | 视频源名称 |
| url | TEXT | 视频源地址 |
| description | TEXT | 视频源描述 |
| status | TEXT | 视频源状态 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 算法表 (algorithms)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| name | TEXT | 算法名称 |
| version | TEXT | 算法版本 |
| description | TEXT | 算法描述 |
| path | TEXT | 算法路径 |
| status | TEXT | 算法状态 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 告警记录表 (alerts)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| video_id | INTEGER | 视频源ID |
| video_name | TEXT | 视频源名称 |
| algorithm_id | INTEGER | 算法ID |
| algorithm_name | TEXT | 算法名称 |
| content | TEXT | 告警内容 |
| image_path | TEXT | 图片路径 |
| level | TEXT | 告警级别 |
| status | TEXT | 告警状态 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 使用方法

```go
// 创建数据库连接
dbConfig := &database.Config{
    Path: "./data/ecp.db",
}
db, err := database.NewDB(dbConfig)
if err != nil {
    log.Fatalf("连接数据库失败: %v", err)
}
defer db.Close()

// 初始化表结构
if err := db.InitSchema(); err != nil {
    log.Fatalf("初始化数据库表结构失败: %v", err)
}

// 执行查询
rows, err := db.Query("SELECT * FROM videos")
if err != nil {
    log.Fatalf("查询失败: %v", err)
}
defer rows.Close()
``` 