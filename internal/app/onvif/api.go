package onvif

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// API ONVIF API控制器
type API struct {
	service *Service
}

// NewAPI 创建ONVIF API实例
func NewAPI(service *Service) *API {
	return &API{
		service: service,
	}
}

// RegisterRoutes 注册路由
func (api *API) RegisterRoutes(r *gin.RouterGroup) {
	onvif := r.Group("/onvif")
	{
		onvif.GET("/discover", api.DiscoverDevices)       // 发现设备
		onvif.POST("/connect", api.ConnectDevice)         // 连接设备
		onvif.POST("/test", api.TestConnection)           // 测试连接
		onvif.POST("/stream-url", api.GetStreamURL)       // 获取流地址
		onvif.POST("/snapshot-url", api.GetSnapshotURL)   // 获取快照地址
		onvif.POST("/capabilities", api.GetCapabilities)  // 获取设备能力
		onvif.POST("/parse-url", api.ParseURL)            // 解析ONVIF URL
		onvif.POST("/generate-rtsp", api.GenerateRTSPURL) // 生成RTSP URL
	}
}

// DiscoverRequest 发现设备请求
type DiscoverRequest struct {
	Timeout int `json:"timeout" form:"timeout"` // 超时时间（秒）
}

// ConnectRequest 连接设备请求
type ConnectRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Host     string `json:"host" binding:"required"`
	Port     int    `json:"port"`
}

// URLRequest URL相关请求
type URLRequest struct {
	URL string `json:"url" binding:"required"`
}

// StreamURLRequest 获取流地址请求
type StreamURLRequest struct {
	Username     string `json:"username" binding:"required"`
	Password     string `json:"password" binding:"required"`
	Host         string `json:"host" binding:"required"`
	Port         int    `json:"port"`
	ProfileToken string `json:"profile_token" binding:"required"`
}

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// DiscoverDevices 发现ONVIF设备
func (api *API) DiscoverDevices(c *gin.Context) {
	var req DiscoverRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	timeout := 10 * time.Second
	if req.Timeout > 0 {
		timeout = time.Duration(req.Timeout) * time.Second
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout+5*time.Second)
	defer cancel()

	result, err := api.service.DiscoverDevices(ctx, timeout)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "发现设备失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "发现设备成功",
		Data:    result,
	})
}

// ConnectDevice 连接ONVIF设备
func (api *API) ConnectDevice(c *gin.Context) {
	var req ConnectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	if req.Port == 0 {
		req.Port = 80
	}

	creds := &ONVIFCredentials{
		Username: req.Username,
		Password: req.Password,
		Host:     req.Host,
		Port:     req.Port,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := api.service.ConnectDevice(ctx, creds)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "连接设备失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "连接设备成功",
		Data:    result,
	})
}

// TestConnection 测试ONVIF设备连接
func (api *API) TestConnection(c *gin.Context) {
	var req ConnectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	if req.Port == 0 {
		req.Port = 80
	}

	creds := &ONVIFCredentials{
		Username: req.Username,
		Password: req.Password,
		Host:     req.Host,
		Port:     req.Port,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err := api.service.TestConnection(ctx, creds)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "连接测试失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "连接测试成功",
		Data: map[string]interface{}{
			"status": "connected",
			"host":   req.Host,
			"port":   req.Port,
		},
	})
}

// GetStreamURL 获取流地址
func (api *API) GetStreamURL(c *gin.Context) {
	var req StreamURLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	if req.Port == 0 {
		req.Port = 80
	}

	creds := &ONVIFCredentials{
		Username: req.Username,
		Password: req.Password,
		Host:     req.Host,
		Port:     req.Port,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	streamURL, err := api.service.GetStreamURL(ctx, creds, req.ProfileToken)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取流地址失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "获取流地址成功",
		Data: map[string]interface{}{
			"stream_url":    streamURL,
			"profile_token": req.ProfileToken,
		},
	})
}

// GetSnapshotURL 获取快照地址
func (api *API) GetSnapshotURL(c *gin.Context) {
	var req StreamURLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	if req.Port == 0 {
		req.Port = 80
	}

	creds := &ONVIFCredentials{
		Username: req.Username,
		Password: req.Password,
		Host:     req.Host,
		Port:     req.Port,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	snapshotURL, err := api.service.GetSnapshotURL(ctx, creds, req.ProfileToken)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取快照地址失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "获取快照地址成功",
		Data: map[string]interface{}{
			"snapshot_url":  snapshotURL,
			"profile_token": req.ProfileToken,
		},
	})
}

// GetCapabilities 获取设备能力
func (api *API) GetCapabilities(c *gin.Context) {
	var req ConnectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	if req.Port == 0 {
		req.Port = 80
	}

	creds := &ONVIFCredentials{
		Username: req.Username,
		Password: req.Password,
		Host:     req.Host,
		Port:     req.Port,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	capabilities, err := api.service.GetDeviceCapabilities(ctx, creds)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取设备能力失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "获取设备能力成功",
		Data:    capabilities,
	})
}

// ParseURL 解析ONVIF URL
func (api *API) ParseURL(c *gin.Context) {
	var req URLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	creds, err := api.service.ParseONVIFURL(req.URL)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "解析URL失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "解析URL成功",
		Data:    creds,
	})
}

// GenerateRTSPURL 从ONVIF URL生成RTSP URL
func (api *API) GenerateRTSPURL(c *gin.Context) {
	var req URLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	rtspURL, err := api.service.GenerateRTSPURL(ctx, req.URL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "生成RTSP URL失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "生成RTSP URL成功",
		Data: map[string]interface{}{
			"onvif_url": req.URL,
			"rtsp_url":  rtspURL,
		},
	})
}

// Health ONVIF服务健康检查
func (api *API) Health(c *gin.Context) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "ONVIF服务正常",
		Data: map[string]interface{}{
			"service": "onvif",
			"status":  "healthy",
			"time":    time.Now().Format("2006-01-02 15:04:05"),
		},
	})
}
