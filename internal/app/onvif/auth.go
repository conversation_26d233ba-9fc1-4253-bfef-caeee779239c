package onvif

import (
	"context"
	"crypto/rand"
	"crypto/sha1"
	"encoding/base64"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"time"
)

// AuthMethod 认证方式枚举
type AuthMethod int

const (
	AuthMethodWSecurity AuthMethod = iota // WS-Security 摘要认证
	AuthMethodBasic                       // HTTP Basic 认证
	AuthMethodNone                        // 无认证
)

// AuthConfig 认证配置
type AuthConfig struct {
	Username      string
	Password      string
	PreferredAuth AuthMethod
	AllowFallback bool
}

// AuthManager 认证管理器
type AuthManager struct {
	config     *AuthConfig
	httpClient *http.Client
}

// NewAuthManager 创建认证管理器
func NewAuthManager(username, password string) *AuthManager {
	// 创建带有自定义传输层的HTTP客户端
	transport := &http.Transport{
		DialContext: (&net.Dialer{
			Timeout:   10 * time.Second,
			KeepAlive: 0, // 禁用 Keep-Alive
		}).DialContext,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		ResponseHeaderTimeout: 10 * time.Second,
		DisableKeepAlives:     true,  // 禁用连接复用
		ForceAttemptHTTP2:     false, // 禁用 HTTP/2
	}

	return &AuthManager{
		config: &AuthConfig{
			Username:      username,
			Password:      password,
			PreferredAuth: AuthMethodWSecurity,
			AllowFallback: true,
		},
		httpClient: &http.Client{
			Transport: transport,
			Timeout:   30 * time.Second,
		},
	}
}

// SetAuthPreference 设置认证偏好
func (am *AuthManager) SetAuthPreference(method AuthMethod, allowFallback bool) {
	am.config.PreferredAuth = method
	am.config.AllowFallback = allowFallback
}

// AuthenticatedRequest 执行带认证的请求
func (am *AuthManager) AuthenticatedRequest(ctx context.Context, url, soapBody string) ([]byte, error) {
	// 定义认证方法的尝试顺序
	var authMethods []AuthMethod

	if am.config.Username == "" || am.config.Password == "" {
		// 没有认证信息，尝试无认证
		authMethods = []AuthMethod{AuthMethodNone}
	} else {
		// 根据偏好设置尝试顺序
		authMethods = []AuthMethod{am.config.PreferredAuth}

		if am.config.AllowFallback {
			// 添加其他认证方法作为后备
			switch am.config.PreferredAuth {
			case AuthMethodWSecurity:
				authMethods = append(authMethods, AuthMethodBasic)
			case AuthMethodBasic:
				authMethods = append(authMethods, AuthMethodWSecurity)
			}
		}
	}

	var lastErr error

	// 依次尝试不同的认证方法
	for i, method := range authMethods {
		// 在每次请求之前添加延迟，避免请求过于频繁
		if i > 0 {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(2 * time.Second): // 添加2秒延迟
			}
		}

		respBody, err := am.tryAuthentication(ctx, url, soapBody, method)
		if err == nil {
			// 成功，记录使用的认证方法
			if i > 0 {
				fmt.Printf("认证成功，使用方法: %s (第%d次尝试)\n", am.authMethodName(method), i+1)
			}
			return respBody, nil
		}

		lastErr = err

		// 如果是401错误且允许降级，继续尝试下一种方法
		if strings.Contains(err.Error(), "401") && am.config.AllowFallback {
			fmt.Printf("认证方法 %s 失败(401)，尝试下一种方法\n", am.authMethodName(method))
			continue
		}

		// 如果是EOF或连接重置错误，等待更长时间后重试
		if strings.Contains(err.Error(), "EOF") ||
			strings.Contains(err.Error(), "connection reset") ||
			strings.Contains(err.Error(), "forcibly closed") {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(5 * time.Second): // 添加5秒延迟
				continue
			}
		}

		// 其他错误类型不进行重试
		break
	}

	return nil, fmt.Errorf("所有认证方法都失败了，最后错误: %w", lastErr)
}

// tryAuthentication 尝试使用指定的认证方法
func (am *AuthManager) tryAuthentication(ctx context.Context, url, soapBody string, method AuthMethod) ([]byte, error) {
	var reqBody string

	switch method {
	case AuthMethodWSecurity:
		reqBody = am.buildWSSecurityRequest(soapBody)
	case AuthMethodBasic:
		reqBody = am.buildBasicRequest(soapBody)
	case AuthMethodNone:
		reqBody = am.buildNoAuthRequest(soapBody)
	default:
		return nil, fmt.Errorf("不支持的认证方法: %d", method)
	}

	// 最大重试次数
	maxRetries := 3
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			fmt.Printf("DEBUG: 第%d次重试...\n", i+1)
			// 重试前等待一段时间
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(time.Duration(i) * time.Second):
			}
		}

		// 在实际发送请求前打印请求体
		fmt.Printf("DEBUG: 正在发送 %s 认证请求到端点: %s\n", am.authMethodName(method), url)
		fmt.Printf("DEBUG: 请求体:\n%s\n", reqBody)

		req, err := http.NewRequestWithContext(ctx, "POST", url, strings.NewReader(reqBody))
		if err != nil {
			lastErr = fmt.Errorf("创建请求失败: %w", err)
			continue
		}

		req.Header.Set("Content-Type", "application/soap+xml; charset=utf-8")
		req.Header.Set("SOAPAction", "")

		// 对于Basic认证，同时设置HTTP头
		if method == AuthMethodBasic && am.config.Username != "" {
			req.SetBasicAuth(am.config.Username, am.config.Password)
			fmt.Printf("DEBUG: 已设置Basic认证头\n")
		}

		resp, err := am.httpClient.Do(req)
		if err != nil {
			fmt.Printf("DEBUG: HTTP请求失败: %v\n", err)
			lastErr = fmt.Errorf("HTTP请求失败: %w", err)
			// 如果是EOF或连接被重置，继续重试
			if err == io.EOF || strings.Contains(err.Error(), "connection reset") ||
				strings.Contains(err.Error(), "forcibly closed") {
				continue
			}
			return nil, lastErr
		}

		fmt.Printf("DEBUG: HTTP请求成功，状态码: %d\n", resp.StatusCode)

		respBody, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			fmt.Printf("DEBUG: 读取响应体失败: %v\n", err)
			lastErr = fmt.Errorf("读取响应失败: %w", err)
			continue
		}

		fmt.Printf("DEBUG: 响应状态码: %d\n", resp.StatusCode)
		fmt.Printf("DEBUG: 响应内容:\n%s\n", string(respBody))

		if resp.StatusCode != http.StatusOK {
			lastErr = fmt.Errorf("HTTP状态码: %d, 认证方法: %s, 响应: %s",
				resp.StatusCode, am.authMethodName(method), string(respBody))
			// 如果是401或403，不再重试当前方法，但可以尝试其他认证方法
			if resp.StatusCode == http.StatusUnauthorized || resp.StatusCode == http.StatusForbidden {
				fmt.Printf("DEBUG: 认证失败，将尝试下一种认证方法\n")
				return nil, lastErr
			}
			continue
		}

		return respBody, nil
	}

	return nil, fmt.Errorf("重试%d次后仍然失败: %v", maxRetries, lastErr)
}

// buildWSSecurityRequest 构建WS-Security认证请求
func (am *AuthManager) buildWSSecurityRequest(body string) string {
	action, bodyContent := am.parseSOAPBody(body)

	// 确定命名空间
	var namespace string
	if strings.HasPrefix(action, "tds:") {
		namespace = `xmlns:tds="http://www.onvif.org/ver10/device/wsdl"`
	} else if strings.HasPrefix(action, "trt:") {
		namespace = `xmlns:trt="http://www.onvif.org/ver10/media/wsdl"`
	} else if action == "GetDeviceInformation" {
		action = "tds:GetDeviceInformation"
		namespace = `xmlns:tds="http://www.onvif.org/ver10/device/wsdl"`
	}

	// 构建WS-Security认证头
	var securityHeader string
	if am.config.Username != "" && am.config.Password != "" {
		nonce := generateNonce()
		created := time.Now().UTC().Format("2006-01-02T15:04:05.000Z")
		digest := generatePasswordDigest(nonce, created, am.config.Password)

		securityHeader = fmt.Sprintf(`
		<s:Header>
			<Security s:mustUnderstand="1" xmlns="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
				<UsernameToken xmlns="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
					<Username>%s</Username>
					<Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">%s</Password>
					<Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">%s</Nonce>
					<Created xmlns="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">%s</Created>
				</UsernameToken>
			</Security>
		</s:Header>`, am.config.Username, digest, nonce, created)
	}

	return fmt.Sprintf(`<?xml version="1.0" encoding="UTF-8"?>
	<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" %s>%s
		<s:Body>
			<%s>%s</%s>
		</s:Body>
	</s:Envelope>`, namespace, securityHeader, action, bodyContent, action)
}

// buildBasicRequest 构建HTTP Basic认证请求
func (am *AuthManager) buildBasicRequest(body string) string {
	action, bodyContent := am.parseSOAPBody(body)

	// 确定命名空间
	var namespace string
	if strings.HasPrefix(action, "tds:") {
		namespace = `xmlns:tds="http://www.onvif.org/ver10/device/wsdl"`
	} else if strings.HasPrefix(action, "trt:") {
		namespace = `xmlns:trt="http://www.onvif.org/ver10/media/wsdl"`
	}

	// 不包含WS-Security头，仅依赖HTTP Basic认证
	return fmt.Sprintf(`<?xml version="1.0" encoding="UTF-8"?>
	<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" %s>
		<s:Body>
			<%s>%s</%s>
		</s:Body>
	</s:Envelope>`, namespace, action, bodyContent, action)
}

// buildNoAuthRequest 构建无认证请求
func (am *AuthManager) buildNoAuthRequest(body string) string {
	return am.buildBasicRequest(body) // 结构相同，只是不设置认证信息
}

// parseSOAPBody 解析SOAP body内容，提取action和body
func (am *AuthManager) parseSOAPBody(body string) (action, bodyContent string) {
	// 根据传入的操作名称和参数构建请求
	if strings.Contains(body, "GetDeviceInformation") {
		return "tds:GetDeviceInformation", ""
	} else if strings.Contains(body, "GetCapabilities") {
		return "tds:GetCapabilities", `<tds:Category>All</tds:Category>`
	} else if strings.Contains(body, "GetProfiles") {
		return "trt:GetProfiles", ""
	} else if strings.Contains(body, "GetStreamUri:") {
		// 解析profile token: GetStreamUri:profileToken
		parts := strings.Split(body, ":")
		if len(parts) >= 2 {
			profileToken := parts[1]
			bodyContent := fmt.Sprintf(`<trt:StreamSetup>
				<trt:Stream xmlns:tt="http://www.onvif.org/ver10/schema">tt:RTP-Unicast</trt:Stream>
				<trt:Transport>
					<trt:Protocol>RTSP</trt:Protocol>
				</trt:Transport>
			</trt:StreamSetup>
			<trt:ProfileToken>%s</trt:ProfileToken>`, profileToken)
			return "trt:GetStreamUri", bodyContent
		}
	} else if strings.Contains(body, "GetSnapshotUri:") {
		// 解析profile token: GetSnapshotUri:profileToken
		parts := strings.Split(body, ":")
		if len(parts) >= 2 {
			profileToken := parts[1]
			bodyContent := fmt.Sprintf(`<trt:ProfileToken>%s</trt:ProfileToken>`, profileToken)
			return "trt:GetSnapshotUri", bodyContent
		}
	}
	// 默认情况
	return "tds:GetDeviceInformation", ""
}

// authMethodName 获取认证方法名称
func (am *AuthManager) authMethodName(method AuthMethod) string {
	switch method {
	case AuthMethodWSecurity:
		return "WS-Security"
	case AuthMethodBasic:
		return "HTTP Basic"
	case AuthMethodNone:
		return "无认证"
	default:
		return "未知"
	}
}

// generateNonce 生成随机nonce
func generateNonce() string {
	nonce := make([]byte, 16)
	rand.Read(nonce)
	return base64.StdEncoding.EncodeToString(nonce)
}

// generatePasswordDigest 生成WS-Security密码摘要
func generatePasswordDigest(nonce, created, password string) string {
	nonceBytes, _ := base64.StdEncoding.DecodeString(nonce)

	// PasswordDigest = Base64 ( SHA-1 ( nonce + created + password ) )
	h := sha1.New()
	h.Write(nonceBytes)
	h.Write([]byte(created))
	h.Write([]byte(password))

	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}
