package onvif

import (
	"context"
	"encoding/xml"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// ONVIFClient ONVIF客户端
type ONVIFClient struct {
	Username    string
	Password    string
	DeviceURL   string
	MediaURL    string
	HTTPClient  *http.Client
	AuthManager *AuthManager // 认证管理器
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	Manufacturer    string `xml:"Manufacturer"`
	Model           string `xml:"Model"`
	FirmwareVersion string `xml:"FirmwareVersion"`
	SerialNumber    string `xml:"SerialNumber"`
	HardwareId      string `xml:"HardwareId"`
}

// Profile 媒体配置
type Profile struct {
	Name                      string                    `xml:"name,attr"`
	Token                     string                    `xml:"token,attr"`
	VideoSourceConfiguration  VideoSourceConfiguration  `xml:"VideoSourceConfiguration"`
	VideoEncoderConfiguration VideoEncoderConfiguration `xml:"VideoEncoderConfiguration"`
}

// VideoSourceConfiguration 视频源配置
type VideoSourceConfiguration struct {
	Name        string `xml:"Name"`
	Token       string `xml:"token,attr"`
	SourceToken string `xml:"SourceToken"`
	Bounds      Bounds `xml:"Bounds"`
}

// VideoEncoderConfiguration 视频编码配置
type VideoEncoderConfiguration struct {
	Name        string      `xml:"Name"`
	Token       string      `xml:"token,attr"`
	Encoding    string      `xml:"Encoding"`
	Resolution  Resolution  `xml:"Resolution"`
	Quality     float64     `xml:"Quality"`
	RateControl RateControl `xml:"RateControl"`
}

// Bounds 边界
type Bounds struct {
	X      int `xml:"x,attr"`
	Y      int `xml:"y,attr"`
	Width  int `xml:"width,attr"`
	Height int `xml:"height,attr"`
}

// Resolution 分辨率
type Resolution struct {
	Width  int `xml:"Width"`
	Height int `xml:"Height"`
}

// RateControl 码率控制
type RateControl struct {
	FrameRateLimit   int `xml:"FrameRateLimit"`
	EncodingInterval int `xml:"EncodingInterval"`
	BitrateLimit     int `xml:"BitrateLimit"`
}

// StreamURI 流地址
type StreamURI struct {
	URI string `xml:"Uri"`
}

// MediaURI 媒体地址响应
type MediaURI struct {
	URI string `xml:"Uri"`
}

// NewONVIFClient 创建ONVIF客户端
func NewONVIFClient(deviceURL, username, password string) *ONVIFClient {
	return &ONVIFClient{
		Username:    username,
		Password:    password,
		DeviceURL:   deviceURL,
		HTTPClient:  &http.Client{Timeout: 30 * time.Second},
		AuthManager: NewAuthManager(username, password),
	}
}

// GetDeviceInformation 获取设备信息
func (c *ONVIFClient) GetDeviceInformation(ctx context.Context) (*DeviceInfo, error) {
	// 使用认证管理器发送请求
	resp, err := c.AuthManager.AuthenticatedRequest(ctx, c.DeviceURL, "GetDeviceInformation")
	if err != nil {
		return nil, fmt.Errorf("SOAP请求失败: %w", err)
	}

	var envelope struct {
		XMLName xml.Name `xml:"Envelope"`
		Body    struct {
			XMLName                      xml.Name `xml:"Body"`
			GetDeviceInformationResponse struct {
				XMLName         xml.Name `xml:"GetDeviceInformationResponse"`
				Manufacturer    string   `xml:"Manufacturer"`
				Model           string   `xml:"Model"`
				FirmwareVersion string   `xml:"FirmwareVersion"`
				SerialNumber    string   `xml:"SerialNumber"`
				HardwareId      string   `xml:"HardwareId"`
			} `xml:"http://www.onvif.org/ver10/device/wsdl GetDeviceInformationResponse"`
		} `xml:"Body"`
	}

	if err := xml.Unmarshal(resp, &envelope); err != nil {
		// 尝试使用不同的命名空间
		var envelope2 struct {
			XMLName xml.Name `xml:"Envelope"`
			Body    struct {
				XMLName                      xml.Name `xml:"Body"`
				GetDeviceInformationResponse struct {
					XMLName         xml.Name `xml:"GetDeviceInformationResponse"`
					Manufacturer    string   `xml:"Manufacturer"`
					Model           string   `xml:"Model"`
					FirmwareVersion string   `xml:"FirmwareVersion"`
					SerialNumber    string   `xml:"SerialNumber"`
					HardwareId      string   `xml:"HardwareId"`
				} `xml:"GetDeviceInformationResponse"`
			} `xml:"Body"`
		}

		if err2 := xml.Unmarshal(resp, &envelope2); err2 != nil {
			return nil, fmt.Errorf("解析响应失败: %w, 响应内容: %s", err, string(resp))
		}

		// 使用第二种解析结果
		return &DeviceInfo{
			Manufacturer:    envelope2.Body.GetDeviceInformationResponse.Manufacturer,
			Model:           envelope2.Body.GetDeviceInformationResponse.Model,
			FirmwareVersion: envelope2.Body.GetDeviceInformationResponse.FirmwareVersion,
			SerialNumber:    envelope2.Body.GetDeviceInformationResponse.SerialNumber,
			HardwareId:      envelope2.Body.GetDeviceInformationResponse.HardwareId,
		}, nil
	}

	return &DeviceInfo{
		Manufacturer:    envelope.Body.GetDeviceInformationResponse.Manufacturer,
		Model:           envelope.Body.GetDeviceInformationResponse.Model,
		FirmwareVersion: envelope.Body.GetDeviceInformationResponse.FirmwareVersion,
		SerialNumber:    envelope.Body.GetDeviceInformationResponse.SerialNumber,
		HardwareId:      envelope.Body.GetDeviceInformationResponse.HardwareId,
	}, nil
}

// GetCapabilities 获取设备能力
func (c *ONVIFClient) GetCapabilities(ctx context.Context) error {
	// 构建SOAP请求体
	soapBody := `<tds:GetCapabilities><tds:Category>All</tds:Category></tds:GetCapabilities>`

	// 使用认证管理器发送请求
	resp, err := c.AuthManager.AuthenticatedRequest(ctx, c.DeviceURL, soapBody)
	if err != nil {
		return fmt.Errorf("获取设备能力失败: %w", err)
	}

	var envelope struct {
		Body struct {
			GetCapabilitiesResponse struct {
				Capabilities struct {
					Media struct {
						XAddr string `xml:"XAddr"`
					} `xml:"Media"`
				} `xml:"Capabilities"`
			} `xml:"GetCapabilitiesResponse"`
		} `xml:"Body"`
	}

	if err := xml.Unmarshal(resp, &envelope); err != nil {
		return fmt.Errorf("解析能力响应失败: %w", err)
	}

	mediaURL := envelope.Body.GetCapabilitiesResponse.Capabilities.Media.XAddr
	fmt.Printf("DEBUG: 原始媒体URL: %s\n", mediaURL)

	// 使用工具函数修复媒体URL
	c.MediaURL = FixMediaURL(c.DeviceURL, mediaURL)
	fmt.Printf("DEBUG: 修复后的媒体URL: %s\n", c.MediaURL)
	return nil
}

// GetProfiles 获取媒体配置文件
func (c *ONVIFClient) GetProfiles(ctx context.Context) ([]Profile, error) {
	if c.MediaURL == "" {
		if err := c.GetCapabilities(ctx); err != nil {
			return nil, err
		}
	}

	// 使用认证管理器发送请求
	resp, err := c.AuthManager.AuthenticatedRequest(ctx, c.MediaURL, "GetProfiles")
	if err != nil {
		return nil, fmt.Errorf("获取配置文件失败: %w", err)
	}

	var envelope struct {
		Body struct {
			GetProfilesResponse struct {
				Profiles []Profile `xml:"Profiles"`
			} `xml:"GetProfilesResponse"`
		} `xml:"Body"`
	}

	if err := xml.Unmarshal(resp, &envelope); err != nil {
		return nil, fmt.Errorf("解析配置文件响应失败: %w", err)
	}

	return envelope.Body.GetProfilesResponse.Profiles, nil
}

// GetStreamURI 获取流地址
func (c *ONVIFClient) GetStreamURI(ctx context.Context, profileToken string) (string, error) {
	if c.MediaURL == "" {
		if err := c.GetCapabilities(ctx); err != nil {
			return "", err
		}
	}

	// 使用认证管理器发送请求，传递profile token
	soapAction := fmt.Sprintf("GetStreamUri:%s", profileToken)
	resp, err := c.AuthManager.AuthenticatedRequest(ctx, c.MediaURL, soapAction)
	if err != nil {
		return "", fmt.Errorf("获取流地址失败: %w", err)
	}

	var envelope struct {
		Body struct {
			GetStreamUriResponse struct {
				MediaUri struct {
					Uri string `xml:"Uri"`
				} `xml:"MediaUri"`
			} `xml:"GetStreamUriResponse"`
		} `xml:"Body"`
	}

	if err := xml.Unmarshal(resp, &envelope); err != nil {
		return "", fmt.Errorf("解析流地址响应失败: %w", err)
	}

	return envelope.Body.GetStreamUriResponse.MediaUri.Uri, nil
}

// GetSnapshotURI 获取快照地址
func (c *ONVIFClient) GetSnapshotURI(ctx context.Context, profileToken string) (string, error) {
	if c.MediaURL == "" {
		if err := c.GetCapabilities(ctx); err != nil {
			return "", err
		}
	}

	// 使用认证管理器发送请求，传递profile token
	soapAction := fmt.Sprintf("GetSnapshotUri:%s", profileToken)
	resp, err := c.AuthManager.AuthenticatedRequest(ctx, c.MediaURL, soapAction)
	if err != nil {
		return "", fmt.Errorf("获取快照地址失败: %w", err)
	}

	var envelope struct {
		Body struct {
			GetSnapshotUriResponse struct {
				MediaUri struct {
					Uri string `xml:"Uri"`
				} `xml:"MediaUri"`
			} `xml:"GetSnapshotUriResponse"`
		} `xml:"Body"`
	}

	if err := xml.Unmarshal(resp, &envelope); err != nil {
		return "", fmt.Errorf("解析快照地址响应失败: %w", err)
	}

	return envelope.Body.GetSnapshotUriResponse.MediaUri.Uri, nil
}

// DiscoverDevices 发现ONVIF设备
func DiscoverDevices(ctx context.Context, timeout time.Duration) ([]string, error) {
	// WS-Discovery 消息
	probe := `<?xml version="1.0" encoding="UTF-8"?>
	<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:a="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:d="http://schemas.xmlsoap.org/ws/2005/04/discovery" xmlns:dn="http://www.onvif.org/ver10/network/wsdl">
		<s:Header>
			<a:Action s:mustUnderstand="1">http://schemas.xmlsoap.org/ws/2005/04/discovery/Probe</a:Action>
			<a:MessageID>urn:uuid:1e1c4cbd-2b51-4ed5-b5d3-f1aa34561bc5</a:MessageID>
			<a:ReplyTo s:mustUnderstand="1">
				<a:Address>http://schemas.xmlsoap.org/ws/2004/08/addressing/role/anonymous</a:Address>
			</a:ReplyTo>
			<a:To s:mustUnderstand="1">urn:schemas-xmlsoap-org:ws:2005:04:discovery</a:To>
		</s:Header>
		<s:Body>
			<d:Probe>
				<d:Types>dn:NetworkVideoTransmitter</d:Types>
			</d:Probe>
		</s:Body>
	</s:Envelope>`

	// 创建UDP连接用于多播
	addr, err := net.ResolveUDPAddr("udp4", "***************:3702")
	if err != nil {
		return nil, fmt.Errorf("解析多播地址失败: %w", err)
	}

	conn, err := net.DialUDP("udp4", nil, addr)
	if err != nil {
		return nil, fmt.Errorf("创建UDP连接失败: %w", err)
	}
	defer conn.Close()

	// 监听响应
	listenAddr, err := net.ResolveUDPAddr("udp4", ":0")
	if err != nil {
		return nil, fmt.Errorf("解析监听地址失败: %w", err)
	}

	listener, err := net.ListenUDP("udp4", listenAddr)
	if err != nil {
		return nil, fmt.Errorf("创建监听器失败: %w", err)
	}
	defer listener.Close()

	// 发送探测消息
	_, err = conn.Write([]byte(probe))
	if err != nil {
		return nil, fmt.Errorf("发送探测消息失败: %w", err)
	}

	// 接收响应
	var devices []string
	deadline := time.Now().Add(timeout)
	listener.SetReadDeadline(deadline)

	for time.Now().Before(deadline) {
		buffer := make([]byte, 1024)
		n, _, err := listener.ReadFromUDP(buffer)
		if err != nil {
			if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
				break
			}
			continue
		}

		response := string(buffer[:n])
		if strings.Contains(response, "NetworkVideoTransmitter") {
			// 解析设备地址
			if deviceURL := parseDeviceURL(response); deviceURL != "" {
				devices = append(devices, deviceURL)
			}
		}
	}

	return devices, nil
}

// SetAuthPreference 设置认证偏好
func (c *ONVIFClient) SetAuthPreference(method AuthMethod, allowFallback bool) {
	c.AuthManager.SetAuthPreference(method, allowFallback)
}

// parseDeviceURL 从WS-Discovery响应中解析设备URL
func parseDeviceURL(response string) string {
	// 简化的URL解析，实际实现应该使用XML解析
	if start := strings.Index(response, "<d:XAddrs>"); start != -1 {
		start += len("<d:XAddrs>")
		if end := strings.Index(response[start:], "</d:XAddrs>"); end != -1 {
			deviceURL := response[start : start+end]
			if u, err := url.Parse(deviceURL); err == nil {
				return u.String()
			}
		}
	}
	return ""
}

// ContinuousMove 执行连续移动PTZ控制
func (c *ONVIFClient) ContinuousMove(ctx context.Context, profileToken string, panSpeed, tiltSpeed, zoomSpeed float64) error {
	// 构建SOAP请求体
	soapBody := fmt.Sprintf(`<tptz:ContinuousMove xmlns:tptz="http://www.onvif.org/ver20/ptz/wsdl">
		<tptz:ProfileToken>%s</tptz:ProfileToken>
		<tptz:Velocity>
			<tt:PanTilt x="%f" y="%f" xmlns:tt="http://www.onvif.org/ver10/schema"/>
			<tt:Zoom x="%f" xmlns:tt="http://www.onvif.org/ver10/schema"/>
		</tptz:Velocity>
	</tptz:ContinuousMove>`, profileToken, panSpeed, tiltSpeed, zoomSpeed)

	// 发送请求到PTZ服务
	ptzServiceURL := strings.Replace(c.DeviceURL, "device_service", "ptz_service", 1)

	// 使用认证管理器发送请求
	_, err := c.AuthManager.AuthenticatedRequest(ctx, ptzServiceURL, soapBody)
	if err != nil {
		return fmt.Errorf("PTZ控制失败: %w", err)
	}

	return nil
}

// Stop 停止PTZ移动
func (c *ONVIFClient) Stop(ctx context.Context, profileToken string) error {
	// 构建SOAP请求体
	soapBody := fmt.Sprintf(`<tptz:Stop xmlns:tptz="http://www.onvif.org/ver20/ptz/wsdl">
		<tptz:ProfileToken>%s</tptz:ProfileToken>
		<tptz:PanTilt>true</tptz:PanTilt>
		<tptz:Zoom>true</tptz:Zoom>
	</tptz:Stop>`, profileToken)

	// 发送请求到PTZ服务
	ptzServiceURL := strings.Replace(c.DeviceURL, "device_service", "ptz_service", 1)

	// 使用认证管理器发送请求
	_, err := c.AuthManager.AuthenticatedRequest(ctx, ptzServiceURL, soapBody)
	if err != nil {
		return fmt.Errorf("停止PTZ失败: %w", err)
	}

	return nil
}

// SetPreset 设置预置点
func (c *ONVIFClient) SetPreset(ctx context.Context, profileToken string, presetName string, presetID int) error {
	// 构建SOAP请求体
	soapBody := fmt.Sprintf(`<tptz:SetPreset xmlns:tptz="http://www.onvif.org/ver20/ptz/wsdl">
		<tptz:ProfileToken>%s</tptz:ProfileToken>
		<tptz:PresetName>%s</tptz:PresetName>
		<tptz:PresetToken>%d</tptz:PresetToken>
	</tptz:SetPreset>`, profileToken, presetName, presetID)

	// 发送请求到PTZ服务
	ptzServiceURL := strings.Replace(c.DeviceURL, "device_service", "ptz_service", 1)

	// 使用认证管理器发送请求
	_, err := c.AuthManager.AuthenticatedRequest(ctx, ptzServiceURL, soapBody)
	if err != nil {
		return fmt.Errorf("设置预置点失败: %w", err)
	}

	return nil
}

// GotoPreset 调用预置点
func (c *ONVIFClient) GotoPreset(ctx context.Context, profileToken string, presetID int) error {
	// 构建SOAP请求体
	soapBody := fmt.Sprintf(`<tptz:GotoPreset xmlns:tptz="http://www.onvif.org/ver20/ptz/wsdl">
		<tptz:ProfileToken>%s</tptz:ProfileToken>
		<tptz:PresetToken>%d</tptz:PresetToken>
	</tptz:GotoPreset>`, profileToken, presetID)

	// 发送请求到PTZ服务
	ptzServiceURL := strings.Replace(c.DeviceURL, "device_service", "ptz_service", 1)

	// 使用认证管理器发送请求
	_, err := c.AuthManager.AuthenticatedRequest(ctx, ptzServiceURL, soapBody)
	if err != nil {
		return fmt.Errorf("调用预置点失败: %w", err)
	}

	return nil
}

// RemovePreset 删除预置点
func (c *ONVIFClient) RemovePreset(ctx context.Context, profileToken string, presetID int) error {
	// 构建SOAP请求体
	soapBody := fmt.Sprintf(`<tptz:RemovePreset xmlns:tptz="http://www.onvif.org/ver20/ptz/wsdl">
		<tptz:ProfileToken>%s</tptz:ProfileToken>
		<tptz:PresetToken>%d</tptz:PresetToken>
	</tptz:RemovePreset>`, profileToken, presetID)

	// 发送请求到PTZ服务
	ptzServiceURL := strings.Replace(c.DeviceURL, "device_service", "ptz_service", 1)

	// 使用认证管理器发送请求
	_, err := c.AuthManager.AuthenticatedRequest(ctx, ptzServiceURL, soapBody)
	if err != nil {
		return fmt.Errorf("删除预置点失败: %w", err)
	}

	return nil
}
