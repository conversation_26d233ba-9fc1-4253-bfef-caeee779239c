package onvif

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// ConnectionTester ONVIF连接测试工具
type ConnectionTester struct {
	Timeout    time.Duration
	MaxRetries int
}

// NewConnectionTester 创建连接测试器
func NewConnectionTester() *ConnectionTester {
	return &ConnectionTester{
		Timeout:    10 * time.Second,
		MaxRetries: 3,
	}
}

// TestNetworkConnectivity 测试网络连通性
func (ct *ConnectionTester) TestNetworkConnectivity(host string, port int) error {
	address := fmt.Sprintf("%s:%d", host, port)

	for i := 0; i < ct.MaxRetries; i++ {
		conn, err := net.DialTimeout("tcp", address, ct.Timeout)
		if err != nil {
			if i == ct.MaxRetries-1 {
				return fmt.Errorf("网络连接失败(重试%d次): %w", ct.MaxRetries, err)
			}
			time.Sleep(time.Second * time.Duration(i+1))
			continue
		}
		conn.Close()
		return nil
	}

	return fmt.Errorf("连接测试失败")
}

// TestONVIFEndpoint 测试ONVIF端点
func (ct *ConnectionTester) TestONVIFEndpoint(endpoint, username, password string) error {
	client := &http.Client{Timeout: ct.Timeout}

	// 简单的GetDeviceInformation请求
	soapRequest := fmt.Sprintf(`<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:tds="http://www.onvif.org/ver10/device/wsdl">
	<s:Header>
		<Security s:mustUnderstand="1" xmlns="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
			<UsernameToken>
				<Username>%s</Username>
				<Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">%s</Password>
			</UsernameToken>
		</Security>
	</s:Header>
	<s:Body>
		<tds:GetDeviceInformation/>
	</s:Body>
</s:Envelope>`, username, password)

	req, err := http.NewRequest("POST", endpoint, strings.NewReader(soapRequest))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/soap+xml; charset=utf-8")
	req.Header.Set("SOAPAction", "")
	req.SetBasicAuth(username, password)

	ctx, cancel := context.WithTimeout(context.Background(), ct.Timeout)
	defer cancel()
	req = req.WithContext(ctx)

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("ONVIF请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("ONVIF响应错误: HTTP %d", resp.StatusCode)
	}

	return nil
}

// DiscoverONVIFEndpoints 发现可用的ONVIF端点
func (ct *ConnectionTester) DiscoverONVIFEndpoints(host string, port int, username, password string) []string {
	endpoints := []string{
		fmt.Sprintf("http://%s:%d/onvif/device_service", host, port),
		fmt.Sprintf("http://%s:%d/onvif/device", host, port),
		fmt.Sprintf("http://%s:%d/device_service", host, port),
		fmt.Sprintf("http://%s:%d/onvif_device_mgmt", host, port),
		fmt.Sprintf("http://%s:%d/MediaService", host, port),
		fmt.Sprintf("http://%s:%d", host, port),
	}

	var workingEndpoints []string

	for _, endpoint := range endpoints {
		if err := ct.TestONVIFEndpoint(endpoint, username, password); err == nil {
			workingEndpoints = append(workingEndpoints, endpoint)
		}
	}

	return workingEndpoints
}

// isPrivateIP 判断是否为内网IP
func isPrivateIP(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	// 检查是否为内网IP地址范围
	privateIPBlocks := []string{
		"10.0.0.0/8",
		"**********/12",
		"***********/16",
		"***********/16",
		"*********/8",
	}

	for _, block := range privateIPBlocks {
		_, ipnet, err := net.ParseCIDR(block)
		if err != nil {
			continue
		}
		if ipnet.Contains(ip) {
			return true
		}
	}
	return false
}

// FixMediaURL 修复媒体服务URL中的内网地址问题
func FixMediaURL(deviceURL, mediaURL string) string {
	if mediaURL == "" {
		return mediaURL
	}

	mediaURLParsed, err := url.Parse(mediaURL)
	if err != nil {
		return mediaURL
	}

	deviceURLParsed, err := url.Parse(deviceURL)
	if err != nil {
		return mediaURL
	}

	// 检查是否为内网IP
	if isPrivateIP(mediaURLParsed.Hostname()) {
		// 使用设备URL的主机地址和端口，但保持媒体URL的路径
		mediaURLParsed.Scheme = deviceURLParsed.Scheme
		mediaURLParsed.Host = deviceURLParsed.Host
		return mediaURLParsed.String()
	}

	return mediaURL
}

// RetryableONVIFClient 带重试机制的ONVIF客户端包装器
type RetryableONVIFClient struct {
	*ONVIFClient
	tester *ConnectionTester
}

// NewRetryableONVIFClient 创建带重试机制的ONVIF客户端
func NewRetryableONVIFClient(deviceURL, username, password string) *RetryableONVIFClient {
	return &RetryableONVIFClient{
		ONVIFClient: NewONVIFClient(deviceURL, username, password),
		tester:      NewConnectionTester(),
	}
}

// GetDeviceInformationWithRetry 带重试的获取设备信息
func (rc *RetryableONVIFClient) GetDeviceInformationWithRetry(ctx context.Context) (*DeviceInfo, error) {
	var lastErr error

	for i := 0; i < rc.tester.MaxRetries; i++ {
		deviceInfo, err := rc.GetDeviceInformation(ctx)
		if err == nil {
			return deviceInfo, nil
		}

		lastErr = err
		if i < rc.tester.MaxRetries-1 {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(time.Second * time.Duration(i+1)):
				// 重试前等待
			}
		}
	}

	return nil, fmt.Errorf("重试%d次后仍然失败: %w", rc.tester.MaxRetries, lastErr)
}
