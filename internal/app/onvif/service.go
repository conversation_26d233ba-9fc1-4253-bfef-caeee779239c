package onvif

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// Service ONVIF服务
type Service struct{}

// NewService 创建ONVIF服务实例
func NewService() *Service {
	return &Service{}
}

// DeviceDiscoveryResult 设备发现结果
type DeviceDiscoveryResult struct {
	Devices []DeviceInfo `json:"devices"`
	Count   int          `json:"count"`
}

// DeviceConnectionInfo 设备连接信息
type DeviceConnectionInfo struct {
	DeviceInfo   *DeviceInfo `json:"device_info"`
	Profiles     []Profile   `json:"profiles"`
	StreamURL    string      `json:"stream_url"`
	SnapshotURL  string      `json:"snapshot_url"`
	Capabilities string      `json:"capabilities"`
}

// ONVIFCredentials ONVIF认证信息
type ONVIFCredentials struct {
	Username string `json:"username"`
	Password string `json:"password"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
}

// ParseONVIFURL 解析ONVIF URL格式: onvif://username:password@host:port
func (s *Service) ParseONVIFURL(onvifURL string) (*ONVIFCredentials, error) {
	// 移除onvif://前缀
	if !strings.HasPrefix(onvifURL, "onvif://") {
		return nil, fmt.Errorf("无效的ONVIF URL格式，应以onvif://开头")
	}

	urlStr := strings.TrimPrefix(onvifURL, "onvif://")

	// 解析URL
	u, err := url.Parse("http://" + urlStr)
	if err != nil {
		return nil, fmt.Errorf("解析URL失败: %w", err)
	}

	creds := &ONVIFCredentials{
		Host: u.Hostname(),
		Port: 80, // 默认端口
	}

	// 获取端口
	if portStr := u.Port(); portStr != "" {
		if port, err := strconv.Atoi(portStr); err == nil {
			creds.Port = port
		}
	}

	// 获取用户名和密码
	if u.User != nil {
		creds.Username = u.User.Username()
		if password, exists := u.User.Password(); exists {
			creds.Password = password
		}
	}

	return creds, nil
}

// DiscoverDevices 发现网络中的ONVIF设备
func (s *Service) DiscoverDevices(ctx context.Context, timeout time.Duration) (*DeviceDiscoveryResult, error) {
	deviceURLs, err := DiscoverDevices(ctx, timeout)
	if err != nil {
		return nil, fmt.Errorf("发现设备失败: %w", err)
	}

	var devices []DeviceInfo

	// 获取每个设备的详细信息
	for _, deviceURL := range deviceURLs {
		client := NewONVIFClient(deviceURL, "", "") // 发现阶段不需要认证

		// 设置较短的超时时间，避免阻塞
		deviceCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
		deviceInfo, err := client.GetDeviceInformation(deviceCtx)
		cancel()

		if err != nil {
			// 如果获取设备信息失败，创建一个基本的设备信息
			u, _ := url.Parse(deviceURL)
			deviceInfo = &DeviceInfo{
				Manufacturer: "Unknown",
				Model:        "Unknown",
				SerialNumber: u.Host,
			}
		}

		devices = append(devices, *deviceInfo)
	}

	return &DeviceDiscoveryResult{
		Devices: devices,
		Count:   len(devices),
	}, nil
}

// ConnectDevice 连接到ONVIF设备并获取详细信息
func (s *Service) ConnectDevice(ctx context.Context, creds *ONVIFCredentials) (*DeviceConnectionInfo, error) {
	// 使用连接测试器发现可用的端点
	tester := NewConnectionTester()

	// 先测试网络连通性
	if err := tester.TestNetworkConnectivity(creds.Host, creds.Port); err != nil {
		return nil, fmt.Errorf("网络连接失败: %w", err)
	}

	// 发现可用的ONVIF端点
	endpoints := tester.DiscoverONVIFEndpoints(creds.Host, creds.Port, creds.Username, creds.Password)
	if len(endpoints) == 0 {
		return nil, fmt.Errorf("未找到可用的ONVIF端点")
	}

	// 使用第一个可用的端点
	deviceURL := endpoints[0]
	client := NewRetryableONVIFClient(deviceURL, creds.Username, creds.Password)

	// 获取设备信息
	deviceInfo, err := client.GetDeviceInformationWithRetry(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取设备信息失败: %w", err)
	}

	// 获取媒体配置
	profiles, err := client.GetProfiles(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取媒体配置失败: %w", err)
	}

	if len(profiles) == 0 {
		return nil, fmt.Errorf("设备没有可用的媒体配置")
	}

	// 使用第一个配置获取流地址
	streamURL, err := client.GetStreamURI(ctx, profiles[0].Token)
	if err != nil {
		return nil, fmt.Errorf("获取流地址失败: %w", err)
	}

	// 获取快照地址
	snapshotURL, err := client.GetSnapshotURI(ctx, profiles[0].Token)
	if err != nil {
		// 快照地址是可选的，如果失败就设为空
		snapshotURL = ""
	}

	return &DeviceConnectionInfo{
		DeviceInfo:  deviceInfo,
		Profiles:    profiles,
		StreamURL:   streamURL,
		SnapshotURL: snapshotURL,
	}, nil
}

// GetStreamURL 获取指定配置的流地址
func (s *Service) GetStreamURL(ctx context.Context, creds *ONVIFCredentials, profileToken string) (string, error) {
	deviceURL := fmt.Sprintf("http://%s:%d/onvif/device_service", creds.Host, creds.Port)
	client := NewONVIFClient(deviceURL, creds.Username, creds.Password)

	return client.GetStreamURI(ctx, profileToken)
}

// GetSnapshotURL 获取指定配置的快照地址
func (s *Service) GetSnapshotURL(ctx context.Context, creds *ONVIFCredentials, profileToken string) (string, error) {
	deviceURL := fmt.Sprintf("http://%s:%d/onvif/device_service", creds.Host, creds.Port)
	client := NewONVIFClient(deviceURL, creds.Username, creds.Password)

	return client.GetSnapshotURI(ctx, profileToken)
}

// TestConnection 测试ONVIF设备连接
func (s *Service) TestConnection(ctx context.Context, creds *ONVIFCredentials) error {
	deviceURL := fmt.Sprintf("http://%s:%d/onvif/device_service", creds.Host, creds.Port)
	client := NewONVIFClient(deviceURL, creds.Username, creds.Password)

	// 尝试获取设备信息来测试连接
	_, err := client.GetDeviceInformation(ctx)
	if err != nil {
		return fmt.Errorf("连接测试失败: %w", err)
	}

	return nil
}

// GenerateRTSPURL 从ONVIF信息生成RTSP URL（兼容现有视频模块）
func (s *Service) GenerateRTSPURL(ctx context.Context, onvifURL string) (string, error) {
	// 解析ONVIF URL
	creds, err := s.ParseONVIFURL(onvifURL)
	if err != nil {
		return "", err
	}

	// 连接设备获取流地址
	deviceInfo, err := s.ConnectDevice(ctx, creds)
	if err != nil {
		return "", err
	}

	// 返回流地址
	return deviceInfo.StreamURL, nil
}

// GetDeviceCapabilities 获取设备完整能力信息
func (s *Service) GetDeviceCapabilities(ctx context.Context, creds *ONVIFCredentials) (map[string]interface{}, error) {
	deviceURL := fmt.Sprintf("http://%s:%d/onvif/device_service", creds.Host, creds.Port)
	client := NewONVIFClient(deviceURL, creds.Username, creds.Password)

	result := make(map[string]interface{})

	// 获取设备信息
	if deviceInfo, err := client.GetDeviceInformation(ctx); err == nil {
		result["device_info"] = deviceInfo
	}

	// 获取媒体配置
	if profiles, err := client.GetProfiles(ctx); err == nil {
		result["profiles"] = profiles
		result["profile_count"] = len(profiles)

		// 如果有配置，获取第一个配置的流信息
		if len(profiles) > 0 {
			if streamURL, err := client.GetStreamURI(ctx, profiles[0].Token); err == nil {
				result["default_stream_url"] = streamURL
			}
			if snapshotURL, err := client.GetSnapshotURI(ctx, profiles[0].Token); err == nil {
				result["default_snapshot_url"] = snapshotURL
			}
		}
	}

	return result, nil
}
