# ONVIF模块

这个模块提供了ONVIF协议的支持，用于管理和操作符合ONVIF标准的网络摄像头设备。

## 功能特性

### 核心功能
- **设备发现**：通过WS-Discovery协议自动发现网络中的ONVIF设备
- **设备连接**：连接到ONVIF设备并获取设备信息
- **流地址获取**：自动获取摄像头的RTSP流地址
- **快照地址获取**：获取设备的快照URL
- **设备能力查询**：获取设备支持的媒体配置和能力信息
- **连接测试**：测试与ONVIF设备的连接状态

### 支持的ONVIF功能
- GetDeviceInformation：获取设备基本信息
- GetCapabilities：获取设备能力信息
- GetProfiles：获取媒体配置文件
- GetStreamUri：获取视频流地址
- GetSnapshotUri：获取快照地址

## 架构设计

### 模块结构
```
internal/app/onvif/
├── client.go      # ONVIF客户端实现
├── service.go     # ONVIF服务层
├── api.go         # HTTP API接口
└── README.md      # 本文档
```

### 核心组件

#### ONVIFClient
- 负责与ONVIF设备的低层通信
- 实现SOAP协议调用
- 处理设备认证和XML解析

#### Service
- 提供高层业务逻辑
- URL解析和格式转换
- 设备发现和管理

#### API
- HTTP REST接口
- 请求参数验证
- 错误处理和响应格式化

## URL格式

### ONVIF URL格式
```
onvif://username:password@host:port
```

示例：
```
onvif://admin:123456@*************:80
onvif://user:<EMAIL>:8080
```

### 自动转换
系统会自动将ONVIF URL转换为实际的RTSP流地址：
```
onvif://admin:123456@*************:80
↓ 自动转换为
rtsp://admin:123456@*************:554/stream1
```

## API接口

### 设备发现
```http
GET /api/onvif/discover?timeout=10
```

### 连接设备
```http
POST /api/onvif/connect
Content-Type: application/json

{
  "username": "admin",
  "password": "123456",
  "host": "*************",
  "port": 80
}
```

### 测试连接
```http
POST /api/onvif/test
Content-Type: application/json

{
  "username": "admin",
  "password": "123456",
  "host": "*************",
  "port": 80
}
```

### 获取流地址
```http
POST /api/onvif/stream-url
Content-Type: application/json

{
  "username": "admin",
  "password": "123456",
  "host": "*************",
  "port": 80,
  "profile_token": "Profile_1"
}
```

### 生成RTSP URL
```http
POST /api/onvif/generate-rtsp
Content-Type: application/json

{
  "url": "onvif://admin:123456@*************:80"
}
```

## 集成到视频模块

### 视频源配置
在创建视频源时，选择协议为`onvif`，系统会：

1. 验证ONVIF URL格式
2. 连接到设备获取配置信息
3. 自动生成RTSP流地址
4. 保存设备信息和流配置

### 自动URL生成
当视频源的`取流方式`设置为`自动生成`且协议为`onvif`时：
- 系统会根据输入的设备信息自动生成ONVIF URL
- 在需要实际播放时，自动转换为RTSP地址

### 扩展API
视频模块增加了以下ONVIF相关端点：

```http
# 测试视频源连接
POST /api/videos/{id}/test-connection

# 获取真实流地址
GET /api/videos/{id}/real-stream-url

# 获取ONVIF设备能力
GET /api/videos/{id}/onvif-capabilities

# 发现ONVIF设备
GET /api/videos/discover-onvif?timeout=10
```

## 使用示例

### 1. 发现网络设备
```bash
curl -X GET "http://localhost:8080/api/onvif/discover?timeout=15"
```

### 2. 连接设备获取信息
```bash
curl -X POST "http://localhost:8080/api/onvif/connect" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "123456",
    "host": "*************",
    "port": 80
  }'
```

### 3. 创建ONVIF视频源
```bash
curl -X POST "http://localhost:8080/api/videos" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "前门摄像头",
    "protocol": "onvif",
    "camera_ip": "*************",
    "username": "admin",
    "password": "123456",
    "port": 80,
    "stream_type": "自动生成",
    "brand": "海康威视"
  }'
```

### 4. 获取真实流地址
```bash
curl -X GET "http://localhost:8080/api/videos/1/real-stream-url"
```

## 错误处理

### 常见错误类型及解决方案

#### 401认证错误 ✅ 已解决
- **现状**: 智能认证系统，支持多种认证方式和自动重试
- **支持**: WS-Security摘要认证、HTTP Basic认证、无认证
- **特性**: 优先级设置、失败自动重试、认证方式降级
- **建议**: 使用`ConnectionTester`进行端点发现和认证测试

#### 网络连接问题 ✅ 已改进
- **现状**: 增加了网络连通性测试和重试机制
- **功能**: 自动发现可用的ONVIF端点
- **特性**: 支持连接重试和超时设置

#### 内网地址映射问题 ✅ 已解决
- **现状**: 自动修复媒体服务URL中的内网地址问题
- **功能**: 将设备返回的内网地址替换为外网地址
- **保持**: 媒体服务的正确路径不变

#### 其他常见问题
- **连接超时**：设备网络不可达或端口不正确
- **协议不支持**：设备不支持ONVIF或版本不兼容
- **URL格式错误**：ONVIF URL格式不正确

### 错误响应格式
```json
{
  "code": 500,
  "message": "连接设备失败: 认证失败"
}
```

### 调试工具
使用`ConnectionTester`进行问题诊断：
```go
tester := onvif.NewConnectionTester()

// 测试网络连通性
err := tester.TestNetworkConnectivity("*************", 80)

// 发现可用端点
endpoints := tester.DiscoverONVIFEndpoints("*************", 80, "admin", "password")
```

### 智能认证系统
#### 认证方式支持
```go
// 创建客户端（默认：WS-Security优先，允许降级）
client := onvif.NewONVIFClient(deviceURL, "admin", "password")

// 设置认证偏好
client.SetAuthPreference(onvif.AuthMethodBasic, true)    // HTTP Basic优先，允许降级
client.SetAuthPreference(onvif.AuthMethodWSecurity, false) // 仅WS-Security，不允许降级
client.SetAuthPreference(onvif.AuthMethodNone, false)     // 无认证访问
```

#### 认证流程
1. **优先认证**: 使用设置的首选认证方式
2. **自动重试**: 401错误时自动尝试其他认证方式
3. **智能降级**: 根据设备支持情况选择最佳认证
4. **错误处理**: 详细的认证失败信息和建议

#### 认证方式说明
- **WS-Security**: ONVIF标准摘要认证，安全性最高
- **HTTP Basic**: 简单用户名密码认证，兼容性最好  
- **无认证**: 适用于开放访问的设备

## 技术实现

### SOAP通信
- 使用标准的SOAP 1.2协议
- 支持HTTP Basic认证
- 自动处理XML命名空间

### 设备发现
- 实现WS-Discovery协议
- UDP多播到***************:3702
- 解析ProbeMatches响应

### 媒体配置
- 获取所有可用的媒体配置文件
- 支持主码流和子码流选择
- 自动选择最优配置

### 兼容性
- 兼容ONVIF Profile S
- 支持ONVIF 2.0及以上版本
- 适配主流摄像头品牌

## 参考go2rtc实现

本模块的设计参考了[go2rtc](https://github.com/AlexxIT/go2rtc)项目的ONVIF实现：

### 相似特性
1. **简化的URL格式**：采用`onvif://username:password@host:port`格式
2. **自动流地址获取**：通过ONVIF协议自动获取RTSP地址
3. **设备发现功能**：支持网络设备自动发现
4. **透明代理**：对上层应用隐藏ONVIF复杂性

### 增强功能
1. **数据库集成**：与ECP平台的数据库模型集成
2. **RESTful API**：提供完整的HTTP API接口
3. **错误处理**：更详细的错误信息和处理机制
4. **扩展性**：可扩展支持更多ONVIF功能

这种设计使得ECP平台能够无缝支持ONVIF协议，同时保持与现有RTSP、GB28181协议的兼容性。 