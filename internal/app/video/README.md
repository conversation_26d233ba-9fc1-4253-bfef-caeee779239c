# 视频接入模块

这个模块负责边缘计算平台的视频源管理，包括视频源的增删改查等功能。

## 功能

- 添加新的视频源
- 编辑现有视频源信息
- 删除不需要的视频源
- 查询视频源列表和详情
- 管理视频源的状态（在线/离线）

## 数据结构

视频源（Video）包含以下字段：

- ID：唯一标识符
- 名称：视频源名称
- URL：视频源地址（RTSP/RTMP等）
- 描述：视频源描述信息
- 状态：视频源状态（在线/离线）
- 创建时间：视频源添加时间
- 更新时间：视频源信息最后更新时间

## 使用方法

```go
// 创建视频服务实例
videoService := video.NewVideoService()

// 获取所有视频源
videos, err := videoService.GetAll()

// 获取特定视频源
video, err := videoService.GetByID(1)

// 创建新视频源
newVideo := &video.Video{
    Name: "摄像头1",
    URL: "rtsp://example.com/stream1",
    Description: "前门摄像头",
}
err := videoService.Create(newVideo)

// 更新视频源
video.Name = "更新后的名称"
err := videoService.Update(video)

// 删除视频源
err := videoService.Delete(1)
``` 