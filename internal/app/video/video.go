package video

import (
	"context"
	"errors"
	"fmt"
	"time"

	"ecp/internal/app/gb28181"
	"ecp/internal/app/onvif"
	"ecp/internal/pkg/database"

	"github.com/mlogclub/simple/sqls"

	"gorm.io/gorm"
)

// Video 表示一个视频源，使用数据库中定义的结构
type Video = database.Video

// VideoService 提供视频源管理功能
type VideoService struct {
	db             *gorm.DB
	onvifService   *onvif.Service
	gb28181Adapter gb28181.Adapter
}

// NewVideoService 创建一个新的视频服务实例
func NewVideoService(db *gorm.DB) *VideoService {
	return &VideoService{
		db:           db,
		onvifService: onvif.NewService(),
	}
}

// NewVideoServiceWithGB28181 创建带GB28181支持的视频服务实例
func NewVideoServiceWithGB28181(db *gorm.DB, gb28181Adapter gb28181.Adapter) *VideoService {
	return &VideoService{
		db:             db,
		onvifService:   onvif.NewService(),
		gb28181Adapter: gb28181Adapter,
	}
}

// GetAll 获取所有视频源
func (s *VideoService) GetAll() ([]Video, error) {
	var videos []Video
	if err := s.db.Find(&videos).Error; err != nil {
		return nil, err
	}
	return videos, nil
}

// GetByID 通过ID获取视频源
func (s *VideoService) GetByID(id int64) (*Video, error) {
	var video Video
	if err := s.db.First(&video, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("视频源不存在")
		}
		return nil, err
	}
	return &video, nil
}

// Create 创建新的视频源
func (s *VideoService) Create(video *Video) error {
	video.CreatedAt = time.Now()
	video.UpdatedAt = time.Now()

	// 如果是自动生成流地址，则根据协议类型生成
	if video.StreamType == "自动生成" {
		switch video.Protocol {
		case "rtsp":
			// 根据品牌生成不同的RTSP URL格式
			switch video.Brand {
			case "海康威视":
				video.URL = generateHikvisionRtspUrl(video)
			case "大华":
				video.URL = generateDahuaRtspUrl(video)
			case "宇视":
				video.URL = generateUniviewRtspUrl(video)
			case "华为":
				video.URL = generateHuaweiRtspUrl(video)
			default:
				video.URL = generateDefaultRtspUrl(video)
			}
		case "onvif":
			// ONVIF URL格式
			video.URL = generateOnvifUrl(video)
		case "gb28181":
			// GB28181 URL格式
			video.URL = generateGb28181Url(video)
		}
	}

	return s.db.Create(video).Error
}

// Update 更新视频源
func (s *VideoService) Update(video *Video) error {
	video.UpdatedAt = time.Now()

	// 如果是自动生成流地址，则根据协议类型生成
	if video.StreamType == "自动生成" {
		switch video.Protocol {
		case "rtsp":
			// 根据品牌生成不同的RTSP URL格式
			switch video.Brand {
			case "海康威视":
				video.URL = generateHikvisionRtspUrl(video)
			case "大华":
				video.URL = generateDahuaRtspUrl(video)
			case "宇视":
				video.URL = generateUniviewRtspUrl(video)
			case "华为":
				video.URL = generateHuaweiRtspUrl(video)
			default:
				video.URL = generateDefaultRtspUrl(video)
			}
		case "onvif":
			// ONVIF URL格式
			video.URL = generateOnvifUrl(video)
		case "gb28181":
			// GB28181 URL格式
			video.URL = generateGb28181Url(video)
		}
	}

	return s.db.Save(video).Error
}

// Delete 删除视频源
func (s *VideoService) Delete(id int64) error {
	return s.db.Delete(&Video{}, id).Error
}

// Query 查询视频源
func (s *VideoService) Query(name string, protocol string, status string, page, pageSize int) ([]Video, *sqls.Paging, error) {
	var videos []Video

	// 创建查询条件
	cnd := sqls.NewCnd()
	if name != "" {
		cnd.Like("name", "%"+name+"%")
	}
	if protocol != "" {
		cnd.Eq("protocol", protocol)
	}
	if status != "" {
		cnd.Eq("status", status)
	}

	// 分页
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	cnd.Page(page, pageSize)

	// 排序
	cnd.Desc("id")

	// 执行查询
	cnd.Find(s.db, &videos)

	// 计算总数
	if cnd.Paging != nil {
		cnd.Paging.Total = cnd.Count(s.db, &Video{})
	}

	return videos, cnd.Paging, nil
}

// QueryExtended 扩展的查询视频源方法，支持更多查询条件
func (s *VideoService) QueryExtended(name string, protocol string, status string, cameraID string, cameraIP string, typeInt int, page, pageSize int) ([]Video, *sqls.Paging, error) {
	var videos []Video

	// 创建查询条件
	cnd := sqls.NewCnd()
	if name != "" {
		cnd.Like("name", "%"+name+"%")
	}
	if protocol != "" {
		cnd.Eq("protocol", protocol)
	}
	if status != "" {
		cnd.Eq("status", status)
	}
	if cameraID != "" {
		cnd.Like("camera_id", "%"+cameraID+"%")
	}
	if cameraIP != "" {
		cnd.Like("camera_ip", "%"+cameraIP+"%")
	}
	if typeInt > 0 {
		cnd.Eq("type", typeInt)
	}

	// 分页
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	cnd.Page(page, pageSize)

	// 排序
	cnd.Desc("id")

	// 执行查询
	cnd.Find(s.db, &videos)

	// 计算总数
	if cnd.Paging != nil {
		cnd.Paging.Total = cnd.Count(s.db, &Video{})
	}

	return videos, cnd.Paging, nil
}

// 生成海康威视摄像头的RTSP URL
func generateHikvisionRtspUrl(video *Video) string {
	streamParam := "1" // 默认主码流
	if video.StreamMode == "子码流" {
		streamParam = "2"
	}
	return "rtsp://" + video.Username + ":" + video.Password + "@" + video.CameraIP + ":" + "554" + "/h264/ch1/" + streamParam + "/av_stream"
}

// 生成大华摄像头的RTSP URL
func generateDahuaRtspUrl(video *Video) string {
	streamParam := "0" // 默认主码流
	if video.StreamMode == "子码流" {
		streamParam = "1"
	}
	return "rtsp://" + video.Username + ":" + video.Password + "@" + video.CameraIP + ":" + "554" + "/cam/realmonitor?channel=1&subtype=" + streamParam
}

// 生成宇视摄像头的RTSP URL
func generateUniviewRtspUrl(video *Video) string {
	streamParam := "main" // 默认主码流
	if video.StreamMode == "子码流" {
		streamParam = "sub"
	}
	return "rtsp://" + video.Username + ":" + video.Password + "@" + video.CameraIP + ":" + "554" + "/media/" + streamParam + "stream"
}

// 生成华为摄像头的RTSP URL
func generateHuaweiRtspUrl(video *Video) string {
	streamParam := "0" // 默认主码流
	if video.StreamMode == "子码流" {
		streamParam = "1"
	}
	return "rtsp://" + video.Username + ":" + video.Password + "@" + video.CameraIP + ":" + "554" + "/LiveMedia/ch1/" + streamParam
}

// 生成默认RTSP URL
func generateDefaultRtspUrl(video *Video) string {
	return "rtsp://" + video.Username + ":" + video.Password + "@" + video.CameraIP + ":" + "554" + "/stream"
}

// 生成ONVIF URL
func generateOnvifUrl(video *Video) string {
	port := "80" // 默认端口
	if video.Port > 0 {
		port = fmt.Sprintf("%d", video.Port)
	}
	return "onvif://" + video.Username + ":" + video.Password + "@" + video.CameraIP + ":" + port
}

// 生成GB28181 URL
func generateGb28181Url(video *Video) string {
	return "gb28181://" + video.CameraID
}

// GetRealStreamURL 获取真实的流地址（对于ONVIF协议，会自动转换为RTSP地址）
func (s *VideoService) GetRealStreamURL(ctx context.Context, video *Video) (string, error) {
	if video.Protocol == "onvif" && video.StreamType == "自动生成" {
		// 使用ONVIF服务获取真实的RTSP地址
		rtspURL, err := s.onvifService.GenerateRTSPURL(ctx, video.URL)
		if err != nil {
			return "", fmt.Errorf("从ONVIF URL获取RTSP地址失败: %w", err)
		}
		return rtspURL, nil
	} else if video.Protocol == "gb28181" && s.gb28181Adapter != nil {
		// 使用GB28181适配器获取真实的流地址
		return s.gb28181Adapter.GetRealStreamURL(video)
	}

	// 对于其他协议，直接返回配置的URL
	return video.URL, nil
}

// TestConnection 测试视频源连接
func (s *VideoService) TestConnection(ctx context.Context, video *Video) error {
	if video.Protocol == "onvif" {
		// 解析ONVIF URL并测试连接
		creds, err := s.onvifService.ParseONVIFURL(video.URL)
		if err != nil {
			return fmt.Errorf("解析ONVIF URL失败: %w", err)
		}

		return s.onvifService.TestConnection(ctx, creds)
	} else if video.Protocol == "gb28181" {
		if s.gb28181Adapter == nil {
			return fmt.Errorf("GB28181适配器未初始化")
		}

		// 检查GB28181服务是否健康
		if !s.gb28181Adapter.IsHealthy() {
			return fmt.Errorf("GB28181服务不可用")
		}

		// 检查设备状态
		if video.CameraID != "" {
			status, err := s.gb28181Adapter.GetDeviceStatus(video.CameraID)
			if err != nil {
				return fmt.Errorf("获取GB28181设备状态失败: %w", err)
			}

			if status.Online != "ONLINE" {
				return fmt.Errorf("GB28181设备离线")
			}
		}

		return nil
	}

	// 对于其他协议，这里可以添加相应的测试逻辑
	// 目前返回nil表示跳过测试
	return nil
}

// GetONVIFCapabilities 获取ONVIF设备能力（仅对ONVIF协议有效）
func (s *VideoService) GetONVIFCapabilities(ctx context.Context, video *Video) (map[string]interface{}, error) {
	if video.Protocol != "onvif" {
		return nil, fmt.Errorf("只有ONVIF协议的视频源才支持获取设备能力")
	}

	creds, err := s.onvifService.ParseONVIFURL(video.URL)
	if err != nil {
		return nil, fmt.Errorf("解析ONVIF URL失败: %w", err)
	}

	return s.onvifService.GetDeviceCapabilities(ctx, creds)
}

// DiscoverONVIFDevices 发现网络中的ONVIF设备
func (s *VideoService) DiscoverONVIFDevices(ctx context.Context, timeout time.Duration) (*onvif.DeviceDiscoveryResult, error) {
	return s.onvifService.DiscoverDevices(ctx, timeout)
}

// ===== GB28181相关方法 =====

// GetGB28181Devices 获取GB28181设备列表
func (s *VideoService) GetGB28181Devices() ([]gb28181.Device, error) {
	if s.gb28181Adapter == nil {
		return nil, fmt.Errorf("GB28181适配器未初始化")
	}
	return s.gb28181Adapter.GetDevices()
}

// GetGB28181DeviceChannels 获取GB28181设备通道列表
func (s *VideoService) GetGB28181DeviceChannels(deviceID string) ([]gb28181.Channel, error) {
	if s.gb28181Adapter == nil {
		return nil, fmt.Errorf("GB28181适配器未初始化")
	}
	return s.gb28181Adapter.GetDeviceChannels(deviceID)
}

// SyncGB28181Devices 同步GB28181设备到视频源表
func (s *VideoService) SyncGB28181Devices() error {
	if s.gb28181Adapter == nil {
		return fmt.Errorf("GB28181适配器未初始化")
	}
	return s.gb28181Adapter.SyncDevicesToVideoSource()
}

// StartGB28181Stream 启动GB28181视频流
func (s *VideoService) StartGB28181Stream(video *Video) (*gb28181.StreamInfo, error) {
	if s.gb28181Adapter == nil {
		return nil, fmt.Errorf("GB28181适配器未初始化")
	}

	if video.Protocol != "gb28181" {
		return nil, fmt.Errorf("视频源协议不是GB28181")
	}

	return s.gb28181Adapter.StartStreamForVideo(video)
}

// StopGB28181Stream 停止GB28181视频流
func (s *VideoService) StopGB28181Stream(streamID string) error {
	if s.gb28181Adapter == nil {
		return fmt.Errorf("GB28181适配器未初始化")
	}
	return s.gb28181Adapter.StopStreamForVideo(streamID)
}

// PTZControl GB28181云台控制
func (s *VideoService) PTZControl(video *Video, command string, horizontalSpeed, verticalSpeed, zoomSpeed int) error {
	if s.gb28181Adapter == nil {
		return fmt.Errorf("GB28181适配器未初始化")
	}

	if video.Protocol != "gb28181" {
		return fmt.Errorf("视频源协议不是GB28181，不支持云台控制")
	}

	params := gb28181.PTZParams{
		Command:         command,
		HorizontalSpeed: horizontalSpeed,
		VerticalSpeed:   verticalSpeed,
		ZoomSpeed:       zoomSpeed,
	}

	return s.gb28181Adapter.PTZControl(video, command, params)
}

// GetGB28181DeviceStatus 获取GB28181设备状态
func (s *VideoService) GetGB28181DeviceStatus(deviceID string) (*gb28181.DeviceStatus, error) {
	if s.gb28181Adapter == nil {
		return nil, fmt.Errorf("GB28181适配器未初始化")
	}
	return s.gb28181Adapter.GetDeviceStatus(deviceID)
}

// SyncGB28181DeviceStatus 同步GB28181设备状态
func (s *VideoService) SyncGB28181DeviceStatus() error {
	if s.gb28181Adapter == nil {
		return fmt.Errorf("GB28181适配器未初始化")
	}
	return s.gb28181Adapter.SyncDeviceStatus()
}

// IsGB28181ServiceHealthy 检查GB28181服务是否健康
func (s *VideoService) IsGB28181ServiceHealthy() bool {
	if s.gb28181Adapter == nil {
		return false
	}
	return s.gb28181Adapter.IsHealthy()
}
