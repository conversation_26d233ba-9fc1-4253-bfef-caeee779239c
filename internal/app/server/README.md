# 服务器模块

这个模块负责边缘计算平台的HTTP服务器实现，包括路由配置、中间件和API处理。

## 功能

- HTTP服务器初始化和配置
- API路由注册
- 中间件处理（认证、日志、CORS等）
- 请求处理和响应
- 优雅关闭

## 文件结构

- `server.go`: 服务器核心实现
- `router.go`: API路由配置
- `middleware.go`: HTTP中间件
- `handler.go`: 请求处理器

## 使用方法

```go
// 创建服务器实例
server, err := server.NewServer()
if err != nil {
    log.Fatalf("创建服务器失败: %v", err)
}

// 启动服务器
if err := server.Start(); err != nil {
    log.Fatalf("启动服务器失败: %v", err)
}

// 停止服务器
if err := server.Stop(); err != nil {
    log.Fatalf("停止服务器失败: %v", err)
}
``` 