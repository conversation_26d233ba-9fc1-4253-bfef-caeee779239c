package server

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"ecp/internal/app/alert"
	"ecp/internal/app/algorithm"
	"ecp/internal/app/binding"
	"ecp/internal/app/camera"
	"ecp/internal/app/gb28181"
	"ecp/internal/app/linkage"
	"ecp/internal/app/middleware"
	"ecp/internal/app/network"
	"ecp/internal/app/onvif"
	"ecp/internal/app/pipeline"
	"ecp/internal/app/system"
	"ecp/internal/app/user"
	"ecp/internal/app/video"
	"ecp/internal/pkg/config"
	"ecp/internal/pkg/database"

	"github.com/gin-gonic/gin"
	"github.com/mlogclub/simple/sqls"
	"github.com/mlogclub/simple/web"
)

// Server 表示边缘计算平台的HTTP服务器
type Server struct {
	httpServer       *http.Server
	db               *database.DB
	router           *gin.Engine
	videoService     *video.VideoService
	algorithmService *algorithm.AlgorithmService
	alertService     *alert.AlertService
	bindingService   *binding.BindingService
	userService      *user.UserService
	onvifAPI         *onvif.API
	cameraFactory    *camera.CameraControllerFactory
	pipelineService  pipeline.PipelineService
	networkService   network.NetworkService
	networkAPI       *network.API
	systemService    system.SystemService
	systemAPI        *system.API
	linkageEngine    *linkage.LinkageEngine
	linkageAPI       *linkage.API
}

// NewServer 创建并返回一个新的服务器实例
func NewServer() (*Server, error) {
	// 从配置文件获取数据库配置
	cfg := config.GetConfig()
	dbConfig := &database.Config{
		Path: cfg.Database.Path,
	}
	db, err := database.NewDB(dbConfig)
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 初始化表结构
	if err := db.InitSchema(); err != nil {
		return nil, fmt.Errorf("初始化数据库表结构失败: %w", err)
	}

	// 创建服务
	videoService := video.NewVideoService(db.DB)
	algorithmService := algorithm.NewAlgorithmService(db.DB)
	alertService := alert.NewAlertService(db.DB)
	bindingService := binding.NewBindingService(db.DB)
	userService := user.NewUserService(db.DB)

	// 创建ONVIF API
	onvifService := onvif.NewService()
	onvifAPI := onvif.NewAPI(onvifService)

	// 创建GB28181服务
	gb28181Service := gb28181.NewService(cfg.GB28181.ServerURL, db.DB)

	// 创建摄像头控制器工厂
	cameraFactory := camera.NewCameraControllerFactory(onvifService, gb28181Service)

	// 创建管道服务
	var pipelineService pipeline.PipelineService
	if cfg.Pipeline.Enabled {
		// 解析超时时间
		timeout, err := time.ParseDuration(cfg.Pipeline.Remote.Timeout)
		if err != nil {
			timeout = 30 * time.Second // 默认30秒
		}

		// 创建管道配置
		pipelineConfig := pipeline.PipelineConfig{
			DeploymentMode: cfg.Pipeline.DeploymentMode,
			TemplatePath:   cfg.Pipeline.TemplatePath,
			NodeMapping: pipeline.NodeMapping{
				VideoSource: cfg.Pipeline.NodeMapping.VideoSource,
				Algorithms:  cfg.Pipeline.NodeMapping.Algorithms,
			},
			Local: pipeline.LocalConfig{
				ConfigStoragePath: cfg.Pipeline.Local.ConfigStoragePath,
				StatusStoragePath: cfg.Pipeline.Local.StatusStoragePath,
			},
			Remote: pipeline.RemoteConfig{
				APIEndpoint: cfg.Pipeline.Remote.APIEndpoint,
				APIKey:      cfg.Pipeline.Remote.APIKey,
				Timeout:     timeout,
				RetryCount:  cfg.Pipeline.Remote.RetryCount,
			},
		}

		// 创建管道客户端（根据配置自动选择本地或远程）
		pipelineClient := pipeline.CreatePipelineClient(pipelineConfig)

		// 创建模板管理器
		templateManager := pipeline.NewTemplateManager(pipelineConfig.TemplatePath)

		// 创建数据映射器
		dataMapper := pipeline.NewDataMapper(pipelineConfig.NodeMapping)

		// 创建管道构建器
		pipelineBuilder := pipeline.NewPipelineBuilder(templateManager, dataMapper)

		// 创建管道服务
		pipelineService = pipeline.NewPipelineService(pipelineClient, pipelineBuilder, pipelineConfig)
	}

	// 创建网络服务
	networkConfig := network.NetworkConfig{
		MonitorInterval:   5 * time.Second,
		TrafficSampleRate: 1,
		PingTimeout:       5 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   10 * time.Second,
		IptablesPath:      "/sbin/iptables",
		BackupPath:        "/etc/ecp/network/backup",
		NetworkConfigPath: "/etc/netplan",
		DNSConfigPath:     "/etc/resolv.conf",
		RouteTablePath:    "/proc/net/route",
	}
	networkService := network.NewNetworkService(networkConfig)
	networkAPI := network.NewAPI(networkService)

	// 创建系统管理服务
	systemService := system.NewSystemService()
	systemAPI := system.NewAPI(systemService)

	// 创建联动功能
	linkageConfig := &linkage.LinkageConfig{
		Enabled:                 true,
		MaxConcurrentExecutions: 10,
		ExecutionTimeout:        30 * time.Second,
		RetryCount:              3,
		RetryInterval:           5 * time.Second,
	}
	linkageEngine := linkage.NewLinkageEngine(db, linkageConfig)
	linkageAPI := linkage.NewAPI(linkageEngine)

	// 创建路由
	router := gin.Default()

	// 创建HTTP服务器
	// 从配置文件获取服务器配置
	serverCfg := config.GetConfig()
	readTimeout, _ := time.ParseDuration(serverCfg.Server.ReadTimeout)
	writeTimeout, _ := time.ParseDuration(serverCfg.Server.WriteTimeout)

	httpServer := &http.Server{
		Addr:         fmt.Sprintf(":%d", serverCfg.Server.Port),
		Handler:      router,
		ReadTimeout:  readTimeout,
		WriteTimeout: writeTimeout,
	}

	server := &Server{
		httpServer:       httpServer,
		db:               db,
		router:           router,
		videoService:     videoService,
		algorithmService: algorithmService,
		alertService:     alertService,
		bindingService:   bindingService,
		userService:      userService,
		onvifAPI:         onvifAPI,
		cameraFactory:    cameraFactory,
		pipelineService:  pipelineService,
		networkService:   networkService,
		networkAPI:       networkAPI,
		systemService:    systemService,
		systemAPI:        systemAPI,
		linkageEngine:    linkageEngine,
		linkageAPI:       linkageAPI,
	}

	// 初始化路由
	server.initRoutes()

	return server, nil
}

// initRoutes 初始化路由
func (s *Server) initRoutes() {
	// 提供前端静态资源服务
	webDistPath := filepath.Join("web", "dist")
	s.router.Static("/app", webDistPath)

	// SPA路由处理
	s.router.NoRoute(func(c *gin.Context) {
		if strings.HasPrefix(c.Request.URL.Path, "/app") {
			indexPath := filepath.Join(webDistPath, "index.html")
			c.File(indexPath)
			return
		}
		c.String(http.StatusNotFound, "404 页面不存在")
	})

	// API路由组
	api := s.router.Group("/api")

	// 认证API - 无需认证
	authAPI := api.Group("/auth")
	{
		authAPI.POST("/login", s.handleLogin)
		authAPI.POST("/register", s.handleRegister)
		authAPI.POST("/refresh", s.handleRefreshToken)
	}

	// 需要认证的API
	authenticated := api.Group("")
	authenticated.Use(middleware.JWTAuthMiddleware(s.db.DB))
	{
		// 用户API
		userAPI := authenticated.Group("/users")
		{
			userAPI.GET("/me", s.handleGetCurrentUser)
			userAPI.PUT("/me", s.handleUpdateCurrentUser)
			userAPI.POST("/me/change-password", s.handleChangePassword)
		}

		// 需要管理员权限的用户管理API
		adminUserAPI := authenticated.Group("/admin/users")
		adminUserAPI.Use(middleware.AdminAuthMiddleware())
		{
			adminUserAPI.GET("", s.handleGetUsers)
			adminUserAPI.GET("/:id", s.handleGetUser)
			adminUserAPI.POST("", s.handleCreateUser)
			adminUserAPI.PUT("/:id", s.handleUpdateUser)
			adminUserAPI.DELETE("/:id", s.handleDeleteUser)
		}

		// 视频接入API
		videoAPI := authenticated.Group("/videos")
		{
			videoAPI.GET("", s.handleGetVideos)
			videoAPI.GET("/:id", s.handleGetVideo)
			videoAPI.POST("", s.handleCreateVideo)
			videoAPI.PUT("/:id", s.handleUpdateVideo)
			videoAPI.DELETE("/:id", s.handleDeleteVideo)
			videoAPI.GET("/:id/bindings", s.handleGetVideoBindings)
			videoAPI.POST("/:id/bindings", s.handleCreateVideoBindings)
			// ONVIF相关API
			videoAPI.POST("/:id/test-connection", s.handleTestVideoConnection)
			videoAPI.GET("/:id/real-stream-url", s.handleGetRealStreamURL)
			videoAPI.GET("/:id/onvif-capabilities", s.handleGetVideoONVIFCapabilities)
			videoAPI.GET("/discover-onvif", s.handleDiscoverONVIFDevices)
			// 摄像头控制API
			videoAPI.GET("/:id/snapshot", s.handleGetVideoSnapshot)
			videoAPI.POST("/:id/ptz", s.handlePTZControl)
			videoAPI.POST("/:id/preset", s.handlePresetControl)
		}

		// 算法仓库API
		algorithmAPI := authenticated.Group("/algorithms")
		{
			algorithmAPI.GET("", s.handleGetAlgorithms)
			algorithmAPI.GET("/:id", s.handleGetAlgorithm)
			algorithmAPI.POST("", s.handleImportAlgorithm)
			algorithmAPI.DELETE("/:id", s.handleDeleteAlgorithm)
			algorithmAPI.PUT("/:id/activate", s.handleActivateAlgorithm)
			algorithmAPI.PUT("/:id/deactivate", s.handleDeactivateAlgorithm)
			algorithmAPI.GET("/:id/bindings", s.handleGetAlgorithmBindings)
		}

		// 告警记录API
		alertAPI := authenticated.Group("/alerts")
		{
			alertAPI.GET("", s.handleGetAlerts)
			alertAPI.GET("/:id", s.handleGetAlert)
			alertAPI.PUT("/:id/status", s.handleUpdateAlertStatus)
			alertAPI.DELETE("/:id", s.handleDeleteAlert)
			alertAPI.GET("/query", s.handleQueryAlerts)
			alertAPI.POST("/:id/images", s.handleUploadAlertImages)
		}

		// 绑定关系API
		bindingAPI := authenticated.Group("/bindings")
		{
			bindingAPI.GET("", s.handleGetBindings)
			bindingAPI.GET("/:id", s.handleGetBinding)
			bindingAPI.POST("", s.handleCreateBinding)
			bindingAPI.PUT("/:id", s.handleUpdateBinding)
			bindingAPI.DELETE("/:id", s.handleDeleteBinding)
			bindingAPI.PUT("/:id/status", s.handleUpdateBindingStatus)
		}

		// 综合管道API
		if s.pipelineService != nil {
			comprehensiveAPI := authenticated.Group("/comprehensive-pipeline")
			{
				comprehensiveAPI.POST("/create", s.handleCreateComprehensivePipeline)
				comprehensiveAPI.GET("/config", s.handleGetComprehensivePipelineConfig) // 新增：获取配置
				comprehensiveAPI.GET("/status", s.handleGetComprehensivePipelineStatus)
				comprehensiveAPI.PUT("/start", s.handleStartComprehensivePipeline)
				comprehensiveAPI.PUT("/stop", s.handleStopComprehensivePipeline)
				comprehensiveAPI.POST("/rebuild", s.handleRebuildComprehensivePipeline)
				comprehensiveAPI.DELETE("", s.handleDeleteComprehensivePipeline)
			}
		}

		// 网络管理API
		s.networkAPI.RegisterRoutes(authenticated)

		// 系统管理API
		s.systemAPI.RegisterRoutes(authenticated)

		// ONVIF API
		s.onvifAPI.RegisterRoutes(authenticated)

		// 联动管理API
		s.linkageAPI.RegisterRoutes(s.router)
	}
}

// Start 启动服务器
func (s *Server) Start() error {
	// 启动联动引擎
	if err := s.linkageEngine.Start(); err != nil {
		return fmt.Errorf("启动联动引擎失败: %w", err)
	}

	cfg := config.GetConfig()
	fmt.Printf("服务器正在监听端口 :%d\n", cfg.Server.Port)
	fmt.Printf("Web应用可通过 http://localhost:%d/app 访问\n", cfg.Server.Port)
	return s.httpServer.ListenAndServe()
}

// Stop 停止服务器
func (s *Server) Stop() error {
	// 停止联动引擎
	if err := s.linkageEngine.Stop(); err != nil {
		fmt.Printf("停止联动引擎失败: %v\n", err)
	}

	// 创建一个5秒的上下文用于优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 关闭HTTP服务器
	if err := s.httpServer.Shutdown(ctx); err != nil {
		return err
	}

	// 关闭数据库连接
	return s.db.Close()
}

// getIntParam 获取整数参数
func getIntParam(c *gin.Context, key string, defaultValue int) int {
	valueStr := c.Query(key)
	if valueStr == "" {
		return defaultValue
	}
	value, err := strconv.Atoi(valueStr)
	if err != nil {
		return defaultValue
	}
	return value
}

// getInt64Param 获取int64参数
func getInt64Param(c *gin.Context, key string, defaultValue int64) int64 {
	valueStr := c.Param(key)
	if valueStr == "" {
		return defaultValue
	}
	value, err := strconv.ParseInt(valueStr, 10, 64)
	if err != nil {
		return defaultValue
	}
	return value
}

// 视频接入API处理函数
func (s *Server) handleGetVideos(c *gin.Context) {
	name := c.Query("name")
	protocol := c.Query("protocol")
	status := c.Query("status")
	cameraID := c.Query("camera_id") // 添加摄像头/视频ID查询参数
	cameraIP := c.Query("camera_ip") // 添加IP地址查询参数
	typeStr := c.Query("type")       // 添加类型查询参数
	page := getIntParam(c, "page", 1)
	pageSize := getIntParam(c, "limit", 20) // 修改pageSize参数名为limit，与前端一致

	// 将type转为整数
	typeInt := 0
	if typeStr != "" {
		typeInt, _ = strconv.Atoi(typeStr)
	}

	videos, paging, err := s.videoService.QueryExtended(name, protocol, status, cameraID, cameraIP, typeInt, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonPageData(videos, paging))
}

func (s *Server) handleGetVideo(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	video, err := s.videoService.GetByID(id)
	if err != nil {
		if err.Error() == "视频源不存在" {
			c.JSON(http.StatusNotFound, web.JsonErrorMsg(err.Error()))
		} else {
			c.JSON(http.StatusInternalServerError, web.JsonError(err))
		}
		return
	}

	c.JSON(http.StatusOK, web.JsonData(video))
}

func (s *Server) handleCreateVideo(c *gin.Context) {
	var video video.Video
	if err := c.ShouldBindJSON(&video); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	if err := s.videoService.Create(&video); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusCreated, web.JsonData(video))
}

func (s *Server) handleUpdateVideo(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	var video video.Video
	if err := c.ShouldBindJSON(&video); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	video.ID = id
	if err := s.videoService.Update(&video); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(video))
}

func (s *Server) handleDeleteVideo(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	if err := s.videoService.Delete(id); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

// 算法仓库API处理函数
func (s *Server) handleGetAlgorithms(c *gin.Context) {
	name := c.Query("name")
	status := c.Query("status")
	page := getIntParam(c, "page", 1)
	pageSize := getIntParam(c, "limit", 20) // 修改参数名从pageSize为limit

	algorithms, paging, err := s.algorithmService.Query(name, status, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonPageData(algorithms, paging))
}

func (s *Server) handleGetAlgorithm(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	algorithm, err := s.algorithmService.GetByID(id)
	if err != nil {
		if err.Error() == "算法不存在" {
			c.JSON(http.StatusNotFound, web.JsonErrorMsg(err.Error()))
		} else {
			c.JSON(http.StatusInternalServerError, web.JsonError(err))
		}
		return
	}

	c.JSON(http.StatusOK, web.JsonData(algorithm))
}

func (s *Server) handleImportAlgorithm(c *gin.Context) {
	// 获取算法信息
	var alg algorithm.Algorithm

	// 从表单字段中获取算法信息
	alg.Name = c.PostForm("name")
	alg.Description = c.PostForm("description")
	alg.Version = c.PostForm("version")

	// 验证必填字段
	if alg.Name == "" || alg.Version == "" {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("算法名称和版本号不能为空"))
		return
	}

	// 获取算法文件
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的文件"))
		return
	}

	// 读取文件内容
	fileContent, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}
	defer fileContent.Close()

	// 读取文件数据
	fileData := make([]byte, file.Size)
	if _, err := fileContent.Read(fileData); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	// 导入算法
	if err := s.algorithmService.Import(&alg, fileData); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusCreated, web.JsonData(alg))
}

func (s *Server) handleDeleteAlgorithm(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	if err := s.algorithmService.Delete(id); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

func (s *Server) handleActivateAlgorithm(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	if err := s.algorithmService.Activate(id); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

func (s *Server) handleDeactivateAlgorithm(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	if err := s.algorithmService.Deactivate(id); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

// 告警记录API处理函数
func (s *Server) handleGetAlerts(c *gin.Context) {
	alerts, err := s.alertService.GetAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(alerts))
}

func (s *Server) handleGetAlert(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	alert, err := s.alertService.GetByID(id)
	if err != nil {
		if err.Error() == "告警记录不存在" {
			c.JSON(http.StatusNotFound, web.JsonErrorMsg(err.Error()))
		} else {
			c.JSON(http.StatusInternalServerError, web.JsonError(err))
		}
		return
	}

	c.JSON(http.StatusOK, web.JsonData(alert))
}

func (s *Server) handleUpdateAlertStatus(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	var req struct {
		Status string `json:"status"`
	}
	if err := c.ShouldBindJSON(&req); err != nil || req.Status == "" {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的状态"))
		return
	}

	if err := s.alertService.UpdateStatus(id, req.Status); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

func (s *Server) handleDeleteAlert(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	if err := s.alertService.Delete(id); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

func (s *Server) handleQueryAlerts(c *gin.Context) {
	// 获取查询参数
	videoIDStr := c.Query("videoId")
	algorithmIDStr := c.Query("algorithmId")
	level := c.Query("level")
	type_ := c.Query("type")
	status := c.Query("status")
	videoName := c.Query("videoName") // 新增视频源名称查询参数

	// 分页参数
	page := getIntParam(c, "page", 1)
	pageSize := getIntParam(c, "limit", 20)

	// 转换ID参数
	var videoID, algorithmID int64
	if videoIDStr != "" {
		videoID, _ = strconv.ParseInt(videoIDStr, 10, 64)
	}
	if algorithmIDStr != "" {
		algorithmID, _ = strconv.ParseInt(algorithmIDStr, 10, 64)
	}

	// 解析时间范围
	startTimeStr := c.Query("startTime")
	endTimeStr := c.Query("endTime")

	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse("2006-01-02 15:04:05", startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的开始时间格式"))
			return
		}
	}

	if endTimeStr != "" {
		endTime, err = time.Parse("2006-01-02 15:04:05", endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的结束时间格式"))
			return
		}
	}

	var alerts []alert.Alert
	var paging *sqls.Paging

	// 如果有视频源名称查询，使用专门的方法
	if videoName != "" {
		alerts, paging, err = s.alertService.QueryByVideoName(videoName, level, page, pageSize)
	} else {
		alerts, paging, err = s.alertService.Query(videoID, algorithmID, level, type_, status, startTime, endTime, page, pageSize)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonPageData(alerts, paging))
}

func (s *Server) handleUploadAlertImages(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	// 获取原始图片
	originalImage, err := c.FormFile("originalImage")
	var originalImageData []byte
	if err == nil {
		// 读取原始图片内容
		originalImageContent, err := originalImage.Open()
		if err != nil {
			c.JSON(http.StatusInternalServerError, web.JsonError(err))
			return
		}
		defer originalImageContent.Close()

		// 读取原始图片数据
		originalImageData = make([]byte, originalImage.Size)
		if _, err := originalImageContent.Read(originalImageData); err != nil {
			c.JSON(http.StatusInternalServerError, web.JsonError(err))
			return
		}
	}

	// 获取告警图片
	alertImage, err := c.FormFile("alertImage")
	var alertImageData []byte
	if err == nil {
		// 读取告警图片内容
		alertImageContent, err := alertImage.Open()
		if err != nil {
			c.JSON(http.StatusInternalServerError, web.JsonError(err))
			return
		}
		defer alertImageContent.Close()

		// 读取告警图片数据
		alertImageData = make([]byte, alertImage.Size)
		if _, err := alertImageContent.Read(alertImageData); err != nil {
			c.JSON(http.StatusInternalServerError, web.JsonError(err))
			return
		}
	}

	// 保存告警图片
	if err := s.alertService.SaveAlertImage(id, originalImageData, alertImageData); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

// 绑定关系API处理函数
func (s *Server) handleGetBindings(c *gin.Context) {
	videoID := getInt64Param(c, "videoId", 0)
	algorithmID := getInt64Param(c, "algorithmId", 0)
	status := c.Query("status")
	page := getIntParam(c, "page", 1)
	pageSize := getIntParam(c, "pageSize", 20)

	bindings, paging, err := s.bindingService.Query(videoID, algorithmID, status, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonPageData(bindings, paging))
}

func (s *Server) handleGetBinding(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	binding, err := s.bindingService.GetByID(id)
	if err != nil {
		if err.Error() == "绑定关系不存在" {
			c.JSON(http.StatusNotFound, web.JsonErrorMsg(err.Error()))
		} else {
			c.JSON(http.StatusInternalServerError, web.JsonError(err))
		}
		return
	}

	c.JSON(http.StatusOK, web.JsonData(binding))
}

func (s *Server) handleCreateBinding(c *gin.Context) {
	var binding binding.VideoAlgorithmBinding
	if err := c.ShouldBindJSON(&binding); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	if err := s.bindingService.Create(&binding); err != nil {
		c.JSON(http.StatusOK, web.JsonError(err))
		return
	}

	c.JSON(http.StatusCreated, web.JsonData(binding))
}

func (s *Server) handleUpdateBinding(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	var binding binding.VideoAlgorithmBinding
	if err := c.ShouldBindJSON(&binding); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	binding.ID = id
	if err := s.bindingService.Update(&binding); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(binding))
}

func (s *Server) handleDeleteBinding(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	if err := s.bindingService.Delete(id); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

func (s *Server) handleUpdateBindingStatus(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的ID"))
		return
	}

	var req struct {
		Status string `json:"status"`
	}
	if err := c.ShouldBindJSON(&req); err != nil || req.Status == "" {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的状态"))
		return
	}

	if err := s.bindingService.UpdateStatus(id, req.Status); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

func (s *Server) handleGetVideoBindings(c *gin.Context) {
	videoID := getInt64Param(c, "id", 0)
	if videoID <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的视频ID"))
		return
	}

	bindings, err := s.bindingService.GetByVideoID(videoID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(bindings))
}

func (s *Server) handleCreateVideoBindings(c *gin.Context) {
	videoID := getInt64Param(c, "id", 0)
	if videoID <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的视频ID"))
		return
	}

	var bindings []binding.VideoAlgorithmBinding
	if err := c.ShouldBindJSON(&bindings); err != nil {
		// 检查是否是因为空数组导致的错误
		if c.Request.ContentLength == 0 || c.Request.ContentLength == 2 { // [] 的长度是2
			// 空数组或空请求体，将bindings设置为空数组
			bindings = []binding.VideoAlgorithmBinding{}
		} else {
			c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
			return
		}
	}

	if err := s.bindingService.BatchCreate(videoID, bindings); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusCreated, web.JsonData(bindings))
}

func (s *Server) handleGetAlgorithmBindings(c *gin.Context) {
	algorithmID := getInt64Param(c, "id", 0)
	if algorithmID <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的算法ID"))
		return
	}

	bindings, err := s.bindingService.GetByAlgorithmID(algorithmID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(bindings))
}

func (s *Server) handleTestVideoConnection(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的视频ID"))
		return
	}

	// 获取视频源
	video, err := s.videoService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err = s.videoService.TestConnection(ctx, video)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonErrorMsg("连接测试失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(map[string]interface{}{
		"status":  "connected",
		"message": "连接测试成功",
	}))
}

func (s *Server) handleGetRealStreamURL(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的视频ID"))
		return
	}

	// 获取视频源
	video, err := s.videoService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	// 获取真实流地址
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	streamURL, err := s.videoService.GetRealStreamURL(ctx, video)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(map[string]interface{}{
		"original_url":    video.URL,
		"real_stream_url": streamURL,
		"protocol":        video.Protocol,
	}))
}

func (s *Server) handleGetVideoONVIFCapabilities(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的视频ID"))
		return
	}

	// 获取视频源
	video, err := s.videoService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	// 获取ONVIF能力
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	capabilities, err := s.videoService.GetONVIFCapabilities(ctx, video)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(capabilities))
}

func (s *Server) handleDiscoverONVIFDevices(c *gin.Context) {
	// 获取超时参数
	timeoutParam := c.Query("timeout")
	timeout := 10 * time.Second
	if timeoutParam != "" {
		if t, err := strconv.Atoi(timeoutParam); err == nil && t > 0 {
			timeout = time.Duration(t) * time.Second
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout+5*time.Second)
	defer cancel()

	devices, err := s.videoService.DiscoverONVIFDevices(ctx, timeout)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(devices))
}

// handleGetVideoSnapshot 获取视频源快照
func (s *Server) handleGetVideoSnapshot(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的视频源ID"))
		return
	}

	// 获取视频源
	video, err := s.videoService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonErrorMsg(fmt.Sprintf("获取视频源失败: %v", err)))
		return
	}

	// 获取对应协议的控制器
	controller, err := s.cameraFactory.GetController(video.Protocol)
	if err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg(fmt.Sprintf("不支持的摄像头协议: %v", err)))
		return
	}

	// 获取快照
	imageData, contentType, err := controller.GetSnapshot(c.Request.Context(), video)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonErrorMsg(fmt.Sprintf("获取快照失败: %v", err)))
		return
	}
	defer imageData.Close()

	// 设置响应头
	c.Header("Content-Type", contentType)
	c.Header("Content-Disposition", fmt.Sprintf("inline; filename=snapshot_%d.jpg", id))

	// 发送图片数据
	c.Status(http.StatusOK)
	_, err = io.Copy(c.Writer, imageData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonErrorMsg(fmt.Sprintf("发送快照数据失败: %v", err)))
		return
	}
}

// PTZRequest 表示PTZ控制请求
type PTZRequest struct {
	Direction string `json:"direction"` // up, down, left, right, zoomin, zoomout, stop
	Speed     int    `json:"speed"`     // 1-100
}

// handlePTZControl 处理PTZ控制请求
func (s *Server) handlePTZControl(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的视频源ID"))
		return
	}

	// 获取视频源
	video, err := s.videoService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonErrorMsg(fmt.Sprintf("获取视频源失败: %v", err)))
		return
	}

	if video.CameraType != "2" {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("非球机不支持PTZ控制"))
		return
	}

	// 解析请求
	var req PTZRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg(fmt.Sprintf("无效的请求参数: %v", err)))
		return
	}

	// 验证参数
	if req.Direction == "" {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("缺少方向参数"))
		return
	}

	// 限制速度范围
	if req.Speed < 1 {
		req.Speed = 1
	} else if req.Speed > 100 {
		req.Speed = 100
	}

	// 获取对应协议的控制器
	controller, err := s.cameraFactory.GetController(video.Protocol)
	if err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg(fmt.Sprintf("不支持的摄像头协议: %v", err)))
		return
	}

	// 执行PTZ控制
	if err := controller.PTZControl(c.Request.Context(), video, req.Direction, req.Speed); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonErrorMsg(fmt.Sprintf("PTZ控制失败: %v", err)))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

// PresetRequest 表示预置点控制请求
type PresetRequest struct {
	Action   string `json:"action"`   // set, goto, remove
	PresetID int    `json:"presetId"` // 预置点ID
}

// handlePresetControl 处理预置点控制请求
func (s *Server) handlePresetControl(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的视频源ID"))
		return
	}

	// 获取视频源
	video, err := s.videoService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonErrorMsg(fmt.Sprintf("获取视频源失败: %v", err)))
		return
	}

	if video.CameraType != "2" {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("非球机不支持预置点控制"))
		return
	}

	// 解析请求
	var req PresetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg(fmt.Sprintf("无效的请求参数: %v", err)))
		return
	}

	// 验证参数
	if req.Action == "" {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("缺少操作参数"))
		return
	}

	if req.PresetID <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的预置点ID"))
		return
	}

	// 获取对应协议的控制器
	controller, err := s.cameraFactory.GetController(video.Protocol)
	if err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg(fmt.Sprintf("不支持的摄像头协议: %v", err)))
		return
	}

	// 执行预置点控制
	if err := controller.PresetControl(c.Request.Context(), video, req.Action, req.PresetID); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonErrorMsg(fmt.Sprintf("预置点控制失败: %v", err)))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

// ==================== 综合管道相关处理函数 ====================

// convertToPipelineBinding 将binding包的VideoAlgorithmBinding转换为pipeline包的VideoAlgorithmBinding
func (s *Server) convertToPipelineBinding(dbBinding *binding.VideoAlgorithmBinding) *pipeline.VideoAlgorithmBinding {
	if dbBinding == nil {
		return nil
	}

	return &pipeline.VideoAlgorithmBinding{
		ID:              dbBinding.ID,
		VideoID:         dbBinding.VideoID,
		AlgorithmID:     dbBinding.AlgorithmID,
		DetectionArea:   dbBinding.DetectionArea,
		AlertInterval:   dbBinding.AlertInterval,
		AlertWindow:     dbBinding.AlertWindow,
		AlertThreshold:  dbBinding.AlertThreshold,
		VoiceContent:    dbBinding.VoiceContent,
		DangerLevel:     dbBinding.DangerLevel,
		ExtensionFields: dbBinding.ExtensionFields,
		Status:          dbBinding.Status,
		Video: pipeline.Video{
			ID:          dbBinding.Video.ID,
			Name:        dbBinding.Video.Name,
			Type:        dbBinding.Video.Type,
			Protocol:    dbBinding.Video.Protocol,
			CameraID:    dbBinding.Video.CameraID,
			CameraType:  dbBinding.Video.CameraType,
			Description: dbBinding.Video.Description,
			StreamType:  dbBinding.Video.StreamType,
			CameraIP:    dbBinding.Video.CameraIP,
			Username:    dbBinding.Video.Username,
			Password:    dbBinding.Video.Password,
			Brand:       dbBinding.Video.Brand,
			StreamMode:  dbBinding.Video.StreamMode,
			URL:         dbBinding.Video.URL,
			Port:        dbBinding.Video.Port,
			Status:      dbBinding.Video.Status,
		},
		Algorithm: pipeline.Algorithm{
			ID:          dbBinding.Algorithm.ID,
			Name:        dbBinding.Algorithm.Name,
			Description: dbBinding.Algorithm.Description,
			Version:     dbBinding.Algorithm.Version,
			Path:        dbBinding.Algorithm.Path,
			Status:      dbBinding.Algorithm.Status,
		},
	}
}

// ==================== 综合管道相关处理函数 ====================

// handleCreateComprehensivePipeline 创建综合管道
func (s *Server) handleCreateComprehensivePipeline(c *gin.Context) {
	if s.pipelineService == nil {
		c.JSON(http.StatusServiceUnavailable, web.JsonErrorMsg("管道服务未启用"))
		return
	}

	// 获取所有激活的绑定关系
	bindings, err := s.bindingService.GetActiveBindings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	if len(bindings) == 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("没有找到激活的绑定关系"))
		return
	}

	// 转换绑定关系类型
	pipelineBindings := make([]*pipeline.VideoAlgorithmBinding, 0, len(bindings))
	for _, binding := range bindings {
		pipelineBindings = append(pipelineBindings, s.convertToPipelineBinding(binding))
	}

	// 创建综合管道
	err = s.pipelineService.CreateComprehensivePipeline(pipelineBindings)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(map[string]interface{}{
		"message":        "综合管道创建成功",
		"bindings_count": len(pipelineBindings),
	}))
}

// handleGetComprehensivePipelineStatus 获取综合管道状态
func (s *Server) handleGetComprehensivePipelineStatus(c *gin.Context) {
	if s.pipelineService == nil {
		c.JSON(http.StatusServiceUnavailable, web.JsonErrorMsg("管道服务未启用"))
		return
	}

	// 获取综合管道状态
	status, err := s.pipelineService.GetComprehensivePipelineStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(status))
}

// handleGetComprehensivePipelineConfig 获取综合管道配置
func (s *Server) handleGetComprehensivePipelineConfig(c *gin.Context) {
	if s.pipelineService == nil {
		c.JSON(http.StatusServiceUnavailable, web.JsonErrorMsg("管道服务未启用"))
		return
	}

	// 获取综合管道配置
	config, err := s.pipelineService.GetComprehensivePipelineConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(config))
}

// handleStartComprehensivePipeline 启动综合管道
func (s *Server) handleStartComprehensivePipeline(c *gin.Context) {
	if s.pipelineService == nil {
		c.JSON(http.StatusServiceUnavailable, web.JsonErrorMsg("管道服务未启用"))
		return
	}

	// 启动综合管道
	err := s.pipelineService.StartComprehensivePipeline()
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(map[string]interface{}{
		"message": "综合管道启动成功",
	}))
}

// handleStopComprehensivePipeline 停止综合管道
func (s *Server) handleStopComprehensivePipeline(c *gin.Context) {
	if s.pipelineService == nil {
		c.JSON(http.StatusServiceUnavailable, web.JsonErrorMsg("管道服务未启用"))
		return
	}

	// 停止综合管道
	err := s.pipelineService.StopComprehensivePipeline()
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(map[string]interface{}{
		"message": "综合管道停止成功",
	}))
}

// handleRebuildComprehensivePipeline 重建综合管道
func (s *Server) handleRebuildComprehensivePipeline(c *gin.Context) {
	if s.pipelineService == nil {
		c.JSON(http.StatusServiceUnavailable, web.JsonErrorMsg("管道服务未启用"))
		return
	}

	// 获取所有激活的绑定关系
	bindings, err := s.bindingService.GetActiveBindings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	// 转换绑定关系类型
	pipelineBindings := make([]*pipeline.VideoAlgorithmBinding, 0, len(bindings))
	for _, binding := range bindings {
		pipelineBindings = append(pipelineBindings, s.convertToPipelineBinding(binding))
	}

	// 重建综合管道
	err = s.pipelineService.UpdateComprehensivePipeline(pipelineBindings)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(map[string]interface{}{
		"message":        "综合管道重建成功",
		"bindings_count": len(pipelineBindings),
	}))
}

// handleDeleteComprehensivePipeline 删除综合管道
func (s *Server) handleDeleteComprehensivePipeline(c *gin.Context) {
	if s.pipelineService == nil {
		c.JSON(http.StatusServiceUnavailable, web.JsonErrorMsg("管道服务未启用"))
		return
	}

	// 删除综合管道
	err := s.pipelineService.DeleteComprehensivePipeline()
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(map[string]interface{}{
		"message": "综合管道删除成功",
	}))
}
