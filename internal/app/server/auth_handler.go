package server

import (
	"ecp/internal/app/user"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/mlogclub/simple/web"
)

// 认证相关处理函数

// handleLogin 处理用户登录
func (s *Server) handleLogin(c *gin.Context) {
	var req user.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	// 登录验证
	tokenResp, err := s.userService.Login(&req)
	if err != nil {
		c.JSO<PERSON>(http.StatusUnauthorized, web.JsonErrorMsg(err.Error()))
		return
	}

	c.JSO<PERSON>(http.StatusOK, web.JsonData(tokenResp))
}

// handleRegister 处理用户注册
func (s *Server) handleRegister(c *gin.Context) {
	var req user.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.<PERSON>SO<PERSON>(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	// 注册用户
	newUser, err := s.userService.Register(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg(err.Error()))
		return
	}

	c.JSON(http.StatusCreated, web.JsonData(newUser))
}

// handleRefreshToken 处理刷新令牌
func (s *Server) handleRefreshToken(c *gin.Context) {
	type RefreshRequest struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	// 刷新令牌
	tokenResp, err := s.userService.RefreshToken(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, web.JsonErrorMsg(err.Error()))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(tokenResp))
}

// handleGetCurrentUser 获取当前用户信息
func (s *Server) handleGetCurrentUser(c *gin.Context) {
	userID, _ := c.Get("user_id")
	user, err := s.userService.GetByID(userID.(int64))
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(user))
}

// handleUpdateCurrentUser 更新当前用户信息
func (s *Server) handleUpdateCurrentUser(c *gin.Context) {
	userID, _ := c.Get("user_id")

	// 获取当前用户
	currentUser, err := s.userService.GetByID(userID.(int64))
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	// 绑定请求数据
	var updateData struct {
		Name   string `json:"name"`
		Email  string `json:"email"`
		Mobile string `json:"mobile"`
	}
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	// 更新字段
	currentUser.Name = updateData.Name
	currentUser.Email = updateData.Email
	currentUser.Mobile = updateData.Mobile

	// 保存更新
	if err := s.userService.Update(currentUser); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(currentUser))
}

// handleChangePassword 修改当前用户密码
func (s *Server) handleChangePassword(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req struct {
		OldPassword string `json:"old_password" binding:"required"`
		NewPassword string `json:"new_password" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	// 修改密码
	if err := s.userService.ChangePassword(userID.(int64), req.OldPassword, req.NewPassword); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg(err.Error()))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

// 管理员用户管理API

// handleGetUsers 获取用户列表
func (s *Server) handleGetUsers(c *gin.Context) {
	username := c.Query("username")
	role := c.Query("role")
	status := c.Query("status")
	page := getIntParam(c, "page", 1)
	pageSize := getIntParam(c, "pageSize", 20)

	users, paging, err := s.userService.Query(username, role, status, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonPageData(users, paging))
}

// handleGetUser 获取单个用户
func (s *Server) handleGetUser(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的用户ID"))
		return
	}

	user, err := s.userService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(user))
}

// handleCreateUser 创建用户
func (s *Server) handleCreateUser(c *gin.Context) {
	var req user.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	// 注册用户
	newUser, err := s.userService.Register(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg(err.Error()))
		return
	}

	c.JSON(http.StatusCreated, web.JsonData(newUser))
}

// handleUpdateUser 更新用户
func (s *Server) handleUpdateUser(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的用户ID"))
		return
	}

	// 获取现有用户
	existingUser, err := s.userService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	// 绑定请求数据
	var updateData struct {
		Username string `json:"username"`
		Password string `json:"password"`
		Name     string `json:"name"`
		Email    string `json:"email"`
		Mobile   string `json:"mobile"`
		Role     string `json:"role"`
		Status   string `json:"status"`
	}
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	// 更新字段
	if updateData.Username != "" {
		existingUser.Username = updateData.Username
	}
	if updateData.Password != "" {
		existingUser.Password = updateData.Password
	}
	if updateData.Name != "" {
		existingUser.Name = updateData.Name
	}
	if updateData.Email != "" {
		existingUser.Email = updateData.Email
	}
	if updateData.Mobile != "" {
		existingUser.Mobile = updateData.Mobile
	}
	if updateData.Role != "" {
		existingUser.Role = updateData.Role
	}
	if updateData.Status != "" {
		existingUser.Status = updateData.Status
	}

	// 保存更新
	if err := s.userService.Update(existingUser); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(existingUser))
}

// handleDeleteUser 删除用户
func (s *Server) handleDeleteUser(c *gin.Context) {
	id := getInt64Param(c, "id", 0)
	if id <= 0 {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的用户ID"))
		return
	}

	// 检查是否为当前用户
	currentUserID, _ := c.Get("user_id")
	if currentUserID.(int64) == id {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("不能删除当前登录用户"))
		return
	}

	if err := s.userService.Delete(id); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}
