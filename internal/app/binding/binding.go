package binding

import (
	"errors"
	"time"

	"ecp/internal/pkg/database"

	"github.com/mlogclub/simple/sqls"
	"gorm.io/gorm"
)

// VideoAlgorithmBinding 表示视频源和算法的绑定关系，使用数据库中定义的结构
type VideoAlgorithmBinding = database.VideoAlgorithmBinding

// BindingService 提供视频源和算法绑定关系管理功能
type BindingService struct {
	db *gorm.DB
}

// NewBindingService 创建一个新的绑定服务实例
func NewBindingService(db *gorm.DB) *BindingService {
	return &BindingService{
		db: db,
	}
}

// GetByVideoID 获取指定视频源的所有算法绑定
func (s *BindingService) GetByVideoID(videoID int64) ([]VideoAlgorithmBinding, error) {
	var bindings []VideoAlgorithmBinding
	if err := s.db.Preload("Video").Preload("Algorithm").Where("video_id = ?", videoID).Find(&bindings).Error; err != nil {
		return nil, err
	}
	return bindings, nil
}

// GetByAlgorithmID 获取指定算法的所有视频源绑定
func (s *BindingService) GetByAlgorithmID(algorithmID int64) ([]VideoAlgorithmBinding, error) {
	var bindings []VideoAlgorithmBinding
	if err := s.db.Preload("Video").Preload("Algorithm").Where("algorithm_id = ?", algorithmID).Find(&bindings).Error; err != nil {
		return nil, err
	}
	return bindings, nil
}

// GetByID 通过ID获取绑定关系
func (s *BindingService) GetByID(id int64) (*VideoAlgorithmBinding, error) {
	var binding VideoAlgorithmBinding
	if err := s.db.Preload("Video").Preload("Algorithm").First(&binding, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("绑定关系不存在")
		}
		return nil, err
	}
	return &binding, nil
}

// GetActiveBindings 获取所有激活状态的绑定关系
func (s *BindingService) GetActiveBindings() ([]*VideoAlgorithmBinding, error) {
	var bindings []*VideoAlgorithmBinding
	if err := s.db.Preload("Video").Preload("Algorithm").Where("status = ?", "active").Find(&bindings).Error; err != nil {
		return nil, err
	}
	return bindings, nil
}

// Create 创建新的绑定关系
func (s *BindingService) Create(binding *VideoAlgorithmBinding) error {
	// 检查是否已存在相同的绑定
	var count int64
	if err := s.db.Model(&VideoAlgorithmBinding{}).Where("video_id = ? AND algorithm_id = ?", binding.VideoID, binding.AlgorithmID).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return errors.New("该视频源和算法的绑定关系已存在")
	}

	binding.CreatedAt = time.Now()
	binding.UpdatedAt = time.Now()
	return s.db.Create(binding).Error
}

// Update 更新绑定关系
func (s *BindingService) Update(binding *VideoAlgorithmBinding) error {
	binding.UpdatedAt = time.Now()
	return s.db.Save(binding).Error
}

// Delete 删除绑定关系
func (s *BindingService) Delete(id int64) error {
	return s.db.Delete(&VideoAlgorithmBinding{}, id).Error
}

// DeleteByVideoID 删除指定视频源的所有绑定关系
func (s *BindingService) DeleteByVideoID(videoID int64) error {
	return s.db.Where("video_id = ?", videoID).Delete(&VideoAlgorithmBinding{}).Error
}

// DeleteByAlgorithmID 删除指定算法的所有绑定关系
func (s *BindingService) DeleteByAlgorithmID(algorithmID int64) error {
	return s.db.Where("algorithm_id = ?", algorithmID).Delete(&VideoAlgorithmBinding{}).Error
}

// UpdateStatus 更新绑定状态
func (s *BindingService) UpdateStatus(id int64, status string) error {
	return s.db.Model(&VideoAlgorithmBinding{}).Where("id = ?", id).Update("status", status).Error
}

// Query 查询绑定关系
func (s *BindingService) Query(videoID int64, algorithmID int64, status string, page, pageSize int) ([]VideoAlgorithmBinding, *sqls.Paging, error) {
	var bindings []VideoAlgorithmBinding

	// 创建查询条件
	cnd := sqls.NewCnd()
	if videoID > 0 {
		cnd.Eq("video_id", videoID)
	}
	if algorithmID > 0 {
		cnd.Eq("algorithm_id", algorithmID)
	}
	if status != "" {
		cnd.Eq("status", status)
	}

	// 分页
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	cnd.Page(page, pageSize)

	// 排序
	cnd.Desc("id")

	// 执行查询
	cnd.Find(s.db.Preload("Video").Preload("Algorithm"), &bindings)

	// 计算总数
	if cnd.Paging != nil {
		cnd.Paging.Total = cnd.Count(s.db, &VideoAlgorithmBinding{})
	}

	return bindings, cnd.Paging, nil
}

// BatchCreate 批量创建绑定关系
func (s *BindingService) BatchCreate(videoID int64, bindings []VideoAlgorithmBinding) error {
	// 先删除该视频源的所有绑定
	if err := s.DeleteByVideoID(videoID); err != nil {
		return err
	}

	// 如果绑定数组为空，则只执行删除操作，不进行创建
	if len(bindings) == 0 {
		return nil
	}

	// 批量创建新的绑定
	for i := range bindings {
		bindings[i].VideoID = videoID
		bindings[i].CreatedAt = time.Now()
		bindings[i].UpdatedAt = time.Now()
	}

	return s.db.Create(&bindings).Error
}

// GetActiveBindingsByVideoID 获取视频源的活跃绑定关系
func (s *BindingService) GetActiveBindingsByVideoID(videoID int64) ([]VideoAlgorithmBinding, error) {
	var bindings []VideoAlgorithmBinding
	if err := s.db.Preload("Video").Preload("Algorithm").Where("video_id = ? AND status = ?", videoID, "active").Find(&bindings).Error; err != nil {
		return nil, err
	}
	return bindings, nil
}
