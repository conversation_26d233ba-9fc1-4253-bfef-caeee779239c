package camera

import (
	"context"
	"fmt"
	"io"
	"net/http"

	"ecp/internal/app/gb28181"
	"ecp/internal/app/onvif"
	"ecp/internal/pkg/database"
)

// CameraController 定义摄像头控制接口
type CameraController interface {
	// GetSnapshot 获取摄像头当前快照
	GetSnapshot(ctx context.Context, video *database.Video) (io.ReadCloser, string, error)

	// PTZControl 摄像头云台控制
	// direction: up, down, left, right, zoomin, zoomout, stop
	// speed: 1-100 (百分比)
	PTZControl(ctx context.Context, video *database.Video, direction string, speed int) error

	// GetRTSPURL 获取RTSP流地址
	GetRTSPURL(ctx context.Context, video *database.Video) (string, error)

	// PresetControl 预置点控制
	// action: set, goto, remove
	// presetID: 预置点ID
	PresetControl(ctx context.Context, video *database.Video, action string, presetID int) error
}

// CameraControllerFactory 摄像头控制器工厂
type CameraControllerFactory struct {
	onvifService   *onvif.Service
	gb28181Service *gb28181.Service
}

// NewCameraControllerFactory 创建摄像头控制器工厂
func NewCameraControllerFactory(onvifService *onvif.Service, gb28181Service *gb28181.Service) *CameraControllerFactory {
	return &CameraControllerFactory{
		onvifService:   onvifService,
		gb28181Service: gb28181Service,
	}
}

// GetController 根据视频源协议获取对应的控制器
func (f *CameraControllerFactory) GetController(protocol string) (CameraController, error) {
	switch protocol {
	case "onvif":
		return &ONVIFController{service: f.onvifService}, nil
	case "gb28181":
		return &GB28181Controller{service: f.gb28181Service}, nil
	default:
		return nil, fmt.Errorf("不支持的摄像头协议: %s", protocol)
	}
}

// ONVIFController ONVIF协议摄像头控制器
type ONVIFController struct {
	service *onvif.Service
}

// GetSnapshot 获取ONVIF摄像头当前快照
func (c *ONVIFController) GetSnapshot(ctx context.Context, video *database.Video) (io.ReadCloser, string, error) {
	// 解析ONVIF URL
	creds, err := c.service.ParseONVIFURL(video.URL)
	if err != nil {
		return nil, "", fmt.Errorf("解析ONVIF URL失败: %w", err)
	}

	// 连接设备获取快照地址
	deviceInfo, err := c.service.ConnectDevice(ctx, creds)
	if err != nil {
		return nil, "", fmt.Errorf("连接ONVIF设备失败: %w", err)
	}

	if deviceInfo.SnapshotURL == "" {
		return nil, "", fmt.Errorf("设备不支持快照功能")
	}

	// 获取快照图片
	resp, err := http.Get(deviceInfo.SnapshotURL)
	if err != nil {
		return nil, "", fmt.Errorf("获取快照失败: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		resp.Body.Close()
		return nil, "", fmt.Errorf("获取快照返回错误状态码: %d", resp.StatusCode)
	}

	contentType := resp.Header.Get("Content-Type")
	return resp.Body, contentType, nil
}

// PTZControl ONVIF摄像头云台控制
func (c *ONVIFController) PTZControl(ctx context.Context, video *database.Video, direction string, speed int) error {
	// 解析ONVIF URL
	creds, err := c.service.ParseONVIFURL(video.URL)
	if err != nil {
		return fmt.Errorf("解析ONVIF URL失败: %w", err)
	}

	// 连接设备获取设备信息
	deviceInfo, err := c.service.ConnectDevice(ctx, creds)
	if err != nil {
		return fmt.Errorf("连接ONVIF设备失败: %w", err)
	}

	if len(deviceInfo.Profiles) == 0 {
		return fmt.Errorf("设备没有可用的媒体配置")
	}

	// 计算速度值 (ONVIF速度范围是-1.0到1.0)
	speedValue := float64(speed) / 100.0
	if speedValue > 1.0 {
		speedValue = 1.0
	}

	// 创建ONVIF客户端
	deviceURL := fmt.Sprintf("http://%s:%d/onvif/device_service", creds.Host, creds.Port)
	client := onvif.NewONVIFClient(deviceURL, creds.Username, creds.Password)

	// 根据方向执行相应的PTZ控制
	var ptzErr error
	switch direction {
	case "up":
		ptzErr = client.ContinuousMove(ctx, deviceInfo.Profiles[0].Token, 0, speedValue, 0)
	case "down":
		ptzErr = client.ContinuousMove(ctx, deviceInfo.Profiles[0].Token, 0, -speedValue, 0)
	case "left":
		ptzErr = client.ContinuousMove(ctx, deviceInfo.Profiles[0].Token, -speedValue, 0, 0)
	case "right":
		ptzErr = client.ContinuousMove(ctx, deviceInfo.Profiles[0].Token, speedValue, 0, 0)
	case "zoomin":
		ptzErr = client.ContinuousMove(ctx, deviceInfo.Profiles[0].Token, 0, 0, speedValue)
	case "zoomout":
		ptzErr = client.ContinuousMove(ctx, deviceInfo.Profiles[0].Token, 0, 0, -speedValue)
	case "stop":
		ptzErr = client.Stop(ctx, deviceInfo.Profiles[0].Token)
	default:
		return fmt.Errorf("不支持的PTZ控制命令: %s", direction)
	}

	if ptzErr != nil {
		return fmt.Errorf("PTZ控制失败: %w", ptzErr)
	}

	return nil
}

// GetRTSPURL 获取ONVIF摄像头RTSP流地址
func (c *ONVIFController) GetRTSPURL(ctx context.Context, video *database.Video) (string, error) {
	return c.service.GenerateRTSPURL(ctx, video.URL)
}

// PresetControl ONVIF摄像头预置点控制
func (c *ONVIFController) PresetControl(ctx context.Context, video *database.Video, action string, presetID int) error {
	// 解析ONVIF URL
	creds, err := c.service.ParseONVIFURL(video.URL)
	if err != nil {
		return fmt.Errorf("解析ONVIF URL失败: %w", err)
	}

	// 连接设备获取设备信息
	deviceInfo, err := c.service.ConnectDevice(ctx, creds)
	if err != nil {
		return fmt.Errorf("连接ONVIF设备失败: %w", err)
	}

	if len(deviceInfo.Profiles) == 0 {
		return fmt.Errorf("设备没有可用的媒体配置")
	}

	// 创建ONVIF客户端
	deviceURL := fmt.Sprintf("http://%s:%d/onvif/device_service", creds.Host, creds.Port)
	client := onvif.NewONVIFClient(deviceURL, creds.Username, creds.Password)

	// 根据操作执行相应的预置点控制
	switch action {
	case "set":
		presetName := fmt.Sprintf("Preset%d", presetID)
		if err := client.SetPreset(ctx, deviceInfo.Profiles[0].Token, presetName, presetID); err != nil {
			return fmt.Errorf("设置预置点失败: %w", err)
		}
	case "goto":
		if err := client.GotoPreset(ctx, deviceInfo.Profiles[0].Token, presetID); err != nil {
			return fmt.Errorf("调用预置点失败: %w", err)
		}
	case "remove":
		if err := client.RemovePreset(ctx, deviceInfo.Profiles[0].Token, presetID); err != nil {
			return fmt.Errorf("删除预置点失败: %w", err)
		}
	default:
		return fmt.Errorf("不支持的预置点操作: %s", action)
	}

	return nil
}

// GB28181Controller GB28181协议摄像头控制器
type GB28181Controller struct {
	service *gb28181.Service
}

// GetSnapshot 获取GB28181摄像头当前快照
func (c *GB28181Controller) GetSnapshot(ctx context.Context, video *database.Video) (io.ReadCloser, string, error) {
	// GB28181协议没有直接获取快照的API，需要通过截取视频帧实现
	// 这里我们可以调用GB28181服务的扩展功能来获取快照

	if video.CameraID == "" {
		return nil, "", fmt.Errorf("GB28181视频源缺少设备ID")
	}

	// 构建快照API URL
	snapshotURL := fmt.Sprintf("%s/api/device/snapshot/%s/%s",
		c.service.GetBaseURL(), video.CameraID, video.CameraID)

	// 获取快照图片
	resp, err := http.Get(snapshotURL)
	if err != nil {
		return nil, "", fmt.Errorf("获取快照失败: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		resp.Body.Close()
		return nil, "", fmt.Errorf("获取快照返回错误状态码: %d", resp.StatusCode)
	}

	contentType := resp.Header.Get("Content-Type")
	return resp.Body, contentType, nil
}

// PTZControl GB28181摄像头云台控制
func (c *GB28181Controller) PTZControl(ctx context.Context, video *database.Video, direction string, speed int) error {
	if video.CameraID == "" {
		return fmt.Errorf("GB28181视频源缺少设备ID")
	}

	// 转换速度值 (GB28181速度范围是1-255)
	gbSpeed := int(float64(speed) * 2.55)
	if gbSpeed < 1 {
		gbSpeed = 1
	} else if gbSpeed > 255 {
		gbSpeed = 255
	}

	// 创建PTZ参数
	params := gb28181.PTZParams{
		HorizontalSpeed: gbSpeed,
		VerticalSpeed:   gbSpeed,
		ZoomSpeed:       gbSpeed,
	}

	// 根据方向设置命令
	switch direction {
	case "up", "down", "left", "right", "zoomin", "zoomout", "stop":
		return c.service.PTZControl(video, direction, params)
	default:
		return fmt.Errorf("不支持的PTZ控制命令: %s", direction)
	}
}

// GetRTSPURL 获取GB28181摄像头RTSP流地址
func (c *GB28181Controller) GetRTSPURL(ctx context.Context, video *database.Video) (string, error) {
	return c.service.GetRealStreamURL(video)
}

// PresetControl GB28181摄像头预置点控制
func (c *GB28181Controller) PresetControl(ctx context.Context, video *database.Video, action string, presetID int) error {
	if video.CameraID == "" {
		return fmt.Errorf("GB28181视频源缺少设备ID")
	}

	// 创建PTZ参数
	params := gb28181.PTZParams{
		HorizontalSpeed: 50, // 默认速度
		VerticalSpeed:   50,
		ZoomSpeed:       50,
	}

	// 根据操作执行相应的预置点控制
	var command string
	switch action {
	case "set":
		command = fmt.Sprintf("preset_set_%d", presetID)
	case "goto":
		command = fmt.Sprintf("preset_goto_%d", presetID)
	case "remove":
		command = fmt.Sprintf("preset_remove_%d", presetID)
	default:
		return fmt.Errorf("不支持的预置点操作: %s", action)
	}

	return c.service.PTZControl(video, command, params)
}
