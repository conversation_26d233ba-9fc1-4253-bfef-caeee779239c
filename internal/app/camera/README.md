# 摄像头控制接口层

本模块实现了一个统一的摄像头控制接口层，用于抽象不同协议（ONVIF、GB28181）的摄像头控制操作，提供统一的API接口。

## 设计思路

采用了策略模式和工厂模式的设计思想：

1. **接口定义**：通过`CameraController`接口定义了摄像头控制的标准方法
2. **具体实现**：针对不同协议（ONVIF、GB28181）分别实现了接口
3. **工厂类**：通过`CameraControllerFactory`工厂类根据协议类型创建对应的控制器实例

这种设计使得上层应用可以通过统一的接口操作不同协议的摄像头，而不需要关心具体的实现细节。

## 主要功能

摄像头控制接口层提供以下核心功能：

1. **获取摄像头当前快照**：获取摄像头的实时图像
2. **摄像头PTZ控制**：控制摄像头的云台移动（上、下、左、右、放大、缩小等）
3. **获取RTSP流地址**：获取摄像头的实时视频流地址
4. **预置点控制**：设置、调用和删除摄像头预置点

## 使用方法

### 初始化

```go
// 创建ONVIF服务
onvifService := onvif.NewService()

// 创建GB28181服务
gb28181Service := gb28181.NewService("http://localhost:18080", db.DB)

// 创建摄像头控制器工厂
cameraFactory := camera.NewCameraControllerFactory(onvifService, gb28181Service)
```

### 获取摄像头控制器

```go
// 获取视频源
var video database.Video
db.First(&video, videoID)

// 获取对应协议的控制器
controller, err := cameraFactory.GetController(video.Protocol)
if err != nil {
    return fmt.Errorf("获取摄像头控制器失败: %w", err)
}
```

### 获取摄像头快照

```go
// 获取快照
imageData, contentType, err := controller.GetSnapshot(ctx, &video)
if err != nil {
    return fmt.Errorf("获取快照失败: %w", err)
}
defer imageData.Close()

// 使用快照数据
// ...
```

### 控制摄像头PTZ

```go
// 向上移动摄像头，速度为50%
err := controller.PTZControl(ctx, &video, "up", 50)
if err != nil {
    return fmt.Errorf("PTZ控制失败: %w", err)
}

// 停止移动
err = controller.PTZControl(ctx, &video, "stop", 0)
if err != nil {
    return fmt.Errorf("停止PTZ失败: %w", err)
}
```

### 获取RTSP流地址

```go
// 获取RTSP流地址
rtspURL, err := controller.GetRTSPURL(ctx, &video)
if err != nil {
    return fmt.Errorf("获取RTSP流地址失败: %w", err)
}

// 使用RTSP地址
// ...
```

### 预置点控制

```go
// 设置预置点1
err := controller.PresetControl(ctx, &video, "set", 1)
if err != nil {
    return fmt.Errorf("设置预置点失败: %w", err)
}

// 调用预置点1
err = controller.PresetControl(ctx, &video, "goto", 1)
if err != nil {
    return fmt.Errorf("调用预置点失败: %w", err)
}

// 删除预置点1
err = controller.PresetControl(ctx, &video, "remove", 1)
if err != nil {
    return fmt.Errorf("删除预置点失败: %w", err)
}
```

## 扩展性

该接口层设计具有良好的扩展性，如需支持新的摄像头协议，只需：

1. 实现`CameraController`接口
2. 在`CameraControllerFactory.GetController`方法中添加对应的实现类

例如，要添加对RTSP协议的直接支持：

```go
// RTSPController RTSP协议摄像头控制器
type RTSPController struct {
    // ...
}

// 实现CameraController接口的所有方法
// ...

// 在工厂方法中添加支持
func (f *CameraControllerFactory) GetController(protocol string) (CameraController, error) {
    switch protocol {
    case "onvif":
        return &ONVIFController{service: f.onvifService}, nil
    case "gb28181":
        return &GB28181Controller{service: f.gb28181Service}, nil
    case "rtsp":
        return &RTSPController{}, nil
    default:
        return nil, fmt.Errorf("不支持的摄像头协议: %s", protocol)
    }
}
``` 