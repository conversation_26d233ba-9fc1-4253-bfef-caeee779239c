package middleware

import (
	"ecp/internal/app/user"
	"ecp/internal/pkg/database"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/mlogclub/simple/web"
	"gorm.io/gorm"
)

// JWTAuthMiddleware JWT认证中间件
func JWTAuthMiddleware(db *gorm.DB) gin.HandlerFunc {
	userService := user.NewUserService(db)

	return func(c *gin.Context) {
		// 从Authorization头获取令牌
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, web.JsonErrorMsg("未提供认证令牌"))
			c.Abort()
			return
		}

		// 检查Bearer前缀
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			c.JSON(http.StatusUnauthorized, web.JsonErrorMsg("无效的认证格式"))
			c.Abort()
			return
		}

		// 解析令牌
		tokenString := parts[1]
		claims, err := userService.ParseToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, web.JsonErrorMsg("无效的认证令牌: "+err.Error()))
			c.Abort()
			return
		}

		// 验证用户是否存在
		var dbUser database.User
		if err := db.First(&dbUser, claims.UserID).Error; err != nil {
			c.JSON(http.StatusUnauthorized, web.JsonErrorMsg("用户不存在或已被删除"))
			c.Abort()
			return
		}

		// 验证用户状态
		if dbUser.Status != "active" {
			c.JSON(http.StatusForbidden, web.JsonErrorMsg("用户已被禁用"))
			c.Abort()
			return
		}

		// 将用户信息存储在上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("user", &dbUser)

		c.Next()
	}
}

// AdminAuthMiddleware 管理员权限中间件
func AdminAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户角色
		role, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, web.JsonErrorMsg("未经认证的请求"))
			c.Abort()
			return
		}

		// 检查是否为管理员
		if role != "admin" {
			c.JSON(http.StatusForbidden, web.JsonErrorMsg("需要管理员权限"))
			c.Abort()
			return
		}

		c.Next()
	}
}
