package algorithm

import (
	"errors"
	"time"

	"ecp/internal/pkg/config"
	"ecp/internal/pkg/database"
	"ecp/internal/pkg/utils"

	"github.com/mlogclub/simple/sqls"
	"gorm.io/gorm"
)

// Algorithm 表示一个算法，使用数据库中定义的结构
type Algorithm = database.Algorithm

// AlgorithmService 提供算法管理功能
type AlgorithmService struct {
	db *gorm.DB
}

// NewAlgorithmService 创建一个新的算法服务实例
func NewAlgorithmService(db *gorm.DB) *AlgorithmService {
	return &AlgorithmService{
		db: db,
	}
}

// GetAll 获取所有算法
func (s *AlgorithmService) GetAll() ([]Algorithm, error) {
	var algorithms []Algorithm
	if err := s.db.Find(&algorithms).Error; err != nil {
		return nil, err
	}
	return algorithms, nil
}

// GetByID 通过ID获取算法
func (s *AlgorithmService) GetByID(id int64) (*Algorithm, error) {
	var algorithm Algorithm
	if err := s.db.First(&algorithm, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("算法不存在")
		}
		return nil, err
	}
	return &algorithm, nil
}

// Import 导入新的算法
func (s *AlgorithmService) Import(algorithm *Algorithm, file []byte) error {
	// 从配置文件获取算法存储路径
	cfg := config.GetConfig()
	storagePath := cfg.Algorithm.StoragePath

	// 生成文件路径
	filePath, err := utils.GenerateFilePath(storagePath, "algorithm")
	if err != nil {
		return err
	}

	// 保存文件
	if err := utils.SaveFile(file, filePath); err != nil {
		return err
	}

	// 设置算法路径
	algorithm.Path = filePath
	algorithm.CreatedAt = time.Now()
	algorithm.UpdatedAt = time.Now()

	// 保存到数据库
	return s.db.Create(algorithm).Error
}

// Delete 删除算法
func (s *AlgorithmService) Delete(id int64) error {
	// 先查询算法信息
	algorithm, err := s.GetByID(id)
	if err != nil {
		return err
	}

	// 删除算法文件
	if algorithm.Path != "" {
		_ = utils.DeleteFile(algorithm.Path)
	}

	// 从数据库删除
	return s.db.Delete(&Algorithm{}, id).Error
}

// Activate 激活算法
func (s *AlgorithmService) Activate(id int64) error {
	return s.db.Model(&Algorithm{}).Where("id = ?", id).Update("status", "active").Error
}

// Deactivate 停用算法
func (s *AlgorithmService) Deactivate(id int64) error {
	return s.db.Model(&Algorithm{}).Where("id = ?", id).Update("status", "inactive").Error
}

// Query 查询算法
func (s *AlgorithmService) Query(name string, status string, page, pageSize int) ([]Algorithm, *sqls.Paging, error) {
	var algorithms []Algorithm

	// 创建查询条件
	cnd := sqls.NewCnd()
	if name != "" {
		cnd.Like("name", "%"+name+"%")
	}
	if status != "" {
		cnd.Eq("status", status)
	}

	// 分页
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	cnd.Page(page, pageSize)

	// 排序
	cnd.Desc("id")

	// 执行查询
	cnd.Find(s.db, &algorithms)

	// 计算总数
	if cnd.Paging != nil {
		cnd.Paging.Total = cnd.Count(s.db, &Algorithm{})
	}

	return algorithms, cnd.Paging, nil
}
