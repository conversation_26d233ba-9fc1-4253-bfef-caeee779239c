# 算法仓库模块

这个模块负责边缘计算平台的算法管理，包括算法的导入和删除等功能。

## 功能

- 导入新的算法
- 删除不需要的算法
- 查询算法列表和详情
- 管理算法的状态（激活/停用）

## 数据结构

算法（Algorithm）包含以下字段：

- ID：唯一标识符
- 名称：算法名称
- 版本：算法版本号
- 描述：算法描述信息
- 路径：算法文件存储路径
- 状态：算法状态（激活/停用）
- 创建时间：算法添加时间
- 更新时间：算法信息最后更新时间

## 使用方法

```go
// 创建算法服务实例
algorithmService := algorithm.NewAlgorithmService()

// 获取所有算法
algorithms, err := algorithmService.GetAll()

// 获取特定算法
alg, err := algorithmService.GetByID(1)

// 导入新算法
newAlgorithm := &algorithm.Algorithm{
    Name: "人脸识别",
    Version: "1.0.0",
    Description: "基于深度学习的人脸识别算法",
}
err := algorithmService.Import(newAlgorithm, fileBytes)

// 删除算法
err := algorithmService.Delete(1)

// 激活算法
err := algorithmService.Activate(1)

// 停用算法
err := algorithmService.Deactivate(1)
``` 