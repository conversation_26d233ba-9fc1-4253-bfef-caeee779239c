# 告警记录模块

这个模块负责边缘计算平台的告警记录管理，包括告警记录的查询和删除等功能。

## 功能

- 查询告警记录列表和详情
- 更新告警记录状态
- 删除不需要的告警记录
- 按条件筛选告警记录

## 数据结构

告警记录（Alert）包含以下字段：

- ID：唯一标识符
- 视频ID：关联的视频源ID
- 视频名称：关联的视频源名称
- 算法ID：关联的算法ID
- 算法名称：关联的算法名称
- 内容：告警内容
- 图片路径：告警截图存储路径
- 级别：告警级别（信息、警告、错误）
- 状态：告警状态（新建、已处理、已忽略）
- 创建时间：告警生成时间
- 更新时间：告警信息最后更新时间

## 使用方法

```go
// 创建告警服务实例
alertService := alert.NewAlertService()

// 获取所有告警记录
alerts, err := alertService.GetAll()

// 获取特定告警记录
alert, err := alertService.GetByID(1)

// 创建新告警记录
newAlert := &alert.Alert{
    VideoID: 1,
    VideoName: "前门摄像头",
    AlgorithmID: 2,
    AlgorithmName: "人脸识别",
    Content: "检测到未授权人员",
    Level: "warning",
    Status: "new",
}
err := alertService.Create(newAlert)

// 更新告警记录状态
err := alertService.UpdateStatus(1, "processed")

// 删除告警记录
err := alertService.Delete(1)

// 查询告警记录
startTime := time.Now().Add(-24 * time.Hour)
endTime := time.Now()
alerts, err := alertService.Query(1, 2, "warning", "new", startTime, endTime)
``` 