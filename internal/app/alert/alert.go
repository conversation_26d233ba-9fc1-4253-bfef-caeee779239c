package alert

import (
	"errors"
	"time"

	"ecp/internal/pkg/database"
	"ecp/internal/pkg/utils"

	"github.com/mlogclub/simple/sqls"

	"gorm.io/gorm"
)

// Alert 表示一条告警记录，使用数据库中定义的结构
type Alert = database.Alert

// AlertService 提供告警记录管理功能
type AlertService struct {
	db *gorm.DB
}

// NewAlertService 创建一个新的告警服务实例
func NewAlertService(db *gorm.DB) *AlertService {
	return &AlertService{
		db: db,
	}
}

// GetAll 获取所有告警记录
func (s *AlertService) GetAll() ([]Alert, error) {
	var alerts []Alert
	if err := s.db.Find(&alerts).Error; err != nil {
		return nil, err
	}
	return alerts, nil
}

// GetByID 通过ID获取告警记录
func (s *AlertService) GetByID(id int64) (*Alert, error) {
	var alert Alert
	if err := s.db.First(&alert, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("告警记录不存在")
		}
		return nil, err
	}
	return &alert, nil
}

// Create 创建新的告警记录
func (s *AlertService) Create(alert *Alert) error {
	alert.CreatedAt = time.Now()
	alert.UpdatedAt = time.Now()
	return s.db.Create(alert).Error
}

// UpdateStatus 更新告警记录状态
func (s *AlertService) UpdateStatus(id int64, status string) error {
	return s.db.Model(&Alert{}).Where("id = ?", id).Update("status", status).Error
}

// Delete 删除告警记录
func (s *AlertService) Delete(id int64) error {
	// 先查询告警记录信息
	alert, err := s.GetByID(id)
	if err != nil {
		return err
	}

	// 删除告警图片
	if alert.ImagePath != "" {
		_ = utils.DeleteFile(alert.ImagePath)
	}

	// 删除原始图片
	if alert.OriginalImage != "" {
		_ = utils.DeleteFile(alert.OriginalImage)
	}

	// 从数据库删除
	return s.db.Delete(&Alert{}, id).Error
}

// Query 查询告警记录
func (s *AlertService) Query(videoID int64, algorithmID int64, level string, type_ string, status string, startTime, endTime time.Time, page, pageSize int) ([]Alert, *sqls.Paging, error) {
	var alerts []Alert

	// 创建查询条件
	cnd := sqls.NewCnd()
	if videoID > 0 {
		cnd.Eq("video_id", videoID)
	}
	if algorithmID > 0 {
		cnd.Eq("algorithm_id", algorithmID)
	}
	if level != "" {
		cnd.Eq("level", level)
	}
	if type_ != "" {
		cnd.Eq("type", type_)
	}
	if status != "" {
		cnd.Eq("status", status)
	}

	// 时间范围
	if !startTime.IsZero() {
		cnd.Gte("created_at", startTime)
	}
	if !endTime.IsZero() {
		cnd.Lte("created_at", endTime)
	}

	// 分页参数验证
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100
	}

	// 分页
	cnd.Page(page, pageSize)

	// 排序
	cnd.Desc("id")

	// 执行查询
	cnd.Find(s.db, &alerts)

	// 计算总数
	if cnd.Paging != nil {
		cnd.Paging.Total = cnd.Count(s.db, &Alert{})
	}

	return alerts, cnd.Paging, nil
}

// QueryByVideoName 根据视频源名称查询告警记录
func (s *AlertService) QueryByVideoName(videoName string, level string, page, pageSize int) ([]Alert, *sqls.Paging, error) {
	var alerts []Alert

	// 创建查询条件
	cnd := sqls.NewCnd()

	// 视频源名称模糊查询
	if videoName != "" {
		cnd.Like("video_name", "%"+videoName+"%")
	}

	// 危险等级查询
	if level != "" {
		cnd.Eq("level", level)
	}

	// 分页参数验证
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100
	}

	// 分页
	cnd.Page(page, pageSize)

	// 排序
	cnd.Desc("id")

	// 执行查询
	cnd.Find(s.db, &alerts)

	// 计算总数
	if cnd.Paging != nil {
		cnd.Paging.Total = cnd.Count(s.db, &Alert{})
	}

	return alerts, cnd.Paging, nil
}

// SaveAlertImage 保存告警图片
func (s *AlertService) SaveAlertImage(alertID int64, originalImage []byte, alertImage []byte) error {
	// 获取告警记录
	alert, err := s.GetByID(alertID)
	if err != nil {
		return err
	}

	// 保存原始图片
	if originalImage != nil && len(originalImage) > 0 {
		originalImagePath, err := utils.GenerateFilePath("./data/images/original", "image")
		if err != nil {
			return err
		}

		if err := utils.SaveFile(originalImage, originalImagePath); err != nil {
			return err
		}

		alert.OriginalImage = originalImagePath
	}

	// 保存告警图片
	if alertImage != nil && len(alertImage) > 0 {
		alertImagePath, err := utils.GenerateFilePath("./data/images/alert", "image")
		if err != nil {
			return err
		}

		if err := utils.SaveFile(alertImage, alertImagePath); err != nil {
			return err
		}

		alert.ImagePath = alertImagePath
	}

	// 更新告警记录
	return s.db.Save(alert).Error
}
