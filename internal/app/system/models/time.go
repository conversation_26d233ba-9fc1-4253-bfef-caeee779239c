package models

import (
	"time"
)

// SystemTime 系统时间信息
type SystemTime struct {
	CurrentTime    time.Time `json:"current_time"`    // 当前系统时间
	Timezone       string    `json:"timezone"`        // 时区
	TimezoneOffset int       `json:"timezone_offset"` // 时区偏移（秒）
	NTPEnabled     bool      `json:"ntp_enabled"`     // NTP是否启用
	NTPSynced      bool      `json:"ntp_synced"`      // NTP是否已同步
	LastNTPSync    time.Time `json:"last_ntp_sync"`   // 最后NTP同步时间
}

// SetTimeRequest 设置时间请求
type SetTimeRequest struct {
	DateTime string `json:"datetime" binding:"required"` // 时间字符串 (RFC3339格式)
	Timezone string `json:"timezone"`                    // 时区 (可选)
}

// NTPConfig NTP配置
type NTPConfig struct {
	Enabled     bool     `json:"enabled"`     // 是否启用NTP
	Servers     []string `json:"servers"`     // NTP服务器列表
	SyncInterval int      `json:"sync_interval"` // 同步间隔（秒）
	LastSync    time.Time `json:"last_sync"`   // 最后同步时间
	SyncStatus  string   `json:"sync_status"` // 同步状态 (synced, syncing, failed, disabled)
}

// NTPServer NTP服务器信息
type NTPServer struct {
	Address    string    `json:"address"`     // 服务器地址
	Reachable  bool      `json:"reachable"`   // 是否可达
	Delay      float64   `json:"delay"`       // 延迟（毫秒）
	Offset     float64   `json:"offset"`      // 时间偏移（毫秒）
	LastSync   time.Time `json:"last_sync"`   // 最后同步时间
	Status     string    `json:"status"`      // 状态 (active, backup, unreachable)
}

// TimezoneInfo 时区信息
type TimezoneInfo struct {
	Name        string `json:"name"`         // 时区名称 (如: Asia/Shanghai)
	DisplayName string `json:"display_name"` // 显示名称 (如: 中国标准时间)
	Offset      int    `json:"offset"`       // UTC偏移（秒）
	Abbreviation string `json:"abbreviation"` // 时区缩写 (如: CST)
}

// TimeStatus 时间状态
type TimeStatus struct {
	SystemTime     *SystemTime    `json:"system_time"`     // 系统时间信息
	NTPConfig      *NTPConfig     `json:"ntp_config"`      // NTP配置
	NTPServers     []*NTPServer   `json:"ntp_servers"`     // NTP服务器状态
	AvailableZones []*TimezoneInfo `json:"available_zones"` // 可用时区列表
}

// NTPSyncRequest NTP同步请求
type NTPSyncRequest struct {
	Force bool `json:"force"` // 是否强制同步
}

// SetTimezoneRequest 设置时区请求
type SetTimezoneRequest struct {
	Timezone string `json:"timezone" binding:"required"` // 时区名称
}

// NTPConfigRequest NTP配置请求
type NTPConfigRequest struct {
	Enabled      bool     `json:"enabled"`       // 是否启用NTP
	Servers      []string `json:"servers"`       // NTP服务器列表
	SyncInterval int      `json:"sync_interval"` // 同步间隔（秒，默认3600）
}

// 预定义的常用NTP服务器
var DefaultNTPServers = []string{
	"ntp.aliyun.com",
	"time.pool.aliyun.com",
	"cn.pool.ntp.org",
	"pool.ntp.org",
	"time.nist.gov",
}

// 预定义的常用时区
var CommonTimezones = []*TimezoneInfo{
	{Name: "Asia/Shanghai", DisplayName: "中国标准时间", Offset: 28800, Abbreviation: "CST"},
	{Name: "UTC", DisplayName: "协调世界时", Offset: 0, Abbreviation: "UTC"},
	{Name: "America/New_York", DisplayName: "美国东部时间", Offset: -18000, Abbreviation: "EST"},
	{Name: "Europe/London", DisplayName: "英国时间", Offset: 0, Abbreviation: "GMT"},
	{Name: "Asia/Tokyo", DisplayName: "日本标准时间", Offset: 32400, Abbreviation: "JST"},
	{Name: "Australia/Sydney", DisplayName: "澳大利亚东部时间", Offset: 36000, Abbreviation: "AEST"},
}

// ValidateNTPServer 验证NTP服务器地址格式
func ValidateNTPServer(server string) bool {
	if server == "" {
		return false
	}
	// 简单验证：不能包含特殊字符，长度合理
	if len(server) > 255 || len(server) < 3 {
		return false
	}
	// 可以是域名或IP地址
	return true
}

// ValidateTimezone 验证时区名称
func ValidateTimezone(timezone string) bool {
	if timezone == "" {
		return false
	}
	// 检查是否在常用时区列表中
	for _, tz := range CommonTimezones {
		if tz.Name == timezone {
			return true
		}
	}
	// 简单格式验证：应该包含 "/"
	return len(timezone) > 3 && len(timezone) < 50
}
