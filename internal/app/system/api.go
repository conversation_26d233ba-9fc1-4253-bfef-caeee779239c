package system

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"ecp/internal/app/system/models"
)

// API 系统管理API
type API struct {
	service SystemService
}

// NewAPI 创建系统管理API
func NewAPI(service SystemService) *API {
	return &API{
		service: service,
	}
}

// RegisterRoutes 注册路由
func (api *API) RegisterRoutes(router *gin.RouterGroup) {
	system := router.Group("/system")
	{
		// 时间管理路由
		time := system.Group("/time")
		{
			time.GET("", api.GetSystemTime)
			time.POST("/set", api.SetSystemTime)
			time.GET("/status", api.GetTimeStatus)
			
			// NTP管理
			time.GET("/ntp", api.GetNTPConfig)
			time.POST("/ntp", api.SetNTPConfig)
			time.POST("/ntp/sync", api.SyncNTP)
			time.GET("/ntp/servers", api.GetNTPServersStatus)
			
			// 时区管理
			time.GET("/timezone", api.GetTimezone)
			time.POST("/timezone", api.SetTimezone)
			time.GET("/timezones", api.GetAvailableTimezones)
		}
	}
}

// GetSystemTime 获取系统时间
func (api *API) GetSystemTime(c *gin.Context) {
	systemTime, err := api.service.GetSystemTime()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取系统时间失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    systemTime,
	})
}

// SetSystemTime 设置系统时间
func (api *API) SetSystemTime(c *gin.Context) {
	var request models.SetTimeRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := api.service.SetSystemTime(&request); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "设置系统时间失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "系统时间设置成功",
	})
}

// GetTimeStatus 获取时间状态
func (api *API) GetTimeStatus(c *gin.Context) {
	status, err := api.service.GetTimeStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取时间状态失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": status,
	})
}

// GetNTPConfig 获取NTP配置
func (api *API) GetNTPConfig(c *gin.Context) {
	config, err := api.service.GetNTPConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取NTP配置失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": config,
	})
}

// SetNTPConfig 设置NTP配置
func (api *API) SetNTPConfig(c *gin.Context) {
	var request models.NTPConfigRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := api.service.SetNTPConfig(&request); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "设置NTP配置失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "NTP配置设置成功",
	})
}

// SyncNTP 执行NTP同步
func (api *API) SyncNTP(c *gin.Context) {
	var request models.NTPSyncRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := api.service.SyncNTP(&request); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "NTP同步失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "NTP同步成功",
	})
}

// GetNTPServersStatus 获取NTP服务器状态
func (api *API) GetNTPServersStatus(c *gin.Context) {
	servers, err := api.service.GetNTPServersStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取NTP服务器状态失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": servers,
	})
}

// GetTimezone 获取当前时区
func (api *API) GetTimezone(c *gin.Context) {
	timezone, err := api.service.GetTimezone()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取时区信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": timezone,
	})
}

// SetTimezone 设置时区
func (api *API) SetTimezone(c *gin.Context) {
	var request models.SetTimezoneRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := api.service.SetTimezone(&request); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "设置时区失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "时区设置成功",
	})
}

// GetAvailableTimezones 获取可用时区列表
func (api *API) GetAvailableTimezones(c *gin.Context) {
	timezones, err := api.service.GetAvailableTimezones()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取时区列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": timezones,
	})
}
