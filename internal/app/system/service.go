package system

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"ecp/internal/app/system/models"
)

// systemService 系统管理服务实现
type systemService struct {
	mutex sync.RWMutex
}

// NewSystemService 创建系统管理服务
func NewSystemService() SystemService {
	return &systemService{}
}

// GetSystemTime 获取系统时间信息
func (s *systemService) GetSystemTime() (*models.SystemTime, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	now := time.Now()
	timezone, offset := now.Zone()

	// 检查NTP状态
	ntpEnabled, ntpSynced, lastSync := s.checkNTPStatus()

	return &models.SystemTime{
		CurrentTime:    now,
		Timezone:       timezone,
		TimezoneOffset: offset,
		NTPEnabled:     ntpEnabled,
		NTPSynced:      ntpSynced,
		LastNTPSync:    lastSync,
	}, nil
}

// SetSystemTime 设置系统时间
func (s *systemService) SetSystemTime(request *models.SetTimeRequest) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	log.Printf("设置系统时间: %s", request.DateTime)

	// 解析时间
	targetTime, err := time.Parse(time.RFC3339, request.DateTime)
	if err != nil {
		return fmt.Errorf("时间格式错误: %v", err)
	}

	// 使用date命令设置系统时间
	timeStr := targetTime.Format("2006-01-02 15:04:05")
	cmd := exec.Command("sudo", "date", "-s", timeStr)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("设置系统时间失败: %v", err)
	}

	// 如果指定了时区，也设置时区
	if request.Timezone != "" {
		if err := s.setTimezone(request.Timezone); err != nil {
			log.Printf("设置时区失败: %v", err)
		}
	}

	log.Printf("系统时间设置成功: %s", timeStr)
	return nil
}

// GetNTPConfig 获取NTP配置
func (s *systemService) GetNTPConfig() (*models.NTPConfig, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 检查NTP服务状态
	enabled := s.isNTPEnabled()
	servers := s.getNTPServers()
	_, synced, lastSync := s.checkNTPStatus()

	status := "disabled"
	if enabled {
		if synced {
			status = "synced"
		} else {
			status = "failed"
		}
	}

	return &models.NTPConfig{
		Enabled:      enabled,
		Servers:      servers,
		SyncInterval: 3600, // 默认1小时
		LastSync:     lastSync,
		SyncStatus:   status,
	}, nil
}

// SetNTPConfig 设置NTP配置
func (s *systemService) SetNTPConfig(config *models.NTPConfigRequest) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	log.Printf("设置NTP配置: enabled=%v, servers=%v", config.Enabled, config.Servers)

	// 验证NTP服务器地址
	for _, server := range config.Servers {
		if !models.ValidateNTPServer(server) {
			return fmt.Errorf("无效的NTP服务器地址: %s", server)
		}
	}

	// 更新NTP配置文件
	if err := s.updateNTPConfig(config); err != nil {
		return fmt.Errorf("更新NTP配置失败: %v", err)
	}

	// 启用或禁用NTP服务
	if config.Enabled {
		if err := s.enableNTP(); err != nil {
			return fmt.Errorf("启用NTP服务失败: %v", err)
		}
	} else {
		if err := s.disableNTP(); err != nil {
			return fmt.Errorf("禁用NTP服务失败: %v", err)
		}
	}

	log.Printf("NTP配置设置成功")
	return nil
}

// SyncNTP 执行NTP同步
func (s *systemService) SyncNTP(request *models.NTPSyncRequest) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	log.Printf("执行NTP同步: force=%v", request.Force)

	// 获取NTP服务器列表
	servers := s.getNTPServers()
	if len(servers) == 0 {
		return fmt.Errorf("未配置NTP服务器")
	}

	// 尝试与第一个可用的NTP服务器同步
	for _, server := range servers {
		cmd := exec.Command("sudo", "ntpdate", "-s", server)
		if err := cmd.Run(); err != nil {
			log.Printf("与NTP服务器 %s 同步失败: %v", server, err)
			continue
		}
		log.Printf("与NTP服务器 %s 同步成功", server)
		return nil
	}

	return fmt.Errorf("所有NTP服务器同步失败")
}

// GetTimezone 获取当前时区
func (s *systemService) GetTimezone() (*models.TimezoneInfo, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 使用timedatectl获取时区信息
	cmd := exec.Command("timedatectl", "show", "--property=Timezone", "--value")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("获取时区信息失败: %v", err)
	}

	timezone := strings.TrimSpace(string(output))

	// 查找匹配的时区信息
	for _, tz := range models.CommonTimezones {
		if tz.Name == timezone {
			return tz, nil
		}
	}

	// 如果不在常用时区列表中，返回基本信息
	now := time.Now()
	_, offset := now.Zone()

	return &models.TimezoneInfo{
		Name:         timezone,
		DisplayName:  timezone,
		Offset:       offset,
		Abbreviation: timezone,
	}, nil
}

// SetTimezone 设置时区
func (s *systemService) SetTimezone(request *models.SetTimezoneRequest) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	log.Printf("设置时区: %s", request.Timezone)

	if !models.ValidateTimezone(request.Timezone) {
		return fmt.Errorf("无效的时区: %s", request.Timezone)
	}

	return s.setTimezone(request.Timezone)
}

// GetTimeStatus 获取完整的时间状态
func (s *systemService) GetTimeStatus() (*models.TimeStatus, error) {
	systemTime, err := s.GetSystemTime()
	if err != nil {
		return nil, err
	}

	ntpConfig, err := s.GetNTPConfig()
	if err != nil {
		return nil, err
	}

	ntpServers, err := s.GetNTPServersStatus()
	if err != nil {
		return nil, err
	}

	availableZones, err := s.GetAvailableTimezones()
	if err != nil {
		return nil, err
	}

	return &models.TimeStatus{
		SystemTime:     systemTime,
		NTPConfig:      ntpConfig,
		NTPServers:     ntpServers,
		AvailableZones: availableZones,
	}, nil
}

// GetAvailableTimezones 获取可用时区列表
func (s *systemService) GetAvailableTimezones() ([]*models.TimezoneInfo, error) {
	return models.CommonTimezones, nil
}

// GetNTPServersStatus 获取NTP服务器状态
func (s *systemService) GetNTPServersStatus() ([]*models.NTPServer, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	servers := s.getNTPServers()
	result := make([]*models.NTPServer, 0, len(servers))

	for _, server := range servers {
		status := s.checkNTPServerStatus(server)
		result = append(result, status)
	}

	return result, nil
}

// 私有方法

// checkNTPStatus 检查NTP状态
func (s *systemService) checkNTPStatus() (enabled, synced bool, lastSync time.Time) {
	// 检查timedatectl状态
	cmd := exec.Command("timedatectl", "status")
	output, err := cmd.Output()
	if err != nil {
		return false, false, time.Time{}
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "NTP service:") {
			enabled = strings.Contains(line, "active")
		}
		if strings.Contains(line, "NTP synchronized:") {
			synced = strings.Contains(line, "yes")
		}
	}

	// 尝试获取最后同步时间（简化实现）
	if synced {
		lastSync = time.Now().Add(-time.Hour) // 假设1小时前同步过
	}

	return enabled, synced, lastSync
}

// isNTPEnabled 检查NTP是否启用
func (s *systemService) isNTPEnabled() bool {
	cmd := exec.Command("systemctl", "is-active", "ntp")
	err := cmd.Run()
	if err == nil {
		return true
	}

	// 尝试检查chrony
	cmd = exec.Command("systemctl", "is-active", "chronyd")
	err = cmd.Run()
	return err == nil
}

// getNTPServers 获取配置的NTP服务器列表
func (s *systemService) getNTPServers() []string {
	servers := []string{}

	// 检测NTP服务类型并从相应配置文件读取
	ntpService := s.detectNTPService()

	switch ntpService {
	case "chrony":
		servers = s.readChronyServers()
	case "ntp":
		servers = s.readNTPDServers()
	case "systemd-timesyncd":
		servers = s.readTimesyncdServers()
	}

	// 如果没有配置，返回默认服务器
	if len(servers) == 0 {
		servers = models.DefaultNTPServers
	}

	return servers
}

// readChronyServers 从chrony配置文件读取服务器列表
func (s *systemService) readChronyServers() []string {
	servers := []string{}
	configPath := "/etc/chrony/chrony.conf"

	if file, err := os.Open(configPath); err == nil {
		defer file.Close()
		scanner := bufio.NewScanner(file)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "server ") || strings.HasPrefix(line, "pool ") {
				parts := strings.Fields(line)
				if len(parts) >= 2 {
					servers = append(servers, parts[1])
				}
			}
		}
	}

	return servers
}

// readNTPDServers 从ntp配置文件读取服务器列表
func (s *systemService) readNTPDServers() []string {
	servers := []string{}
	configPath := "/etc/ntp.conf"

	if file, err := os.Open(configPath); err == nil {
		defer file.Close()
		scanner := bufio.NewScanner(file)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "server ") {
				parts := strings.Fields(line)
				if len(parts) >= 2 {
					servers = append(servers, parts[1])
				}
			}
		}
	}

	return servers
}

// readTimesyncdServers 从systemd-timesyncd配置文件读取服务器列表
func (s *systemService) readTimesyncdServers() []string {
	servers := []string{}
	configPath := "/etc/systemd/timesyncd.conf"

	if file, err := os.Open(configPath); err == nil {
		defer file.Close()
		scanner := bufio.NewScanner(file)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "NTP=") {
				ntpLine := strings.TrimPrefix(line, "NTP=")
				serverList := strings.Fields(ntpLine)
				servers = append(servers, serverList...)
			}
		}
	}

	return servers
}

// setTimezone 设置时区
func (s *systemService) setTimezone(timezone string) error {
	cmd := exec.Command("sudo", "timedatectl", "set-timezone", timezone)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("设置时区失败: %v", err)
	}
	return nil
}

// updateNTPConfig 更新NTP配置
func (s *systemService) updateNTPConfig(config *models.NTPConfigRequest) error {
	// 检测系统使用的NTP服务
	ntpService := s.detectNTPService()

	switch ntpService {
	case "chrony":
		return s.updateChronyConfig(config)
	case "ntp":
		return s.updateNTPDConfig(config)
	case "systemd-timesyncd":
		return s.updateTimesyncdConfig(config)
	default:
		log.Printf("未检测到支持的NTP服务，使用systemd-timesyncd")
		return s.updateTimesyncdConfig(config)
	}
}

// detectNTPService 检测系统使用的NTP服务
func (s *systemService) detectNTPService() string {
	// 检查chrony
	if cmd := exec.Command("systemctl", "is-active", "chronyd"); cmd.Run() == nil {
		return "chrony"
	}

	// 检查ntp
	if cmd := exec.Command("systemctl", "is-active", "ntp"); cmd.Run() == nil {
		return "ntp"
	}

	// 检查systemd-timesyncd
	if cmd := exec.Command("systemctl", "is-active", "systemd-timesyncd"); cmd.Run() == nil {
		return "systemd-timesyncd"
	}

	return "unknown"
}

// updateChronyConfig 更新Chrony配置
func (s *systemService) updateChronyConfig(config *models.NTPConfigRequest) error {
	configPath := "/etc/chrony/chrony.conf"
	backupPath := configPath + ".ecp.backup"

	// 备份原配置文件
	if err := s.backupConfigFile(configPath, backupPath); err != nil {
		log.Printf("备份chrony配置文件失败: %v", err)
	}

	// 生成新的配置内容
	configContent := "# Chrony configuration generated by ECP\n"
	configContent += "# Original configuration backed up to " + backupPath + "\n\n"

	// 添加NTP服务器
	for _, server := range config.Servers {
		configContent += fmt.Sprintf("server %s iburst\n", server)
	}

	configContent += "\n# 其他配置\n"
	configContent += "driftfile /var/lib/chrony/drift\n"
	configContent += "makestep 1.0 3\n"
	configContent += "rtcsync\n"
	configContent += "allow 127.0.0.1\n"
	configContent += "local stratum 10\n"

	// 写入配置文件
	if err := s.writeConfigFile(configPath, configContent); err != nil {
		return fmt.Errorf("写入chrony配置文件失败: %v", err)
	}

	// 重启chrony服务
	return s.restartNTPService("chronyd")
}

// updateNTPDConfig 更新NTPD配置
func (s *systemService) updateNTPDConfig(config *models.NTPConfigRequest) error {
	configPath := "/etc/ntp.conf"
	backupPath := configPath + ".ecp.backup"

	// 备份原配置文件
	if err := s.backupConfigFile(configPath, backupPath); err != nil {
		log.Printf("备份ntp配置文件失败: %v", err)
	}

	// 生成新的配置内容
	configContent := "# NTP configuration generated by ECP\n"
	configContent += "# Original configuration backed up to " + backupPath + "\n\n"

	// 添加NTP服务器
	for _, server := range config.Servers {
		configContent += fmt.Sprintf("server %s iburst\n", server)
	}

	configContent += "\n# 基本配置\n"
	configContent += "driftfile /var/lib/ntp/ntp.drift\n"
	configContent += "statistics loopstats peerstats clockstats\n"
	configContent += "filegen loopstats file loopstats type day enable\n"
	configContent += "filegen peerstats file peerstats type day enable\n"
	configContent += "filegen clockstats file clockstats type day enable\n\n"

	configContent += "# 访问控制\n"
	configContent += "restrict -4 default kod notrap nomodify nopeer noquery limited\n"
	configContent += "restrict -6 default kod notrap nomodify nopeer noquery limited\n"
	configContent += "restrict 127.0.0.1\n"
	configContent += "restrict ::1\n"

	// 写入配置文件
	if err := s.writeConfigFile(configPath, configContent); err != nil {
		return fmt.Errorf("写入ntp配置文件失败: %v", err)
	}

	// 重启ntp服务
	return s.restartNTPService("ntp")
}

// updateTimesyncdConfig 更新systemd-timesyncd配置
func (s *systemService) updateTimesyncdConfig(config *models.NTPConfigRequest) error {
	configPath := "/etc/systemd/timesyncd.conf"
	backupPath := configPath + ".ecp.backup"

	// 备份原配置文件
	if err := s.backupConfigFile(configPath, backupPath); err != nil {
		log.Printf("备份timesyncd配置文件失败: %v", err)
	}

	// 生成新的配置内容
	configContent := "# systemd-timesyncd configuration generated by ECP\n"
	configContent += "# Original configuration backed up to " + backupPath + "\n\n"
	configContent += "[Time]\n"

	if len(config.Servers) > 0 {
		configContent += "NTP=" + strings.Join(config.Servers, " ") + "\n"
	}

	configContent += "FallbackNTP=ntp.ubuntu.com\n"
	configContent += "#RootDistanceMaxSec=5\n"
	configContent += "#PollIntervalMinSec=32\n"
	configContent += "#PollIntervalMaxSec=2048\n"

	// 写入配置文件
	if err := s.writeConfigFile(configPath, configContent); err != nil {
		return fmt.Errorf("写入timesyncd配置文件失败: %v", err)
	}

	// 重启timesyncd服务
	return s.restartNTPService("systemd-timesyncd")
}

// enableNTP 启用NTP服务
func (s *systemService) enableNTP() error {
	cmd := exec.Command("sudo", "timedatectl", "set-ntp", "true")
	return cmd.Run()
}

// disableNTP 禁用NTP服务
func (s *systemService) disableNTP() error {
	cmd := exec.Command("sudo", "timedatectl", "set-ntp", "false")
	return cmd.Run()
}

// checkNTPServerStatus 检查单个NTP服务器状态
func (s *systemService) checkNTPServerStatus(server string) *models.NTPServer {
	// 使用ntpdate -q查询服务器状态
	cmd := exec.Command("ntpdate", "-q", server)
	output, err := cmd.Output()

	status := &models.NTPServer{
		Address:   server,
		Reachable: err == nil,
		Status:    "unreachable",
		LastSync:  time.Time{},
	}

	if err == nil {
		status.Status = "active"
		// 解析延迟和偏移（简化实现）
		if delay, offset := s.parseNTPOutput(string(output)); delay > 0 {
			status.Delay = delay
			status.Offset = offset
		}
	}

	return status
}

// parseNTPOutput 解析ntpdate输出
func (s *systemService) parseNTPOutput(output string) (delay, offset float64) {
	// 简化的解析实现
	delayRe := regexp.MustCompile(`delay ([0-9.]+)`)
	offsetRe := regexp.MustCompile(`offset ([+-]?[0-9.]+)`)

	if matches := delayRe.FindStringSubmatch(output); len(matches) > 1 {
		if d, err := strconv.ParseFloat(matches[1], 64); err == nil {
			delay = d * 1000 // 转换为毫秒
		}
	}

	if matches := offsetRe.FindStringSubmatch(output); len(matches) > 1 {
		if o, err := strconv.ParseFloat(matches[1], 64); err == nil {
			offset = o * 1000 // 转换为毫秒
		}
	}

	return delay, offset
}

// backupConfigFile 备份配置文件
func (s *systemService) backupConfigFile(originalPath, backupPath string) error {
	// 检查原文件是否存在
	if _, err := os.Stat(originalPath); os.IsNotExist(err) {
		return nil // 原文件不存在，无需备份
	}

	// 读取原文件内容
	data, err := os.ReadFile(originalPath)
	if err != nil {
		return fmt.Errorf("读取原配置文件失败: %v", err)
	}

	// 写入备份文件
	if err := os.WriteFile(backupPath, data, 0644); err != nil {
		return fmt.Errorf("写入备份文件失败: %v", err)
	}

	log.Printf("配置文件已备份: %s -> %s", originalPath, backupPath)
	return nil
}

// writeConfigFile 写入配置文件
func (s *systemService) writeConfigFile(configPath, content string) error {
	// 创建目录（如果不存在）
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %v", err)
	}

	// 写入配置文件
	if err := os.WriteFile(configPath, []byte(content), 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	log.Printf("配置文件已更新: %s", configPath)
	return nil
}

// restartNTPService 重启NTP服务
func (s *systemService) restartNTPService(serviceName string) error {
	log.Printf("重启NTP服务: %s", serviceName)

	// 停止服务
	stopCmd := exec.Command("sudo", "systemctl", "stop", serviceName)
	if err := stopCmd.Run(); err != nil {
		log.Printf("停止服务失败: %v", err)
	}

	// 启动服务
	startCmd := exec.Command("sudo", "systemctl", "start", serviceName)
	if err := startCmd.Run(); err != nil {
		return fmt.Errorf("启动服务失败: %v", err)
	}

	// 启用服务（开机自启）
	enableCmd := exec.Command("sudo", "systemctl", "enable", serviceName)
	if err := enableCmd.Run(); err != nil {
		log.Printf("启用服务失败: %v", err)
	}

	log.Printf("NTP服务重启成功: %s", serviceName)
	return nil
}
