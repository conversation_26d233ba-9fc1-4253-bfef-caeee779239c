package system

import (
	"ecp/internal/app/system/models"
)

// SystemService 系统管理服务接口
type SystemService interface {
	// 时间管理
	GetSystemTime() (*models.SystemTime, error)
	SetSystemTime(request *models.SetTimeRequest) error
	GetNTPConfig() (*models.NTPConfig, error)
	SetNTPConfig(config *models.NTPConfigRequest) error
	SyncNTP(request *models.NTPSyncRequest) error
	GetTimezone() (*models.TimezoneInfo, error)
	SetTimezone(request *models.SetTimezoneRequest) error
	GetTimeStatus() (*models.TimeStatus, error)
	GetAvailableTimezones() ([]*models.TimezoneInfo, error)
	GetNTPServersStatus() ([]*models.NTPServer, error)
}
