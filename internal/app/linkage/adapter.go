package linkage

import (
	"context"
	"ecp/internal/pkg/database"
)

// ProtocolAdapter 协议适配器接口
type ProtocolAdapter interface {
	// Connect 连接设备
	Connect(device *database.LinkageDevice) error
	
	// Disconnect 断开设备连接
	Disconnect(deviceID string) error
	
	// SendCommand 发送命令到设备
	SendCommand(deviceID string, command string, params map[string]interface{}) error
	
	// IsConnected 检查设备是否已连接
	IsConnected(deviceID string) bool
	
	// GetStatus 获取设备状态
	GetStatus(deviceID string) (string, error)
	
	// GetDeviceInfo 获取设备信息
	GetDeviceInfo(deviceID string) (*DeviceStatus, error)
	
	// HealthCheck 健康检查
	HealthCheck() error
	
	// Close 关闭适配器
	Close() error
	
	// GetProtocolType 获取协议类型
	GetProtocolType() string
}

// AdapterManager 适配器管理器接口
type AdapterManager interface {
	// RegisterAdapter 注册协议适配器
	RegisterAdapter(protocol string, adapter ProtocolAdapter) error
	
	// GetAdapter 获取协议适配器
	GetAdapter(protocol string) (ProtocolAdapter, error)
	
	// GetAllAdapters 获取所有适配器
	GetAllAdapters() map[string]ProtocolAdapter
	
	// ConnectDevice 连接设备
	ConnectDevice(device *database.LinkageDevice) error
	
	// DisconnectDevice 断开设备连接
	DisconnectDevice(deviceID string) error
	
	// SendCommand 发送命令
	SendCommand(deviceID string, command string, params map[string]interface{}) error
	
	// GetDeviceStatus 获取设备状态
	GetDeviceStatus(deviceID string) (*DeviceStatus, error)
	
	// HealthCheck 健康检查
	HealthCheck() map[string]error
	
	// Close 关闭所有适配器
	Close() error
}

// BaseAdapter 基础适配器结构
type BaseAdapter struct {
	protocol    string
	devices     map[string]*database.LinkageDevice
	connections map[string]interface{}
	config      interface{}
	ctx         context.Context
	cancel      context.CancelFunc
}

// NewBaseAdapter 创建基础适配器
func NewBaseAdapter(protocol string) *BaseAdapter {
	ctx, cancel := context.WithCancel(context.Background())
	return &BaseAdapter{
		protocol:    protocol,
		devices:     make(map[string]*database.LinkageDevice),
		connections: make(map[string]interface{}),
		ctx:         ctx,
		cancel:      cancel,
	}
}

// GetProtocolType 获取协议类型
func (a *BaseAdapter) GetProtocolType() string {
	return a.protocol
}

// AddDevice 添加设备
func (a *BaseAdapter) AddDevice(device *database.LinkageDevice) {
	a.devices[device.ID] = device
}

// RemoveDevice 移除设备
func (a *BaseAdapter) RemoveDevice(deviceID string) {
	delete(a.devices, deviceID)
	delete(a.connections, deviceID)
}

// GetDevice 获取设备
func (a *BaseAdapter) GetDevice(deviceID string) (*database.LinkageDevice, bool) {
	device, exists := a.devices[deviceID]
	return device, exists
}

// GetConnection 获取连接
func (a *BaseAdapter) GetConnection(deviceID string) (interface{}, bool) {
	conn, exists := a.connections[deviceID]
	return conn, exists
}

// SetConnection 设置连接
func (a *BaseAdapter) SetConnection(deviceID string, conn interface{}) {
	a.connections[deviceID] = conn
}

// Close 关闭适配器
func (a *BaseAdapter) Close() error {
	a.cancel()
	return nil
}

// AdapterFactory 适配器工厂接口
type AdapterFactory interface {
	// CreateAdapter 创建适配器
	CreateAdapter(protocol string, config interface{}) (ProtocolAdapter, error)
	
	// GetSupportedProtocols 获取支持的协议列表
	GetSupportedProtocols() []string
}

// DefaultAdapterManager 默认适配器管理器
type DefaultAdapterManager struct {
	adapters map[string]ProtocolAdapter
	devices  map[string]*database.LinkageDevice
}

// NewAdapterManager 创建适配器管理器
func NewAdapterManager() AdapterManager {
	return &DefaultAdapterManager{
		adapters: make(map[string]ProtocolAdapter),
		devices:  make(map[string]*database.LinkageDevice),
	}
}

// RegisterAdapter 注册协议适配器
func (m *DefaultAdapterManager) RegisterAdapter(protocol string, adapter ProtocolAdapter) error {
	m.adapters[protocol] = adapter
	return nil
}

// GetAdapter 获取协议适配器
func (m *DefaultAdapterManager) GetAdapter(protocol string) (ProtocolAdapter, error) {
	adapter, exists := m.adapters[protocol]
	if !exists {
		return nil, ErrAdapterNotFound
	}
	return adapter, nil
}

// GetAllAdapters 获取所有适配器
func (m *DefaultAdapterManager) GetAllAdapters() map[string]ProtocolAdapter {
	return m.adapters
}

// ConnectDevice 连接设备
func (m *DefaultAdapterManager) ConnectDevice(device *database.LinkageDevice) error {
	adapter, err := m.GetAdapter(device.Protocol)
	if err != nil {
		return err
	}
	
	m.devices[device.ID] = device
	return adapter.Connect(device)
}

// DisconnectDevice 断开设备连接
func (m *DefaultAdapterManager) DisconnectDevice(deviceID string) error {
	device, exists := m.devices[deviceID]
	if !exists {
		return ErrDeviceNotFound
	}
	
	adapter, err := m.GetAdapter(device.Protocol)
	if err != nil {
		return err
	}
	
	delete(m.devices, deviceID)
	return adapter.Disconnect(deviceID)
}

// SendCommand 发送命令
func (m *DefaultAdapterManager) SendCommand(deviceID string, command string, params map[string]interface{}) error {
	device, exists := m.devices[deviceID]
	if !exists {
		return ErrDeviceNotFound
	}
	
	adapter, err := m.GetAdapter(device.Protocol)
	if err != nil {
		return err
	}
	
	return adapter.SendCommand(deviceID, command, params)
}

// GetDeviceStatus 获取设备状态
func (m *DefaultAdapterManager) GetDeviceStatus(deviceID string) (*DeviceStatus, error) {
	device, exists := m.devices[deviceID]
	if !exists {
		return nil, ErrDeviceNotFound
	}
	
	adapter, err := m.GetAdapter(device.Protocol)
	if err != nil {
		return nil, err
	}
	
	return adapter.GetDeviceInfo(deviceID)
}

// HealthCheck 健康检查
func (m *DefaultAdapterManager) HealthCheck() map[string]error {
	results := make(map[string]error)
	for protocol, adapter := range m.adapters {
		results[protocol] = adapter.HealthCheck()
	}
	return results
}

// Close 关闭所有适配器
func (m *DefaultAdapterManager) Close() error {
	for _, adapter := range m.adapters {
		if err := adapter.Close(); err != nil {
			// 记录错误但继续关闭其他适配器
			continue
		}
	}
	return nil
}

// CommandValidator 命令验证器接口
type CommandValidator interface {
	// ValidateCommand 验证命令
	ValidateCommand(deviceType string, command string, params map[string]interface{}) error
	
	// GetSupportedCommands 获取支持的命令列表
	GetSupportedCommands(deviceType string) []string
}

// DeviceTypeManager 设备类型管理器接口
type DeviceTypeManager interface {
	// RegisterDeviceType 注册设备类型
	RegisterDeviceType(deviceType string, commands []string) error
	
	// GetDeviceType 获取设备类型信息
	GetDeviceType(deviceType string) ([]string, error)
	
	// GetAllDeviceTypes 获取所有设备类型
	GetAllDeviceTypes() map[string][]string
}

// ConnectionPool 连接池接口
type ConnectionPool interface {
	// Get 获取连接
	Get(deviceID string) (interface{}, error)
	
	// Put 归还连接
	Put(deviceID string, conn interface{}) error
	
	// Remove 移除连接
	Remove(deviceID string) error
	
	// Size 获取连接池大小
	Size() int
	
	// Close 关闭连接池
	Close() error
}
