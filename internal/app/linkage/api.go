package linkage

import (
	"net/http"
	"time"

	"ecp/internal/pkg/database"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/mlogclub/simple/web"
)

// API请求结构体

// CreateRuleRequest 创建规则请求
type CreateRuleRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Enabled     bool   `json:"enabled"`
	Priority    int    `json:"priority"`
	Conditions  string `json:"conditions" binding:"required"`
	Actions     string `json:"actions" binding:"required"`
}

// UpdateRuleRequest 更新规则请求
type UpdateRuleRequest struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
	Enabled     *bool   `json:"enabled"`
	Priority    *int    `json:"priority"`
	Conditions  *string `json:"conditions"`
	Actions     *string `json:"actions"`
}

// CreateDeviceRequest 创建设备请求
type CreateDeviceRequest struct {
	Name     string `json:"name" binding:"required"`
	Type     string `json:"type"`
	Protocol string `json:"protocol" binding:"required"`
	Address  string `json:"address" binding:"required"`
	Port     int    `json:"port"`
	Config   string `json:"config"`
}

// UpdateDeviceRequest 更新设备请求
type UpdateDeviceRequest struct {
	Name     *string `json:"name"`
	Type     *string `json:"type"`
	Protocol *string `json:"protocol"`
	Address  *string `json:"address"`
	Port     *int    `json:"port"`
	Config   *string `json:"config"`
}

// API 联动功能API处理器
type API struct {
	engine        *LinkageEngine
	ruleManager   *RuleManager
	deviceManager *DeviceManager
	sseHandler    *SSEHandler
	wsHandler     *WebSocketHandler
}

// NewAPI 创建联动API实例
func NewAPI(engine *LinkageEngine) *API {
	api := &API{
		engine:        engine,
		ruleManager:   engine.ruleManager,
		deviceManager: engine.deviceManager,
	}

	// 初始化实时通信处理器
	api.sseHandler = NewSSEHandler(engine.eventManager)
	api.wsHandler = NewWebSocketHandler(engine.wsHub, engine)

	return api
}

// RegisterRoutes 注册路由
func (api *API) RegisterRoutes(router *gin.Engine) {
	linkageGroup := router.Group("/api/linkage")

	// 联动规则管理
	rulesGroup := linkageGroup.Group("/rules")
	{
		rulesGroup.GET("", api.GetRules)
		rulesGroup.POST("", api.CreateRule)
		rulesGroup.GET("/:id", api.GetRule)
		rulesGroup.PUT("/:id", api.UpdateRule)
		rulesGroup.DELETE("/:id", api.DeleteRule)
		rulesGroup.PUT("/:id/enable", api.EnableRule)
		rulesGroup.PUT("/:id/disable", api.DisableRule)
	}

	// 联动设备管理
	devicesGroup := linkageGroup.Group("/devices")
	{
		devicesGroup.GET("", api.GetDevices)
		devicesGroup.POST("", api.CreateDevice)
		devicesGroup.GET("/:id", api.GetDevice)
		devicesGroup.PUT("/:id", api.UpdateDevice)
		devicesGroup.DELETE("/:id", api.DeleteDevice)
		devicesGroup.GET("/:id/status", api.GetDeviceStatus)
	}

	// 联动执行记录
	executionsGroup := linkageGroup.Group("/executions")
	{
		executionsGroup.GET("", api.GetExecutions)
		executionsGroup.GET("/:id", api.GetExecution)
		executionsGroup.GET("/stats", api.GetExecutionStats)
	}

	// 系统状态和统计
	linkageGroup.GET("/statistics", api.GetStatistics)
	linkageGroup.GET("/health", api.GetHealthStatus)

	// SSE实时数据流
	streamGroup := linkageGroup.Group("/stream")
	{
		streamGroup.GET("/devices/status", api.sseHandler.StreamDeviceStatus)
		streamGroup.GET("/executions", api.sseHandler.StreamExecutionRecords)
		streamGroup.GET("/alerts", api.sseHandler.StreamSystemAlerts)
		streamGroup.GET("/statistics", api.sseHandler.StreamStatistics)
		streamGroup.GET("/all", api.sseHandler.StreamAll)
		streamGroup.GET("/filter", api.sseHandler.StreamWithFilter)
	}

	// WebSocket双向通信
	wsGroup := linkageGroup.Group("/ws")
	{
		wsGroup.GET("/control", api.wsHandler.HandleControlWebSocket)
		wsGroup.GET("/management", api.wsHandler.HandleManagementWebSocket)
	}
}

// GetRules 获取联动规则列表
func (api *API) GetRules(c *gin.Context) {
	rules := api.ruleManager.GetAllRules()
	c.JSON(http.StatusOK, web.JsonData(rules))
}

// CreateRule 创建联动规则
func (api *API) CreateRule(c *gin.Context) {
	var req CreateRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	rule := &database.LinkageRule{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		Enabled:     req.Enabled,
		Priority:    req.Priority,
		Conditions:  req.Conditions,
		Actions:     req.Actions,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := api.ruleManager.CreateRule(rule); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusCreated, web.JsonData(rule))
}

// GetRule 获取单个联动规则
func (api *API) GetRule(c *gin.Context) {
	ruleID := c.Param("id")

	rule, err := api.ruleManager.GetRule(ruleID)
	if err != nil {
		c.JSON(http.StatusNotFound, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(rule))
}

// UpdateRule 更新联动规则
func (api *API) UpdateRule(c *gin.Context) {
	ruleID := c.Param("id")

	var req UpdateRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	rule, err := api.ruleManager.GetRule(ruleID)
	if err != nil {
		c.JSON(http.StatusNotFound, web.JsonError(err))
		return
	}

	// 更新字段
	if req.Name != nil {
		rule.Name = *req.Name
	}
	if req.Description != nil {
		rule.Description = *req.Description
	}
	if req.Enabled != nil {
		rule.Enabled = *req.Enabled
	}
	if req.Priority != nil {
		rule.Priority = *req.Priority
	}
	if req.Conditions != nil {
		rule.Conditions = *req.Conditions
	}
	if req.Actions != nil {
		rule.Actions = *req.Actions
	}
	rule.UpdatedAt = time.Now()

	if err := api.ruleManager.UpdateRule(rule); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(rule))
}

// DeleteRule 删除联动规则
func (api *API) DeleteRule(c *gin.Context) {
	ruleID := c.Param("id")

	if err := api.ruleManager.DeleteRule(ruleID); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

// EnableRule 启用联动规则
func (api *API) EnableRule(c *gin.Context) {
	ruleID := c.Param("id")

	if err := api.ruleManager.EnableRule(ruleID); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

// DisableRule 禁用联动规则
func (api *API) DisableRule(c *gin.Context) {
	ruleID := c.Param("id")

	if err := api.ruleManager.DisableRule(ruleID); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

// GetDevices 获取联动设备列表
func (api *API) GetDevices(c *gin.Context) {
	protocol := c.Query("protocol")
	status := c.Query("status")

	var devices []*database.LinkageDevice

	if protocol != "" {
		devices = api.deviceManager.GetDevicesByProtocol(protocol)
	} else if status != "" {
		devices = api.deviceManager.GetDevicesByStatus(status)
	} else {
		devices = api.deviceManager.GetAllDevices()
	}

	c.JSON(http.StatusOK, web.JsonData(devices))
}

// CreateDevice 创建联动设备
func (api *API) CreateDevice(c *gin.Context) {
	var req CreateDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	device := &database.LinkageDevice{
		ID:        uuid.New().String(),
		Name:      req.Name,
		Type:      req.Type,
		Protocol:  req.Protocol,
		Address:   req.Address,
		Port:      req.Port,
		Config:    req.Config,
		Status:    DeviceStatusOffline,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := api.deviceManager.CreateDevice(device); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusCreated, web.JsonData(device))
}

// GetDevice 获取单个联动设备
func (api *API) GetDevice(c *gin.Context) {
	deviceID := c.Param("id")

	device, err := api.deviceManager.GetDevice(deviceID)
	if err != nil {
		c.JSON(http.StatusNotFound, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(device))
}

// DeleteDevice 删除联动设备
func (api *API) DeleteDevice(c *gin.Context) {
	deviceID := c.Param("id")

	if err := api.deviceManager.DeleteDevice(deviceID); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonSuccess())
}

// GetDeviceStatus 获取设备状态
func (api *API) GetDeviceStatus(c *gin.Context) {
	deviceID := c.Param("id")

	status, err := api.deviceManager.GetDeviceStatus(deviceID)
	if err != nil {
		c.JSON(http.StatusNotFound, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(status))
}

// GetExecutions 获取联动执行记录
func (api *API) GetExecutions(c *gin.Context) {
	var executions []database.LinkageExecution
	if err := api.engine.db.Order("executed_at DESC").Limit(100).Find(&executions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(executions))
}

// GetExecution 获取单个执行记录
func (api *API) GetExecution(c *gin.Context) {
	executionID := c.Param("id")

	var execution database.LinkageExecution
	if err := api.engine.db.Where("id = ?", executionID).First(&execution).Error; err != nil {
		c.JSON(http.StatusNotFound, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(execution))
}

// GetExecutionStats 获取执行记录统计信息
func (api *API) GetExecutionStats(c *gin.Context) {
	// 获取查询参数
	startTime := c.Query("start_time")
	endTime := c.Query("end_time")

	// 构建基础查询
	baseQuery := api.engine.db.Model(&database.LinkageExecution{})

	// 添加时间范围过滤
	if startTime != "" {
		baseQuery = baseQuery.Where("executed_at >= ?", startTime)
	}
	if endTime != "" {
		baseQuery = baseQuery.Where("executed_at <= ?", endTime)
	}

	// 统计数据
	var stats struct {
		SuccessCount int64   `json:"success_count"`
		FailedCount  int64   `json:"failed_count"`
		AvgDuration  float64 `json:"avg_duration"`
		SuccessRate  float64 `json:"success_rate"`
	}

	// 查询成功执行数量
	successQuery := baseQuery.Where("status = ?", "success")
	successQuery.Count(&stats.SuccessCount)

	// 查询失败执行数量
	failedQuery := baseQuery.Where("status = ?", "failed")
	failedQuery.Count(&stats.FailedCount)

	// 计算总数
	totalCount := stats.SuccessCount + stats.FailedCount

	// 计算成功率
	if totalCount > 0 {
		stats.SuccessRate = float64(stats.SuccessCount) / float64(totalCount) * 100
	} else {
		stats.SuccessRate = 0
	}

	// 计算平均执行耗时
	var totalDuration int64
	if totalCount > 0 {
		// 在所有记录上计算总耗时
		baseQuery.Select("COALESCE(SUM(duration), 0)").Scan(&totalDuration)
		stats.AvgDuration = float64(totalDuration) / float64(totalCount)
	} else {
		stats.AvgDuration = 0
	}

	c.JSON(http.StatusOK, web.JsonData(stats))
}

// GetStatistics 获取统计信息
func (api *API) GetStatistics(c *gin.Context) {
	stats := api.engine.GetStatistics()
	c.JSON(http.StatusOK, web.JsonData(stats))
}

// GetHealthStatus 获取健康状态
func (api *API) GetHealthStatus(c *gin.Context) {
	health := api.engine.GetHealthStatus()

	// 根据健康状态设置HTTP状态码
	statusCode := http.StatusOK
	if health.Status == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	} else if health.Status == "degraded" {
		statusCode = http.StatusPartialContent
	}

	c.JSON(statusCode, web.JsonData(health))
}

// UpdateDevice 更新联动设备
func (api *API) UpdateDevice(c *gin.Context) {
	deviceID := c.Param("id")

	var req UpdateDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
		return
	}

	device, err := api.deviceManager.GetDevice(deviceID)
	if err != nil {
		c.JSON(http.StatusNotFound, web.JsonError(err))
		return
	}

	// 更新字段
	if req.Name != nil {
		device.Name = *req.Name
	}
	if req.Type != nil {
		device.Type = *req.Type
	}
	if req.Protocol != nil {
		device.Protocol = *req.Protocol
	}
	if req.Address != nil {
		device.Address = *req.Address
	}
	if req.Port != nil {
		device.Port = *req.Port
	}
	if req.Config != nil {
		device.Config = *req.Config
	}
	device.UpdatedAt = time.Now()

	if err := api.deviceManager.UpdateDevice(device); err != nil {
		c.JSON(http.StatusInternalServerError, web.JsonError(err))
		return
	}

	c.JSON(http.StatusOK, web.JsonData(device))
}
