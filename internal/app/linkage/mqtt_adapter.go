package linkage

import (
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"sync"
	"time"

	"ecp/internal/pkg/database"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// MQTTAdapter MQTT协议适配器
type MQTTAdapter struct {
	*BaseAdapter
	clients map[string]mqtt.Client
	config  MQTTConfig
	mutex   sync.RWMutex
}

// NewMQTTAdapter 创建MQTT适配器
func NewMQTTAdapter(config MQTTConfig) *MQTTAdapter {
	adapter := &MQTTAdapter{
		BaseAdapter: NewBaseAdapter(ProtocolMQTT),
		clients:     make(map[string]mqtt.Client),
		config:      config,
	}
	return adapter
}

// Connect 连接MQTT设备
func (a *MQTTAdapter) Connect(device *database.LinkageDevice) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 检查是否已连接
	if client, exists := a.clients[device.ID]; exists && client.IsConnected() {
		return nil
	}

	// 解析设备配置
	var mqttConfig struct {
		BrokerURL string `json:"broker_url"`
		Username  string `json:"username"`
		Password  string `json:"password"`
		ClientID  string `json:"client_id"`
		UseTLS    bool   `json:"use_tls"`
		CertFile  string `json:"cert_file"`
		KeyFile   string `json:"key_file"`
		CAFile    string `json:"ca_file"`
		QoS       int    `json:"qos"`
		KeepAlive int    `json:"keep_alive"`
	}

	if err := json.Unmarshal([]byte(device.Config), &mqttConfig); err != nil {
		return fmt.Errorf("解析MQTT配置失败: %w", err)
	}

	// 设置默认值
	if mqttConfig.BrokerURL == "" {
		mqttConfig.BrokerURL = fmt.Sprintf("tcp://%s:%d", device.Address, device.Port)
	}
	if mqttConfig.ClientID == "" {
		mqttConfig.ClientID = fmt.Sprintf("ecp_linkage_%s", device.ID)
	}
	if mqttConfig.QoS == 0 {
		mqttConfig.QoS = a.config.DefaultQoS
	}
	if mqttConfig.KeepAlive == 0 {
		mqttConfig.KeepAlive = int(a.config.KeepAlive.Seconds())
	}

	// 创建MQTT客户端选项
	opts := mqtt.NewClientOptions()
	opts.AddBroker(mqttConfig.BrokerURL)
	opts.SetClientID(mqttConfig.ClientID)
	opts.SetKeepAlive(time.Duration(mqttConfig.KeepAlive) * time.Second)
	opts.SetCleanSession(a.config.CleanSession)
	opts.SetConnectTimeout(a.config.ConnectTimeout)
	opts.SetAutoReconnect(true)
	opts.SetMaxReconnectInterval(30 * time.Second)

	// 设置认证
	if mqttConfig.Username != "" {
		opts.SetUsername(mqttConfig.Username)
		opts.SetPassword(mqttConfig.Password)
	}

	// 设置TLS
	if mqttConfig.UseTLS {
		tlsConfig, err := a.createTLSConfig(mqttConfig.CertFile, mqttConfig.KeyFile, mqttConfig.CAFile)
		if err != nil {
			return fmt.Errorf("创建TLS配置失败: %w", err)
		}
		opts.SetTLSConfig(tlsConfig)
	}

	// 设置连接回调
	opts.SetOnConnectHandler(func(client mqtt.Client) {
		log.Printf("MQTT设备 %s (%s) 连接成功", device.Name, device.ID)
		a.updateDeviceStatus(device.ID, DeviceStatusOnline, 0, nil)
	})

	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		log.Printf("MQTT设备 %s (%s) 连接丢失: %v", device.Name, device.ID, err)
		a.updateDeviceStatus(device.ID, DeviceStatusOffline, 0, err)
	})

	// 创建客户端并连接
	client := mqtt.NewClient(opts)
	
	startTime := time.Now()
	token := client.Connect()
	token.Wait()
	duration := time.Since(startTime)

	if token.Error() != nil {
		a.updateDeviceStatus(device.ID, DeviceStatusError, duration.Milliseconds(), token.Error())
		return fmt.Errorf("MQTT连接失败: %w", token.Error())
	}

	// 保存客户端
	a.clients[device.ID] = client
	a.AddDevice(device)
	a.updateDeviceStatus(device.ID, DeviceStatusOnline, duration.Milliseconds(), nil)

	log.Printf("MQTT设备 %s (%s) 连接成功，耗时: %v", device.Name, device.ID, duration)
	return nil
}

// Disconnect 断开MQTT设备连接
func (a *MQTTAdapter) Disconnect(deviceID string) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	client, exists := a.clients[deviceID]
	if !exists {
		return ErrDeviceNotFound
	}

	if client.IsConnected() {
		client.Disconnect(250) // 250ms优雅断开
	}

	delete(a.clients, deviceID)
	a.RemoveDevice(deviceID)
	a.updateDeviceStatus(deviceID, DeviceStatusOffline, 0, nil)

	log.Printf("MQTT设备 %s 已断开连接", deviceID)
	return nil
}

// SendCommand 发送命令到MQTT设备
func (a *MQTTAdapter) SendCommand(deviceID string, command string, params map[string]interface{}) error {
	a.mutex.RLock()
	client, exists := a.clients[deviceID]
	a.mutex.RUnlock()

	if !exists {
		return ErrDeviceNotFound
	}

	if !client.IsConnected() {
		return ErrDeviceOffline
	}

	startTime := time.Now()

	// 构建消息负载
	payload := map[string]interface{}{
		"command":   command,
		"params":    params,
		"timestamp": time.Now().Unix(),
		"device_id": deviceID,
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化消息负载失败: %w", err)
	}

	// 获取发布参数
	topic := command
	qos := a.config.DefaultQoS
	retain := false

	if topicValue, ok := params["topic"]; ok {
		if topicStr, ok := topicValue.(string); ok {
			topic = topicStr
		}
	}

	if qosValue, ok := params["qos"]; ok {
		if qosInt, ok := qosValue.(int); ok {
			qos = qosInt
		}
	}

	if retainValue, ok := params["retain"]; ok {
		if retainBool, ok := retainValue.(bool); ok {
			retain = retainBool
		}
	}

	// 发布消息
	token := client.Publish(topic, byte(qos), retain, payloadBytes)
	token.Wait()
	duration := time.Since(startTime)

	if token.Error() != nil {
		a.updateDeviceStatus(deviceID, DeviceStatusError, duration.Milliseconds(), token.Error())
		return fmt.Errorf("发布MQTT消息失败: %w", token.Error())
	}

	a.updateDeviceStatus(deviceID, DeviceStatusOnline, duration.Milliseconds(), nil)
	log.Printf("MQTT命令发送成功: 设备=%s, 主题=%s, 命令=%s, 耗时=%v", 
		deviceID, topic, command, duration)

	return nil
}

// IsConnected 检查设备是否已连接
func (a *MQTTAdapter) IsConnected(deviceID string) bool {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	client, exists := a.clients[deviceID]
	if !exists {
		return false
	}

	return client.IsConnected()
}

// GetStatus 获取设备状态
func (a *MQTTAdapter) GetStatus(deviceID string) (string, error) {
	if a.IsConnected(deviceID) {
		return DeviceStatusOnline, nil
	}

	if _, exists := a.clients[deviceID]; exists {
		return DeviceStatusOffline, nil
	}

	return DeviceStatusOffline, ErrDeviceNotFound
}

// GetDeviceInfo 获取设备信息
func (a *MQTTAdapter) GetDeviceInfo(deviceID string) (*DeviceStatus, error) {
	device, exists := a.GetDevice(deviceID)
	if !exists {
		return nil, ErrDeviceNotFound
	}

	status := &DeviceStatus{
		DeviceID: deviceID,
		LastSeen: device.LastSeen,
	}

	if a.IsConnected(deviceID) {
		status.Status = DeviceStatusOnline
	} else {
		status.Status = DeviceStatusOffline
	}

	return status, nil
}

// HealthCheck 健康检查
func (a *MQTTAdapter) HealthCheck() error {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	disconnectedCount := 0
	for deviceID, client := range a.clients {
		if !client.IsConnected() {
			disconnectedCount++
			log.Printf("MQTT设备 %s 连接异常", deviceID)
		}
	}

	if disconnectedCount > 0 {
		return fmt.Errorf("有 %d 个MQTT设备连接异常", disconnectedCount)
	}

	return nil
}

// Close 关闭适配器
func (a *MQTTAdapter) Close() error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	for deviceID, client := range a.clients {
		if client.IsConnected() {
			client.Disconnect(250)
		}
		log.Printf("MQTT设备 %s 连接已关闭", deviceID)
	}

	a.clients = make(map[string]mqtt.Client)
	return a.BaseAdapter.Close()
}

// createTLSConfig 创建TLS配置
func (a *MQTTAdapter) createTLSConfig(certFile, keyFile, caFile string) (*tls.Config, error) {
	tlsConfig := &tls.Config{
		InsecureSkipVerify: false,
	}

	// 加载客户端证书
	if certFile != "" && keyFile != "" {
		cert, err := tls.LoadX509KeyPair(certFile, keyFile)
		if err != nil {
			return nil, fmt.Errorf("加载客户端证书失败: %w", err)
		}
		tlsConfig.Certificates = []tls.Certificate{cert}
	}

	// 加载CA证书
	if caFile != "" {
		caCert, err := ioutil.ReadFile(caFile)
		if err != nil {
			return nil, fmt.Errorf("读取CA证书失败: %w", err)
		}
		caCertPool := x509.NewCertPool()
		if !caCertPool.AppendCertsFromPEM(caCert) {
			return nil, fmt.Errorf("解析CA证书失败")
		}
		tlsConfig.RootCAs = caCertPool
	}

	return tlsConfig, nil
}

// updateDeviceStatus 更新设备状态（需要实现具体的状态更新逻辑）
func (a *MQTTAdapter) updateDeviceStatus(deviceID string, status string, responseTime int64, err error) {
	// 这里应该调用DeviceManager的UpdateDeviceStatus方法
	// 由于架构限制，这里先记录日志
	if err != nil {
		log.Printf("MQTT设备 %s 状态更新: %s, 响应时间: %dms, 错误: %v", 
			deviceID, status, responseTime, err)
	} else {
		log.Printf("MQTT设备 %s 状态更新: %s, 响应时间: %dms", 
			deviceID, status, responseTime)
	}
}
