package linkage

import (
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"sync"
	"time"

	"ecp/internal/pkg/database"
	"github.com/Knetic/govaluate"
)

// RuleManager 规则管理器
type RuleManager struct {
	db       *database.DB
	rules    map[string]*database.LinkageRule
	mutex    sync.RWMutex
	evaluator *govaluate.EvaluableExpression
}

// NewRuleManager 创建规则管理器
func NewRuleManager(db *database.DB) *RuleManager {
	return &RuleManager{
		db:    db,
		rules: make(map[string]*database.LinkageRule),
	}
}

// LoadRules 加载所有规则
func (rm *RuleManager) LoadRules() error {
	var rules []database.LinkageRule
	if err := rm.db.Find(&rules).Error; err != nil {
		return fmt.Errorf("加载联动规则失败: %w", err)
	}
	
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	rm.rules = make(map[string]*database.LinkageRule)
	for i := range rules {
		rm.rules[rules[i].ID] = &rules[i]
	}
	
	log.Printf("加载了 %d 个联动规则", len(rules))
	return nil
}

// GetRule 获取规则
func (rm *RuleManager) GetRule(ruleID string) (*database.LinkageRule, error) {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	rule, exists := rm.rules[ruleID]
	if !exists {
		return nil, ErrRuleNotFound
	}
	
	return rule, nil
}

// GetAllRules 获取所有规则
func (rm *RuleManager) GetAllRules() []*database.LinkageRule {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	rules := make([]*database.LinkageRule, 0, len(rm.rules))
	for _, rule := range rm.rules {
		rules = append(rules, rule)
	}
	
	return rules
}

// GetActiveRuleCount 获取活跃规则数量
func (rm *RuleManager) GetActiveRuleCount() int {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	count := 0
	for _, rule := range rm.rules {
		if rule.Enabled {
			count++
		}
	}
	
	return count
}

// CreateRule 创建规则
func (rm *RuleManager) CreateRule(rule *database.LinkageRule) error {
	// 验证规则
	if err := rm.validateRule(rule); err != nil {
		return err
	}
	
	// 保存到数据库
	if err := rm.db.Create(rule).Error; err != nil {
		return fmt.Errorf("创建联动规则失败: %w", err)
	}
	
	// 更新内存缓存
	rm.mutex.Lock()
	rm.rules[rule.ID] = rule
	rm.mutex.Unlock()
	
	log.Printf("创建联动规则成功: ID=%s, Name=%s", rule.ID, rule.Name)
	return nil
}

// UpdateRule 更新规则
func (rm *RuleManager) UpdateRule(rule *database.LinkageRule) error {
	// 验证规则
	if err := rm.validateRule(rule); err != nil {
		return err
	}
	
	// 更新数据库
	if err := rm.db.Save(rule).Error; err != nil {
		return fmt.Errorf("更新联动规则失败: %w", err)
	}
	
	// 更新内存缓存
	rm.mutex.Lock()
	rm.rules[rule.ID] = rule
	rm.mutex.Unlock()
	
	log.Printf("更新联动规则成功: ID=%s, Name=%s", rule.ID, rule.Name)
	return nil
}

// DeleteRule 删除规则
func (rm *RuleManager) DeleteRule(ruleID string) error {
	// 从数据库删除
	if err := rm.db.Delete(&database.LinkageRule{}, "id = ?", ruleID).Error; err != nil {
		return fmt.Errorf("删除联动规则失败: %w", err)
	}
	
	// 从内存缓存删除
	rm.mutex.Lock()
	delete(rm.rules, ruleID)
	rm.mutex.Unlock()
	
	log.Printf("删除联动规则成功: ID=%s", ruleID)
	return nil
}

// FindMatchingRules 查找匹配的规则
func (rm *RuleManager) FindMatchingRules(alert *database.Alert) ([]*database.LinkageRule, error) {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	var matchedRules []*database.LinkageRule
	
	for _, rule := range rm.rules {
		if !rule.Enabled {
			continue
		}
		
		matched, err := rm.isRuleMatched(rule, alert)
		if err != nil {
			log.Printf("规则匹配检查失败: RuleID=%s, Error=%v", rule.ID, err)
			continue
		}
		
		if matched {
			matchedRules = append(matchedRules, rule)
		}
	}
	
	// 按优先级排序（优先级高的在前）
	sort.Slice(matchedRules, func(i, j int) bool {
		return matchedRules[i].Priority > matchedRules[j].Priority
	})
	
	return matchedRules, nil
}

// isRuleMatched 检查规则是否匹配
func (rm *RuleManager) isRuleMatched(rule *database.LinkageRule, alert *database.Alert) (bool, error) {
	// 解析规则条件
	var conditions RuleConditions
	if err := json.Unmarshal([]byte(rule.Conditions), &conditions); err != nil {
		return false, fmt.Errorf("解析规则条件失败: %w", err)
	}
	
	// 基础条件匹配
	if !rm.matchBasicConditions(&conditions, alert) {
		return false, nil
	}
	
	// 时间范围匹配
	if !rm.matchTimeRange(conditions.TimeRange, alert.CreatedAt) {
		return false, nil
	}
	
	// 自定义表达式匹配
	if conditions.Expression != "" {
		matched, err := rm.evaluateExpression(conditions.Expression, alert)
		if err != nil {
			return false, fmt.Errorf("表达式求值失败: %w", err)
		}
		if !matched {
			return false, nil
		}
	}
	
	return true, nil
}

// matchBasicConditions 匹配基础条件
func (rm *RuleManager) matchBasicConditions(conditions *RuleConditions, alert *database.Alert) bool {
	// 视频源ID匹配
	if len(conditions.VideoID) > 0 {
		found := false
		for _, id := range conditions.VideoID {
			if id == alert.VideoID {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	
	// 算法ID匹配
	if len(conditions.AlgorithmID) > 0 {
		found := false
		for _, id := range conditions.AlgorithmID {
			if id == alert.AlgorithmID {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	
	// 告警等级匹配
	if len(conditions.Level) > 0 {
		found := false
		for _, level := range conditions.Level {
			if level == alert.Level {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	
	// 告警类型匹配
	if len(conditions.Type) > 0 {
		found := false
		for _, alertType := range conditions.Type {
			if alertType == alert.Type {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	
	return true
}

// matchTimeRange 匹配时间范围
func (rm *RuleManager) matchTimeRange(timeRange *TimeRange, alertTime time.Time) bool {
	if timeRange == nil {
		return true
	}
	
	// 检查工作日
	if len(timeRange.Weekdays) > 0 {
		weekday := int(alertTime.Weekday())
		if weekday == 0 {
			weekday = 7 // 将周日从0改为7
		}
		
		found := false
		for _, day := range timeRange.Weekdays {
			if day == weekday {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	
	// 检查时间范围
	if timeRange.StartTime != "" && timeRange.EndTime != "" {
		currentTime := alertTime.Format("15:04:05")
		
		// 处理跨天的情况
		if timeRange.StartTime <= timeRange.EndTime {
			// 同一天内的时间范围
			if currentTime < timeRange.StartTime || currentTime > timeRange.EndTime {
				return false
			}
		} else {
			// 跨天的时间范围
			if currentTime < timeRange.StartTime && currentTime > timeRange.EndTime {
				return false
			}
		}
	}
	
	return true
}

// evaluateExpression 求值表达式
func (rm *RuleManager) evaluateExpression(expression string, alert *database.Alert) (bool, error) {
	// 创建表达式求值器
	expr, err := govaluate.NewEvaluableExpression(expression)
	if err != nil {
		return false, fmt.Errorf("创建表达式求值器失败: %w", err)
	}
	
	// 准备参数
	parameters := map[string]interface{}{
		"alert_id":        alert.ID,
		"source":          alert.Source,
		"ip":              alert.IP,
		"level":           alert.Level,
		"type":            alert.Type,
		"video_id":        alert.VideoID,
		"video_name":      alert.VideoName,
		"algorithm_id":    alert.AlgorithmID,
		"algorithm_name":  alert.AlgorithmName,
		"content":         alert.Content,
		"status":          alert.Status,
		"created_at":      alert.CreatedAt,
		"hour":            alert.CreatedAt.Hour(),
		"minute":          alert.CreatedAt.Minute(),
		"weekday":         int(alert.CreatedAt.Weekday()),
	}
	
	// 求值
	result, err := expr.Evaluate(parameters)
	if err != nil {
		return false, fmt.Errorf("表达式求值失败: %w", err)
	}
	
	// 转换为布尔值
	switch v := result.(type) {
	case bool:
		return v, nil
	case float64:
		return v != 0, nil
	case string:
		return v != "", nil
	default:
		return false, fmt.Errorf("表达式结果类型不支持: %T", result)
	}
}

// validateRule 验证规则
func (rm *RuleManager) validateRule(rule *database.LinkageRule) error {
	if rule.Name == "" {
		return NewLinkageError(ErrCodeInvalidRuleCondition, "规则名称不能为空")
	}
	
	// 验证条件格式
	if rule.Conditions != "" {
		var conditions RuleConditions
		if err := json.Unmarshal([]byte(rule.Conditions), &conditions); err != nil {
			return WrapError(err, ErrCodeInvalidRuleCondition, "规则条件格式错误")
		}
		
		// 验证表达式语法
		if conditions.Expression != "" {
			_, err := govaluate.NewEvaluableExpression(conditions.Expression)
			if err != nil {
				return WrapError(err, ErrCodeRuleExpressionError, "规则表达式语法错误")
			}
		}
	}
	
	// 验证动作格式
	if rule.Actions != "" {
		var actions []LinkageAction
		if err := json.Unmarshal([]byte(rule.Actions), &actions); err != nil {
			return WrapError(err, ErrCodeInvalidRuleAction, "规则动作格式错误")
		}
		
		// 验证每个动作
		for i, action := range actions {
			if action.DeviceID == "" {
				return NewLinkageError(ErrCodeInvalidRuleAction, fmt.Sprintf("动作 %d 的设备ID不能为空", i+1))
			}
			if action.Command == "" {
				return NewLinkageError(ErrCodeInvalidRuleAction, fmt.Sprintf("动作 %d 的命令不能为空", i+1))
			}
		}
	}
	
	return nil
}

// EnableRule 启用规则
func (rm *RuleManager) EnableRule(ruleID string) error {
	rule, err := rm.GetRule(ruleID)
	if err != nil {
		return err
	}
	
	rule.Enabled = true
	return rm.UpdateRule(rule)
}

// DisableRule 禁用规则
func (rm *RuleManager) DisableRule(ruleID string) error {
	rule, err := rm.GetRule(ruleID)
	if err != nil {
		return err
	}
	
	rule.Enabled = false
	return rm.UpdateRule(rule)
}

// TestRule 测试规则匹配
func (rm *RuleManager) TestRule(ruleID string, alert *database.Alert) (*RuleMatchResult, error) {
	rule, err := rm.GetRule(ruleID)
	if err != nil {
		return nil, err
	}
	
	matched, err := rm.isRuleMatched(rule, alert)
	if err != nil {
		return nil, err
	}
	
	result := &RuleMatchResult{
		RuleID:   rule.ID,
		RuleName: rule.Name,
		Matched:  matched,
		Priority: rule.Priority,
	}
	
	if matched {
		result.Confidence = 1.0
		result.MatchReason = "所有条件匹配"
	} else {
		result.Confidence = 0.0
		result.MatchReason = "条件不匹配"
	}
	
	return result, nil
}
