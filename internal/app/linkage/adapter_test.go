package linkage

import (
	"encoding/json"
	"testing"
	"time"

	"ecp/internal/pkg/database"
)

// TestMQTTAdapter 测试MQTT适配器
func TestMQTTAdapter(t *testing.T) {
	config := MQTTConfig{
		Enabled:        true,
		DefaultQoS:     1,
		KeepAlive:      60 * time.Second,
		ConnectTimeout: 10 * time.Second,
		CleanSession:   true,
	}

	adapter := NewMQTTAdapter(config)

	// 测试协议类型
	if adapter.GetProtocolType() != ProtocolMQTT {
		t.Errorf("协议类型不正确: 期望 %s, 实际 %s", ProtocolMQTT, adapter.GetProtocolType())
	}

	// 创建测试设备配置
	deviceConfig := map[string]interface{}{
		"broker_url": "tcp://localhost:1883",
		"username":   "test",
		"password":   "test",
		"client_id":  "test_client",
		"qos":        1,
	}
	configJSON, _ := json.Marshal(deviceConfig)

	device := &database.LinkageDevice{
		ID:       "mqtt_test_001",
		Name:     "测试MQTT设备",
		Protocol: ProtocolMQTT,
		Address:  "localhost",
		Port:     1883,
		Config:   string(configJSON),
	}

	// 测试连接（预期失败，因为没有MQTT服务器）
	err := adapter.Connect(device)
	if err == nil {
		t.Log("MQTT连接成功（如果有MQTT服务器运行）")
		
		// 测试发送命令
		params := map[string]interface{}{
			"topic": "test/command",
			"qos":   1,
		}
		
		err = adapter.SendCommand("mqtt_test_001", "turn_on", params)
		if err != nil {
			t.Logf("发送MQTT命令失败（预期）: %v", err)
		}
		
		// 断开连接
		adapter.Disconnect("mqtt_test_001")
	} else {
		t.Logf("MQTT连接失败（预期，因为没有MQTT服务器）: %v", err)
	}

	// 测试健康检查
	err = adapter.HealthCheck()
	if err != nil {
		t.Logf("MQTT健康检查失败（预期）: %v", err)
	}

	// 关闭适配器
	adapter.Close()
}

// TestModbusAdapter 测试Modbus适配器
func TestModbusAdapter(t *testing.T) {
	config := ModbusConfig{
		Enabled:    true,
		Timeout:    5 * time.Second,
		RetryCount: 3,
		SlaveID:    1,
	}

	adapter := NewModbusAdapter(config)

	// 测试协议类型
	if adapter.GetProtocolType() != ProtocolModbus {
		t.Errorf("协议类型不正确: 期望 %s, 实际 %s", ProtocolModbus, adapter.GetProtocolType())
	}

	// 创建测试设备配置
	deviceConfig := map[string]interface{}{
		"slave_id":    1,
		"timeout":     5,
		"retry_count": 3,
		"mode":        "tcp",
	}
	configJSON, _ := json.Marshal(deviceConfig)

	device := &database.LinkageDevice{
		ID:       "modbus_test_001",
		Name:     "测试Modbus设备",
		Protocol: ProtocolModbus,
		Address:  "localhost",
		Port:     502,
		Config:   string(configJSON),
	}

	// 测试连接（预期失败，因为没有Modbus服务器）
	err := adapter.Connect(device)
	if err == nil {
		t.Log("Modbus连接成功（如果有Modbus服务器运行）")
		
		// 测试发送命令
		params := map[string]interface{}{
			"address":  0,
			"quantity": 1,
		}
		
		err = adapter.SendCommand("modbus_test_001", "read_coils", params)
		if err != nil {
			t.Logf("发送Modbus命令失败（预期）: %v", err)
		}
		
		// 断开连接
		adapter.Disconnect("modbus_test_001")
	} else {
		t.Logf("Modbus连接失败（预期，因为没有Modbus服务器）: %v", err)
	}

	// 测试健康检查
	err = adapter.HealthCheck()
	if err != nil {
		t.Logf("Modbus健康检查失败（预期）: %v", err)
	}

	// 关闭适配器
	adapter.Close()
}

// TestRS485Adapter 测试RS485适配器
func TestRS485Adapter(t *testing.T) {
	config := RS485Config{
		Enabled:    true,
		Timeout:    3 * time.Second,
		BufferSize: 1024,
	}

	adapter := NewRS485Adapter(config)

	// 测试协议类型
	if adapter.GetProtocolType() != ProtocolRS485 {
		t.Errorf("协议类型不正确: 期望 %s, 实际 %s", ProtocolRS485, adapter.GetProtocolType())
	}

	// 创建测试设备配置
	deviceConfig := map[string]interface{}{
		"serial_port": "COM1",
		"baud_rate":   9600,
		"data_bits":   8,
		"stop_bits":   1,
		"parity":      "none",
		"timeout":     3,
		"protocol":    "custom",
		"device_addr": 1,
	}
	configJSON, _ := json.Marshal(deviceConfig)

	device := &database.LinkageDevice{
		ID:       "rs485_test_001",
		Name:     "测试RS485设备",
		Protocol: ProtocolRS485,
		Address:  "COM1",
		Port:     0,
		Config:   string(configJSON),
	}

	// 测试连接（预期失败，因为没有COM1端口）
	err := adapter.Connect(device)
	if err == nil {
		t.Log("RS485连接成功（如果有COM1端口）")
		
		// 测试发送命令
		params := map[string]interface{}{
			"data": "010300000001840A",
		}
		
		err = adapter.SendCommand("rs485_test_001", "raw_hex", params)
		if err != nil {
			t.Logf("发送RS485命令失败（预期）: %v", err)
		}
		
		// 断开连接
		adapter.Disconnect("rs485_test_001")
	} else {
		t.Logf("RS485连接失败（预期，因为没有COM1端口）: %v", err)
	}

	// 测试健康检查
	err = adapter.HealthCheck()
	if err != nil {
		t.Logf("RS485健康检查失败（预期）: %v", err)
	}

	// 关闭适配器
	adapter.Close()
}

// TestAdapterManager 测试适配器管理器
func TestAdapterManager(t *testing.T) {
	manager := NewAdapterManager()

	// 创建测试适配器
	mqttConfig := MQTTConfig{
		Enabled:        true,
		DefaultQoS:     1,
		KeepAlive:      60 * time.Second,
		ConnectTimeout: 10 * time.Second,
		CleanSession:   true,
	}
	mqttAdapter := NewMQTTAdapter(mqttConfig)

	modbusConfig := ModbusConfig{
		Enabled:    true,
		Timeout:    5 * time.Second,
		RetryCount: 3,
		SlaveID:    1,
	}
	modbusAdapter := NewModbusAdapter(modbusConfig)

	// 注册适配器
	err := manager.RegisterAdapter(ProtocolMQTT, mqttAdapter)
	if err != nil {
		t.Fatalf("注册MQTT适配器失败: %v", err)
	}

	err = manager.RegisterAdapter(ProtocolModbus, modbusAdapter)
	if err != nil {
		t.Fatalf("注册Modbus适配器失败: %v", err)
	}

	// 测试获取适配器
	adapter, err := manager.GetAdapter(ProtocolMQTT)
	if err != nil {
		t.Fatalf("获取MQTT适配器失败: %v", err)
	}

	if adapter.GetProtocolType() != ProtocolMQTT {
		t.Errorf("适配器协议类型不正确: 期望 %s, 实际 %s", 
			ProtocolMQTT, adapter.GetProtocolType())
	}

	// 测试获取所有适配器
	allAdapters := manager.GetAllAdapters()
	if len(allAdapters) != 2 {
		t.Errorf("适配器数量不正确: 期望 2, 实际 %d", len(allAdapters))
	}

	// 测试健康检查
	healthResults := manager.HealthCheck()
	if len(healthResults) != 2 {
		t.Errorf("健康检查结果数量不正确: 期望 2, 实际 %d", len(healthResults))
	}

	// 关闭管理器
	manager.Close()
}

// TestCommandBuilding 测试命令构建
func TestCommandBuilding(t *testing.T) {
	config := RS485Config{
		Enabled:    true,
		Timeout:    3 * time.Second,
		BufferSize: 1024,
	}
	adapter := NewRS485Adapter(config)

	// 测试原始十六进制命令
	params := map[string]interface{}{
		"data": "010300000001",
	}
	
	cmd, err := adapter.buildCommand("raw_hex", params)
	if err != nil {
		t.Fatalf("构建raw_hex命令失败: %v", err)
	}
	
	expected := []byte{0x01, 0x03, 0x00, 0x00, 0x00, 0x01}
	if len(cmd) != len(expected) {
		t.Errorf("命令长度不正确: 期望 %d, 实际 %d", len(expected), len(cmd))
	}

	// 测试Modbus读取命令
	params = map[string]interface{}{
		"device_addr": float64(1),
		"function":    float64(3),
		"start_addr":  float64(0),
		"quantity":    float64(1),
	}
	
	cmd, err = adapter.buildCommand("modbus_read", params)
	if err != nil {
		t.Fatalf("构建modbus_read命令失败: %v", err)
	}
	
	if len(cmd) != 8 { // 6字节数据 + 2字节CRC
		t.Errorf("Modbus命令长度不正确: 期望 8, 实际 %d", len(cmd))
	}

	// 测试CRC16计算
	data := []byte{0x01, 0x03, 0x00, 0x00, 0x00, 0x01}
	crc := adapter.calculateCRC16(data)
	expectedCRC := uint16(0x840A) // 已知的CRC值
	
	if crc != expectedCRC {
		t.Errorf("CRC16计算不正确: 期望 0x%04X, 实际 0x%04X", expectedCRC, crc)
	}
}
