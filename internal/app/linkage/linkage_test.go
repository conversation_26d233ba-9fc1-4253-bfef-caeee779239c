package linkage

import (
	"encoding/json"
	"testing"
	"time"

	"ecp/internal/pkg/database"
)

// TestLinkageEngine 测试联动引擎基础功能
func TestLinkageEngine(t *testing.T) {
	// 创建内存数据库用于测试
	db, err := database.NewDB(&database.Config{Path: ":memory:"})
	if err != nil {
		t.Fatalf("创建测试数据库失败: %v", err)
	}
	defer db.Close()

	// 初始化数据库表结构
	if err := db.InitSchema(); err != nil {
		t.Fatalf("初始化数据库表结构失败: %v", err)
	}

	// 创建联动引擎配置
	config := &LinkageConfig{
		Enabled:                 true,
		MaxConcurrentExecutions: 5,
		ExecutionTimeout:        10 * time.Second,
		RetryCount:              2,
		RetryInterval:           1 * time.Second,
	}

	// 创建联动引擎
	engine := NewLinkageEngine(db, config)

	// 测试引擎启动和停止
	if err := engine.Start(); err != nil {
		t.Fatalf("启动联动引擎失败: %v", err)
	}

	// 验证引擎状态
	if !engine.isRunning() {
		t.Error("引擎应该处于运行状态")
	}

	// 停止引擎
	if err := engine.Stop(); err != nil {
		t.Fatalf("停止联动引擎失败: %v", err)
	}

	// 验证引擎状态
	if engine.isRunning() {
		t.Error("引擎应该处于停止状态")
	}
}

// TestRuleManager 测试规则管理器
func TestRuleManager(t *testing.T) {
	// 创建内存数据库用于测试
	db, err := database.NewDB(&database.Config{Path: ":memory:"})
	if err != nil {
		t.Fatalf("创建测试数据库失败: %v", err)
	}
	defer db.Close()

	// 初始化数据库表结构
	if err := db.InitSchema(); err != nil {
		t.Fatalf("初始化数据库表结构失败: %v", err)
	}

	// 创建规则管理器
	ruleManager := NewRuleManager(db.DB)

	// 创建测试规则
	conditions := RuleConditions{
		VideoID:     []int64{1, 2},
		AlgorithmID: []int64{1},
		Level:       []string{"warning", "error"},
		Type:        []string{"人员检测"},
	}
	conditionsJSON, _ := json.Marshal(conditions)

	actions := []LinkageAction{
		{
			DeviceID: "device_001",
			Command:  "turn_on",
			Params:   map[string]interface{}{"brightness": 100},
			Delay:    0,
		},
	}
	actionsJSON, _ := json.Marshal(actions)

	rule := &database.LinkageRule{
		ID:          "test_rule_001",
		Name:        "测试规则",
		Description: "这是一个测试规则",
		Enabled:     true,
		Priority:    10,
		Conditions:  string(conditionsJSON),
		Actions:     string(actionsJSON),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 测试创建规则
	if err := ruleManager.CreateRule(rule); err != nil {
		t.Fatalf("创建规则失败: %v", err)
	}

	// 测试获取规则
	retrievedRule, err := ruleManager.GetRule("test_rule_001")
	if err != nil {
		t.Fatalf("获取规则失败: %v", err)
	}

	if retrievedRule.Name != "测试规则" {
		t.Errorf("规则名称不匹配: 期望 '测试规则', 实际 '%s'", retrievedRule.Name)
	}

	// 测试规则匹配
	alert := &database.Alert{
		ID:            1,
		VideoID:       1,
		AlgorithmID:   1,
		Level:         "warning",
		Type:          "人员检测",
		CreatedAt:     time.Now(),
	}

	matchedRules, err := ruleManager.FindMatchingRules(alert)
	if err != nil {
		t.Fatalf("查找匹配规则失败: %v", err)
	}

	if len(matchedRules) != 1 {
		t.Errorf("期望匹配1个规则, 实际匹配%d个", len(matchedRules))
	}

	if matchedRules[0].ID != "test_rule_001" {
		t.Errorf("匹配的规则ID不正确: 期望 'test_rule_001', 实际 '%s'", matchedRules[0].ID)
	}

	// 测试删除规则
	if err := ruleManager.DeleteRule("test_rule_001"); err != nil {
		t.Fatalf("删除规则失败: %v", err)
	}

	// 验证规则已删除
	_, err = ruleManager.GetRule("test_rule_001")
	if err == nil {
		t.Error("规则应该已被删除")
	}
}

// TestDeviceManager 测试设备管理器
func TestDeviceManager(t *testing.T) {
	// 创建内存数据库用于测试
	db, err := database.NewDB(&database.Config{Path: ":memory:"})
	if err != nil {
		t.Fatalf("创建测试数据库失败: %v", err)
	}
	defer db.Close()

	// 初始化数据库表结构
	if err := db.InitSchema(); err != nil {
		t.Fatalf("初始化数据库表结构失败: %v", err)
	}

	// 创建设备管理器
	deviceManager := NewDeviceManager(db.DB)

	// 创建测试设备
	config := DeviceConfig{
		SlaveID:    1,
		Timeout:    5,
		RetryCount: 3,
	}
	configJSON, _ := json.Marshal(config)

	device := &database.LinkageDevice{
		ID:        "test_device_001",
		Name:      "测试设备",
		Type:      "警报灯",
		Protocol:  "modbus",
		Address:   "*************",
		Port:      502,
		Config:    string(configJSON),
		Status:    DeviceStatusOffline,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 测试创建设备
	if err := deviceManager.CreateDevice(device); err != nil {
		t.Fatalf("创建设备失败: %v", err)
	}

	// 测试获取设备
	retrievedDevice, err := deviceManager.GetDevice("test_device_001")
	if err != nil {
		t.Fatalf("获取设备失败: %v", err)
	}

	if retrievedDevice.Name != "测试设备" {
		t.Errorf("设备名称不匹配: 期望 '测试设备', 实际 '%s'", retrievedDevice.Name)
	}

	// 测试按协议获取设备
	modbusDevices := deviceManager.GetDevicesByProtocol("modbus")
	if len(modbusDevices) != 1 {
		t.Errorf("期望找到1个Modbus设备, 实际找到%d个", len(modbusDevices))
	}

	// 测试更新设备状态
	deviceManager.UpdateDeviceStatus("test_device_001", DeviceStatusOnline, 100, nil)

	status, err := deviceManager.GetDeviceStatus("test_device_001")
	if err != nil {
		t.Fatalf("获取设备状态失败: %v", err)
	}

	if status.Status != DeviceStatusOnline {
		t.Errorf("设备状态不正确: 期望 '%s', 实际 '%s'", DeviceStatusOnline, status.Status)
	}

	// 测试删除设备
	if err := deviceManager.DeleteDevice("test_device_001"); err != nil {
		t.Fatalf("删除设备失败: %v", err)
	}

	// 验证设备已删除
	_, err = deviceManager.GetDevice("test_device_001")
	if err == nil {
		t.Error("设备应该已被删除")
	}
}

// TestRuleExpressionEvaluation 测试规则表达式求值
func TestRuleExpressionEvaluation(t *testing.T) {
	// 创建内存数据库用于测试
	db, err := database.NewDB(&database.Config{Path: ":memory:"})
	if err != nil {
		t.Fatalf("创建测试数据库失败: %v", err)
	}
	defer db.Close()

	// 初始化数据库表结构
	if err := db.InitSchema(); err != nil {
		t.Fatalf("初始化数据库表结构失败: %v", err)
	}

	// 创建规则管理器
	ruleManager := NewRuleManager(db.DB)

	// 创建测试告警
	alert := &database.Alert{
		ID:            1,
		VideoID:       1,
		AlgorithmID:   1,
		Level:         "warning",
		Type:          "人员检测",
		CreatedAt:     time.Date(2024, 1, 15, 14, 30, 0, 0, time.UTC),
	}

	// 测试简单表达式
	result, err := ruleManager.evaluateExpression("hour >= 9 && hour <= 17", alert)
	if err != nil {
		t.Fatalf("表达式求值失败: %v", err)
	}

	if !result {
		t.Error("表达式结果应该为true")
	}

	// 测试复杂表达式
	result, err = ruleManager.evaluateExpression("level == 'warning' && video_id == 1", alert)
	if err != nil {
		t.Fatalf("表达式求值失败: %v", err)
	}

	if !result {
		t.Error("表达式结果应该为true")
	}

	// 测试错误表达式
	_, err = ruleManager.evaluateExpression("invalid_expression +++", alert)
	if err == nil {
		t.Error("无效表达式应该返回错误")
	}
}
