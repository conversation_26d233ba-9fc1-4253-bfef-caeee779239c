# 告警联动模块

这个模块负责边缘计算平台的告警联动功能，当算法产生告警时自动触发设备控制。

## 功能

- 联动规则管理：创建、更新、删除和查询联动规则
- 联动设备管理：管理支持MQTT、Modbus、RS485协议的设备
- 告警处理：监听告警事件并触发相应的联动动作
- 执行记录：记录和查询联动执行历史
- 实时监控：监控设备状态和联动执行情况

## 核心组件

### LinkageEngine (联动引擎)
- 告警事件处理
- 规则匹配和执行
- 任务队列管理
- 错误处理和重试

### RuleManager (规则管理器)
- 规则加载和缓存
- 条件匹配算法
- 表达式求值
- 规则优先级处理

### DeviceManager (设备管理器)
- 设备连接管理
- 状态监控
- 协议适配器调度

### ProtocolAdapter (协议适配器)
- MQTT协议支持
- Modbus协议支持
- RS485协议支持
- 统一的设备控制接口

## 数据结构

### LinkageRule (联动规则)
- ID: 规则唯一标识
- Name: 规则名称
- Description: 规则描述
- Enabled: 是否启用
- Priority: 优先级
- Conditions: 触发条件（JSON格式）
- Actions: 联动动作（JSON格式）

### LinkageDevice (联动设备)
- ID: 设备唯一标识
- Name: 设备名称
- Type: 设备类型
- Protocol: 协议类型
- Address: 设备地址
- Port: 端口号
- Config: 协议配置
- Status: 设备状态

### LinkageExecution (执行记录)
- ID: 执行记录唯一标识
- AlertID: 关联的告警ID
- RuleID: 执行的规则ID
- DeviceID: 目标设备ID
- Action: 执行的动作
- Status: 执行状态
- ErrorMessage: 错误信息
- Duration: 执行耗时

## 使用方法

```go
// 创建联动引擎
engine := linkage.NewLinkageEngine(db)

// 启动引擎
err := engine.Start()
if err != nil {
    log.Fatal("启动联动引擎失败:", err)
}

// 处理告警
alert := &database.Alert{
    ID: 1,
    Level: "warning",
    Type: "人员检测",
    VideoID: 1,
    AlgorithmID: 1,
}

err = engine.ProcessAlert(alert)
if err != nil {
    log.Error("处理告警失败:", err)
}

// 停止引擎
engine.Stop()
```

## API接口

- GET /api/linkage/rules - 获取联动规则列表
- POST /api/linkage/rules - 创建联动规则
- PUT /api/linkage/rules/{id} - 更新联动规则
- DELETE /api/linkage/rules/{id} - 删除联动规则
- GET /api/linkage/devices - 获取联动设备列表
- POST /api/linkage/devices - 添加联动设备
- PUT /api/linkage/devices/{id} - 更新联动设备
- DELETE /api/linkage/devices/{id} - 删除联动设备
- GET /api/linkage/executions - 获取执行记录列表

## 配置

联动功能的配置位于 `configs/linkage.yaml` 中：

```yaml
linkage:
  enabled: true
  max_concurrent_executions: 10
  execution_timeout: 30s
  retry_count: 3
  retry_interval: 5s
  
  protocols:
    mqtt:
      enabled: true
      default_qos: 1
      keep_alive: 60s
    
    modbus:
      enabled: true
      timeout: 5s
      retry_count: 3
      
    rs485:
      enabled: true
      timeout: 3s
      buffer_size: 1024
```
