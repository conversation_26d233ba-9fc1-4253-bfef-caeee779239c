package linkage

import (
	"time"
)

// LinkageTask 表示一个联动任务
type LinkageTask struct {
	ID        string                 `json:"id"`
	AlertID   int64                  `json:"alert_id"`
	RuleID    string                 `json:"rule_id"`
	DeviceID  string                 `json:"device_id"`
	Action    string                 `json:"action"`
	Params    map[string]interface{} `json:"params"`
	Delay     int                    `json:"delay"`     // 延迟执行秒数
	Timestamp time.Time              `json:"timestamp"`
}

// RuleConditions 表示规则触发条件
type RuleConditions struct {
	VideoID       []int64    `json:"video_id,omitempty"`       // 视频源ID列表
	AlgorithmID   []int64    `json:"algorithm_id,omitempty"`   // 算法ID列表
	Level         []string   `json:"level,omitempty"`          // 告警等级列表
	Type          []string   `json:"type,omitempty"`           // 告警类型列表
	TimeRange     *TimeRange `json:"time_range,omitempty"`     // 时间范围
	Expression    string     `json:"expression,omitempty"`     // 自定义表达式
}

// TimeRange 表示时间范围
type TimeRange struct {
	StartTime string `json:"start_time"` // 开始时间 HH:MM:SS
	EndTime   string `json:"end_time"`   // 结束时间 HH:MM:SS
	Weekdays  []int  `json:"weekdays"`   // 工作日 1-7 (周一到周日)
}

// LinkageAction 表示联动动作
type LinkageAction struct {
	DeviceID string                 `json:"device_id"` // 设备ID
	Command  string                 `json:"command"`   // 命令
	Params   map[string]interface{} `json:"params"`    // 参数
	Delay    int                    `json:"delay"`     // 延迟执行秒数
}

// DeviceConfig 表示设备配置
type DeviceConfig struct {
	// MQTT配置
	Username     string `json:"username,omitempty"`
	Password     string `json:"password,omitempty"`
	ClientID     string `json:"client_id,omitempty"`
	UseTLS       bool   `json:"use_tls,omitempty"`
	CertFile     string `json:"cert_file,omitempty"`
	KeyFile      string `json:"key_file,omitempty"`
	CAFile       string `json:"ca_file,omitempty"`
	QoS          int    `json:"qos,omitempty"`
	
	// Modbus配置
	SlaveID      byte `json:"slave_id,omitempty"`
	Timeout      int  `json:"timeout,omitempty"`
	RetryCount   int  `json:"retry_count,omitempty"`
	
	// RS485配置
	BaudRate     int    `json:"baud_rate,omitempty"`
	DataBits     int    `json:"data_bits,omitempty"`
	StopBits     int    `json:"stop_bits,omitempty"`
	Parity       string `json:"parity,omitempty"`
	BufferSize   int    `json:"buffer_size,omitempty"`
}

// ExecutionResult 表示执行结果
type ExecutionResult struct {
	TaskID       string        `json:"task_id"`
	Success      bool          `json:"success"`
	ErrorMessage string        `json:"error_message,omitempty"`
	ResponseData string        `json:"response_data,omitempty"`
	Duration     time.Duration `json:"duration"`
	ExecutedAt   time.Time     `json:"executed_at"`
}

// DeviceStatus 表示设备状态
type DeviceStatus struct {
	DeviceID     string    `json:"device_id"`
	Status       string    `json:"status"`        // online, offline, error
	LastSeen     time.Time `json:"last_seen"`
	ResponseTime int64     `json:"response_time"` // 响应时间（毫秒）
	ErrorCount   int       `json:"error_count"`   // 错误次数
	LastError    string    `json:"last_error"`    // 最后错误信息
}

// LinkageConfig 表示联动系统配置
type LinkageConfig struct {
	Enabled                bool                    `yaml:"enabled"`
	MaxConcurrentExecutions int                    `yaml:"max_concurrent_executions"`
	ExecutionTimeout       time.Duration           `yaml:"execution_timeout"`
	RetryCount             int                     `yaml:"retry_count"`
	RetryInterval          time.Duration           `yaml:"retry_interval"`
	Protocols              ProtocolsConfig         `yaml:"protocols"`
}

// ProtocolsConfig 表示协议配置
type ProtocolsConfig struct {
	MQTT   MQTTConfig   `yaml:"mqtt"`
	Modbus ModbusConfig `yaml:"modbus"`
	RS485  RS485Config  `yaml:"rs485"`
}

// MQTTConfig 表示MQTT协议配置
type MQTTConfig struct {
	Enabled        bool          `yaml:"enabled"`
	DefaultQoS     int           `yaml:"default_qos"`
	KeepAlive      time.Duration `yaml:"keep_alive"`
	ConnectTimeout time.Duration `yaml:"connect_timeout"`
	CleanSession   bool          `yaml:"clean_session"`
}

// ModbusConfig 表示Modbus协议配置
type ModbusConfig struct {
	Enabled    bool          `yaml:"enabled"`
	Timeout    time.Duration `yaml:"timeout"`
	RetryCount int           `yaml:"retry_count"`
	SlaveID    byte          `yaml:"slave_id"`
}

// RS485Config 表示RS485协议配置
type RS485Config struct {
	Enabled    bool          `yaml:"enabled"`
	Timeout    time.Duration `yaml:"timeout"`
	BufferSize int           `yaml:"buffer_size"`
}

// Statistics 表示统计信息
type Statistics struct {
	TotalExecutions    int64   `json:"total_executions"`
	SuccessExecutions  int64   `json:"success_executions"`
	FailedExecutions   int64   `json:"failed_executions"`
	SuccessRate        float64 `json:"success_rate"`
	AverageResponseTime int64  `json:"average_response_time"`
	OnlineDevices      int     `json:"online_devices"`
	OfflineDevices     int     `json:"offline_devices"`
	ActiveRules        int     `json:"active_rules"`
}

// HealthStatus 表示健康状态
type HealthStatus struct {
	Status             string                    `json:"status"` // healthy, degraded, unhealthy
	EngineStatus       string                    `json:"engine_status"`
	QueueLength        int                       `json:"queue_length"`
	ActiveRules        int                       `json:"active_rules"`
	DeviceStatus       map[string]DeviceStatus   `json:"device_status"`
	ProtocolAdapters   map[string]string         `json:"protocol_adapters"`
	LastHealthCheck    time.Time                 `json:"last_health_check"`
}

// AlertContext 表示告警上下文信息
type AlertContext struct {
	AlertID       int64                  `json:"alert_id"`
	Source        string                 `json:"source"`
	Level         string                 `json:"level"`
	Type          string                 `json:"type"`
	VideoID       int64                  `json:"video_id"`
	VideoName     string                 `json:"video_name"`
	AlgorithmID   int64                  `json:"algorithm_id"`
	AlgorithmName string                 `json:"algorithm_name"`
	Content       string                 `json:"content"`
	ImagePath     string                 `json:"image_path"`
	Timestamp     time.Time              `json:"timestamp"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// RuleMatchResult 表示规则匹配结果
type RuleMatchResult struct {
	RuleID      string  `json:"rule_id"`
	RuleName    string  `json:"rule_name"`
	Matched     bool    `json:"matched"`
	Priority    int     `json:"priority"`
	Confidence  float64 `json:"confidence"`
	MatchReason string  `json:"match_reason"`
}

// TaskStatus 表示任务状态
const (
	TaskStatusPending   = "pending"   // 等待执行
	TaskStatusRunning   = "running"   // 正在执行
	TaskStatusSuccess   = "success"   // 执行成功
	TaskStatusFailed    = "failed"    // 执行失败
	TaskStatusTimeout   = "timeout"   // 执行超时
	TaskStatusCancelled = "cancelled" // 已取消
)

// DeviceStatusConstants 设备状态常量
const (
	DeviceStatusOnline  = "online"  // 在线
	DeviceStatusOffline = "offline" // 离线
	DeviceStatusError   = "error"   // 错误
)

// ProtocolTypes 协议类型常量
const (
	ProtocolMQTT   = "mqtt"
	ProtocolModbus = "modbus"
	ProtocolRS485  = "rs485"
)

// AlertLevels 告警等级常量
const (
	AlertLevelInfo    = "info"
	AlertLevelWarning = "warning"
	AlertLevelError   = "error"
)
