package linkage

import (
	"errors"
	"fmt"
	"time"
)

// 基础错误定义
var (
	// 引擎相关错误
	ErrEngineNotStarted     = errors.New("联动引擎未启动")
	ErrEngineAlreadyStarted = errors.New("联动引擎已经启动")
	ErrEngineShutdown       = errors.New("联动引擎正在关闭")

	// 规则相关错误
	ErrRuleNotFound         = errors.New("联动规则不存在")
	ErrRuleAlreadyExists    = errors.New("联动规则已存在")
	ErrRuleDisabled         = errors.New("联动规则已禁用")
	ErrInvalidRuleCondition = errors.New("无效的规则条件")
	ErrInvalidRuleAction    = errors.New("无效的规则动作")
	ErrRuleExpressionError  = errors.New("规则表达式错误")

	// 设备相关错误
	ErrDeviceNotFound         = errors.New("联动设备不存在")
	ErrDeviceAlreadyExists    = errors.New("联动设备已存在")
	ErrDeviceOffline          = errors.New("设备离线")
	ErrDeviceTimeout          = errors.New("设备响应超时")
	ErrDeviceConnectionFailed = errors.New("设备连接失败")
	ErrInvalidDeviceConfig    = errors.New("无效的设备配置")

	// 适配器相关错误
	ErrAdapterNotFound     = errors.New("协议适配器不存在")
	ErrAdapterNotSupported = errors.New("不支持的协议类型")
	ErrAdapterInitFailed   = errors.New("适配器初始化失败")

	// 命令相关错误
	ErrInvalidCommand         = errors.New("无效的命令")
	ErrCommandNotSupported    = errors.New("不支持的命令")
	ErrCommandExecutionFailed = errors.New("命令执行失败")
	ErrInvalidCommandParams   = errors.New("无效的命令参数")

	// 任务相关错误
	ErrTaskNotFound       = errors.New("任务不存在")
	ErrTaskAlreadyRunning = errors.New("任务正在执行")
	ErrTaskCancelled      = errors.New("任务已取消")
	ErrTaskTimeout        = errors.New("任务执行超时")
	ErrTaskQueueFull      = errors.New("任务队列已满")

	// 配置相关错误
	ErrInvalidConfig    = errors.New("无效的配置")
	ErrConfigNotFound   = errors.New("配置不存在")
	ErrConfigLoadFailed = errors.New("配置加载失败")

	// 数据库相关错误
	ErrDatabaseConnection  = errors.New("数据库连接错误")
	ErrDatabaseQuery       = errors.New("数据库查询错误")
	ErrDatabaseTransaction = errors.New("数据库事务错误")

	// 网络相关错误
	ErrNetworkConnection  = errors.New("网络连接错误")
	ErrNetworkTimeout     = errors.New("网络超时")
	ErrNetworkUnreachable = errors.New("网络不可达")
)

// LinkageError 联动系统错误类型
type LinkageError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
	Cause   error  `json:"-"`
}

// Error 实现error接口
func (e *LinkageError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// Unwrap 返回原始错误
func (e *LinkageError) Unwrap() error {
	return e.Cause
}

// NewLinkageError 创建联动错误
func NewLinkageError(code, message string) *LinkageError {
	return &LinkageError{
		Code:    code,
		Message: message,
	}
}

// NewLinkageErrorWithDetails 创建带详情的联动错误
func NewLinkageErrorWithDetails(code, message, details string) *LinkageError {
	return &LinkageError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// NewLinkageErrorWithCause 创建带原因的联动错误
func NewLinkageErrorWithCause(code, message string, cause error) *LinkageError {
	return &LinkageError{
		Code:    code,
		Message: message,
		Cause:   cause,
	}
}

// 错误代码常量
const (
	// 引擎错误代码
	ErrCodeEngineNotStarted     = "ENGINE_NOT_STARTED"
	ErrCodeEngineAlreadyStarted = "ENGINE_ALREADY_STARTED"
	ErrCodeEngineShutdown       = "ENGINE_SHUTDOWN"

	// 规则错误代码
	ErrCodeRuleNotFound         = "RULE_NOT_FOUND"
	ErrCodeRuleAlreadyExists    = "RULE_ALREADY_EXISTS"
	ErrCodeRuleDisabled         = "RULE_DISABLED"
	ErrCodeInvalidRuleCondition = "INVALID_RULE_CONDITION"
	ErrCodeInvalidRuleAction    = "INVALID_RULE_ACTION"
	ErrCodeRuleExpressionError  = "RULE_EXPRESSION_ERROR"

	// 设备错误代码
	ErrCodeDeviceNotFound         = "DEVICE_NOT_FOUND"
	ErrCodeDeviceAlreadyExists    = "DEVICE_ALREADY_EXISTS"
	ErrCodeDeviceOffline          = "DEVICE_OFFLINE"
	ErrCodeDeviceTimeout          = "DEVICE_TIMEOUT"
	ErrCodeDeviceConnectionFailed = "DEVICE_CONNECTION_FAILED"
	ErrCodeInvalidDeviceConfig    = "INVALID_DEVICE_CONFIG"

	// 适配器错误代码
	ErrCodeAdapterNotFound     = "ADAPTER_NOT_FOUND"
	ErrCodeAdapterNotSupported = "ADAPTER_NOT_SUPPORTED"
	ErrCodeAdapterInitFailed   = "ADAPTER_INIT_FAILED"

	// 命令错误代码
	ErrCodeInvalidCommand         = "INVALID_COMMAND"
	ErrCodeCommandNotSupported    = "COMMAND_NOT_SUPPORTED"
	ErrCodeCommandExecutionFailed = "COMMAND_EXECUTION_FAILED"
	ErrCodeInvalidCommandParams   = "INVALID_COMMAND_PARAMS"

	// 任务错误代码
	ErrCodeTaskNotFound       = "TASK_NOT_FOUND"
	ErrCodeTaskAlreadyRunning = "TASK_ALREADY_RUNNING"
	ErrCodeTaskCancelled      = "TASK_CANCELLED"
	ErrCodeTaskTimeout        = "TASK_TIMEOUT"
	ErrCodeTaskQueueFull      = "TASK_QUEUE_FULL"

	// 配置错误代码
	ErrCodeInvalidConfig    = "INVALID_CONFIG"
	ErrCodeConfigNotFound   = "CONFIG_NOT_FOUND"
	ErrCodeConfigLoadFailed = "CONFIG_LOAD_FAILED"

	// 数据库错误代码
	ErrCodeDatabaseConnection  = "DATABASE_CONNECTION"
	ErrCodeDatabaseQuery       = "DATABASE_QUERY"
	ErrCodeDatabaseTransaction = "DATABASE_TRANSACTION"

	// 网络错误代码
	ErrCodeNetworkConnection  = "NETWORK_CONNECTION"
	ErrCodeNetworkTimeout     = "NETWORK_TIMEOUT"
	ErrCodeNetworkUnreachable = "NETWORK_UNREACHABLE"
)

// 预定义的联动错误
var (
	LinkageErrEngineNotStarted     = NewLinkageError(ErrCodeEngineNotStarted, "联动引擎未启动")
	LinkageErrEngineAlreadyStarted = NewLinkageError(ErrCodeEngineAlreadyStarted, "联动引擎已经启动")
	LinkageErrEngineShutdown       = NewLinkageError(ErrCodeEngineShutdown, "联动引擎正在关闭")

	LinkageErrRuleNotFound      = NewLinkageError(ErrCodeRuleNotFound, "联动规则不存在")
	LinkageErrRuleAlreadyExists = NewLinkageError(ErrCodeRuleAlreadyExists, "联动规则已存在")
	LinkageErrRuleDisabled      = NewLinkageError(ErrCodeRuleDisabled, "联动规则已禁用")

	LinkageErrDeviceNotFound      = NewLinkageError(ErrCodeDeviceNotFound, "联动设备不存在")
	LinkageErrDeviceAlreadyExists = NewLinkageError(ErrCodeDeviceAlreadyExists, "联动设备已存在")
	LinkageErrDeviceOffline       = NewLinkageError(ErrCodeDeviceOffline, "设备离线")
	LinkageErrDeviceTimeout       = NewLinkageError(ErrCodeDeviceTimeout, "设备响应超时")

	LinkageErrAdapterNotFound     = NewLinkageError(ErrCodeAdapterNotFound, "协议适配器不存在")
	LinkageErrAdapterNotSupported = NewLinkageError(ErrCodeAdapterNotSupported, "不支持的协议类型")

	LinkageErrInvalidCommand      = NewLinkageError(ErrCodeInvalidCommand, "无效的命令")
	LinkageErrCommandNotSupported = NewLinkageError(ErrCodeCommandNotSupported, "不支持的命令")

	LinkageErrTaskQueueFull = NewLinkageError(ErrCodeTaskQueueFull, "任务队列已满")
	LinkageErrTaskTimeout   = NewLinkageError(ErrCodeTaskTimeout, "任务执行超时")
)

// IsLinkageError 检查是否为联动错误
func IsLinkageError(err error) bool {
	_, ok := err.(*LinkageError)
	return ok
}

// GetLinkageError 获取联动错误
func GetLinkageError(err error) (*LinkageError, bool) {
	linkageErr, ok := err.(*LinkageError)
	return linkageErr, ok
}

// WrapError 包装错误为联动错误
func WrapError(err error, code, message string) *LinkageError {
	return &LinkageError{
		Code:    code,
		Message: message,
		Cause:   err,
	}
}

// WrapErrorWithDetails 包装错误为带详情的联动错误
func WrapErrorWithDetails(err error, code, message, details string) *LinkageError {
	return &LinkageError{
		Code:    code,
		Message: message,
		Details: details,
		Cause:   err,
	}
}

// ErrorHandler 错误处理器接口
type ErrorHandler interface {
	// HandleError 处理错误
	HandleError(err error) error

	// ShouldRetry 判断是否应该重试
	ShouldRetry(err error) bool

	// GetRetryDelay 获取重试延迟
	GetRetryDelay(attempt int) time.Duration
}

// DefaultErrorHandler 默认错误处理器
type DefaultErrorHandler struct {
	maxRetries      int
	baseDelay       time.Duration
	maxDelay        time.Duration
	retryableErrors []string
}

// NewDefaultErrorHandler 创建默认错误处理器
func NewDefaultErrorHandler() ErrorHandler {
	return &DefaultErrorHandler{
		maxRetries: 3,
		baseDelay:  time.Second,
		maxDelay:   30 * time.Second,
		retryableErrors: []string{
			ErrCodeDeviceTimeout,
			ErrCodeNetworkTimeout,
			ErrCodeNetworkConnection,
		},
	}
}

// HandleError 处理错误
func (h *DefaultErrorHandler) HandleError(err error) error {
	// 记录错误日志
	// 这里可以添加日志记录逻辑
	return err
}

// ShouldRetry 判断是否应该重试
func (h *DefaultErrorHandler) ShouldRetry(err error) bool {
	if linkageErr, ok := GetLinkageError(err); ok {
		for _, code := range h.retryableErrors {
			if linkageErr.Code == code {
				return true
			}
		}
	}
	return false
}

// GetRetryDelay 获取重试延迟
func (h *DefaultErrorHandler) GetRetryDelay(attempt int) time.Duration {
	delay := h.baseDelay * time.Duration(1<<uint(attempt)) // 指数退避
	if delay > h.maxDelay {
		delay = h.maxDelay
	}
	return delay
}
