package linkage

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// WebSocket升级器
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源，生产环境中应该更严格
		return true
	},
}

// WebSocketMessage WebSocket消息结构
type WebSocketMessage struct {
	Type      string      `json:"type"`
	Payload   interface{} `json:"payload"`
	Timestamp time.Time   `json:"timestamp"`
	ID        string      `json:"id"`
}

// WebSocketClient WebSocket客户端
type WebSocketClient struct {
	ID         string
	conn       *websocket.Conn
	send       chan WebSocketMessage
	hub        *WebSocketHub
	clientType string // "control" 或 "management"
}

// WebSocketHub WebSocket连接管理中心
type WebSocketHub struct {
	clients    map[string]*WebSocketClient
	register   chan *WebSocketClient
	unregister chan *WebSocketClient
	broadcast  chan WebSocketMessage
	engine     *LinkageEngine
}

// NewWebSocketHub 创建WebSocket管理中心
func NewWebSocketHub(engine *LinkageEngine) *WebSocketHub {
	return &WebSocketHub{
		clients:    make(map[string]*WebSocketClient),
		register:   make(chan *WebSocketClient),
		unregister: make(chan *WebSocketClient),
		broadcast:  make(chan WebSocketMessage),
		engine:     engine,
	}
}

// Run 运行WebSocket管理中心
func (h *WebSocketHub) Run() {
	for {
		select {
		case client := <-h.register:
			h.clients[client.ID] = client
			log.Printf("WebSocket客户端已连接: %s (类型: %s)", client.ID, client.clientType)

			// 发送连接确认消息
			welcomeMsg := WebSocketMessage{
				Type: "connected",
				Payload: map[string]interface{}{
					"client_id":   client.ID,
					"client_type": client.clientType,
					"timestamp":   time.Now().Format(time.RFC3339),
				},
				Timestamp: time.Now(),
				ID:        generateEventID(),
			}

			select {
			case client.send <- welcomeMsg:
			default:
				close(client.send)
				delete(h.clients, client.ID)
			}

		case client := <-h.unregister:
			if _, ok := h.clients[client.ID]; ok {
				delete(h.clients, client.ID)
				close(client.send)
				log.Printf("WebSocket客户端已断开: %s", client.ID)
			}

		case message := <-h.broadcast:
			// 广播消息给所有客户端
			for clientID, client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, clientID)
				}
			}
		}
	}
}

// BroadcastToType 向特定类型的客户端广播消息
func (h *WebSocketHub) BroadcastToType(clientType string, message WebSocketMessage) {
	for clientID, client := range h.clients {
		if client.clientType == clientType {
			select {
			case client.send <- message:
			default:
				close(client.send)
				delete(h.clients, clientID)
			}
		}
	}
}

// WebSocketHandler WebSocket处理器
type WebSocketHandler struct {
	hub    *WebSocketHub
	engine *LinkageEngine
}

// NewWebSocketHandler 创建WebSocket处理器
func NewWebSocketHandler(hub *WebSocketHub, engine *LinkageEngine) *WebSocketHandler {
	return &WebSocketHandler{
		hub:    hub,
		engine: engine,
	}
}

// HandleControlWebSocket 处理设备控制WebSocket连接
func (h *WebSocketHandler) HandleControlWebSocket(c *gin.Context) {
	h.handleWebSocket(c, "control")
}

// HandleManagementWebSocket 处理系统管理WebSocket连接
func (h *WebSocketHandler) HandleManagementWebSocket(c *gin.Context) {
	h.handleWebSocket(c, "management")
}

// handleWebSocket 处理WebSocket连接的通用方法
func (h *WebSocketHandler) handleWebSocket(c *gin.Context, clientType string) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	clientID := uuid.New().String()
	client := &WebSocketClient{
		ID:         clientID,
		conn:       conn,
		send:       make(chan WebSocketMessage, 256),
		hub:        h.hub,
		clientType: clientType,
	}

	// 注册客户端
	h.hub.register <- client

	// 启动读写协程
	go client.writePump()
	go client.readPump(h.engine)
}

// readPump 读取WebSocket消息
func (c *WebSocketClient) readPump(engine *LinkageEngine) {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	// 设置读取超时
	c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		var msg WebSocketMessage
		err := c.conn.ReadJSON(&msg)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket读取错误: %v", err)
			}
			break
		}

		// 处理接收到的消息
		c.handleMessage(msg, engine)
	}
}

// writePump 写入WebSocket消息
func (c *WebSocketClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.conn.WriteJSON(message); err != nil {
				log.Printf("WebSocket写入错误: %v", err)
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理接收到的WebSocket消息
func (c *WebSocketClient) handleMessage(msg WebSocketMessage, engine *LinkageEngine) {
	switch msg.Type {
	case "device_control":
		c.handleDeviceControl(msg, engine)
	case "rule_test":
		c.handleRuleTest(msg, engine)
	case "system_command":
		c.handleSystemCommand(msg, engine)
	case "ping":
		c.handlePing(msg)
	default:
		c.sendError(fmt.Sprintf("Unknown message type: %s", msg.Type))
	}
}

// handleDeviceControl 处理设备控制命令
func (c *WebSocketClient) handleDeviceControl(msg WebSocketMessage, engine *LinkageEngine) {
	payload, ok := msg.Payload.(map[string]interface{})
	if !ok {
		c.sendError("Invalid device control payload")
		return
	}

	deviceID, _ := payload["device_id"].(string)
	command, _ := payload["command"].(string)
	params, _ := payload["params"].(map[string]interface{})

	if deviceID == "" || command == "" {
		c.sendError("Missing device_id or command")
		return
	}

	// 执行设备控制
	go func() {
		err := engine.adapterManager.SendCommand(deviceID, command, params)

		response := WebSocketMessage{
			Type: "device_control_response",
			Payload: map[string]interface{}{
				"device_id": deviceID,
				"command":   command,
				"success":   err == nil,
				"error":     nil,
			},
			Timestamp: time.Now(),
			ID:        generateEventID(),
		}

		if err != nil {
			response.Payload.(map[string]interface{})["error"] = err.Error()
		}

		select {
		case c.send <- response:
		default:
			log.Printf("Failed to send device control response to client %s", c.ID)
		}
	}()
}

// handleRuleTest 处理规则测试
func (c *WebSocketClient) handleRuleTest(msg WebSocketMessage, engine *LinkageEngine) {
	// 实现规则测试逻辑
	response := WebSocketMessage{
		Type: "rule_test_response",
		Payload: map[string]interface{}{
			"success": true,
			"message": "Rule test completed",
		},
		Timestamp: time.Now(),
		ID:        generateEventID(),
	}

	select {
	case c.send <- response:
	default:
		log.Printf("Failed to send rule test response to client %s", c.ID)
	}
}

// handleSystemCommand 处理系统命令
func (c *WebSocketClient) handleSystemCommand(msg WebSocketMessage, engine *LinkageEngine) {
	// 实现系统命令处理逻辑
	response := WebSocketMessage{
		Type: "system_command_response",
		Payload: map[string]interface{}{
			"success": true,
			"message": "System command executed",
		},
		Timestamp: time.Now(),
		ID:        generateEventID(),
	}

	select {
	case c.send <- response:
	default:
		log.Printf("Failed to send system command response to client %s", c.ID)
	}
}

// handlePing 处理ping消息
func (c *WebSocketClient) handlePing(msg WebSocketMessage) {
	response := WebSocketMessage{
		Type: "pong",
		Payload: map[string]interface{}{
			"timestamp": time.Now().Format(time.RFC3339),
		},
		Timestamp: time.Now(),
		ID:        generateEventID(),
	}

	select {
	case c.send <- response:
	default:
		log.Printf("Failed to send pong to client %s", c.ID)
	}
}

// sendError 发送错误消息
func (c *WebSocketClient) sendError(errorMsg string) {
	response := WebSocketMessage{
		Type: "error",
		Payload: map[string]interface{}{
			"error": errorMsg,
		},
		Timestamp: time.Now(),
		ID:        generateEventID(),
	}

	select {
	case c.send <- response:
	default:
		log.Printf("Failed to send error to client %s", c.ID)
	}
}
