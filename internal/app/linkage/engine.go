package linkage

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"ecp/internal/pkg/database"

	"github.com/google/uuid"
)

// LinkageEngine 联动引擎
type LinkageEngine struct {
	db             *database.DB
	ruleManager    *RuleManager
	deviceManager  *DeviceManager
	adapterManager AdapterManager
	config         *LinkageConfig

	// 任务队列和处理
	taskQueue      chan *LinkageTask
	executionQueue chan *ExecutionTask

	// 状态管理
	running bool
	ctx     context.Context
	cancel  context.CancelFunc
	wg      sync.WaitGroup
	mutex   sync.RWMutex

	// 统计信息
	stats      *Statistics
	statsMutex sync.RWMutex

	// 错误处理
	errorHandler ErrorHandler

	// 实时通信
	eventManager *EventManager
	wsHub        *WebSocketHub
}

// ExecutionTask 执行任务
type ExecutionTask struct {
	Task       *LinkageTask
	StartTime  time.Time
	Attempt    int
	MaxRetries int
}

// NewLinkageEngine 创建联动引擎
func NewLinkageEngine(db *database.DB, config *LinkageConfig) *LinkageEngine {
	ctx, cancel := context.WithCancel(context.Background())

	engine := &LinkageEngine{
		db:             db,
		config:         config,
		taskQueue:      make(chan *LinkageTask, 1000),
		executionQueue: make(chan *ExecutionTask, 1000),
		ctx:            ctx,
		cancel:         cancel,
		stats:          &Statistics{},
		errorHandler:   NewDefaultErrorHandler(),
	}

	// 初始化管理器
	engine.ruleManager = NewRuleManager(db)
	engine.deviceManager = NewDeviceManager(db)
	engine.adapterManager = NewAdapterManager()

	// 初始化实时通信组件
	engine.eventManager = NewEventManager()
	engine.wsHub = NewWebSocketHub(engine)

	// 设置适配器管理器
	engine.deviceManager.SetAdapterManager(engine.adapterManager)

	// 注册协议适配器
	engine.registerProtocolAdapters()

	return engine
}

// Start 启动联动引擎
func (e *LinkageEngine) Start() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if e.running {
		return LinkageErrEngineAlreadyStarted
	}

	log.Println("启动联动引擎...")

	// 加载规则和设备
	if err := e.ruleManager.LoadRules(); err != nil {
		return fmt.Errorf("加载联动规则失败: %w", err)
	}

	if err := e.deviceManager.LoadDevices(); err != nil {
		return fmt.Errorf("加载联动设备失败: %w", err)
	}

	// 启动实时通信组件
	if err := e.eventManager.Start(); err != nil {
		return fmt.Errorf("启动事件管理器失败: %w", err)
	}

	// 启动WebSocket Hub
	go e.wsHub.Run()

	// 启动工作协程
	e.wg.Add(3)
	go e.taskProcessor()
	go e.executionProcessor()
	go e.statisticsUpdater()

	e.running = true
	log.Println("联动引擎启动成功")

	return nil
}

// Stop 停止联动引擎
func (e *LinkageEngine) Stop() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if !e.running {
		return nil
	}

	log.Println("停止联动引擎...")

	e.running = false
	e.cancel()

	// 等待所有协程结束
	e.wg.Wait()

	// 停止实时通信组件
	if err := e.eventManager.Stop(); err != nil {
		log.Printf("停止事件管理器失败: %v", err)
	}

	// 关闭适配器
	if err := e.adapterManager.Close(); err != nil {
		log.Printf("关闭适配器失败: %v", err)
	}

	log.Println("联动引擎已停止")
	return nil
}

// ProcessAlert 处理告警
func (e *LinkageEngine) ProcessAlert(alert *database.Alert) error {
	if !e.isRunning() {
		return LinkageErrEngineNotStarted
	}

	log.Printf("处理告警: ID=%d, Level=%s, Type=%s", alert.ID, alert.Level, alert.Type)

	// 查找匹配的规则
	matchedRules, err := e.ruleManager.FindMatchingRules(alert)
	if err != nil {
		return fmt.Errorf("查找匹配规则失败: %w", err)
	}

	if len(matchedRules) == 0 {
		log.Printf("告警 %d 没有匹配的联动规则", alert.ID)
		return nil
	}

	log.Printf("告警 %d 匹配到 %d 个联动规则", alert.ID, len(matchedRules))

	// 创建联动任务
	tasks := make([]*LinkageTask, 0)
	for _, rule := range matchedRules {
		ruleTasks, err := e.createTasksFromRule(alert, rule)
		if err != nil {
			log.Printf("创建规则 %s 的联动任务失败: %v", rule.ID, err)
			continue
		}
		tasks = append(tasks, ruleTasks...)
	}

	// 提交任务到队列
	for _, task := range tasks {
		select {
		case e.taskQueue <- task:
			log.Printf("联动任务已加入队列: TaskID=%s, RuleID=%s, DeviceID=%s",
				task.ID, task.RuleID, task.DeviceID)
		default:
			log.Printf("任务队列已满，丢弃联动任务: TaskID=%s", task.ID)
			return LinkageErrTaskQueueFull
		}
	}

	// 更新告警联动状态
	return e.updateAlertLinkageStatus(alert.ID, true, nil)
}

// createTasksFromRule 从规则创建联动任务
func (e *LinkageEngine) createTasksFromRule(alert *database.Alert, rule *database.LinkageRule) ([]*LinkageTask, error) {
	// 解析规则动作
	var actions []LinkageAction
	if err := json.Unmarshal([]byte(rule.Actions), &actions); err != nil {
		return nil, fmt.Errorf("解析规则动作失败: %w", err)
	}

	tasks := make([]*LinkageTask, 0, len(actions))
	for _, action := range actions {
		task := &LinkageTask{
			ID:        uuid.New().String(),
			AlertID:   alert.ID,
			RuleID:    rule.ID,
			DeviceID:  action.DeviceID,
			Action:    action.Command,
			Params:    action.Params,
			Delay:     action.Delay,
			Timestamp: time.Now(),
		}
		tasks = append(tasks, task)
	}

	return tasks, nil
}

// taskProcessor 任务处理器
func (e *LinkageEngine) taskProcessor() {
	defer e.wg.Done()

	for {
		select {
		case <-e.ctx.Done():
			return
		case task := <-e.taskQueue:
			e.processTask(task)
		}
	}
}

// processTask 处理单个任务
func (e *LinkageEngine) processTask(task *LinkageTask) {
	// 检查是否需要延迟执行
	if task.Delay > 0 {
		select {
		case <-e.ctx.Done():
			return
		case <-time.After(time.Duration(task.Delay) * time.Second):
			// 延迟结束，继续执行
		}
	}

	// 创建执行任务
	execTask := &ExecutionTask{
		Task:       task,
		StartTime:  time.Now(),
		Attempt:    1,
		MaxRetries: e.config.RetryCount,
	}

	// 提交到执行队列
	select {
	case e.executionQueue <- execTask:
	default:
		log.Printf("执行队列已满，丢弃任务: TaskID=%s", task.ID)
	}
}

// executionProcessor 执行处理器
func (e *LinkageEngine) executionProcessor() {
	defer e.wg.Done()

	// 创建工作协程池
	for i := 0; i < e.config.MaxConcurrentExecutions; i++ {
		e.wg.Add(1)
		go e.executionWorker()
	}
}

// executionWorker 执行工作协程
func (e *LinkageEngine) executionWorker() {
	defer e.wg.Done()

	for {
		select {
		case <-e.ctx.Done():
			return
		case execTask := <-e.executionQueue:
			e.executeTask(execTask)
		}
	}
}

// executeTask 执行任务
func (e *LinkageEngine) executeTask(execTask *ExecutionTask) {
	task := execTask.Task
	startTime := time.Now()

	log.Printf("执行联动任务: TaskID=%s, DeviceID=%s, Action=%s, Attempt=%d",
		task.ID, task.DeviceID, task.Action, execTask.Attempt)

	// 执行命令
	err := e.adapterManager.SendCommand(task.DeviceID, task.Action, task.Params)
	duration := time.Since(startTime)

	// 创建执行记录
	execution := &database.LinkageExecution{
		ID:         task.ID + "_" + fmt.Sprintf("%d", execTask.Attempt),
		AlertID:    task.AlertID,
		RuleID:     task.RuleID,
		DeviceID:   task.DeviceID,
		Action:     task.Action,
		Duration:   duration.Milliseconds(),
		ExecutedAt: startTime,
	}

	if err != nil {
		execution.Status = TaskStatusFailed
		execution.ErrorMessage = err.Error()

		log.Printf("联动任务执行失败: TaskID=%s, Error=%v", task.ID, err)

		// 检查是否需要重试
		if execTask.Attempt < execTask.MaxRetries && e.errorHandler.ShouldRetry(err) {
			log.Printf("准备重试联动任务: TaskID=%s, Attempt=%d", task.ID, execTask.Attempt+1)

			// 计算重试延迟
			retryDelay := e.errorHandler.GetRetryDelay(execTask.Attempt)

			// 延迟后重新提交任务
			go func() {
				time.Sleep(retryDelay)
				execTask.Attempt++
				select {
				case e.executionQueue <- execTask:
				default:
					log.Printf("重试时执行队列已满: TaskID=%s", task.ID)
				}
			}()

			return
		}
	} else {
		execution.Status = TaskStatusSuccess
		log.Printf("联动任务执行成功: TaskID=%s, Duration=%v", task.ID, duration)
	}

	// 保存执行记录
	if err := e.db.Create(execution).Error; err != nil {
		log.Printf("保存执行记录失败: %v", err)
	} else {
		// 发布执行记录事件
		e.PublishExecutionRecord(execution)
	}

	// 更新统计信息
	e.updateStatistics(execution.Status == TaskStatusSuccess, duration)
}

// updateAlertLinkageStatus 更新告警联动状态
func (e *LinkageEngine) updateAlertLinkageStatus(alertID int64, triggered bool, results interface{}) error {
	updates := map[string]interface{}{
		"linkage_triggered": triggered,
	}

	if results != nil {
		if resultsJSON, err := json.Marshal(results); err == nil {
			updates["linkage_results"] = string(resultsJSON)
		}
	}

	return e.db.Model(&database.Alert{}).Where("id = ?", alertID).Updates(updates).Error
}

// updateStatistics 更新统计信息
func (e *LinkageEngine) updateStatistics(success bool, duration time.Duration) {
	e.statsMutex.Lock()
	defer e.statsMutex.Unlock()

	e.stats.TotalExecutions++
	if success {
		e.stats.SuccessExecutions++
	} else {
		e.stats.FailedExecutions++
	}

	if e.stats.TotalExecutions > 0 {
		e.stats.SuccessRate = float64(e.stats.SuccessExecutions) / float64(e.stats.TotalExecutions) * 100
	}

	// 更新平均响应时间
	if e.stats.AverageResponseTime == 0 {
		e.stats.AverageResponseTime = duration.Milliseconds()
	} else {
		e.stats.AverageResponseTime = (e.stats.AverageResponseTime + duration.Milliseconds()) / 2
	}
}

// statisticsUpdater 统计信息更新器
func (e *LinkageEngine) statisticsUpdater() {
	defer e.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			return
		case <-ticker.C:
			e.updateDeviceStatistics()
		}
	}
}

// updateDeviceStatistics 更新设备统计信息
func (e *LinkageEngine) updateDeviceStatistics() {
	devices := e.deviceManager.GetAllDevices()

	e.statsMutex.Lock()
	defer e.statsMutex.Unlock()

	onlineCount := 0
	offlineCount := 0

	for _, device := range devices {
		if device.Status == DeviceStatusOnline {
			onlineCount++
		} else {
			offlineCount++
		}
	}

	e.stats.OnlineDevices = onlineCount
	e.stats.OfflineDevices = offlineCount
	e.stats.ActiveRules = e.ruleManager.GetActiveRuleCount()

	// 发布统计信息更新事件
	e.PublishStatisticsUpdate()
}

// isRunning 检查引擎是否运行中
func (e *LinkageEngine) isRunning() bool {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return e.running
}

// GetStatistics 获取统计信息
func (e *LinkageEngine) GetStatistics() *Statistics {
	e.statsMutex.RLock()
	defer e.statsMutex.RUnlock()

	// 返回统计信息的副本
	stats := *e.stats
	return &stats
}

// GetHealthStatus 获取健康状态
func (e *LinkageEngine) GetHealthStatus() *HealthStatus {
	status := &HealthStatus{
		LastHealthCheck: time.Now(),
	}

	if e.isRunning() {
		status.EngineStatus = "running"
		status.Status = "healthy"
	} else {
		status.EngineStatus = "stopped"
		status.Status = "unhealthy"
	}

	status.QueueLength = len(e.taskQueue)
	status.ActiveRules = e.ruleManager.GetActiveRuleCount()

	// 检查适配器健康状态
	status.ProtocolAdapters = make(map[string]string)
	healthResults := e.adapterManager.HealthCheck()
	for protocol, err := range healthResults {
		if err == nil {
			status.ProtocolAdapters[protocol] = "healthy"
		} else {
			status.ProtocolAdapters[protocol] = "unhealthy"
			if status.Status == "healthy" {
				status.Status = "degraded"
			}
		}
	}

	return status
}

// registerProtocolAdapters 注册协议适配器
func (e *LinkageEngine) registerProtocolAdapters() {
	// 注册MQTT适配器
	mqttAdapter := NewMQTTAdapter(e.config.Protocols.MQTT)
	e.adapterManager.RegisterAdapter(ProtocolMQTT, mqttAdapter)

	// 注册Modbus适配器
	modbusAdapter := NewModbusAdapter(e.config.Protocols.Modbus)
	e.adapterManager.RegisterAdapter(ProtocolModbus, modbusAdapter)

	// 注册RS485适配器
	rs485Adapter := NewRS485Adapter(e.config.Protocols.RS485)
	e.adapterManager.RegisterAdapter(ProtocolRS485, rs485Adapter)

	log.Println("协议适配器注册完成: MQTT, Modbus, RS485")
}

// 事件发布方法

// PublishDeviceStatusUpdate 发布设备状态更新事件
func (e *LinkageEngine) PublishDeviceStatusUpdate(deviceID, status string, lastSeen time.Time, errorCount int, responseTime *int) {
	event := DeviceStatusUpdateEvent{
		DeviceID:     deviceID,
		Status:       status,
		LastSeen:     lastSeen,
		ErrorCount:   errorCount,
		ResponseTime: responseTime,
	}

	if err := e.eventManager.Publish(EventDeviceStatusUpdate, event); err != nil {
		log.Printf("发布设备状态更新事件失败: %v", err)
	}
}

// PublishExecutionRecord 发布执行记录事件
func (e *LinkageEngine) PublishExecutionRecord(execution *database.LinkageExecution) {
	event := ExecutionRecordEvent{
		ID:         execution.ID, // ID是string类型，不需要转换
		RuleID:     execution.RuleID,
		DeviceID:   execution.DeviceID,
		Action:     execution.Action,
		Status:     execution.Status,
		Result:     execution.ResponseData, // 使用ResponseData字段
		ExecutedAt: execution.ExecutedAt,
		Duration:   int(execution.Duration), // 转换为int
	}

	if err := e.eventManager.Publish(EventExecutionRecord, event); err != nil {
		log.Printf("发布执行记录事件失败: %v", err)
	}
}

// PublishRuleStatusChange 发布规则状态变更事件
func (e *LinkageEngine) PublishRuleStatusChange(ruleID string, enabled bool, changedBy string) {
	event := RuleStatusChangeEvent{
		RuleID:    ruleID,
		Enabled:   enabled,
		ChangedBy: changedBy,
		ChangedAt: time.Now(),
	}

	if err := e.eventManager.Publish(EventRuleStatusChange, event); err != nil {
		log.Printf("发布规则状态变更事件失败: %v", err)
	}
}

// PublishSystemAlert 发布系统告警事件
func (e *LinkageEngine) PublishSystemAlert(level, message, component string) {
	event := SystemAlertEvent{
		Level:     level,
		Message:   message,
		Component: component,
		Timestamp: time.Now(),
	}

	if err := e.eventManager.Publish(EventSystemAlert, event); err != nil {
		log.Printf("发布系统告警事件失败: %v", err)
	}
}

// PublishStatisticsUpdate 发布统计信息更新事件
func (e *LinkageEngine) PublishStatisticsUpdate() {
	stats := e.GetStatistics()

	event := StatisticsUpdateEvent{
		TotalExecutions:   stats.TotalExecutions,   // int64 -> int64
		SuccessExecutions: stats.SuccessExecutions, // int64 -> int64
		FailedExecutions:  stats.FailedExecutions,  // int64 -> int64
		SuccessRate:       stats.SuccessRate,       // float64 -> float64
		OnlineDevices:     stats.OnlineDevices,     // int -> int
		OfflineDevices:    stats.OfflineDevices,    // int -> int
		ActiveRules:       stats.ActiveRules,       // int -> int
	}

	if err := e.eventManager.Publish(EventStatisticsUpdate, event); err != nil {
		log.Printf("发布统计信息更新事件失败: %v", err)
	}
}
