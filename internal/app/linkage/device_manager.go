package linkage

import (
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"ecp/internal/pkg/database"
)

// DeviceManager 设备管理器
type DeviceManager struct {
	db             *database.DB
	devices        map[string]*database.LinkageDevice
	status         map[string]*DeviceStatus
	adapterManager AdapterManager
	mutex          sync.RWMutex
}

// NewDeviceManager 创建设备管理器
func NewDeviceManager(db *database.DB) *DeviceManager {
	return &DeviceManager{
		db:      db,
		devices: make(map[string]*database.LinkageDevice),
		status:  make(map[string]*DeviceStatus),
	}
}

// SetAdapterManager 设置适配器管理器
func (dm *DeviceManager) SetAdapterManager(adapterManager AdapterManager) {
	dm.adapterManager = adapterManager
}

// LoadDevices 加载所有设备
func (dm *DeviceManager) LoadDevices() error {
	var devices []database.LinkageDevice
	if err := dm.db.Find(&devices).Error; err != nil {
		return fmt.Errorf("加载联动设备失败: %w", err)
	}

	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	dm.devices = make(map[string]*database.LinkageDevice)
	dm.status = make(map[string]*DeviceStatus)

	for i := range devices {
		device := &devices[i]
		dm.devices[device.ID] = device

		// 初始化设备状态
		dm.status[device.ID] = &DeviceStatus{
			DeviceID:     device.ID,
			Status:       device.Status,
			LastSeen:     device.LastSeen,
			ResponseTime: 0,
			ErrorCount:   0,
		}
	}

	log.Printf("加载了 %d 个联动设备", len(devices))
	return nil
}

// GetDevice 获取设备
func (dm *DeviceManager) GetDevice(deviceID string) (*database.LinkageDevice, error) {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	device, exists := dm.devices[deviceID]
	if !exists {
		return nil, ErrDeviceNotFound
	}

	return device, nil
}

// GetAllDevices 获取所有设备
func (dm *DeviceManager) GetAllDevices() []*database.LinkageDevice {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	devices := make([]*database.LinkageDevice, 0, len(dm.devices))
	for _, device := range dm.devices {
		devices = append(devices, device)
	}

	return devices
}

// GetDevicesByProtocol 按协议获取设备
func (dm *DeviceManager) GetDevicesByProtocol(protocol string) []*database.LinkageDevice {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	var devices []*database.LinkageDevice
	for _, device := range dm.devices {
		if device.Protocol == protocol {
			devices = append(devices, device)
		}
	}

	return devices
}

// CreateDevice 创建设备
func (dm *DeviceManager) CreateDevice(device *database.LinkageDevice) error {
	// 验证设备配置
	if err := dm.validateDevice(device); err != nil {
		return err
	}

	// 检查设备是否已存在
	dm.mutex.RLock()
	_, exists := dm.devices[device.ID]
	dm.mutex.RUnlock()

	if exists {
		return ErrDeviceAlreadyExists
	}

	// 保存到数据库
	if err := dm.db.Create(device).Error; err != nil {
		return fmt.Errorf("创建联动设备失败: %w", err)
	}

	// 更新内存缓存
	dm.mutex.Lock()
	dm.devices[device.ID] = device
	dm.status[device.ID] = &DeviceStatus{
		DeviceID:     device.ID,
		Status:       DeviceStatusOffline,
		LastSeen:     time.Now(),
		ResponseTime: 0,
		ErrorCount:   0,
	}
	dm.mutex.Unlock()

	log.Printf("创建联动设备成功: ID=%s, Name=%s, Protocol=%s",
		device.ID, device.Name, device.Protocol)
	return nil
}

// UpdateDevice 更新设备
func (dm *DeviceManager) UpdateDevice(device *database.LinkageDevice) error {
	// 验证设备配置
	if err := dm.validateDevice(device); err != nil {
		return err
	}

	// 检查设备是否存在
	dm.mutex.RLock()
	_, exists := dm.devices[device.ID]
	dm.mutex.RUnlock()

	if !exists {
		return ErrDeviceNotFound
	}

	// 更新数据库
	if err := dm.db.Save(device).Error; err != nil {
		return fmt.Errorf("更新联动设备失败: %w", err)
	}

	// 更新内存缓存
	dm.mutex.Lock()
	dm.devices[device.ID] = device
	dm.mutex.Unlock()

	log.Printf("更新联动设备成功: ID=%s, Name=%s", device.ID, device.Name)
	return nil
}

// DeleteDevice 删除设备
func (dm *DeviceManager) DeleteDevice(deviceID string) error {
	// 检查设备是否存在
	dm.mutex.RLock()
	_, exists := dm.devices[deviceID]
	dm.mutex.RUnlock()

	if !exists {
		return ErrDeviceNotFound
	}

	// 从数据库删除
	if err := dm.db.Delete(&database.LinkageDevice{}, "id = ?", deviceID).Error; err != nil {
		return fmt.Errorf("删除联动设备失败: %w", err)
	}

	// 从内存缓存删除
	dm.mutex.Lock()
	delete(dm.devices, deviceID)
	delete(dm.status, deviceID)
	dm.mutex.Unlock()

	log.Printf("删除联动设备成功: ID=%s", deviceID)
	return nil
}

// GetDeviceStatus 获取设备状态
func (dm *DeviceManager) GetDeviceStatus(deviceID string) (*DeviceStatus, error) {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	status, exists := dm.status[deviceID]
	if !exists {
		return nil, ErrDeviceNotFound
	}

	// 返回状态的副本
	statusCopy := *status
	return &statusCopy, nil
}

// UpdateDeviceStatus 更新设备状态
func (dm *DeviceManager) UpdateDeviceStatus(deviceID string, status string, responseTime int64, err error) {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	deviceStatus, exists := dm.status[deviceID]
	if !exists {
		return
	}

	deviceStatus.Status = status
	deviceStatus.LastSeen = time.Now()
	deviceStatus.ResponseTime = responseTime

	if err != nil {
		deviceStatus.ErrorCount++
		deviceStatus.LastError = err.Error()
	} else {
		deviceStatus.ErrorCount = 0
		deviceStatus.LastError = ""
	}

	// 更新数据库中的设备状态
	updates := map[string]interface{}{
		"status":    status,
		"last_seen": deviceStatus.LastSeen,
	}

	if dbErr := dm.db.Model(&database.LinkageDevice{}).Where("id = ?", deviceID).Updates(updates).Error; dbErr != nil {
		log.Printf("更新设备状态到数据库失败: %v", dbErr)
	}
}

// GetDeviceStatusSummary 获取设备状态摘要
func (dm *DeviceManager) GetDeviceStatusSummary() map[string]int {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	summary := map[string]int{
		DeviceStatusOnline:  0,
		DeviceStatusOffline: 0,
		DeviceStatusError:   0,
	}

	for _, status := range dm.status {
		summary[status.Status]++
	}

	return summary
}

// GetDevicesByStatus 按状态获取设备
func (dm *DeviceManager) GetDevicesByStatus(status string) []*database.LinkageDevice {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	var devices []*database.LinkageDevice
	for deviceID, deviceStatus := range dm.status {
		if deviceStatus.Status == status {
			if device, exists := dm.devices[deviceID]; exists {
				devices = append(devices, device)
			}
		}
	}

	return devices
}

// validateDevice 验证设备配置
func (dm *DeviceManager) validateDevice(device *database.LinkageDevice) error {
	if device.Name == "" {
		return NewLinkageError(ErrCodeInvalidDeviceConfig, "设备名称不能为空")
	}

	if device.Protocol == "" {
		return NewLinkageError(ErrCodeInvalidDeviceConfig, "设备协议不能为空")
	}

	// 验证协议类型
	supportedProtocols := []string{ProtocolMQTT, ProtocolModbus, ProtocolRS485}
	protocolSupported := false
	for _, protocol := range supportedProtocols {
		if device.Protocol == protocol {
			protocolSupported = true
			break
		}
	}

	if !protocolSupported {
		return NewLinkageError(ErrCodeAdapterNotSupported,
			fmt.Sprintf("不支持的协议类型: %s", device.Protocol))
	}

	if device.Address == "" {
		return NewLinkageError(ErrCodeInvalidDeviceConfig, "设备地址不能为空")
	}

	// 验证配置格式
	if device.Config != "" {
		var config DeviceConfig
		if err := json.Unmarshal([]byte(device.Config), &config); err != nil {
			return WrapError(err, ErrCodeInvalidDeviceConfig, "设备配置格式错误")
		}

		// 根据协议类型验证特定配置
		switch device.Protocol {
		case ProtocolMQTT:
			if err := dm.validateMQTTConfig(&config); err != nil {
				return err
			}
		case ProtocolModbus:
			if err := dm.validateModbusConfig(&config); err != nil {
				return err
			}
		case ProtocolRS485:
			if err := dm.validateRS485Config(&config); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateMQTTConfig 验证MQTT配置
func (dm *DeviceManager) validateMQTTConfig(config *DeviceConfig) error {
	if config.QoS < 0 || config.QoS > 2 {
		return NewLinkageError(ErrCodeInvalidDeviceConfig, "MQTT QoS级别必须在0-2之间")
	}

	return nil
}

// validateModbusConfig 验证Modbus配置
func (dm *DeviceManager) validateModbusConfig(config *DeviceConfig) error {
	if config.SlaveID < 1 || config.SlaveID > 247 {
		return NewLinkageError(ErrCodeInvalidDeviceConfig, "Modbus从站ID必须在1-247之间")
	}

	if config.Timeout <= 0 {
		return NewLinkageError(ErrCodeInvalidDeviceConfig, "Modbus超时时间必须大于0")
	}

	return nil
}

// validateRS485Config 验证RS485配置
func (dm *DeviceManager) validateRS485Config(config *DeviceConfig) error {
	validBaudRates := []int{9600, 19200, 38400, 57600, 115200}
	baudRateValid := false
	for _, rate := range validBaudRates {
		if config.BaudRate == rate {
			baudRateValid = true
			break
		}
	}

	if !baudRateValid {
		return NewLinkageError(ErrCodeInvalidDeviceConfig, "无效的波特率")
	}

	if config.DataBits < 5 || config.DataBits > 8 {
		return NewLinkageError(ErrCodeInvalidDeviceConfig, "数据位必须在5-8之间")
	}

	if config.StopBits < 1 || config.StopBits > 2 {
		return NewLinkageError(ErrCodeInvalidDeviceConfig, "停止位必须在1-2之间")
	}

	validParity := []string{"none", "odd", "even", "mark", "space"}
	parityValid := false
	for _, parity := range validParity {
		if config.Parity == parity {
			parityValid = true
			break
		}
	}

	if !parityValid {
		return NewLinkageError(ErrCodeInvalidDeviceConfig, "无效的校验位设置")
	}

	return nil
}

// HealthCheck 健康检查
func (dm *DeviceManager) HealthCheck() error {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	// 检查是否有设备长时间离线
	now := time.Now()
	offlineThreshold := 5 * time.Minute

	for deviceID, status := range dm.status {
		if status.Status == DeviceStatusOffline && now.Sub(status.LastSeen) > offlineThreshold {
			log.Printf("设备 %s 长时间离线: %v", deviceID, now.Sub(status.LastSeen))
		}
	}

	return nil
}

// GetStatistics 获取设备统计信息
func (dm *DeviceManager) GetStatistics() map[string]interface{} {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_devices":   len(dm.devices),
		"online_devices":  0,
		"offline_devices": 0,
		"error_devices":   0,
		"protocol_stats":  make(map[string]int),
	}

	protocolStats := make(map[string]int)

	for _, device := range dm.devices {
		protocolStats[device.Protocol]++

		if status, exists := dm.status[device.ID]; exists {
			switch status.Status {
			case DeviceStatusOnline:
				stats["online_devices"] = stats["online_devices"].(int) + 1
			case DeviceStatusOffline:
				stats["offline_devices"] = stats["offline_devices"].(int) + 1
			case DeviceStatusError:
				stats["error_devices"] = stats["error_devices"].(int) + 1
			}
		}
	}

	stats["protocol_stats"] = protocolStats
	return stats
}
