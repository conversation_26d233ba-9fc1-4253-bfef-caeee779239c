package linkage

import (
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// SSEHandler SSE处理器
type SSEHandler struct {
	eventManager *EventManager
}

// NewSSEHandler 创建SSE处理器
func NewSSEHandler(eventManager *EventManager) *SSEHandler {
	return &SSEHandler{
		eventManager: eventManager,
	}
}

// StreamDeviceStatus 设备状态流
func (h *SSEHandler) StreamDeviceStatus(c *gin.Context) {
	h.handleSSEStream(c, []EventType{EventDeviceStatusUpdate})
}

// StreamExecutionRecords 执行记录流
func (h *SSEHandler) StreamExecutionRecords(c *gin.Context) {
	h.handleSSEStream(c, []EventType{EventExecutionRecord})
}

// StreamSystemAlerts 系统告警流
func (h *SSEHandler) StreamSystemAlerts(c *gin.Context) {
	h.handleSSEStream(c, []EventType{EventSystemAlert})
}

// StreamStatistics 统计数据流
func (h *SSEHandler) StreamStatistics(c *gin.Context) {
	h.handleSSEStream(c, []EventType{EventStatisticsUpdate})
}

// StreamAll 所有事件流
func (h *SSEHandler) StreamAll(c *gin.Context) {
	h.handleSSEStream(c, []EventType{
		EventDeviceStatusUpdate,
		EventExecutionRecord,
		EventRuleStatusChange,
		EventSystemAlert,
		EventStatisticsUpdate,
	})
}

// handleSSEStream 处理SSE流的通用方法
func (h *SSEHandler) handleSSEStream(c *gin.Context, eventTypes []EventType) {
	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 创建订阅者
	subscriberID := uuid.New().String()
	eventChan := make(chan Event, 100)
	
	subscriber := &SSESubscriber{
		ID:      subscriberID,
		Channel: eventChan,
		Types:   eventTypes,
	}

	// 注册订阅者
	if err := h.eventManager.Subscribe(subscriber); err != nil {
		c.SSEvent("error", fmt.Sprintf("Failed to subscribe: %v", err))
		return
	}

	// 确保在连接关闭时取消订阅
	defer func() {
		h.eventManager.Unsubscribe(subscriberID)
		close(eventChan)
	}()

	// 发送连接确认消息
	c.SSEvent("connected", map[string]interface{}{
		"subscriber_id": subscriberID,
		"event_types":   eventTypes,
		"timestamp":     time.Now().Format(time.RFC3339),
	})
	c.Writer.Flush()

	// 创建心跳定时器
	heartbeatTicker := time.NewTicker(30 * time.Second)
	defer heartbeatTicker.Stop()

	// 处理事件流
	for {
		select {
		case event, ok := <-eventChan:
			if !ok {
				return
			}

			// 发送事件数据
			eventData := map[string]interface{}{
				"type":      event.Type,
				"payload":   event.Payload,
				"timestamp": event.Timestamp.Format(time.RFC3339),
				"id":        event.ID,
			}

			c.SSEvent("data", eventData)
			c.Writer.Flush()

		case <-heartbeatTicker.C:
			// 发送心跳消息
			c.SSEvent("heartbeat", map[string]interface{}{
				"timestamp": time.Now().Format(time.RFC3339),
				"message":   "heartbeat",
			})
			c.Writer.Flush()

		case <-c.Request.Context().Done():
			// 客户端断开连接
			return
		}
	}
}

// StreamWithFilter 带过滤器的事件流
func (h *SSEHandler) StreamWithFilter(c *gin.Context) {
	// 获取查询参数
	eventTypesParam := c.Query("types")
	deviceIDParam := c.Query("device_id")
	ruleIDParam := c.Query("rule_id")

	// 解析事件类型
	var eventTypes []EventType
	if eventTypesParam != "" {
		typeStrings := strings.Split(eventTypesParam, ",")
		for _, typeStr := range typeStrings {
			eventTypes = append(eventTypes, EventType(strings.TrimSpace(typeStr)))
		}
	} else {
		// 默认订阅所有事件类型
		eventTypes = []EventType{
			EventDeviceStatusUpdate,
			EventExecutionRecord,
			EventRuleStatusChange,
			EventSystemAlert,
			EventStatisticsUpdate,
		}
	}

	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 创建过滤订阅者
	subscriberID := uuid.New().String()
	eventChan := make(chan Event, 100)
	
	subscriber := &FilteredSSESubscriber{
		SSESubscriber: SSESubscriber{
			ID:      subscriberID,
			Channel: eventChan,
			Types:   eventTypes,
		},
		DeviceID: deviceIDParam,
		RuleID:   ruleIDParam,
	}

	// 注册订阅者
	if err := h.eventManager.Subscribe(subscriber); err != nil {
		c.SSEvent("error", fmt.Sprintf("Failed to subscribe: %v", err))
		return
	}

	// 确保在连接关闭时取消订阅
	defer func() {
		h.eventManager.Unsubscribe(subscriberID)
		close(eventChan)
	}()

	// 发送连接确认消息
	c.SSEvent("connected", map[string]interface{}{
		"subscriber_id": subscriberID,
		"event_types":   eventTypes,
		"filters": map[string]string{
			"device_id": deviceIDParam,
			"rule_id":   ruleIDParam,
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
	c.Writer.Flush()

	// 创建心跳定时器
	heartbeatTicker := time.NewTicker(30 * time.Second)
	defer heartbeatTicker.Stop()

	// 处理事件流
	for {
		select {
		case event, ok := <-eventChan:
			if !ok {
				return
			}

			// 发送事件数据
			eventData := map[string]interface{}{
				"type":      event.Type,
				"payload":   event.Payload,
				"timestamp": event.Timestamp.Format(time.RFC3339),
				"id":        event.ID,
			}

			c.SSEvent("data", eventData)
			c.Writer.Flush()

		case <-heartbeatTicker.C:
			// 发送心跳消息
			c.SSEvent("heartbeat", map[string]interface{}{
				"timestamp": time.Now().Format(time.RFC3339),
				"message":   "heartbeat",
			})
			c.Writer.Flush()

		case <-c.Request.Context().Done():
			// 客户端断开连接
			return
		}
	}
}

// FilteredSSESubscriber 带过滤器的SSE订阅者
type FilteredSSESubscriber struct {
	SSESubscriber
	DeviceID string
	RuleID   string
}

func (s *FilteredSSESubscriber) OnEvent(event Event) error {
	// 先检查事件类型
	if len(s.Types) > 0 {
		subscribed := false
		for _, eventType := range s.Types {
			if eventType == event.Type {
				subscribed = true
				break
			}
		}
		if !subscribed {
			return nil
		}
	}

	// 应用过滤器
	if !s.matchesFilter(event) {
		return nil
	}

	select {
	case s.Channel <- event:
		return nil
	default:
		return fmt.Errorf("subscriber %s channel is full", s.ID)
	}
}

func (s *FilteredSSESubscriber) matchesFilter(event Event) bool {
	// 根据事件类型应用不同的过滤逻辑
	switch event.Type {
	case EventDeviceStatusUpdate:
		if s.DeviceID != "" {
			if payload, ok := event.Payload.(DeviceStatusUpdateEvent); ok {
				return payload.DeviceID == s.DeviceID
			}
		}
	case EventExecutionRecord:
		if payload, ok := event.Payload.(ExecutionRecordEvent); ok {
			if s.DeviceID != "" && payload.DeviceID != s.DeviceID {
				return false
			}
			if s.RuleID != "" && payload.RuleID != s.RuleID {
				return false
			}
		}
	case EventRuleStatusChange:
		if s.RuleID != "" {
			if payload, ok := event.Payload.(RuleStatusChangeEvent); ok {
				return payload.RuleID == s.RuleID
			}
		}
	}

	return true
}
