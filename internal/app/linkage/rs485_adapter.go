package linkage

import (
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"ecp/internal/pkg/database"

	"go.bug.st/serial"
)

// RS485Adapter RS485协议适配器
type RS485Adapter struct {
	*BaseAdapter
	ports  map[string]serial.Port
	config RS485Config
	mutex  sync.RWMutex
}

// NewRS485Adapter 创建RS485适配器
func NewRS485Adapter(config RS485Config) *RS485Adapter {
	adapter := &RS485Adapter{
		BaseAdapter: NewBaseAdapter(ProtocolRS485),
		ports:       make(map[string]serial.Port),
		config:      config,
	}
	return adapter
}

// Connect 连接RS485设备
func (a *RS485Adapter) Connect(device *database.LinkageDevice) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 检查是否已连接
	if port, exists := a.ports[device.ID]; exists && port != nil {
		return nil
	}

	// 解析设备配置
	var rs485Config struct {
		SerialPort string `json:"serial_port"`
		BaudRate   int    `json:"baud_rate"`
		DataBits   int    `json:"data_bits"`
		StopBits   int    `json:"stop_bits"`
		Parity     string `json:"parity"`
		Timeout    int    `json:"timeout"`
		BufferSize int    `json:"buffer_size"`
		Protocol   string `json:"protocol"`   // custom, modbus-rtu等
		DeviceAddr byte   `json:"device_addr"` // 设备地址
	}

	if err := json.Unmarshal([]byte(device.Config), &rs485Config); err != nil {
		return fmt.Errorf("解析RS485配置失败: %w", err)
	}

	// 设置默认值
	serialPort := device.Address
	if rs485Config.SerialPort != "" {
		serialPort = rs485Config.SerialPort
	}

	if rs485Config.BaudRate == 0 {
		rs485Config.BaudRate = 9600
	}
	if rs485Config.DataBits == 0 {
		rs485Config.DataBits = 8
	}
	if rs485Config.StopBits == 0 {
		rs485Config.StopBits = 1
	}
	if rs485Config.Parity == "" {
		rs485Config.Parity = "none"
	}
	if rs485Config.Timeout == 0 {
		rs485Config.Timeout = int(a.config.Timeout.Seconds())
	}
	if rs485Config.BufferSize == 0 {
		rs485Config.BufferSize = a.config.BufferSize
	}
	if rs485Config.Protocol == "" {
		rs485Config.Protocol = "custom"
	}

	startTime := time.Now()

	// 配置串口参数
	mode := &serial.Mode{
		BaudRate: rs485Config.BaudRate,
		DataBits: rs485Config.DataBits,
		StopBits: serial.StopBits(rs485Config.StopBits),
	}

	// 设置校验位
	switch rs485Config.Parity {
	case "none", "N":
		mode.Parity = serial.NoParity
	case "odd", "O":
		mode.Parity = serial.OddParity
	case "even", "E":
		mode.Parity = serial.EvenParity
	case "mark", "M":
		mode.Parity = serial.MarkParity
	case "space", "S":
		mode.Parity = serial.SpaceParity
	default:
		return fmt.Errorf("不支持的校验位设置: %s", rs485Config.Parity)
	}

	// 打开串口
	port, err := serial.Open(serialPort, mode)
	if err != nil {
		duration := time.Since(startTime)
		a.updateDeviceStatus(device.ID, DeviceStatusError, duration.Milliseconds(), err)
		return fmt.Errorf("打开串口失败: %w", err)
	}

	// 设置读取超时
	if err := port.SetReadTimeout(time.Duration(rs485Config.Timeout) * time.Second); err != nil {
		port.Close()
		duration := time.Since(startTime)
		a.updateDeviceStatus(device.ID, DeviceStatusError, duration.Milliseconds(), err)
		return fmt.Errorf("设置读取超时失败: %w", err)
	}

	// 测试连接（发送一个简单的查询命令）
	testErr := a.testConnection(port, rs485Config)
	duration := time.Since(startTime)

	if testErr != nil {
		log.Printf("RS485设备 %s (%s) 连接测试警告: %v", device.Name, device.ID, testErr)
		// 不返回错误，因为测试命令可能不适用于所有设备
	}

	// 保存端口
	a.ports[device.ID] = port
	a.AddDevice(device)
	a.updateDeviceStatus(device.ID, DeviceStatusOnline, duration.Milliseconds(), nil)

	log.Printf("RS485设备 %s (%s) 连接成功，串口: %s，波特率: %d，耗时: %v", 
		device.Name, device.ID, serialPort, rs485Config.BaudRate, duration)
	return nil
}

// testConnection 测试连接
func (a *RS485Adapter) testConnection(port serial.Port, config interface{}) error {
	// 发送一个简单的测试命令
	testCmd := []byte{0x01, 0x03, 0x00, 0x00, 0x00, 0x01} // 简单的Modbus读取命令
	
	// 计算CRC16校验
	crc := a.calculateCRC16(testCmd)
	testCmd = append(testCmd, byte(crc&0xFF), byte(crc>>8))

	// 发送命令
	_, err := port.Write(testCmd)
	if err != nil {
		return err
	}

	// 尝试读取响应
	buffer := make([]byte, 64)
	port.SetReadTimeout(1 * time.Second)
	_, err = port.Read(buffer)
	
	// 恢复原始超时设置
	rs485Config := config.(struct {
		SerialPort string `json:"serial_port"`
		BaudRate   int    `json:"baud_rate"`
		DataBits   int    `json:"data_bits"`
		StopBits   int    `json:"stop_bits"`
		Parity     string `json:"parity"`
		Timeout    int    `json:"timeout"`
		BufferSize int    `json:"buffer_size"`
		Protocol   string `json:"protocol"`
		DeviceAddr byte   `json:"device_addr"`
	})
	port.SetReadTimeout(time.Duration(rs485Config.Timeout) * time.Second)

	return err // 返回读取结果，但不影响连接状态
}

// Disconnect 断开RS485设备连接
func (a *RS485Adapter) Disconnect(deviceID string) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	port, exists := a.ports[deviceID]
	if !exists {
		return ErrDeviceNotFound
	}

	if port != nil {
		port.Close()
	}

	delete(a.ports, deviceID)
	a.RemoveDevice(deviceID)
	a.updateDeviceStatus(deviceID, DeviceStatusOffline, 0, nil)

	log.Printf("RS485设备 %s 已断开连接", deviceID)
	return nil
}

// SendCommand 发送命令到RS485设备
func (a *RS485Adapter) SendCommand(deviceID string, command string, params map[string]interface{}) error {
	a.mutex.RLock()
	port, exists := a.ports[deviceID]
	a.mutex.RUnlock()

	if !exists {
		return ErrDeviceNotFound
	}

	if port == nil {
		return ErrDeviceOffline
	}

	startTime := time.Now()

	// 构建命令数据
	cmdData, err := a.buildCommand(command, params)
	if err != nil {
		return fmt.Errorf("构建命令失败: %w", err)
	}

	// 发送命令
	_, err = port.Write(cmdData)
	if err != nil {
		duration := time.Since(startTime)
		a.updateDeviceStatus(deviceID, DeviceStatusError, duration.Milliseconds(), err)
		return fmt.Errorf("发送RS485命令失败: %w", err)
	}

	// 读取响应（如果需要）
	if needResponse, ok := params["need_response"].(bool); ok && needResponse {
		buffer := make([]byte, 256)
		n, err := port.Read(buffer)
		duration := time.Since(startTime)

		if err != nil {
			a.updateDeviceStatus(deviceID, DeviceStatusError, duration.Milliseconds(), err)
			return fmt.Errorf("读取RS485响应失败: %w", err)
		}

		response := buffer[:n]
		log.Printf("RS485设备 %s 响应: %s", deviceID, hex.EncodeToString(response))

		// 验证响应
		if err := a.validateResponse(response, params); err != nil {
			a.updateDeviceStatus(deviceID, DeviceStatusError, duration.Milliseconds(), err)
			return fmt.Errorf("响应验证失败: %w", err)
		}
	}

	duration := time.Since(startTime)
	a.updateDeviceStatus(deviceID, DeviceStatusOnline, duration.Milliseconds(), nil)
	log.Printf("RS485命令发送成功: 设备=%s, 命令=%s, 数据=%s, 耗时=%v", 
		deviceID, command, hex.EncodeToString(cmdData), duration)

	return nil
}

// buildCommand 构建命令数据
func (a *RS485Adapter) buildCommand(command string, params map[string]interface{}) ([]byte, error) {
	switch command {
	case "raw_hex":
		// 发送原始十六进制数据
		if hexStr, ok := params["data"].(string); ok {
			return hex.DecodeString(hexStr)
		}
		return nil, fmt.Errorf("raw_hex命令需要data参数")

	case "modbus_read":
		// Modbus读取命令
		deviceAddr := byte(params["device_addr"].(float64))
		function := byte(params["function"].(float64))
		startAddr := uint16(params["start_addr"].(float64))
		quantity := uint16(params["quantity"].(float64))

		cmd := []byte{deviceAddr, function}
		cmd = append(cmd, byte(startAddr>>8), byte(startAddr&0xFF))
		cmd = append(cmd, byte(quantity>>8), byte(quantity&0xFF))

		// 计算CRC16校验
		crc := a.calculateCRC16(cmd)
		cmd = append(cmd, byte(crc&0xFF), byte(crc>>8))

		return cmd, nil

	case "modbus_write":
		// Modbus写入命令
		deviceAddr := byte(params["device_addr"].(float64))
		function := byte(params["function"].(float64))
		startAddr := uint16(params["start_addr"].(float64))
		value := uint16(params["value"].(float64))

		cmd := []byte{deviceAddr, function}
		cmd = append(cmd, byte(startAddr>>8), byte(startAddr&0xFF))
		cmd = append(cmd, byte(value>>8), byte(value&0xFF))

		// 计算CRC16校验
		crc := a.calculateCRC16(cmd)
		cmd = append(cmd, byte(crc&0xFF), byte(crc>>8))

		return cmd, nil

	case "custom":
		// 自定义协议命令
		if cmdBytes, ok := params["command_bytes"].([]interface{}); ok {
			cmd := make([]byte, len(cmdBytes))
			for i, b := range cmdBytes {
				cmd[i] = byte(b.(float64))
			}

			// 如果需要校验，添加校验码
			if addChecksum, ok := params["add_checksum"].(bool); ok && addChecksum {
				checksum := a.calculateChecksum(cmd)
				cmd = append(cmd, checksum)
			}

			return cmd, nil
		}
		return nil, fmt.Errorf("custom命令需要command_bytes参数")

	default:
		return nil, fmt.Errorf("不支持的RS485命令: %s", command)
	}
}

// validateResponse 验证响应
func (a *RS485Adapter) validateResponse(response []byte, params map[string]interface{}) error {
	if len(response) < 2 {
		return fmt.Errorf("响应数据太短")
	}

	// 检查是否是错误响应
	if len(response) >= 3 && (response[1]&0x80) != 0 {
		return fmt.Errorf("设备返回错误码: 0x%02X", response[2])
	}

	// 验证CRC16校验（如果启用）
	if validateCRC, ok := params["validate_crc"].(bool); ok && validateCRC {
		if len(response) < 3 {
			return fmt.Errorf("响应数据长度不足以包含CRC")
		}

		data := response[:len(response)-2]
		expectedCRC := a.calculateCRC16(data)
		actualCRC := uint16(response[len(response)-2]) | (uint16(response[len(response)-1]) << 8)

		if expectedCRC != actualCRC {
			return fmt.Errorf("CRC校验失败: 期望=0x%04X, 实际=0x%04X", expectedCRC, actualCRC)
		}
	}

	return nil
}

// calculateCRC16 计算CRC16校验码（Modbus标准）
func (a *RS485Adapter) calculateCRC16(data []byte) uint16 {
	crc := uint16(0xFFFF)
	for _, b := range data {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if (crc & 0x0001) != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}

// calculateChecksum 计算简单校验和
func (a *RS485Adapter) calculateChecksum(data []byte) byte {
	var sum byte
	for _, b := range data {
		sum += b
	}
	return sum
}

// IsConnected 检查设备是否已连接
func (a *RS485Adapter) IsConnected(deviceID string) bool {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	port, exists := a.ports[deviceID]
	return exists && port != nil
}

// GetStatus 获取设备状态
func (a *RS485Adapter) GetStatus(deviceID string) (string, error) {
	if a.IsConnected(deviceID) {
		return DeviceStatusOnline, nil
	}
	return DeviceStatusOffline, ErrDeviceNotFound
}

// GetDeviceInfo 获取设备信息
func (a *RS485Adapter) GetDeviceInfo(deviceID string) (*DeviceStatus, error) {
	device, exists := a.GetDevice(deviceID)
	if !exists {
		return nil, ErrDeviceNotFound
	}

	status := &DeviceStatus{
		DeviceID: deviceID,
		LastSeen: device.LastSeen,
	}

	if a.IsConnected(deviceID) {
		status.Status = DeviceStatusOnline
	} else {
		status.Status = DeviceStatusOffline
	}

	return status, nil
}

// HealthCheck 健康检查
func (a *RS485Adapter) HealthCheck() error {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	errorCount := 0
	for deviceID, port := range a.ports {
		if port == nil {
			errorCount++
			log.Printf("RS485设备 %s 端口为空", deviceID)
			continue
		}

		// 尝试发送一个简单的测试命令
		testCmd := []byte{0x01, 0x03, 0x00, 0x00, 0x00, 0x01}
		crc := a.calculateCRC16(testCmd)
		testCmd = append(testCmd, byte(crc&0xFF), byte(crc>>8))

		port.SetReadTimeout(1 * time.Second)
		_, err := port.Write(testCmd)
		if err != nil {
			errorCount++
			log.Printf("RS485设备 %s 健康检查失败: %v", deviceID, err)
		}
	}

	if errorCount > 0 {
		return fmt.Errorf("有 %d 个RS485设备健康检查失败", errorCount)
	}

	return nil
}

// Close 关闭适配器
func (a *RS485Adapter) Close() error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	for deviceID, port := range a.ports {
		if port != nil {
			port.Close()
		}
		log.Printf("RS485设备 %s 连接已关闭", deviceID)
	}

	a.ports = make(map[string]serial.Port)
	return a.BaseAdapter.Close()
}

// updateDeviceStatus 更新设备状态
func (a *RS485Adapter) updateDeviceStatus(deviceID string, status string, responseTime int64, err error) {
	if err != nil {
		log.Printf("RS485设备 %s 状态更新: %s, 响应时间: %dms, 错误: %v", 
			deviceID, status, responseTime, err)
	} else {
		log.Printf("RS485设备 %s 状态更新: %s, 响应时间: %dms", 
			deviceID, status, responseTime)
	}
}
