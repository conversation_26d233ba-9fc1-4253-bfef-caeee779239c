package linkage

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// EventType 事件类型
type EventType string

const (
	EventDeviceStatusUpdate EventType = "device_status_update"
	EventExecutionRecord    EventType = "execution_record"
	EventRuleStatusChange   EventType = "rule_status_change"
	EventSystemAlert        EventType = "system_alert"
	EventStatisticsUpdate   EventType = "statistics_update"
)

// Event 通用事件结构
type Event struct {
	Type      EventType   `json:"type"`
	Payload   interface{} `json:"payload"`
	Timestamp time.Time   `json:"timestamp"`
	ID        string      `json:"id"`
}

// EventSubscriber 事件订阅者接口
type EventSubscriber interface {
	OnEvent(event Event) error
	GetID() string
}

// SSESubscriber SSE订阅者
type SSESubscriber struct {
	ID      string
	Channel chan Event
	Types   []EventType // 订阅的事件类型
}

func (s *SSESubscriber) OnEvent(event Event) error {
	// 检查是否订阅了该事件类型
	if len(s.Types) > 0 {
		subscribed := false
		for _, eventType := range s.Types {
			if eventType == event.Type {
				subscribed = true
				break
			}
		}
		if !subscribed {
			return nil
		}
	}

	select {
	case s.Channel <- event:
		return nil
	default:
		// 通道满了，丢弃事件
		return fmt.Errorf("subscriber %s channel is full", s.ID)
	}
}

func (s *SSESubscriber) GetID() string {
	return s.ID
}

// EventManager 事件管理器
type EventManager struct {
	subscribers map[string]EventSubscriber
	mutex       sync.RWMutex
	eventQueue  chan Event
	running     bool
}

// NewEventManager 创建事件管理器
func NewEventManager() *EventManager {
	return &EventManager{
		subscribers: make(map[string]EventSubscriber),
		eventQueue:  make(chan Event, 1000), // 事件队列缓冲区
		running:     false,
	}
}

// Start 启动事件管理器
func (em *EventManager) Start() error {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	if em.running {
		return fmt.Errorf("event manager is already running")
	}

	em.running = true
	go em.processEvents()
	return nil
}

// Stop 停止事件管理器
func (em *EventManager) Stop() error {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	if !em.running {
		return nil
	}

	em.running = false
	close(em.eventQueue)
	return nil
}

// Subscribe 订阅事件
func (em *EventManager) Subscribe(subscriber EventSubscriber) error {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	em.subscribers[subscriber.GetID()] = subscriber
	return nil
}

// Unsubscribe 取消订阅
func (em *EventManager) Unsubscribe(subscriberID string) error {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	delete(em.subscribers, subscriberID)
	return nil
}

// Publish 发布事件
func (em *EventManager) Publish(eventType EventType, payload interface{}) error {
	event := Event{
		Type:      eventType,
		Payload:   payload,
		Timestamp: time.Now(),
		ID:        generateEventID(),
	}

	select {
	case em.eventQueue <- event:
		return nil
	default:
		return fmt.Errorf("event queue is full")
	}
}

// processEvents 处理事件队列
func (em *EventManager) processEvents() {
	for event := range em.eventQueue {
		em.mutex.RLock()
		subscribers := make([]EventSubscriber, 0, len(em.subscribers))
		for _, subscriber := range em.subscribers {
			subscribers = append(subscribers, subscriber)
		}
		em.mutex.RUnlock()

		// 并发发送给所有订阅者
		for _, subscriber := range subscribers {
			go func(sub EventSubscriber) {
				if err := sub.OnEvent(event); err != nil {
					// 记录错误，但不影响其他订阅者
					fmt.Printf("Error sending event to subscriber %s: %v\n", sub.GetID(), err)
				}
			}(subscriber)
		}
	}
}

// 事件数据结构定义

// DeviceStatusUpdateEvent 设备状态更新事件
type DeviceStatusUpdateEvent struct {
	DeviceID     string    `json:"device_id"`
	Status       string    `json:"status"`
	LastSeen     time.Time `json:"last_seen"`
	ErrorCount   int       `json:"error_count"`
	ResponseTime *int      `json:"response_time,omitempty"`
}

// ExecutionRecordEvent 执行记录事件
type ExecutionRecordEvent struct {
	ID         string    `json:"id"`
	RuleID     string    `json:"rule_id"`
	DeviceID   string    `json:"device_id"`
	Action     string    `json:"action"`
	Status     string    `json:"status"`
	Result     string    `json:"result"`
	ExecutedAt time.Time `json:"executed_at"`
	Duration   int       `json:"duration"`
}

// RuleStatusChangeEvent 规则状态变更事件
type RuleStatusChangeEvent struct {
	RuleID    string    `json:"rule_id"`
	Enabled   bool      `json:"enabled"`
	ChangedBy string    `json:"changed_by"`
	ChangedAt time.Time `json:"changed_at"`
}

// SystemAlertEvent 系统告警事件
type SystemAlertEvent struct {
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	Component string    `json:"component"`
	Timestamp time.Time `json:"timestamp"`
}

// StatisticsUpdateEvent 统计信息更新事件
type StatisticsUpdateEvent struct {
	TotalExecutions   int64   `json:"total_executions"`
	SuccessExecutions int64   `json:"success_executions"`
	FailedExecutions  int64   `json:"failed_executions"`
	SuccessRate       float64 `json:"success_rate"`
	OnlineDevices     int     `json:"online_devices"`
	OfflineDevices    int     `json:"offline_devices"`
	ActiveRules       int     `json:"active_rules"`
}

// 辅助函数
func generateEventID() string {
	return fmt.Sprintf("event_%d", time.Now().UnixNano())
}

// ToJSON 将事件转换为JSON字符串
func (e *Event) ToJSON() (string, error) {
	data, err := json.Marshal(e)
	if err != nil {
		return "", err
	}
	return string(data), nil
}
