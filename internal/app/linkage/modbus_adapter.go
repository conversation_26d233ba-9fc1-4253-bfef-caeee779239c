package linkage

import (
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"ecp/internal/pkg/database"

	"github.com/goburrow/modbus"
)

// ModbusAdapter Modbus协议适配器
type ModbusAdapter struct {
	*BaseAdapter
	handlers map[string]modbus.Client
	config   ModbusConfig
	mutex    sync.RWMutex
}

// NewModbusAdapter 创建Modbus适配器
func NewModbusAdapter(config ModbusConfig) *ModbusAdapter {
	adapter := &ModbusAdapter{
		BaseAdapter: NewBaseAdapter(ProtocolModbus),
		handlers:    make(map[string]modbus.Client),
		config:      config,
	}
	return adapter
}

// Connect 连接Modbus设备
func (a *ModbusAdapter) Connect(device *database.LinkageDevice) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 检查是否已连接
	if _, exists := a.handlers[device.ID]; exists {
		return nil
	}

	// 解析设备配置
	var modbusConfig struct {
		SlaveID    byte   `json:"slave_id"`
		Timeout    int    `json:"timeout"`
		RetryCount int    `json:"retry_count"`
		Mode       string `json:"mode"` // tcp, rtu, ascii
		BaudRate   int    `json:"baud_rate"`
		DataBits   int    `json:"data_bits"`
		StopBits   int    `json:"stop_bits"`
		Parity     string `json:"parity"`
		SerialPort string `json:"serial_port"`
	}

	if err := json.Unmarshal([]byte(device.Config), &modbusConfig); err != nil {
		return fmt.Errorf("解析Modbus配置失败: %w", err)
	}

	// 设置默认值
	if modbusConfig.SlaveID == 0 {
		modbusConfig.SlaveID = a.config.SlaveID
	}
	if modbusConfig.Timeout == 0 {
		modbusConfig.Timeout = int(a.config.Timeout.Seconds())
	}
	if modbusConfig.RetryCount == 0 {
		modbusConfig.RetryCount = a.config.RetryCount
	}
	if modbusConfig.Mode == "" {
		modbusConfig.Mode = "tcp"
	}

	var handler modbus.Client
	var err error

	startTime := time.Now()

	// 根据模式创建客户端
	switch modbusConfig.Mode {
	case "tcp":
		handler, err = a.createTCPClient(device, modbusConfig)
	case "rtu":
		handler, err = a.createRTUClient(device, modbusConfig)
	case "ascii":
		handler, err = a.createASCIIClient(device, modbusConfig)
	default:
		err = fmt.Errorf("不支持的Modbus模式: %s", modbusConfig.Mode)
	}

	if err != nil {
		duration := time.Since(startTime)
		a.updateDeviceStatus(device.ID, DeviceStatusError, duration.Milliseconds(), err)
		return err
	}

	// 注意：goburrow/modbus库的客户端配置通常在创建时设置
	// 这里我们记录配置信息，实际的从站ID会在每个请求中指定

	// 测试连接
	_, testErr := handler.ReadCoils(0, 1)
	duration := time.Since(startTime)

	if testErr != nil {
		log.Printf("Modbus设备 %s (%s) 连接测试警告: %v", device.Name, device.ID, testErr)
		// 不返回错误，因为某些设备可能不支持读取线圈
	}

	// 保存处理器
	a.handlers[device.ID] = handler
	a.AddDevice(device)
	a.updateDeviceStatus(device.ID, DeviceStatusOnline, duration.Milliseconds(), nil)

	log.Printf("Modbus设备 %s (%s) 连接成功，模式: %s，耗时: %v",
		device.Name, device.ID, modbusConfig.Mode, duration)
	return nil
}

// createTCPClient 创建TCP客户端
func (a *ModbusAdapter) createTCPClient(device *database.LinkageDevice, config interface{}) (modbus.Client, error) {
	address := fmt.Sprintf("%s:%d", device.Address, device.Port)
	handler := modbus.TCPClient(address)
	return handler, nil
}

// createRTUClient 创建RTU客户端
func (a *ModbusAdapter) createRTUClient(device *database.LinkageDevice, config interface{}) (modbus.Client, error) {
	// 如果有端口号，使用RTU over TCP
	if device.Port > 0 {
		address := fmt.Sprintf("%s:%d", device.Address, device.Port)
		handler := modbus.TCPClient(address)
		return handler, nil
	}

	// 对于串口RTU，使用简化的实现
	// 注意：goburrow/modbus库的串口支持可能需要额外配置
	return nil, fmt.Errorf("串口RTU模式暂不支持，请使用TCP模式")
}

// createASCIIClient 创建ASCII客户端
func (a *ModbusAdapter) createASCIIClient(device *database.LinkageDevice, config interface{}) (modbus.Client, error) {
	// ASCII模式通常用于串口通信
	// 注意：goburrow/modbus库的ASCII支持可能需要额外配置
	return nil, fmt.Errorf("ASCII模式暂不支持，请使用TCP模式")
}

// Disconnect 断开Modbus设备连接
func (a *ModbusAdapter) Disconnect(deviceID string) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	handler, exists := a.handlers[deviceID]
	if !exists {
		return ErrDeviceNotFound
	}

	// Modbus客户端通常不需要显式断开连接
	// 但我们可以尝试关闭连接
	if closer, ok := handler.(interface{ Close() error }); ok {
		closer.Close()
	}

	delete(a.handlers, deviceID)
	a.RemoveDevice(deviceID)
	a.updateDeviceStatus(deviceID, DeviceStatusOffline, 0, nil)

	log.Printf("Modbus设备 %s 已断开连接", deviceID)
	return nil
}

// SendCommand 发送命令到Modbus设备
func (a *ModbusAdapter) SendCommand(deviceID string, command string, params map[string]interface{}) error {
	a.mutex.RLock()
	handler, exists := a.handlers[deviceID]
	a.mutex.RUnlock()

	if !exists {
		return ErrDeviceNotFound
	}

	startTime := time.Now()

	// 执行重试逻辑
	var lastErr error
	for i := 0; i < a.config.RetryCount; i++ {
		err := a.executeModbusCommand(handler, command, params)
		duration := time.Since(startTime)

		if err == nil {
			a.updateDeviceStatus(deviceID, DeviceStatusOnline, duration.Milliseconds(), nil)
			log.Printf("Modbus命令执行成功: 设备=%s, 命令=%s, 耗时=%v",
				deviceID, command, duration)
			return nil
		}

		lastErr = err
		if i < a.config.RetryCount-1 {
			time.Sleep(time.Second)
		}
	}

	duration := time.Since(startTime)
	a.updateDeviceStatus(deviceID, DeviceStatusError, duration.Milliseconds(), lastErr)
	return fmt.Errorf("Modbus命令执行失败 (重试%d次): %w", a.config.RetryCount, lastErr)
}

// executeModbusCommand 执行Modbus命令
func (a *ModbusAdapter) executeModbusCommand(handler modbus.Client, command string, params map[string]interface{}) error {
	switch command {
	case "read_coils":
		address := uint16(params["address"].(float64))
		quantity := uint16(params["quantity"].(float64))
		results, err := handler.ReadCoils(address, quantity)
		if err == nil {
			log.Printf("读取线圈结果: %v", results)
		}
		return err

	case "read_discrete_inputs":
		address := uint16(params["address"].(float64))
		quantity := uint16(params["quantity"].(float64))
		results, err := handler.ReadDiscreteInputs(address, quantity)
		if err == nil {
			log.Printf("读取离散输入结果: %v", results)
		}
		return err

	case "read_holding_registers":
		address := uint16(params["address"].(float64))
		quantity := uint16(params["quantity"].(float64))
		results, err := handler.ReadHoldingRegisters(address, quantity)
		if err == nil {
			log.Printf("读取保持寄存器结果: %v", results)
		}
		return err

	case "read_input_registers":
		address := uint16(params["address"].(float64))
		quantity := uint16(params["quantity"].(float64))
		results, err := handler.ReadInputRegisters(address, quantity)
		if err == nil {
			log.Printf("读取输入寄存器结果: %v", results)
		}
		return err

	case "write_single_coil":
		address := uint16(params["address"].(float64))
		value := params["value"].(bool)
		var coilValue uint16
		if value {
			coilValue = 0xFF00
		} else {
			coilValue = 0x0000
		}
		_, err := handler.WriteSingleCoil(address, coilValue)
		return err

	case "write_single_register":
		address := uint16(params["address"].(float64))
		value := uint16(params["value"].(float64))
		_, err := handler.WriteSingleRegister(address, value)
		return err

	case "write_multiple_coils":
		address := uint16(params["address"].(float64))
		values := params["values"].([]interface{})
		boolValues := make([]bool, len(values))
		for i, v := range values {
			boolValues[i] = v.(bool)
		}

		// 将bool数组转换为字节数组
		quantity := uint16(len(boolValues))
		byteCount := (quantity + 7) / 8
		coilBytes := make([]byte, byteCount)

		for i, value := range boolValues {
			if value {
				byteIndex := i / 8
				bitIndex := i % 8
				coilBytes[byteIndex] |= 1 << bitIndex
			}
		}

		_, err := handler.WriteMultipleCoils(address, quantity, coilBytes)
		return err

	case "write_multiple_registers":
		address := uint16(params["address"].(float64))
		values := params["values"].([]interface{})
		uint16Values := make([]uint16, len(values))
		for i, v := range values {
			uint16Values[i] = uint16(v.(float64))
		}

		// 将uint16数组转换为字节数组
		quantity := uint16(len(uint16Values))
		regBytes := make([]byte, quantity*2)

		for i, value := range uint16Values {
			regBytes[i*2] = byte(value >> 8)     // 高字节
			regBytes[i*2+1] = byte(value & 0xFF) // 低字节
		}

		_, err := handler.WriteMultipleRegisters(address, quantity, regBytes)
		return err

	default:
		return fmt.Errorf("不支持的Modbus命令: %s", command)
	}
}

// IsConnected 检查设备是否已连接
func (a *ModbusAdapter) IsConnected(deviceID string) bool {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	_, exists := a.handlers[deviceID]
	return exists
}

// GetStatus 获取设备状态
func (a *ModbusAdapter) GetStatus(deviceID string) (string, error) {
	if a.IsConnected(deviceID) {
		return DeviceStatusOnline, nil
	}
	return DeviceStatusOffline, ErrDeviceNotFound
}

// GetDeviceInfo 获取设备信息
func (a *ModbusAdapter) GetDeviceInfo(deviceID string) (*DeviceStatus, error) {
	device, exists := a.GetDevice(deviceID)
	if !exists {
		return nil, ErrDeviceNotFound
	}

	status := &DeviceStatus{
		DeviceID: deviceID,
		LastSeen: device.LastSeen,
	}

	if a.IsConnected(deviceID) {
		status.Status = DeviceStatusOnline
	} else {
		status.Status = DeviceStatusOffline
	}

	return status, nil
}

// HealthCheck 健康检查
func (a *ModbusAdapter) HealthCheck() error {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	// 对每个连接的设备进行简单的读取测试
	errorCount := 0
	for deviceID, handler := range a.handlers {
		_, err := handler.ReadCoils(0, 1)
		if err != nil {
			errorCount++
			log.Printf("Modbus设备 %s 健康检查失败: %v", deviceID, err)
		}
	}

	if errorCount > 0 {
		return fmt.Errorf("有 %d 个Modbus设备健康检查失败", errorCount)
	}

	return nil
}

// Close 关闭适配器
func (a *ModbusAdapter) Close() error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	for deviceID, handler := range a.handlers {
		if closer, ok := handler.(interface{ Close() error }); ok {
			closer.Close()
		}
		log.Printf("Modbus设备 %s 连接已关闭", deviceID)
	}

	a.handlers = make(map[string]modbus.Client)
	return a.BaseAdapter.Close()
}

// updateDeviceStatus 更新设备状态
func (a *ModbusAdapter) updateDeviceStatus(deviceID string, status string, responseTime int64, err error) {
	if err != nil {
		log.Printf("Modbus设备 %s 状态更新: %s, 响应时间: %dms, 错误: %v",
			deviceID, status, responseTime, err)
	} else {
		log.Printf("Modbus设备 %s 状态更新: %s, 响应时间: %dms",
			deviceID, status, responseTime)
	}
}
