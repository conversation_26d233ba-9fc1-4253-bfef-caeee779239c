package gb28181

import (
	"time"
)

// Device 表示GB28181设备
type Device struct {
	DeviceID     string    `json:"device_id"`
	Name         string    `json:"name"`
	Manufacturer string    `json:"manufacturer"`
	Model        string    `json:"model"`
	IP           string    `json:"ip"`
	Port         int       `json:"port"`
	Status       string    `json:"status"` // online, offline
	RegisterTime time.Time `json:"register_time"`
	KeepaliveAt  time.Time `json:"keepalive_at"`
	Channels     []Channel `json:"channels,omitempty"`
}

// Channel 表示GB28181设备通道
type Channel struct {
	DeviceID            string `json:"device_id"`
	ChannelID           string `json:"channel_id"`
	Name                string `json:"name"`
	Status              string `json:"status"` // online, offline
	ParentID            string `json:"parent_id"`
	Manufacturer        string `json:"manufacturer"`
	Model               string `json:"model"`
	Owner               string `json:"owner"`
	CivilCode           string `json:"civil_code"`
	Block               string `json:"block"`
	Address             string `json:"address"`
	Parental            int    `json:"parental"`
	SafetyWay           int    `json:"safety_way"`
	RegisterWay         int    `json:"register_way"`
	CertNum             string `json:"cert_num"`
	Certifiable         int    `json:"certifiable"`
	ErrCode             int    `json:"err_code"`
	EndTime             string `json:"end_time"`
	Secrecy             string `json:"secrecy"`
	IPAddress           string `json:"ip_address"`
	Port                int    `json:"port"`
	Password            string `json:"password"`
	PTZType             int    `json:"ptz_type"`
	PositionType        int    `json:"position_type"`
	RoomType            int    `json:"room_type"`
	UseType             int    `json:"use_type"`
	SupplyLightType     int    `json:"supply_light_type"`
	DirectionType       int    `json:"direction_type"`
	Resolution          string `json:"resolution"`
	BusinessGroupID     string `json:"business_group_id"`
	DownloadSpeed       string `json:"download_speed"`
	SVCSpaceSupportMode int    `json:"svc_space_support_mode"`
	SVCTimeSupportMode  int    `json:"svc_time_support_mode"`
}

// StreamInfo 表示视频流信息
type StreamInfo struct {
	StreamID  string    `json:"stream_id"`
	DeviceID  string    `json:"device_id"`
	ChannelID string    `json:"channel_id"`
	SSRC      string    `json:"ssrc"`
	MediaID   string    `json:"media_id"`
	MediaIP   string    `json:"media_ip"`
	MediaPort int       `json:"media_port"`
	FlvURL    string    `json:"flv_url"`
	HLSURL    string    `json:"hls_url"`
	RTMPURL   string    `json:"rtmp_url"`
	RTSPURL   string    `json:"rtsp_url"`
	Status    string    `json:"status"` // playing, stopped
	StartTime time.Time `json:"start_time"`
}

// PTZParams 表示云台控制参数
type PTZParams struct {
	Command         string `json:"command"`          // up, down, left, right, zoomin, zoomout, stop
	HorizontalSpeed int    `json:"horizontal_speed"` // 水平速度 1-255
	VerticalSpeed   int    `json:"vertical_speed"`   // 垂直速度 1-255
	ZoomSpeed       int    `json:"zoom_speed"`       // 缩放速度 1-255
}

// DeviceConfig 表示设备基本配置
type DeviceConfig struct {
	DeviceID          string `json:"device_id"`
	Name              string `json:"name"`
	SIPServerID       string `json:"sip_server_id"`
	SIPServerIP       string `json:"sip_server_ip"`
	SIPServerPort     string `json:"sip_server_port"`
	DomainName        string `json:"domain_name"`
	Expiration        string `json:"expiration"`
	Password          string `json:"password"`
	HeartBeatInterval int    `json:"heart_beat_interval"`
	HeartBeatCount    int    `json:"heart_beat_count"`
}

// DeviceStatus 表示设备状态信息
type DeviceStatus struct {
	DeviceID   string `json:"device_id"`
	Online     string `json:"online"` // ONLINE, OFFLINE
	Status     string `json:"status"` // OK, ERROR
	DeviceTime string `json:"device_time"`
	Encode     string `json:"encode"` // ON, OFF
	Record     string `json:"record"` // ON, OFF
	Reason     string `json:"reason"`
}

// ApiResponse 表示API响应的通用结构
type ApiResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// PlayResponse 表示开始播放的响应
type PlayResponse struct {
	StreamID  string    `json:"stream_id"`
	SSRC      string    `json:"ssrc"`
	MediaID   string    `json:"media_id"`
	FlvURL    string    `json:"flv_url"`
	HLSURL    string    `json:"hls_url"`
	RTMPURL   string    `json:"rtmp_url"`
	RTSPURL   string    `json:"rtsp_url"`
	StartTime time.Time `json:"start_time"`
}

// DeviceListResponse 表示设备列表响应
type DeviceListResponse struct {
	Code int      `json:"code"`
	Msg  string   `json:"msg"`
	Data []Device `json:"data"`
}

// ChannelListResponse 表示通道列表响应
type ChannelListResponse struct {
	Code int       `json:"code"`
	Msg  string    `json:"msg"`
	Data []Channel `json:"data"`
}
