package gb28181

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Client GB28181服务客户端
type Client struct {
	baseURL    string
	httpClient *http.Client
}

// NewClient 创建新的GB28181客户端
func NewClient(baseURL string) *Client {
	return &Client{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetDevices 获取设备列表
func (c *Client) GetDevices() ([]Device, error) {
	url := fmt.Sprintf("%s/api/device/list", c.baseURL)

	resp, err := c.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("请求设备列表失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var response DeviceListResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if response.Code != 0 {
		return nil, fmt.Errorf("API错误: %s", response.Msg)
	}

	return response.Data, nil
}

// GetDeviceChannels 获取设备通道列表
func (c *Client) GetDeviceChannels(deviceID string) ([]Channel, error) {
	url := fmt.Sprintf("%s/api/channel/list/%s", c.baseURL, deviceID)

	resp, err := c.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("请求设备通道失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var response ChannelListResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if response.Code != 0 {
		return nil, fmt.Errorf("API错误: %s", response.Msg)
	}

	return response.Data, nil
}

// StartStream 开始播放视频流
func (c *Client) StartStream(deviceID, channelID string) (*PlayResponse, error) {
	url := fmt.Sprintf("%s/api/play/start/%s/%s", c.baseURL, deviceID, channelID)

	resp, err := c.httpClient.Post(url, "application/json", nil)
	if err != nil {
		return nil, fmt.Errorf("启动视频流失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var response struct {
		Code int          `json:"code"`
		Msg  string       `json:"msg"`
		Data PlayResponse `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if response.Code != 0 {
		return nil, fmt.Errorf("API错误: %s", response.Msg)
	}

	return &response.Data, nil
}

// StopStream 停止视频流
func (c *Client) StopStream(streamID string) error {
	url := fmt.Sprintf("%s/api/play/stop/%s", c.baseURL, streamID)

	req, err := http.NewRequest(http.MethodDelete, url, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("停止视频流失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	var response ApiResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if response.Code != 0 {
		return fmt.Errorf("API错误: %s", response.Msg)
	}

	return nil
}

// PTZControl 云台控制
func (c *Client) PTZControl(deviceID, channelID string, params PTZParams) error {
	url := fmt.Sprintf("%s/api/control/ptz", c.baseURL)

	reqBody := map[string]interface{}{
		"device_id":        deviceID,
		"channel_id":       channelID,
		"command":          params.Command,
		"horizontal_speed": params.HorizontalSpeed,
		"vertical_speed":   params.VerticalSpeed,
		"zoom_speed":       params.ZoomSpeed,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("序列化请求失败: %w", err)
	}

	resp, err := c.httpClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("云台控制失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	var response ApiResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if response.Code != 0 {
		return fmt.Errorf("API错误: %s", response.Msg)
	}

	return nil
}

// GetDeviceStatus 获取设备状态
func (c *Client) GetDeviceStatus(deviceID string) (*DeviceStatus, error) {
	url := fmt.Sprintf("%s/api/device/status/%s", c.baseURL, deviceID)

	resp, err := c.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("请求设备状态失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var response struct {
		Code int          `json:"code"`
		Msg  string       `json:"msg"`
		Data DeviceStatus `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if response.Code != 0 {
		return nil, fmt.Errorf("API错误: %s", response.Msg)
	}

	return &response.Data, nil
}

// SetDeviceConfig 设置设备基本配置
func (c *Client) SetDeviceConfig(deviceID string, config DeviceConfig) error {
	url := fmt.Sprintf("%s/api/device/config/basic", c.baseURL)

	reqBody := map[string]interface{}{
		"device_id": deviceID,
		"config":    config,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("序列化请求失败: %w", err)
	}

	resp, err := c.httpClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("设置设备配置失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	var response ApiResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if response.Code != 0 {
		return fmt.Errorf("API错误: %s", response.Msg)
	}

	return nil
}

// GetDeviceConfig 获取设备基本配置
func (c *Client) GetDeviceConfig(deviceID string) (*DeviceConfig, error) {
	url := fmt.Sprintf("%s/api/device/config/basic/%s", c.baseURL, deviceID)

	resp, err := c.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("请求设备配置失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var response struct {
		Code int          `json:"code"`
		Msg  string       `json:"msg"`
		Data DeviceConfig `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if response.Code != 0 {
		return nil, fmt.Errorf("API错误: %s", response.Msg)
	}

	return &response.Data, nil
}

// Ping 检测GB28181服务是否可用
func (c *Client) Ping() error {
	url := fmt.Sprintf("%s/api/health", c.baseURL)

	resp, err := c.httpClient.Get(url)
	if err != nil {
		return fmt.Errorf("连接GB28181服务失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("GB28181服务返回错误状态码: %d", resp.StatusCode)
	}

	return nil
}
