package gb28181

import (
	"fmt"
	"log"
	"time"

	"ecp/internal/pkg/database"

	"gorm.io/gorm"
)

// Service GB28181业务服务
type Service struct {
	client *Client
	db     *gorm.DB
}

// NewService 创建新的GB28181服务实例
func NewService(baseURL string, db *gorm.DB) *Service {
	return &Service{
		client: NewClient(baseURL),
		db:     db,
	}
}

// GetBaseURL 获取GB28181服务的基础URL
func (s *Service) GetBaseURL() string {
	return s.client.baseURL
}

// SyncDevicesToVideoSource 同步GB28181设备到视频源表
func (s *Service) SyncDevicesToVideoSource() error {
	// 获取GB28181设备列表
	devices, err := s.client.GetDevices()
	if err != nil {
		return fmt.Errorf("获取GB28181设备列表失败: %w", err)
	}

	for _, device := range devices {
		// 为每个设备获取通道列表
		channels, err := s.client.GetDeviceChannels(device.DeviceID)
		if err != nil {
			log.Printf("获取设备 %s 通道列表失败: %v", device.DeviceID, err)
			continue
		}

		// 为每个通道创建或更新视频源记录
		for _, channel := range channels {
			if err := s.syncChannelToVideo(device, channel); err != nil {
				log.Printf("同步通道 %s 到视频源失败: %v", channel.ChannelID, err)
			}
		}
	}

	return nil
}

// syncChannelToVideo 同步单个通道到视频源
func (s *Service) syncChannelToVideo(device Device, channel Channel) error {
	var video database.Video

	// 查找是否已存在该GB28181通道的视频源记录
	err := s.db.Where("protocol = ? AND camera_id = ?", "gb28181", channel.ChannelID).First(&video).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		return fmt.Errorf("查询视频源记录失败: %w", err)
	}

	// 准备视频源数据
	videoData := database.Video{
		Name:        channel.Name,
		Protocol:    "gb28181",
		CameraID:    channel.ChannelID,
		Description: fmt.Sprintf("GB28181设备: %s, 通道: %s", device.Name, channel.Name),
		StreamType:  "自动生成",
		CameraIP:    device.IP,
		Username:    "", // GB28181不需要用户名密码
		Password:    "",
		Brand:       device.Manufacturer,
		StreamMode:  "主码流", // 默认主码流
		URL:         fmt.Sprintf("gb28181://%s", channel.ChannelID),
		Port:        device.Port,
		Status:      mapDeviceStatus(device.Status),
		UpdatedAt:   time.Now(),
	}

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		videoData.CreatedAt = time.Now()
		if err := s.db.Create(&videoData).Error; err != nil {
			return fmt.Errorf("创建视频源记录失败: %w", err)
		}
		log.Printf("创建GB28181视频源: %s (设备ID: %s)", channel.Name, channel.ChannelID)
	} else {
		// 更新现有记录
		if err := s.db.Model(&video).Updates(videoData).Error; err != nil {
			return fmt.Errorf("更新视频源记录失败: %w", err)
		}
		log.Printf("更新GB28181视频源: %s (设备ID: %s)", channel.Name, channel.ChannelID)
	}

	return nil
}

// mapDeviceStatus 映射设备状态
func mapDeviceStatus(gb28181Status string) string {
	switch gb28181Status {
	case "online":
		return "online"
	case "offline":
		return "offline"
	default:
		return "offline"
	}
}

// GetDevices 获取GB28181设备列表
func (s *Service) GetDevices() ([]Device, error) {
	return s.client.GetDevices()
}

// GetDeviceChannels 获取设备通道列表
func (s *Service) GetDeviceChannels(deviceID string) ([]Channel, error) {
	return s.client.GetDeviceChannels(deviceID)
}

// StartStreamForVideo 为视频源启动GB28181流
func (s *Service) StartStreamForVideo(video *database.Video) (*StreamInfo, error) {
	if video.Protocol != "gb28181" {
		return nil, fmt.Errorf("视频源协议不是GB28181: %s", video.Protocol)
	}

	if video.CameraID == "" {
		return nil, fmt.Errorf("GB28181视频源缺少设备ID")
	}

	// 调用GB28181服务启动流
	playResp, err := s.client.StartStream(video.CameraID, video.CameraID)
	if err != nil {
		return nil, fmt.Errorf("启动GB28181流失败: %w", err)
	}

	// 转换为内部StreamInfo格式
	streamInfo := &StreamInfo{
		StreamID:  playResp.StreamID,
		DeviceID:  video.CameraID,
		ChannelID: video.CameraID,
		SSRC:      playResp.SSRC,
		MediaID:   playResp.MediaID,
		FlvURL:    playResp.FlvURL,
		HLSURL:    playResp.HLSURL,
		RTMPURL:   playResp.RTMPURL,
		RTSPURL:   playResp.RTSPURL,
		Status:    "playing",
		StartTime: playResp.StartTime,
	}

	return streamInfo, nil
}

// StopStreamForVideo 停止视频源的GB28181流
func (s *Service) StopStreamForVideo(streamID string) error {
	return s.client.StopStream(streamID)
}

// PTZControl 云台控制
func (s *Service) PTZControl(video *database.Video, command string, params PTZParams) error {
	if video.Protocol != "gb28181" {
		return fmt.Errorf("视频源协议不是GB28181: %s", video.Protocol)
	}

	if video.CameraID == "" {
		return fmt.Errorf("GB28181视频源缺少设备ID")
	}

	params.Command = command
	return s.client.PTZControl(video.CameraID, video.CameraID, params)
}

// GetDeviceStatus 获取设备状态
func (s *Service) GetDeviceStatus(deviceID string) (*DeviceStatus, error) {
	return s.client.GetDeviceStatus(deviceID)
}

// SyncDeviceStatus 同步设备状态到视频源表
func (s *Service) SyncDeviceStatus() error {
	// 获取所有GB28181协议的视频源
	var videos []database.Video
	if err := s.db.Where("protocol = ?", "gb28181").Find(&videos).Error; err != nil {
		return fmt.Errorf("查询GB28181视频源失败: %w", err)
	}

	for _, video := range videos {
		if video.CameraID == "" {
			continue
		}

		// 获取设备状态
		status, err := s.client.GetDeviceStatus(video.CameraID)
		if err != nil {
			log.Printf("获取设备 %s 状态失败: %v", video.CameraID, err)
			continue
		}

		// 更新视频源状态
		newStatus := mapGBStatusToVideoStatus(status.Online)
		if newStatus != video.Status {
			if err := s.db.Model(&video).Update("status", newStatus).Error; err != nil {
				log.Printf("更新视频源 %d 状态失败: %v", video.ID, err)
			} else {
				log.Printf("更新视频源 %s 状态: %s -> %s", video.Name, video.Status, newStatus)
			}
		}
	}

	return nil
}

// mapGBStatusToVideoStatus 映射GB28181状态到视频源状态
func mapGBStatusToVideoStatus(gbStatus string) string {
	switch gbStatus {
	case "ONLINE":
		return "online"
	case "OFFLINE":
		return "offline"
	default:
		return "offline"
	}
}

// IsHealthy 检查GB28181服务是否健康
func (s *Service) IsHealthy() bool {
	return s.client.Ping() == nil
}

// GetRealStreamURL 获取真实的流地址
func (s *Service) GetRealStreamURL(video *database.Video) (string, error) {
	if video.Protocol != "gb28181" {
		return "", fmt.Errorf("视频源协议不是GB28181")
	}

	// 启动流并获取实际的流地址
	streamInfo, err := s.StartStreamForVideo(video)
	if err != nil {
		return "", err
	}

	// 根据需要返回不同格式的流地址
	// 这里默认返回RTSP地址，因为大多数播放器支持
	if streamInfo.RTSPURL != "" {
		return streamInfo.RTSPURL, nil
	} else if streamInfo.FlvURL != "" {
		return streamInfo.FlvURL, nil
	} else if streamInfo.HLSURL != "" {
		return streamInfo.HLSURL, nil
	}

	return "", fmt.Errorf("没有可用的流地址")
}
