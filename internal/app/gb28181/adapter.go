package gb28181

import (
	"ecp/internal/pkg/database"
)

// Adapter GB28181适配器接口
type Adapter interface {
	// 设备管理
	GetDevices() ([]Device, error)
	GetDeviceChannels(deviceID string) ([]Channel, error)
	SyncDevicesToVideoSource() error

	// 视频流管理
	StartStreamForVideo(video *database.Video) (*StreamInfo, error)
	StopStreamForVideo(streamID string) error
	GetRealStreamURL(video *database.Video) (string, error)

	// 设备控制
	PTZControl(video *database.Video, command string, params PTZParams) error

	// 状态管理
	GetDeviceStatus(deviceID string) (*DeviceStatus, error)
	SyncDeviceStatus() error
	IsHealthy() bool
}

// DefaultAdapter 默认适配器实现
type DefaultAdapter struct {
	service *Service
}

// NewAdapter 创建新的适配器实例
func NewAdapter(baseURL string, db *database.DB) Adapter {
	service := NewService(baseURL, db.DB)
	return &DefaultAdapter{
		service: service,
	}
}

// GetDevices 获取设备列表
func (a *DefaultAdapter) GetDevices() ([]Device, error) {
	return a.service.GetDevices()
}

// GetDeviceChannels 获取设备通道列表
func (a *DefaultAdapter) GetDeviceChannels(deviceID string) ([]Channel, error) {
	return a.service.GetDeviceChannels(deviceID)
}

// SyncDevicesToVideoSource 同步设备到视频源
func (a *DefaultAdapter) SyncDevicesToVideoSource() error {
	return a.service.SyncDevicesToVideoSource()
}

// StartStreamForVideo 为视频源启动流
func (a *DefaultAdapter) StartStreamForVideo(video *database.Video) (*StreamInfo, error) {
	return a.service.StartStreamForVideo(video)
}

// StopStreamForVideo 停止视频源流
func (a *DefaultAdapter) StopStreamForVideo(streamID string) error {
	return a.service.StopStreamForVideo(streamID)
}

// GetRealStreamURL 获取真实流地址
func (a *DefaultAdapter) GetRealStreamURL(video *database.Video) (string, error) {
	return a.service.GetRealStreamURL(video)
}

// PTZControl 云台控制
func (a *DefaultAdapter) PTZControl(video *database.Video, command string, params PTZParams) error {
	return a.service.PTZControl(video, command, params)
}

// GetDeviceStatus 获取设备状态
func (a *DefaultAdapter) GetDeviceStatus(deviceID string) (*DeviceStatus, error) {
	return a.service.GetDeviceStatus(deviceID)
}

// SyncDeviceStatus 同步设备状态
func (a *DefaultAdapter) SyncDeviceStatus() error {
	return a.service.SyncDeviceStatus()
}

// IsHealthy 检查服务健康状态
func (a *DefaultAdapter) IsHealthy() bool {
	return a.service.IsHealthy()
}

// MockAdapter 模拟适配器（用于测试或GB28181服务不可用时）
type MockAdapter struct{}

// NewMockAdapter 创建模拟适配器
func NewMockAdapter() Adapter {
	return &MockAdapter{}
}

// GetDevices 模拟获取设备列表
func (m *MockAdapter) GetDevices() ([]Device, error) {
	return []Device{}, nil
}

// GetDeviceChannels 模拟获取设备通道
func (m *MockAdapter) GetDeviceChannels(deviceID string) ([]Channel, error) {
	return []Channel{}, nil
}

// SyncDevicesToVideoSource 模拟同步设备
func (m *MockAdapter) SyncDevicesToVideoSource() error {
	return nil
}

// StartStreamForVideo 模拟启动流
func (m *MockAdapter) StartStreamForVideo(video *database.Video) (*StreamInfo, error) {
	return nil, nil
}

// StopStreamForVideo 模拟停止流
func (m *MockAdapter) StopStreamForVideo(streamID string) error {
	return nil
}

// GetRealStreamURL 模拟获取流地址
func (m *MockAdapter) GetRealStreamURL(video *database.Video) (string, error) {
	return video.URL, nil
}

// PTZControl 模拟云台控制
func (m *MockAdapter) PTZControl(video *database.Video, command string, params PTZParams) error {
	return nil
}

// GetDeviceStatus 模拟获取设备状态
func (m *MockAdapter) GetDeviceStatus(deviceID string) (*DeviceStatus, error) {
	return &DeviceStatus{
		DeviceID: deviceID,
		Online:   "OFFLINE",
		Status:   "ERROR",
	}, nil
}

// SyncDeviceStatus 模拟同步设备状态
func (m *MockAdapter) SyncDeviceStatus() error {
	return nil
}

// IsHealthy 模拟健康检查
func (m *MockAdapter) IsHealthy() bool {
	return false
}
