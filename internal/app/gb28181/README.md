# GB28181适配层模块

这个模块负责与第三方GB28181服务的集成，提供GB28181设备管理和视频流控制功能。

## 功能

- GB28181设备注册和管理
- 设备状态监控和心跳检测
- 视频流开启和停止
- 云台控制（PTZ）
- 设备目录查询
- 与主项目video模块的数据同步

## 架构设计

```
ECP主项目
├── video模块 (现有功能)
├── GB28181适配层 (本模块)
│   ├── client.go      # GB28181服务客户端
│   ├── service.go     # 业务逻辑层
│   ├── models.go      # 数据模型
│   └── adapter.go     # 适配器接口
└── GB28181服务 (独立进程)
    └── third_party/gb28181
```

## 数据流

1. **设备注册流程**：GB28181设备 -> GB28181服务 -> 适配层 -> video模块
2. **视频流控制**：video模块 -> 适配层 -> GB28181服务 -> 设备
3. **状态同步**：GB28181服务 -> 适配层 -> video模块

## 配置

GB28181服务配置位于 `configs/gb28181.yml`，包括：
- SIP服务器配置
- 流媒体服务配置  
- 数据库配置
- 日志配置

## 使用示例

```go
// 创建GB28181服务实例
gb28181Service := gb28181.NewService("http://localhost:18080")

// 获取设备列表
devices, err := gb28181Service.GetDevices()

// 开启视频流
streamInfo, err := gb28181Service.StartStream("deviceId", "channelId")

// 云台控制
err := gb28181Service.PTZControl("deviceId", "channelId", "up", gb28181.PTZParams{
    Speed: 50,
})
``` 