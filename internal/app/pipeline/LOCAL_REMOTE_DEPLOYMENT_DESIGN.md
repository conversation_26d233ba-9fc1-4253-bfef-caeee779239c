# Pipeline本地/远程部署模式设计

## 设计理念

Pipeline的本地/远程部署模式**仅仅是针对pipeline综合配置的维护方式**，即配置是保存在本地文件系统还是通过HTTP API保存到远程Pipeline平台，**而不涉及pipeline服务的启动方式**。

## 核心概念

### 本地部署模式 (Local Mode)
- **配置存储**：综合管道配置保存在本地文件系统中
- **状态管理**：管道状态信息保存在本地文件中
- **不涉及**：实际的VideoPipe进程启动和管理
- **适用场景**：单机部署、离线环境、简化配置管理

### 远程部署模式 (Remote Mode)
- **配置存储**：综合管道配置通过HTTP API保存到远程Pipeline平台
- **状态管理**：管道状态通过API从远程平台获取
- **不涉及**：实际的VideoPipe进程启动和管理
- **适用场景**：分布式部署、集中化管理、多节点协调

## 架构设计

### 配置结构
```yaml
pipeline:
  # 部署模式：local(本地) 或 remote(远程)
  deployment_mode: "local"
  
  # 本地部署配置（仅配置存储，不涉及进程管理）
  local:
    config_storage_path: "./data/pipelines"    # 管道配置文件存储路径
    status_storage_path: "./data/pipeline-status" # 管道状态文件存储路径
  
  # 远程部署配置
  remote:
    api_endpoint: "http://localhost:8080/api/v1"
    api_key: "${PIPELINE_API_KEY}"
    timeout: 30s
    retry_count: 3
```

### 统一接口设计
```go
// PipelineClient 统一的管道客户端接口
type PipelineClient interface {
    CreateCompletePipeline(config *CompletePipelineConfig) error
    StartPipeline(pipelineID string) error
    StopPipeline(pipelineID string) error
    GetPipelineStatus(pipelineID string) (*PipelineStatus, error)
    DeletePipeline(pipelineID string) error
    ValidatePipelineConfig(config *CompletePipelineConfig) error
}

// 工厂函数根据配置自动选择实现
func CreatePipelineClient(config PipelineConfig) PipelineClient {
    switch config.DeploymentMode {
    case "local":
        return NewLocalPipelineClient(config)  // 本地文件系统实现
    case "remote":
        return NewPipelineClient(config)       // HTTP API实现
    default:
        return NewPipelineClient(config)       // 默认远程模式
    }
}
```

## 实现细节

### 本地模式实现
```go
type LocalPipelineClient struct {
    configStoragePath string // 管道配置文件存储路径
    statusStoragePath string // 管道状态文件存储路径
}

// 创建管道 - 保存配置到本地文件
func (c *LocalPipelineClient) CreateCompletePipeline(config *CompletePipelineConfig) error {
    configPath := filepath.Join(c.configStoragePath, fmt.Sprintf("%s.json", config.ID))
    configData, _ := json.MarshalIndent(config, "", "  ")
    return ioutil.WriteFile(configPath, configData, 0644)
}

// 启动管道 - 更新本地状态文件（不启动实际进程）
func (c *LocalPipelineClient) StartPipeline(pipelineID string) error {
    status := &PipelineStatus{
        PipelineID: pipelineID,
        State:      "running",
        StartTime:  time.Now().Format("2006-01-02T15:04:05Z"),
        // ... 其他状态信息
    }
    return c.saveStatus(pipelineID, status)
}
```

### 远程模式实现
```go
type pipelineClient struct {
    baseURL    string
    apiKey     string
    timeout    time.Duration
    retryCount int
    httpClient *http.Client
}

// 创建管道 - 通过HTTP API发送到远程平台
func (c *pipelineClient) CreateCompletePipeline(config *CompletePipelineConfig) error {
    return c.doRequest("POST", "/pipelines", config, nil)
}

// 启动管道 - 通过HTTP API调用远程平台
func (c *pipelineClient) StartPipeline(pipelineID string) error {
    url := fmt.Sprintf("/pipelines/%s/start", pipelineID)
    return c.doRequest("POST", url, nil, nil)
}
```

## 文件结构

### 本地模式文件布局
```
data/
├── pipelines/                    # 管道配置文件目录
│   ├── comprehensive_pipeline.json
│   ├── binding_1_pipeline.json
│   └── binding_2_pipeline.json
└── pipeline-status/              # 管道状态文件目录
    ├── comprehensive_pipeline_status.json
    ├── binding_1_pipeline_status.json
    └── binding_2_pipeline_status.json
```

### 配置文件示例
```json
{
  "id": "comprehensive_pipeline",
  "name": "综合管道",
  "description": "包含多个绑定关系的综合算法执行管道",
  "version": "1.0",
  "author": "ECP System",
  "globals": {
    "model_dir": "./vp_data/models/",
    "resize_ratio": 0.6
  },
  "nodes": [...],
  "connections": [...]
}
```

### 状态文件示例
```json
{
  "pipeline_id": "comprehensive_pipeline",
  "state": "running",
  "start_time": "2025-07-28T21:30:00Z",
  "uptime_seconds": 1800,
  "nodes": "[{\"id\":\"rtsp_src_0\",\"state\":\"running\"}]",
  "global_metrics": {
    "config_mode": "local",
    "total_fps": 24.8,
    "memory_usage_mb": 285.6
  },
  "errors": []
}
```

## 使用方式

### 代码使用
```go
// 1. 创建配置（本地和远程配置都包含）
pipelineConfig := pipeline.PipelineConfig{
    DeploymentMode: "local", // 或 "remote"
    TemplatePath:   "./configs/pipeline-template.yaml",
    NodeMapping:    pipeline.NodeMapping{...},
    Local: pipeline.LocalConfig{
        ConfigStoragePath: "./data/pipelines",
        StatusStoragePath: "./data/pipeline-status",
    },
    Remote: pipeline.RemoteConfig{
        APIEndpoint: "http://localhost:8080/api/v1",
        APIKey:      "your-api-key",
        Timeout:     30 * time.Second,
        RetryCount:  3,
    },
}

// 2. 自动选择客户端实现
client := pipeline.CreatePipelineClient(pipelineConfig)

// 3. 使用统一接口（代码完全一致）
err := client.CreateCompletePipeline(config)
err = client.StartPipeline("pipeline_id")
status, err := client.GetPipelineStatus("pipeline_id")
err = client.StopPipeline("pipeline_id")
```

### 配置切换
只需修改配置文件中的 `deployment_mode`：
```yaml
# 切换到本地模式
deployment_mode: "local"

# 切换到远程模式
deployment_mode: "remote"
```

## 优势对比

| 特性 | 本地模式 | 远程模式 |
|------|----------|----------|
| **配置存储** | 本地文件系统 | 远程API平台 |
| **网络依赖** | 无 | 需要 |
| **部署复杂度** | 简单 | 复杂 |
| **集中管理** | 不支持 | 支持 |
| **离线能力** | 完全支持 | 不支持 |
| **多节点协调** | 不支持 | 支持 |
| **配置同步** | 手动 | 自动 |

## 测试验证

### 本地模式测试
```bash
go run cmd/test-local-pipeline/main.go
```

**测试结果**：
```
=== 测试本地管道客户端 ===
📦 创建本地管道客户端...
✅ 配置验证通过
🏗️ 创建管道...
✅ 管道创建成功
🚀 启动管道...
✅ 管道启动成功
📊 获取管道状态...
✅ 管道状态: running
```

### 远程模式测试
```bash
go run cmd/test-comprehensive-pipeline/main.go api
```

## 重要说明

### ✅ 包含的功能
1. **配置管理**：创建、读取、更新、删除管道配置
2. **状态管理**：管道状态的存储和查询
3. **配置验证**：管道配置的完整性检查
4. **统一接口**：本地和远程模式使用相同的API

### ❌ 不包含的功能
1. **进程启动**：不涉及实际的VideoPipe进程启动
2. **进程管理**：不涉及进程监控、重启、终止
3. **资源管理**：不涉及CPU、内存、磁盘资源管理
4. **服务启动**：不涉及pipeline服务本身的启动方式

### 🎯 设计目标
- **配置管理的灵活性**：支持本地文件和远程API两种存储方式
- **部署模式的选择性**：根据环境需求选择合适的部署模式
- **接口的统一性**：无论哪种模式，使用方式完全一致
- **功能的专注性**：专注于配置管理，不涉及进程管理

这种设计清晰地分离了配置管理和进程管理的职责，使得系统更加模块化和可维护。
