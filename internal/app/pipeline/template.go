package pipeline

import (
	"fmt"
	"io/ioutil"
	"sync"

	"gopkg.in/yaml.v3"
)

// templateManager 模板管理器实现
type templateManager struct {
	templatePath string
	template     *PipelineTemplate
	mutex        sync.RWMutex
}

// NewTemplateManager 创建新的模板管理器
func NewTemplateManager(templatePath string) TemplateManager {
	return &templateManager{
		templatePath: templatePath,
	}
}

// LoadTemplate 加载固定模板
func (tm *templateManager) LoadTemplate() (*PipelineTemplate, error) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	data, err := ioutil.ReadFile(tm.templatePath)
	if err != nil {
		return nil, fmt.Errorf("读取模板文件失败: %v", err)
	}

	var template PipelineTemplate
	err = yaml.Unmarshal(data, &template)
	if err != nil {
		return nil, fmt.Errorf("解析模板文件失败: %v", err)
	}

	tm.template = &template
	return &template, nil
}

// ReloadTemplate 重新加载模板
func (tm *templateManager) ReloadTemplate() error {
	_, err := tm.LoadTemplate()
	return err
}

// MergeTemplate 合并模板和动态内容
func (tm *templateManager) MergeTemplate(template *PipelineTemplate, dynamic *DynamicContent) (*CompletePipelineConfig, error) {
	if template == nil {
		return nil, fmt.Errorf("模板不能为空")
	}
	if dynamic == nil {
		return nil, fmt.Errorf("动态内容不能为空")
	}

	// 创建完整配置
	config := &CompletePipelineConfig{
		ID:          dynamic.ID,
		Name:        dynamic.Name,
		Description: dynamic.Description,
		Version:     template.Version,
		Author:      template.Author,
		Globals:     make(map[string]interface{}),
		Nodes:       make([]*PipelineNode, 0),
		Connections: make([]*PipelineConnection, 0),
	}

	// 复制全局变量
	for k, v := range template.Globals {
		config.Globals[k] = v
	}

	// 添加固定节点
	for _, node := range template.FixedNodes {
		config.Nodes = append(config.Nodes, &PipelineNode{
			ID:     node.ID,
			Type:   node.Type,
			Params: copyParams(node.Params),
		})
	}

	// 添加动态节点
	if dynamic.VideoNode != nil {
		config.Nodes = append(config.Nodes, &PipelineNode{
			ID:     dynamic.VideoNode.ID,
			Type:   dynamic.VideoNode.Type,
			Params: copyParams(dynamic.VideoNode.Params),
		})
	}

	if dynamic.DetectorNode != nil {
		config.Nodes = append(config.Nodes, &PipelineNode{
			ID:     dynamic.DetectorNode.ID,
			Type:   dynamic.DetectorNode.Type,
			Params: copyParams(dynamic.DetectorNode.Params),
		})
	}

	// 添加连接关系
	for _, conn := range dynamic.Connections {
		config.Connections = append(config.Connections, &PipelineConnection{
			ID:   conn.ID,
			From: conn.From,
			To:   conn.To,
		})
	}

	return config, nil
}

// GetTemplate 获取当前模板（只读）
func (tm *templateManager) GetTemplate() *PipelineTemplate {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()
	return tm.template
}

// copyParams 深拷贝参数映射
func copyParams(params map[string]interface{}) map[string]interface{} {
	if params == nil {
		return make(map[string]interface{})
	}

	result := make(map[string]interface{})
	for k, v := range params {
		result[k] = v
	}
	return result
}

// ValidateTemplate 验证模板配置
func (tm *templateManager) ValidateTemplate(template *PipelineTemplate) error {
	if template == nil {
		return fmt.Errorf("模板不能为空")
	}

	if template.Version == "" {
		return fmt.Errorf("模板版本不能为空")
	}

	if template.Author == "" {
		return fmt.Errorf("模板作者不能为空")
	}

	if template.Globals == nil {
		return fmt.Errorf("全局变量不能为空")
	}

	// 验证必需的全局变量
	requiredGlobals := []string{"model_dir", "video_dir", "output_dir", "api_endpoint"}
	for _, key := range requiredGlobals {
		if _, exists := template.Globals[key]; !exists {
			return fmt.Errorf("缺少必需的全局变量: %s", key)
		}
	}

	// 验证固定节点
	if len(template.FixedNodes) == 0 {
		return fmt.Errorf("至少需要一个固定节点")
	}

	// 验证节点ID唯一性
	nodeIDs := make(map[string]bool)
	for _, node := range template.FixedNodes {
		if node.ID == "" {
			return fmt.Errorf("节点ID不能为空")
		}
		if node.Type == "" {
			return fmt.Errorf("节点类型不能为空")
		}
		if nodeIDs[node.ID] {
			return fmt.Errorf("节点ID重复: %s", node.ID)
		}
		nodeIDs[node.ID] = true
	}

	return nil
}

// GetFixedNodeIDs 获取固定节点ID列表
func (tm *templateManager) GetFixedNodeIDs() []string {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	if tm.template == nil {
		return []string{}
	}

	ids := make([]string, 0, len(tm.template.FixedNodes))
	for _, node := range tm.template.FixedNodes {
		ids = append(ids, node.ID)
	}
	return ids
}
