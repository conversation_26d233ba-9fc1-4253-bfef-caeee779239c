package pipeline

import (
	"time"
)

// CompletePipelineConfig 完整管道配置（对应API请求格式）
type CompletePipelineConfig struct {
	ID          string                 `json:"id" yaml:"id"`
	Name        string                 `json:"name" yaml:"name"`
	Description string                 `json:"description" yaml:"description"`
	Version     string                 `json:"version" yaml:"version"`
	Author      string                 `json:"author" yaml:"author"`
	Globals     map[string]interface{} `json:"globals" yaml:"globals"`
	Nodes       []*PipelineNode        `json:"nodes" yaml:"nodes"`
	Connections []*PipelineConnection  `json:"connections" yaml:"connections"`
}

// PipelineNode 管道节点
type PipelineNode struct {
	ID     string                 `json:"id" yaml:"id"`
	Type   string                 `json:"type" yaml:"type"`
	Params map[string]interface{} `json:"params" yaml:"params"`
}

// PipelineConnection 管道连接
type PipelineConnection struct {
	ID   int         `json:"id" yaml:"id"`
	From interface{} `json:"from" yaml:"from"` // string 或 []string
	To   string      `json:"to" yaml:"to"`
}

// PipelineTemplate 固定模板
type PipelineTemplate struct {
	Version    string                 `yaml:"version"`
	Author     string                 `yaml:"author"`
	Globals    map[string]interface{} `yaml:"globals"`
	FixedNodes []*PipelineNode        `yaml:"fixed_nodes"`
}

// DynamicContent 动态内容（单个绑定关系）
type DynamicContent struct {
	ID           string
	Name         string
	Description  string
	VideoNode    *PipelineNode
	DetectorNode *PipelineNode
	Connections  []*PipelineConnection
}

// ComprehensiveDynamicContent 综合动态内容（所有绑定关系）
type ComprehensiveDynamicContent struct {
	ID            string
	Name          string
	Description   string
	VideoNodes    []*PipelineNode
	DetectorNodes []*PipelineNode
	Connections   []*PipelineConnection
	BindingMatrix map[int64]map[int64]*VideoAlgorithmBinding // videoID -> algorithmID -> binding
}

// BindingAnalysis 绑定关系分析结果
type BindingAnalysis struct {
	UniqueVideos     map[int64]*Video
	UniqueAlgorithms map[int64]*Algorithm
	ActiveBindings   []*VideoAlgorithmBinding
	BindingMatrix    map[int64]map[int64]*VideoAlgorithmBinding
}

// PipelineStatus 管道状态（匹配前端期望的格式）
type PipelineStatus struct {
	PipelineID    string                 `json:"pipeline_id"`
	State         string                 `json:"state"` // running, stopped, error
	StartTime     string                 `json:"start_time"`
	UptimeSeconds int                    `json:"uptime_seconds"`
	Nodes         string                 `json:"nodes"` // JSON字符串格式的节点状态
	GlobalMetrics map[string]interface{} `json:"global_metrics"`
	Errors        []interface{}          `json:"errors"`
}

// NodeStatus 节点状态
type NodeStatus struct {
	ID      string `json:"id"`
	Type    string `json:"type"`
	Status  string `json:"status"` // running, stopped, error
	Message string `json:"message"`
}

// PipelineConfig 管道配置选项
type PipelineConfig struct {
	DeploymentMode string      `yaml:"deployment_mode"` // local 或 remote
	TemplatePath   string      `yaml:"template_path"`
	NodeMapping    NodeMapping `yaml:"node_mapping"`

	// 本地部署配置
	Local LocalConfig `yaml:"local"`

	// 远程部署配置
	Remote RemoteConfig `yaml:"remote"`
}

// LocalConfig 本地部署配置
// 专注于配置文件的本地存储，不涉及进程管理
type LocalConfig struct {
	ConfigStoragePath string `yaml:"config_storage_path"` // 管道配置文件存储路径
	StatusStoragePath string `yaml:"status_storage_path"` // 管道状态文件存储路径
}

// RemoteConfig 远程部署配置
type RemoteConfig struct {
	APIEndpoint string        `yaml:"api_endpoint"`
	APIKey      string        `yaml:"api_key"`
	Timeout     time.Duration `yaml:"timeout"`
	RetryCount  int           `yaml:"retry_count"`
}

// NodeMapping 节点类型映射配置
type NodeMapping struct {
	VideoSource map[string]string `yaml:"video_source"`
	Algorithms  map[string]string `yaml:"algorithms"`
}

// APIResponse API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// CreatePipelineRequest 创建管道请求
type CreatePipelineRequest struct {
	Config *CompletePipelineConfig `json:"config"`
}

// CreatePipelineResponse 创建管道响应
type CreatePipelineResponse struct {
	PipelineID string `json:"pipeline_id"`
	Status     string `json:"status"`
	Message    string `json:"message"`
}

// PipelineService 管道服务接口
type PipelineService interface {
	// === 综合管道管理接口 ===

	// 根据所有绑定关系创建综合管道
	CreateComprehensivePipeline(bindings []*VideoAlgorithmBinding) error

	// 更新综合管道配置
	UpdateComprehensivePipeline(bindings []*VideoAlgorithmBinding) error

	// 删除综合管道
	DeleteComprehensivePipeline() error

	// 启动/停止综合管道
	StartComprehensivePipeline() error
	StopComprehensivePipeline() error

	// 获取综合管道状态
	GetComprehensivePipelineStatus() (*PipelineStatus, error)

	// 获取综合管道配置
	GetComprehensivePipelineConfig() (*CompletePipelineConfig, error)

	// 全量绑定关系变更处理
	OnAllBindingsChanged(bindings []*VideoAlgorithmBinding) error
}

// PipelineBuilder 管道配置构建器接口
type PipelineBuilder interface {
	// 构建完整管道配置（单个绑定，向后兼容）
	BuildCompleteConfig(binding *VideoAlgorithmBinding) (*CompletePipelineConfig, error)

	// 构建视频源节点
	BuildVideoSourceNode(video *Video) (*PipelineNode, error)

	// 构建算法检测节点
	BuildDetectorNode(algorithm *Algorithm, binding *VideoAlgorithmBinding) (*PipelineNode, error)

	// 构建连接关系（单个绑定）
	BuildConnections(videoNodeID, detectorNodeID string, fixedNodeIDs []string) ([]*PipelineConnection, error)

	// === 新增：综合管道构建接口 ===

	// 构建综合管道配置
	BuildComprehensiveConfig(bindings []*VideoAlgorithmBinding) (*CompletePipelineConfig, error)

	// 分析绑定关系
	AnalyzeBindings(bindings []*VideoAlgorithmBinding) (*BindingAnalysis, error)

	// 构建综合连接关系
	BuildComprehensiveConnections(analysis *BindingAnalysis, fixedNodeIDs []string) ([]*PipelineConnection, error)
}

// TemplateManager 模板管理器接口
type TemplateManager interface {
	// 加载固定模板
	LoadTemplate() (*PipelineTemplate, error)

	// 合并模板和动态内容
	MergeTemplate(template *PipelineTemplate, dynamic *DynamicContent) (*CompletePipelineConfig, error)

	// 重新加载模板
	ReloadTemplate() error
}

// PipelineClient VideoPipe API客户端接口
type PipelineClient interface {
	// 创建完整管道
	CreateCompletePipeline(config *CompletePipelineConfig) error

	// 删除管道
	DeletePipeline(pipelineID string) error

	// 启动管道
	StartPipeline(pipelineID string) error

	// 停止管道
	StopPipeline(pipelineID string) error

	// 获取管道状态
	GetPipelineStatus(pipelineID string) (*PipelineStatus, error)

	// 获取管道配置
	GetPipelineConfig(pipelineID string) (*CompletePipelineConfig, error)

	// 验证管道配置
	ValidatePipelineConfig(config *CompletePipelineConfig) error
}

// DataMapper 数据映射器接口
type DataMapper interface {
	// 映射视频源到源节点
	MapVideoToSourceNode(video *Video) (*PipelineNode, error)

	// 映射算法到检测节点
	MapAlgorithmToDetectorNode(algorithm *Algorithm, binding *VideoAlgorithmBinding) (*PipelineNode, error)

	// 获取视频源节点类型
	GetVideoSourceNodeType(protocol string, videoType int) string

	// 获取算法节点类型
	GetAlgorithmNodeType(algorithmName string) string

	// 验证视频源节点配置
	ValidateVideoNode(node *PipelineNode) error

	// 验证检测器节点配置
	ValidateDetectorNode(node *PipelineNode) error
}

// 引用外部类型（避免循环依赖）
type VideoAlgorithmBinding struct {
	ID              int64     `json:"id"`
	VideoID         int64     `json:"video_id"`
	AlgorithmID     int64     `json:"algorithm_id"`
	DetectionArea   string    `json:"detection_area"`
	AlertInterval   int       `json:"alert_interval"`
	AlertWindow     int       `json:"alert_window"`
	AlertThreshold  float64   `json:"alert_threshold"`
	VoiceContent    string    `json:"voice_content"`
	DangerLevel     string    `json:"danger_level"`
	ExtensionFields string    `json:"extension_fields"`
	Status          string    `json:"status"`
	Video           Video     `json:"video"`
	Algorithm       Algorithm `json:"algorithm"`
}

type Video struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Type        int    `json:"type"`
	Protocol    string `json:"protocol"`
	CameraID    string `json:"camera_id"`
	CameraType  string `json:"camera_type"`
	Description string `json:"description"`
	StreamType  string `json:"stream_type"`
	CameraIP    string `json:"camera_ip"`
	Username    string `json:"username"`
	Password    string `json:"password"`
	Brand       string `json:"brand"`
	StreamMode  string `json:"stream_mode"`
	URL         string `json:"url"`
	Port        int    `json:"port"`
	Status      string `json:"status"`
}

type Algorithm struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Version     string `json:"version"`
	Path        string `json:"path"`
	Status      string `json:"status"`
}

// ConvertFromDatabaseModel 从database包的模型转换为pipeline包的模型
func ConvertFromDatabaseModel(dbBinding interface{}) *VideoAlgorithmBinding {
	// 由于binding包使用的是database.VideoAlgorithmBinding
	// 我们需要进行字段映射转换

	// 这里使用类型断言，假设传入的是database.VideoAlgorithmBinding类型
	// 实际实现中可能需要使用反射或其他方式来处理

	// 为了简化，我们直接返回一个新的结构体
	// 在实际使用中，需要根据具体的database模型来实现字段映射
	return &VideoAlgorithmBinding{
		// 这里需要根据实际的database.VideoAlgorithmBinding结构来映射字段
		// 暂时返回空结构体，后续需要完善
	}
}
