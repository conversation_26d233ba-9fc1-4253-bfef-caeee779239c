package pipeline

import (
	"encoding/json"
	"fmt"
)

// dataMapper 数据映射器实现
type dataMapper struct {
	nodeMapping NodeMapping
}

// NewDataMapper 创建新的数据映射器
func NewDataMapper(nodeMapping NodeMapping) DataMapper {
	return &dataMapper{
		nodeMapping: nodeMapping,
	}
}

// MapVideoToSourceNode 映射视频源到源节点
func (m *dataMapper) MapVideoToSourceNode(video *Video) (*PipelineNode, error) {
	if video == nil {
		return nil, fmt.Errorf("视频源不能为空")
	}

	nodeType := m.GetVideoSourceNodeType(video.Protocol, video.Type)
	nodeID := fmt.Sprintf("video_src_%d", video.ID)

	params := map[string]interface{}{
		"channel_index": 0,
		"resize_ratio":  "${globals.resize_ratio}",
	}

	switch video.Type {
	case 1, 2: // 摄像头、视频流
		if video.Protocol == "rtsp" {
			params["rtsp_url"] = video.URL
		} else if video.Protocol == "gb28181" {
			params["device_id"] = video.CameraID
			params["channel_id"] = 1
			if video.CameraIP != "" {
				params["server_ip"] = video.CameraIP
			}
			if video.Port > 0 {
				params["server_port"] = video.Port
			}
		} else {
			// 其他协议默认使用URL
			params["stream_url"] = video.URL
		}

		// 添加认证信息
		if video.Username != "" {
			params["username"] = video.Username
		}
		if video.Password != "" {
			params["password"] = video.Password
		}

	case 3: // 视频文件
		params["file_path"] = video.URL
		params["loop"] = true // 文件循环播放

	default:
		return nil, fmt.Errorf("不支持的视频类型: %d", video.Type)
	}

	// 添加其他可选参数
	if video.Brand != "" {
		params["brand"] = video.Brand
	}
	if video.StreamMode != "" {
		params["stream_mode"] = video.StreamMode
	}

	return &PipelineNode{
		ID:     nodeID,
		Type:   nodeType,
		Params: params,
	}, nil
}

// MapAlgorithmToDetectorNode 映射算法到检测节点
func (m *dataMapper) MapAlgorithmToDetectorNode(algorithm *Algorithm, binding *VideoAlgorithmBinding) (*PipelineNode, error) {
	if algorithm == nil {
		return nil, fmt.Errorf("算法不能为空")
	}
	if binding == nil {
		return nil, fmt.Errorf("绑定关系不能为空")
	}

	nodeType := m.GetAlgorithmNodeType(algorithm.Name)
	nodeID := fmt.Sprintf("detector_%d", algorithm.ID)

	params := map[string]interface{}{
		"model_path":           algorithm.Path,
		"confidence_threshold": binding.AlertThreshold,
	}

	// 处理检测区域
	if binding.DetectionArea != "" {
		// 验证检测区域是否为有效的JSON
		var area interface{}
		if err := json.Unmarshal([]byte(binding.DetectionArea), &area); err == nil {
			params["detection_area"] = binding.DetectionArea
		} else {
			// 如果不是JSON，作为字符串处理
			params["detection_area"] = binding.DetectionArea
		}
	}

	// 处理告警相关参数
	if binding.AlertInterval > 0 {
		params["alert_interval"] = binding.AlertInterval
	}
	if binding.AlertWindow > 0 {
		params["alert_window"] = binding.AlertWindow
	}
	if binding.DangerLevel != "" {
		params["danger_level"] = binding.DangerLevel
	}
	if binding.VoiceContent != "" {
		params["voice_content"] = binding.VoiceContent
	}

	// 处理扩展字段
	if binding.ExtensionFields != "" {
		var extFields map[string]interface{}
		if err := json.Unmarshal([]byte(binding.ExtensionFields), &extFields); err == nil {
			for k, v := range extFields {
				// 避免覆盖已有的关键参数
				if _, exists := params[k]; !exists {
					params[k] = v
				}
			}
		}
	}

	// 添加算法版本信息
	if algorithm.Version != "" {
		params["algorithm_version"] = algorithm.Version
	}

	return &PipelineNode{
		ID:     nodeID,
		Type:   nodeType,
		Params: params,
	}, nil
}

// GetVideoSourceNodeType 获取视频源节点类型
func (m *dataMapper) GetVideoSourceNodeType(protocol string, videoType int) string {
	// 首先检查配置映射
	if nodeType, exists := m.nodeMapping.VideoSource[protocol]; exists {
		return nodeType
	}

	// 默认映射规则
	switch protocol {
	case "rtsp":
		return "vp_rtsp_src_node"
	case "gb28181":
		return "vp_gb28181_src_node"
	case "onvif":
		return "vp_onvif_src_node"
	default:
		if videoType == 3 {
			return "vp_file_src_node"
		}
		return "vp_rtsp_src_node" // 默认使用RTSP
	}
}

// GetAlgorithmNodeType 获取算法节点类型
func (m *dataMapper) GetAlgorithmNodeType(algorithmName string) string {
	// 首先检查配置映射
	if nodeType, exists := m.nodeMapping.Algorithms[algorithmName]; exists {
		return nodeType
	}

	// 根据算法名称进行模糊匹配
	algorithmTypeMap := map[string]string{
		"face_detection":     "vp_yunet_face_detector_node",
		"face_detect":        "vp_yunet_face_detector_node",
		"yunet":              "vp_yunet_face_detector_node",
		"object_detection":   "vp_yolo_detector_node",
		"object_detect":      "vp_yolo_detector_node",
		"yolo":               "vp_yolo_detector_node",
		"person_detection":   "vp_person_detector_node",
		"person_detect":      "vp_person_detector_node",
		"vehicle_detection":  "vp_vehicle_detector_node",
		"vehicle_detect":     "vp_vehicle_detector_node",
		"helmet_detection":   "vp_helmet_detector_node",
		"helmet_detect":      "vp_helmet_detector_node",
		"safety_detection":   "vp_safety_detector_node",
		"safety_detect":      "vp_safety_detector_node",
	}

	// 精确匹配
	if nodeType, exists := algorithmTypeMap[algorithmName]; exists {
		return nodeType
	}

	// 模糊匹配（包含关键词）
	for keyword, nodeType := range algorithmTypeMap {
		if contains(algorithmName, keyword) {
			return nodeType
		}
	}

	// 默认返回通用检测节点
	return "vp_generic_detector_node"
}

// ValidateVideoNode 验证视频源节点配置
func (m *dataMapper) ValidateVideoNode(node *PipelineNode) error {
	if node == nil {
		return fmt.Errorf("节点不能为空")
	}

	if node.ID == "" {
		return fmt.Errorf("节点ID不能为空")
	}

	if node.Type == "" {
		return fmt.Errorf("节点类型不能为空")
	}

	// 根据节点类型验证必需参数
	switch node.Type {
	case "vp_rtsp_src_node":
		if _, exists := node.Params["rtsp_url"]; !exists {
			return fmt.Errorf("RTSP节点缺少rtsp_url参数")
		}
	case "vp_file_src_node":
		if _, exists := node.Params["file_path"]; !exists {
			return fmt.Errorf("文件节点缺少file_path参数")
		}
	case "vp_gb28181_src_node":
		if _, exists := node.Params["device_id"]; !exists {
			return fmt.Errorf("GB28181节点缺少device_id参数")
		}
	}

	return nil
}

// ValidateDetectorNode 验证检测器节点配置
func (m *dataMapper) ValidateDetectorNode(node *PipelineNode) error {
	if node == nil {
		return fmt.Errorf("节点不能为空")
	}

	if node.ID == "" {
		return fmt.Errorf("节点ID不能为空")
	}

	if node.Type == "" {
		return fmt.Errorf("节点类型不能为空")
	}

	// 验证必需参数
	if _, exists := node.Params["model_path"]; !exists {
		return fmt.Errorf("检测器节点缺少model_path参数")
	}

	if _, exists := node.Params["confidence_threshold"]; !exists {
		return fmt.Errorf("检测器节点缺少confidence_threshold参数")
	}

	return nil
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    len(s) > len(substr) && 
		    (s[:len(substr)] == substr || 
		     s[len(s)-len(substr):] == substr ||
		     findSubstring(s, substr)))
}

// findSubstring 在字符串中查找子字符串
func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
