package pipeline

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// pipelineService 管道服务实现
type pipelineService struct {
	client                     PipelineClient
	builder                    PipelineBuilder
	config                     PipelineConfig
	comprehensivePipelineState *PipelineState
	mutex                      sync.RWMutex
}

// PipelineState 管道状态信息
type PipelineState struct {
	BindingID  int64     `json:"binding_id"`
	PipelineID string    `json:"pipeline_id"`
	Status     string    `json:"status"`
	LastError  string    `json:"last_error"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// NewPipelineService 创建新的管道服务
func NewPipelineService(client PipelineClient, builder PipelineBuilder, config PipelineConfig) PipelineService {
	return &pipelineService{
		client:  client,
		builder: builder,
		config:  config,
	}
}

// CreatePipelineClient 根据配置创建合适的管道客户端
func CreatePipelineClient(config PipelineConfig) PipelineClient {
	switch config.DeploymentMode {
	case "local":
		return NewLocalPipelineClient(config)
	case "remote":
		return NewPipelineClient(config)
	default:
		// 默认使用远程模式
		return NewPipelineClient(config)
	}
}

// === 新增：综合管道管理方法 ===

// CreateComprehensivePipeline 根据所有绑定关系创建综合管道
func (ps *pipelineService) CreateComprehensivePipeline(bindings []*VideoAlgorithmBinding) error {
	if len(bindings) == 0 {
		return fmt.Errorf("绑定关系列表不能为空")
	}

	log.Printf("开始创建综合管道，包含 %d 个绑定关系", len(bindings))

	// 1. 构建综合管道配置
	config, err := ps.builder.BuildComprehensiveConfig(bindings)
	if err != nil {
		ps.updateComprehensivePipelineState("", "error", fmt.Sprintf("构建配置失败: %v", err))
		return fmt.Errorf("构建综合管道配置失败: %v", err)
	}

	// 2. 验证配置
	if err := ps.client.ValidatePipelineConfig(config); err != nil {
		ps.updateComprehensivePipelineState(config.ID, "error", fmt.Sprintf("配置验证失败: %v", err))
		return fmt.Errorf("综合管道配置验证失败: %v", err)
	}

	// 3. 创建管道
	if err := ps.client.CreateCompletePipeline(config); err != nil {
		ps.updateComprehensivePipelineState(config.ID, "error", fmt.Sprintf("创建失败: %v", err))
		return fmt.Errorf("创建综合管道失败: %v", err)
	}

	// 4. 更新状态
	ps.updateComprehensivePipelineState(config.ID, "created", "")
	log.Printf("成功创建综合管道 %s", config.ID)

	return nil
}

// UpdateComprehensivePipeline 更新综合管道配置
func (ps *pipelineService) UpdateComprehensivePipeline(bindings []*VideoAlgorithmBinding) error {
	log.Printf("开始更新综合管道，包含 %d 个绑定关系", len(bindings))

	// 1. 停止当前综合管道
	if ps.comprehensivePipelineState != nil && ps.comprehensivePipelineState.Status == "running" {
		if err := ps.StopComprehensivePipeline(); err != nil {
			log.Printf("停止综合管道失败: %v", err)
		}
	}

	// 2. 删除旧管道
	if err := ps.DeleteComprehensivePipeline(); err != nil {
		log.Printf("删除旧综合管道失败: %v", err)
	}

	// 3. 创建新管道
	return ps.CreateComprehensivePipeline(bindings)
}

// DeleteComprehensivePipeline 删除综合管道
func (ps *pipelineService) DeleteComprehensivePipeline() error {
	log.Printf("开始删除综合管道")

	if ps.comprehensivePipelineState == nil {
		log.Printf("综合管道不存在")
		return nil
	}

	// 1. 停止管道
	if ps.comprehensivePipelineState.Status == "running" {
		if err := ps.client.StopPipeline(ps.comprehensivePipelineState.PipelineID); err != nil {
			log.Printf("停止综合管道失败: %v", err)
		}
	}

	// 2. 删除管道
	if err := ps.client.DeletePipeline(ps.comprehensivePipelineState.PipelineID); err != nil {
		ps.updateComprehensivePipelineState(ps.comprehensivePipelineState.PipelineID, "error", fmt.Sprintf("删除失败: %v", err))
		return fmt.Errorf("删除综合管道失败: %v", err)
	}

	// 3. 清理状态
	ps.clearComprehensivePipelineState()
	log.Printf("成功删除综合管道")

	return nil
}

// StartComprehensivePipeline 启动综合管道
func (ps *pipelineService) StartComprehensivePipeline() error {
	log.Printf("开始启动综合管道")

	if ps.comprehensivePipelineState == nil {
		return fmt.Errorf("综合管道不存在")
	}

	if ps.comprehensivePipelineState.Status == "running" {
		log.Printf("综合管道已经在运行中")
		return nil
	}

	if err := ps.client.StartPipeline(ps.comprehensivePipelineState.PipelineID); err != nil {
		ps.updateComprehensivePipelineState(ps.comprehensivePipelineState.PipelineID, "error", fmt.Sprintf("启动失败: %v", err))
		return fmt.Errorf("启动综合管道失败: %v", err)
	}

	ps.updateComprehensivePipelineState(ps.comprehensivePipelineState.PipelineID, "running", "")
	log.Printf("成功启动综合管道")

	return nil
}

// StopComprehensivePipeline 停止综合管道
func (ps *pipelineService) StopComprehensivePipeline() error {
	log.Printf("开始停止综合管道")

	if ps.comprehensivePipelineState == nil {
		return fmt.Errorf("综合管道不存在")
	}

	if ps.comprehensivePipelineState.Status == "stopped" {
		log.Printf("综合管道已经停止")
		return nil
	}

	if err := ps.client.StopPipeline(ps.comprehensivePipelineState.PipelineID); err != nil {
		ps.updateComprehensivePipelineState(ps.comprehensivePipelineState.PipelineID, "error", fmt.Sprintf("停止失败: %v", err))
		return fmt.Errorf("停止综合管道失败: %v", err)
	}

	ps.updateComprehensivePipelineState(ps.comprehensivePipelineState.PipelineID, "stopped", "")
	log.Printf("成功停止综合管道")

	return nil
}

// GetComprehensivePipelineStatus 获取综合管道状态
func (ps *pipelineService) GetComprehensivePipelineStatus() (*PipelineStatus, error) {
	if ps.comprehensivePipelineState == nil {
		// 返回一个表示未部署状态的默认状态，而不是错误
		return &PipelineStatus{
			PipelineID:    "comprehensive",
			State:         "not_deployed",
			StartTime:     "",
			UptimeSeconds: 0,
			Nodes:         "[]", // 空的节点列表
			GlobalMetrics: make(map[string]interface{}),
			Errors:        make([]interface{}, 0),
		}, nil
	}

	// 从API获取最新状态
	status, err := ps.client.GetPipelineStatus(ps.comprehensivePipelineState.PipelineID)
	if err != nil {
		// 如果API调用失败，返回本地状态信息
		return &PipelineStatus{
			PipelineID:    ps.comprehensivePipelineState.PipelineID,
			State:         ps.comprehensivePipelineState.Status,
			StartTime:     ps.comprehensivePipelineState.CreatedAt.Format("2006-01-02T15:04:05Z"),
			UptimeSeconds: 0,
			Nodes:         "[]",
			GlobalMetrics: make(map[string]interface{}),
			Errors:        []interface{}{ps.comprehensivePipelineState.LastError},
		}, nil
	}

	// 更新本地状态
	ps.updateComprehensivePipelineState(ps.comprehensivePipelineState.PipelineID, status.State, "")

	return status, nil
}

// GetComprehensivePipelineConfig 获取综合管道配置
func (ps *pipelineService) GetComprehensivePipelineConfig() (*CompletePipelineConfig, error) {
	if ps.comprehensivePipelineState == nil {
		return nil, fmt.Errorf("综合管道不存在")
	}

	// 根据部署模式选择不同的获取方式
	switch ps.config.DeploymentMode {
	case "local":
		// 本地模式：从配置文件读取
		return ps.getLocalPipelineConfig()
	case "remote":
		// 远程模式：通过API客户端获取
		return ps.getRemotePipelineConfig()
	default:
		return nil, fmt.Errorf("不支持的部署模式: %s", ps.config.DeploymentMode)
	}
}

// getLocalPipelineConfig 从本地文件获取管道配置
func (ps *pipelineService) getLocalPipelineConfig() (*CompletePipelineConfig, error) {
	configPath := filepath.Join(ps.config.Local.ConfigStoragePath, fmt.Sprintf("%s.json", ps.comprehensivePipelineState.PipelineID))

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("管道配置文件不存在: %s", ps.comprehensivePipelineState.PipelineID)
	}

	// 读取配置文件
	configData, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取管道配置文件失败: %v", err)
	}

	var config CompletePipelineConfig
	if err := json.Unmarshal(configData, &config); err != nil {
		return nil, fmt.Errorf("解析管道配置失败: %v", err)
	}

	return &config, nil
}

// getRemotePipelineConfig 通过API客户端获取管道配置
func (ps *pipelineService) getRemotePipelineConfig() (*CompletePipelineConfig, error) {
	// 检查客户端是否支持获取配置的方法
	if configClient, ok := ps.client.(interface {
		GetPipelineConfig(pipelineID string) (*CompletePipelineConfig, error)
	}); ok {
		// 通过API客户端获取配置
		config, err := configClient.GetPipelineConfig(ps.comprehensivePipelineState.PipelineID)
		if err != nil {
			return nil, fmt.Errorf("从远程API获取管道配置失败: %v", err)
		}
		return config, nil
	} else {
		return nil, fmt.Errorf("当前客户端不支持获取管道配置")
	}
}

// OnAllBindingsChanged 全量绑定关系变更处理
func (ps *pipelineService) OnAllBindingsChanged(bindings []*VideoAlgorithmBinding) error {
	log.Printf("处理全量绑定关系变更事件，包含 %d 个绑定关系", len(bindings))

	// 过滤出激活的绑定关系
	activeBindings := make([]*VideoAlgorithmBinding, 0)
	for _, binding := range bindings {
		if binding != nil && binding.Status == "active" {
			activeBindings = append(activeBindings, binding)
		}
	}

	if len(activeBindings) == 0 {
		// 如果没有激活的绑定关系，删除综合管道
		return ps.DeleteComprehensivePipeline()
	} else {
		// 更新综合管道
		return ps.UpdateComprehensivePipeline(activeBindings)
	}
}

// updateComprehensivePipelineState 更新综合管道状态
func (ps *pipelineService) updateComprehensivePipelineState(pipelineID, status, errorMsg string) {
	ps.mutex.Lock()
	defer ps.mutex.Unlock()

	now := time.Now()
	if ps.comprehensivePipelineState != nil {
		ps.comprehensivePipelineState.Status = status
		ps.comprehensivePipelineState.LastError = errorMsg
		ps.comprehensivePipelineState.UpdatedAt = now
		if pipelineID != "" {
			ps.comprehensivePipelineState.PipelineID = pipelineID
		}
	} else {
		ps.comprehensivePipelineState = &PipelineState{
			BindingID:  -1, // 使用-1表示综合管道
			PipelineID: pipelineID,
			Status:     status,
			LastError:  errorMsg,
			CreatedAt:  now,
			UpdatedAt:  now,
		}
	}
}

// clearComprehensivePipelineState 清理综合管道状态
func (ps *pipelineService) clearComprehensivePipelineState() {
	ps.mutex.Lock()
	defer ps.mutex.Unlock()
	ps.comprehensivePipelineState = nil
}

// GetComprehensivePipelineState 获取综合管道状态（内部方法）
func (ps *pipelineService) GetComprehensivePipelineState() *PipelineState {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	if ps.comprehensivePipelineState == nil {
		return nil
	}
	return &PipelineState{
		BindingID:  ps.comprehensivePipelineState.BindingID,
		PipelineID: ps.comprehensivePipelineState.PipelineID,
		Status:     ps.comprehensivePipelineState.Status,
		LastError:  ps.comprehensivePipelineState.LastError,
		CreatedAt:  ps.comprehensivePipelineState.CreatedAt,
		UpdatedAt:  ps.comprehensivePipelineState.UpdatedAt,
	}
}
