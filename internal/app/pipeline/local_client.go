package pipeline

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"time"
)

// LocalPipelineClient 本地管道客户端实现
// 专注于配置的本地文件系统存储，不涉及实际的进程启动
type LocalPipelineClient struct {
	configStoragePath string // 管道配置文件存储路径
	statusStoragePath string // 管道状态文件存储路径
}

// NewLocalPipelineClient 创建本地管道客户端
// 专注于配置的本地文件系统存储
func NewLocalPipelineClient(config PipelineConfig) PipelineClient {
	client := &LocalPipelineClient{
		configStoragePath: config.Local.ConfigStoragePath,
		statusStoragePath: config.Local.StatusStoragePath,
	}

	// 确保存储目录存在
	client.ensureDirectories()

	return client
}

// ensureDirectories 确保必要的目录存在
func (c *LocalPipelineClient) ensureDirectories() {
	dirs := []string{
		c.configStoragePath,
		c.statusStoragePath,
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			fmt.Printf("创建目录失败 %s: %v\n", dir, err)
		}
	}
}

// CreateCompletePipeline 创建完整管道（保存配置文件）
func (c *LocalPipelineClient) CreateCompletePipeline(config *CompletePipelineConfig) error {
	pipelineID := config.ID
	return c.CreatePipeline(pipelineID, config)
}

// CreatePipeline 创建管道（保存配置文件）
func (c *LocalPipelineClient) CreatePipeline(pipelineID string, config *CompletePipelineConfig) error {
	configPath := filepath.Join(c.configStoragePath, fmt.Sprintf("%s.json", pipelineID))

	// 将配置序列化为JSON
	configData, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化管道配置失败: %v", err)
	}

	// 保存配置文件
	if err := ioutil.WriteFile(configPath, configData, 0644); err != nil {
		return fmt.Errorf("保存管道配置文件失败: %v", err)
	}

	// 创建初始状态文件，包含节点信息以便前端显示流程图
	status := &PipelineStatus{
		PipelineID:    pipelineID,
		State:         "created",
		StartTime:     time.Now().Format("2006-01-02T15:04:05Z"),
		UptimeSeconds: 0,
		Nodes:         c.generateNodesFromConfig(config), // 从配置文件生成节点信息
		GlobalMetrics: map[string]interface{}{
			"config_mode": "local",
			"connections": c.generateConnectionsFromConfig(config), // 添加连接信息
		},
		Errors: make([]interface{}, 0),
	}

	return c.saveStatus(pipelineID, status)
}

// StartPipeline 启动管道
// 在本地模式下，这只是更新配置状态，不涉及实际的进程启动
func (c *LocalPipelineClient) StartPipeline(pipelineID string) error {
	configPath := filepath.Join(c.configStoragePath, fmt.Sprintf("%s.json", pipelineID))

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("管道配置文件不存在: %s", pipelineID)
	}

	// 读取配置文件
	configData, err := ioutil.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取管道配置文件失败: %v", err)
	}

	var config CompletePipelineConfig
	if err := json.Unmarshal(configData, &config); err != nil {
		return fmt.Errorf("解析管道配置失败: %v", err)
	}

	// 更新状态为运行中（仅配置层面，不启动实际进程）
	// 从配置文件生成节点信息，并将状态更新为运行中
	nodes := c.generateRunningNodesFromConfig(&config)

	status := &PipelineStatus{
		PipelineID:    pipelineID,
		State:         "running",
		StartTime:     time.Now().Format("2006-01-02T15:04:05Z"),
		UptimeSeconds: 0,
		Nodes:         nodes,
		GlobalMetrics: map[string]interface{}{
			"config_mode":       "local",
			"connections":       c.generateConnectionsFromConfig(&config), // 添加连接信息
			"total_fps":         24.8,
			"memory_usage_mb":   285.6,
			"cpu_usage_percent": 18.5,
		},
		Errors: make([]interface{}, 0),
	}

	return c.saveStatus(pipelineID, status)
}

// StopPipeline 停止管道
// 在本地模式下，这只是更新配置状态，不涉及实际的进程停止
func (c *LocalPipelineClient) StopPipeline(pipelineID string) error {
	// 更新状态为停止（仅配置层面，不停止实际进程）
	status := &PipelineStatus{
		PipelineID:    pipelineID,
		State:         "stopped",
		StartTime:     "",
		UptimeSeconds: 0,
		Nodes:         "[]",
		GlobalMetrics: map[string]interface{}{
			"config_mode": "local",
		},
		Errors: make([]interface{}, 0),
	}

	return c.saveStatus(pipelineID, status)
}

// GetPipelineStatus 获取管道状态
func (c *LocalPipelineClient) GetPipelineStatus(pipelineID string) (*PipelineStatus, error) {
	statusPath := filepath.Join(c.statusStoragePath, fmt.Sprintf("%s_status.json", pipelineID))

	// 检查状态文件是否存在
	if _, err := os.Stat(statusPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("管道状态文件不存在: %s", pipelineID)
	}

	// 读取状态文件
	statusData, err := ioutil.ReadFile(statusPath)
	if err != nil {
		return nil, fmt.Errorf("读取管道状态文件失败: %v", err)
	}

	var status PipelineStatus
	if err := json.Unmarshal(statusData, &status); err != nil {
		return nil, fmt.Errorf("解析管道状态失败: %v", err)
	}

	// 如果管道正在运行，更新运行时间
	if status.State == "running" {
		startTime, err := time.Parse("2006-01-02T15:04:05Z", status.StartTime)
		if err == nil {
			status.UptimeSeconds = int(time.Since(startTime).Seconds())
		}
	}

	return &status, nil
}

// DeletePipeline 删除管道
func (c *LocalPipelineClient) DeletePipeline(pipelineID string) error {
	// 删除配置文件
	configPath := filepath.Join(c.configStoragePath, fmt.Sprintf("%s.json", pipelineID))
	if err := os.Remove(configPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("删除管道配置文件失败: %v", err)
	}

	// 删除状态文件
	statusPath := filepath.Join(c.statusStoragePath, fmt.Sprintf("%s_status.json", pipelineID))
	if err := os.Remove(statusPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("删除管道状态文件失败: %v", err)
	}

	return nil
}

// GetPipelineConfig 获取管道配置
func (c *LocalPipelineClient) GetPipelineConfig(pipelineID string) (*CompletePipelineConfig, error) {
	configPath := filepath.Join(c.configStoragePath, fmt.Sprintf("%s.json", pipelineID))

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("管道配置文件不存在: %s", pipelineID)
	}

	// 读取配置文件
	configData, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取管道配置文件失败: %v", err)
	}

	var config CompletePipelineConfig
	if err := json.Unmarshal(configData, &config); err != nil {
		return nil, fmt.Errorf("解析管道配置失败: %v", err)
	}

	return &config, nil
}

// ValidatePipelineConfig 验证管道配置
func (c *LocalPipelineClient) ValidatePipelineConfig(config *CompletePipelineConfig) error {
	if config == nil {
		return fmt.Errorf("管道配置不能为空")
	}

	if config.ID == "" {
		return fmt.Errorf("管道ID不能为空")
	}

	if len(config.Nodes) == 0 {
		return fmt.Errorf("管道必须包含至少一个节点")
	}

	// 验证节点ID唯一性
	nodeIDs := make(map[string]bool)
	for _, node := range config.Nodes {
		if node.ID == "" {
			return fmt.Errorf("节点ID不能为空")
		}
		if nodeIDs[node.ID] {
			return fmt.Errorf("节点ID重复: %s", node.ID)
		}
		nodeIDs[node.ID] = true
	}

	return nil
}

// saveStatus 保存管道状态
func (c *LocalPipelineClient) saveStatus(pipelineID string, status *PipelineStatus) error {
	statusPath := filepath.Join(c.statusStoragePath, fmt.Sprintf("%s_status.json", pipelineID))

	statusData, err := json.MarshalIndent(status, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化管道状态失败: %v", err)
	}

	if err := ioutil.WriteFile(statusPath, statusData, 0644); err != nil {
		return fmt.Errorf("保存管道状态文件失败: %v", err)
	}

	return nil
}

// generateNodesFromConfig 从配置文件生成节点数据
func (c *LocalPipelineClient) generateNodesFromConfig(config *CompletePipelineConfig) string {
	if config == nil || len(config.Nodes) == 0 {
		// 如果没有配置，返回默认模拟数据
		return c.generateMockNodes()
	}

	nodes := make([]map[string]interface{}, 0, len(config.Nodes))

	for _, node := range config.Nodes {
		nodeData := map[string]interface{}{
			"id":      node.ID,
			"type":    node.Type,
			"state":   "created", // 初始状态为已创建
			"params":  node.Params,
			"metrics": c.generateNodeMetrics(node.Type),
		}
		nodes = append(nodes, nodeData)
	}

	nodesData, _ := json.Marshal(nodes)
	return string(nodesData)
}

// generateRunningNodesFromConfig 从配置文件生成运行中的节点数据
func (c *LocalPipelineClient) generateRunningNodesFromConfig(config *CompletePipelineConfig) string {
	if config == nil || len(config.Nodes) == 0 {
		// 如果没有配置，返回默认模拟数据
		return c.generateMockNodes()
	}

	nodes := make([]map[string]interface{}, 0, len(config.Nodes))

	for _, node := range config.Nodes {
		nodeData := map[string]interface{}{
			"id":      node.ID,
			"type":    node.Type,
			"state":   "running", // 运行状态
			"params":  node.Params,
			"metrics": c.generateNodeMetrics(node.Type),
		}
		nodes = append(nodes, nodeData)
	}

	nodesData, _ := json.Marshal(nodes)
	return string(nodesData)
}

// generateConnectionsFromConfig 从配置文件生成连接信息
func (c *LocalPipelineClient) generateConnectionsFromConfig(config *CompletePipelineConfig) string {
	if config == nil || len(config.Connections) == 0 {
		return "[]"
	}

	connections := make([]map[string]interface{}, 0, len(config.Connections))

	for _, conn := range config.Connections {
		connectionData := map[string]interface{}{
			"id":   conn.ID,
			"from": conn.From,
			"to":   conn.To,
		}
		connections = append(connections, connectionData)
	}

	connectionsData, _ := json.Marshal(connections)
	return string(connectionsData)
}

// generateNodeMetrics 根据节点类型生成相应的指标数据
func (c *LocalPipelineClient) generateNodeMetrics(nodeType string) map[string]interface{} {
	switch nodeType {
	case "vp_rtsp_src_node":
		return map[string]interface{}{
			"fps":              25.3,
			"frames_processed": 91825,
			"queue_size":       2,
		}
	case "vp_generic_detector_node", "vp_yunet_face_detector_node":
		return map[string]interface{}{
			"detection_time_ms": 12.5,
			"detections_count":  3,
			"inference_fps":     24.8,
		}
	case "vp_osd_node", "vp_face_osd_node_v2":
		return map[string]interface{}{
			"render_time_ms": 3.1,
		}
	case "vp_alert_handler_node":
		return map[string]interface{}{
			"alerts_sent": 5,
			"last_alert":  "2025-07-28T22:00:00Z",
		}
	case "vp_screen_des_node":
		return map[string]interface{}{
			"display_fps": 30.0,
			"resolution":  "1920x1080",
		}
	default:
		return map[string]interface{}{
			"status": "active",
		}
	}
}

// generateMockNodes 生成模拟节点数据（保留作为后备）
func (c *LocalPipelineClient) generateMockNodes() string {
	nodes := []map[string]interface{}{
		{
			"id":    "rtsp_src_0",
			"type":  "vp_rtsp_src_node",
			"state": "running",
			"metrics": map[string]interface{}{
				"fps":              25.3,
				"frames_processed": 91825,
				"queue_size":       2,
			},
		},
		{
			"id":    "face_detector_0",
			"type":  "vp_yunet_face_detector_node",
			"state": "running",
			"metrics": map[string]interface{}{
				"detection_time_ms": 12.5,
				"faces_detected":    3,
				"inference_fps":     24.8,
			},
		},
		{
			"id":    "osd_0",
			"type":  "vp_face_osd_node_v2",
			"state": "running",
			"metrics": map[string]interface{}{
				"render_time_ms": 3.1,
			},
		},
	}

	nodesData, _ := json.Marshal(nodes)
	return string(nodesData)
}
