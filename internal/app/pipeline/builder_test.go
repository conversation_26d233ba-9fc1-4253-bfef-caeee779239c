package pipeline

import (
	"encoding/json"
	"testing"
	"time"
)

// TestBuildComprehensiveConfig 测试综合管道配置构建
func TestBuildComprehensiveConfig(t *testing.T) {
	// 创建测试配置
	config := PipelineConfig{
		DeploymentMode: "remote", // 使用远程模式进行测试
		TemplatePath:   "../../../configs/pipeline-template.yaml",
		NodeMapping: NodeMapping{
			VideoSource: map[string]string{
				"rtsp":    "vp_rtsp_src_node",
				"file":    "vp_file_src_node",
				"gb28181": "vp_gb28181_src_node",
			},
			Algorithms: map[string]string{
				"face_detection":   "vp_yunet_face_detector_node",
				"object_detection": "vp_yolo_detector_node",
				"person_detection": "vp_person_detector_node",
			},
		},
		Local: LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		Remote: RemoteConfig{
			APIEndpoint: "http://localhost:8080/api/v1",
			APIKey:      "test-api-key",
			Timeout:     30 * time.Second,
			RetryCount:  3,
		},
	}

	// 创建组件
	templateManager := NewTemplateManager(config.TemplatePath)
	dataMapper := NewDataMapper(config.NodeMapping)
	builder := NewPipelineBuilder(templateManager, dataMapper)

	// 创建测试绑定关系
	bindings := createTestBindings()

	// 测试构建综合管道配置
	pipelineConfig, err := builder.BuildComprehensiveConfig(bindings)
	if err != nil {
		t.Fatalf("构建综合管道配置失败: %v", err)
	}

	// 验证结果
	if pipelineConfig.ID != "comprehensive_pipeline" {
		t.Errorf("期望管道ID为 'comprehensive_pipeline'，实际为 '%s'", pipelineConfig.ID)
	}

	// 验证节点数量
	expectedVideoNodes := 2 // 2个唯一视频源
	expectedAlgoNodes := 3  // 3个唯一算法
	expectedFixedNodes := 3 // 3个固定节点
	expectedTotalNodes := expectedVideoNodes + expectedAlgoNodes + expectedFixedNodes

	if len(pipelineConfig.Nodes) != expectedTotalNodes {
		t.Errorf("期望节点总数为 %d，实际为 %d", expectedTotalNodes, len(pipelineConfig.Nodes))
	}

	// 验证连接关系数量
	expectedConnections := 8 // 基于测试数据的预期连接数
	if len(pipelineConfig.Connections) != expectedConnections {
		t.Errorf("期望连接关系数为 %d，实际为 %d", expectedConnections, len(pipelineConfig.Connections))
	}

	// 打印配置用于调试
	configJSON, _ := json.MarshalIndent(pipelineConfig, "", "  ")
	t.Logf("生成的综合管道配置:\n%s", string(configJSON))
}

// TestAnalyzeBindings 测试绑定关系分析
func TestAnalyzeBindings(t *testing.T) {
	config := PipelineConfig{
		DeploymentMode: "remote",
		TemplatePath:   "../../../configs/pipeline-template.yaml",
		NodeMapping: NodeMapping{
			VideoSource: map[string]string{"rtsp": "vp_rtsp_src_node"},
			Algorithms:  map[string]string{"face_detection": "vp_yunet_face_detector_node"},
		},
		Local: LocalConfig{
			ConfigStoragePath: "./data/pipelines",
			StatusStoragePath: "./data/pipeline-status",
		},
		Remote: RemoteConfig{
			APIEndpoint: "http://localhost:8080/api/v1",
			APIKey:      "test-api-key",
			Timeout:     30 * time.Second,
			RetryCount:  3,
		},
	}

	templateManager := NewTemplateManager("../../../configs/pipeline-template.yaml")
	dataMapper := NewDataMapper(config.NodeMapping)
	builder := NewPipelineBuilder(templateManager, dataMapper)

	bindings := createTestBindings()

	// 测试分析绑定关系
	analysis, err := builder.AnalyzeBindings(bindings)
	if err != nil {
		t.Fatalf("分析绑定关系失败: %v", err)
	}

	// 验证分析结果
	if len(analysis.UniqueVideos) != 2 {
		t.Errorf("期望唯一视频源数为 2，实际为 %d", len(analysis.UniqueVideos))
	}

	if len(analysis.UniqueAlgorithms) != 3 {
		t.Errorf("期望唯一算法数为 3，实际为 %d", len(analysis.UniqueAlgorithms))
	}

	if len(analysis.ActiveBindings) != 3 {
		t.Errorf("期望激活绑定关系数为 3，实际为 %d", len(analysis.ActiveBindings))
	}

	// 验证绑定矩阵
	if len(analysis.BindingMatrix) != 2 {
		t.Errorf("期望绑定矩阵视频源数为 2，实际为 %d", len(analysis.BindingMatrix))
	}

	// 验证视频源1的算法绑定
	if len(analysis.BindingMatrix[1]) != 2 {
		t.Errorf("期望视频源1绑定2个算法，实际绑定 %d 个", len(analysis.BindingMatrix[1]))
	}

	// 验证视频源2的算法绑定
	if len(analysis.BindingMatrix[2]) != 1 {
		t.Errorf("期望视频源2绑定1个算法，实际绑定 %d 个", len(analysis.BindingMatrix[2]))
	}
}

// createTestBindings 创建测试用的绑定关系
func createTestBindings() []*VideoAlgorithmBinding {
	return []*VideoAlgorithmBinding{
		// 绑定关系1：视频源1 + 算法1
		{
			ID:              1,
			VideoID:         1,
			AlgorithmID:     1,
			DetectionArea:   `{"points": [[100,100], [200,100], [200,200], [100,200]]}`,
			AlertInterval:   60,
			AlertWindow:     300,
			AlertThreshold:  0.8,
			VoiceContent:    "检测到人脸",
			DangerLevel:     "warning",
			ExtensionFields: `{"nms_threshold": 0.3, "top_k": 5000}`,
			Status:          "active",
			Video: Video{
				ID:       1,
				Name:     "前门摄像头",
				Type:     1,
				Protocol: "rtsp",
				URL:      "rtsp://admin:password@192.168.1.100:554/stream1",
				Username: "admin",
				Password: "password",
				Status:   "online",
			},
			Algorithm: Algorithm{
				ID:      1,
				Name:    "face_detection",
				Version: "1.0",
				Path:    "./models/face_detection_yunet_2022mar.onnx",
				Status:  "active",
			},
		},
		// 绑定关系2：视频源1 + 算法2
		{
			ID:              2,
			VideoID:         1,
			AlgorithmID:     2,
			DetectionArea:   `{"points": [[50,50], [300,50], [300,250], [50,250]]}`,
			AlertInterval:   30,
			AlertWindow:     180,
			AlertThreshold:  0.7,
			VoiceContent:    "检测到目标",
			DangerLevel:     "info",
			ExtensionFields: `{"classes": ["person", "car", "bicycle"]}`,
			Status:          "active",
			Video: Video{
				ID:       1,
				Name:     "前门摄像头",
				Type:     1,
				Protocol: "rtsp",
				URL:      "rtsp://admin:password@192.168.1.100:554/stream1",
				Username: "admin",
				Password: "password",
				Status:   "online",
			},
			Algorithm: Algorithm{
				ID:      2,
				Name:    "object_detection",
				Version: "1.0",
				Path:    "./models/yolov5s.onnx",
				Status:  "active",
			},
		},
		// 绑定关系3：视频源2 + 算法3
		{
			ID:              3,
			VideoID:         2,
			AlgorithmID:     3,
			DetectionArea:   `{"points": [[0,0], [640,0], [640,480], [0,480]]}`,
			AlertInterval:   45,
			AlertWindow:     240,
			AlertThreshold:  0.75,
			VoiceContent:    "检测到人员",
			DangerLevel:     "warning",
			ExtensionFields: `{"min_size": 50}`,
			Status:          "active",
			Video: Video{
				ID:       2,
				Name:     "后门摄像头",
				Type:     1,
				Protocol: "rtsp",
				URL:      "rtsp://admin:password@192.168.1.101:554/stream1",
				Username: "admin",
				Password: "password",
				Status:   "online",
			},
			Algorithm: Algorithm{
				ID:      3,
				Name:    "person_detection",
				Version: "1.0",
				Path:    "./models/person_detector.onnx",
				Status:  "active",
			},
		},
	}
}
