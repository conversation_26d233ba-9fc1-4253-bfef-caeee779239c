package pipeline

import (
	"fmt"
)

// pipelineBuilder 管道配置构建器实现
type pipelineBuilder struct {
	templateManager TemplateManager
	mapper          DataMapper
}

// NewPipelineBuilder 创建新的管道配置构建器
func NewPipelineBuilder(templateManager TemplateManager, mapper DataMapper) PipelineBuilder {
	return &pipelineBuilder{
		templateManager: templateManager,
		mapper:          mapper,
	}
}

// BuildCompleteConfig 构建完整管道配置
func (b *pipelineBuilder) BuildCompleteConfig(binding *VideoAlgorithmBinding) (*CompletePipelineConfig, error) {
	if binding == nil {
		return nil, fmt.Errorf("绑定关系不能为空")
	}

	// 1. 加载固定模板
	template, err := b.templateManager.LoadTemplate()
	if err != nil {
		return nil, fmt.Errorf("加载模板失败: %v", err)
	}

	// 2. 构建动态内容
	dynamic, err := b.buildDynamicContent(binding)
	if err != nil {
		return nil, fmt.Errorf("构建动态内容失败: %v", err)
	}

	// 3. 合并模板和动态内容
	config, err := b.templateManager.MergeTemplate(template, dynamic)
	if err != nil {
		return nil, fmt.Errorf("合并配置失败: %v", err)
	}

	// 4. 验证最终配置
	if err := b.validateCompleteConfig(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	return config, nil
}

// buildDynamicContent 构建动态内容
func (b *pipelineBuilder) buildDynamicContent(binding *VideoAlgorithmBinding) (*DynamicContent, error) {
	// 构建视频源节点
	videoNode, err := b.BuildVideoSourceNode(&binding.Video)
	if err != nil {
		return nil, fmt.Errorf("构建视频源节点失败: %v", err)
	}

	// 构建检测器节点
	detectorNode, err := b.BuildDetectorNode(&binding.Algorithm, binding)
	if err != nil {
		return nil, fmt.Errorf("构建检测器节点失败: %v", err)
	}

	// 构建连接关系
	fixedNodeIDs := []string{"alert_handler", "screen_output", "osd_display"}
	connections, err := b.BuildConnections(videoNode.ID, detectorNode.ID, fixedNodeIDs)
	if err != nil {
		return nil, fmt.Errorf("构建连接关系失败: %v", err)
	}

	return &DynamicContent{
		ID:           fmt.Sprintf("binding_%d", binding.ID),
		Name:         fmt.Sprintf("%s - %s", binding.Video.Name, binding.Algorithm.Name),
		Description:  fmt.Sprintf("视频源 %s 与算法 %s 的绑定管道", binding.Video.Name, binding.Algorithm.Name),
		VideoNode:    videoNode,
		DetectorNode: detectorNode,
		Connections:  connections,
	}, nil
}

// BuildVideoSourceNode 构建视频源节点
func (b *pipelineBuilder) BuildVideoSourceNode(video *Video) (*PipelineNode, error) {
	if video == nil {
		return nil, fmt.Errorf("视频源不能为空")
	}

	node, err := b.mapper.MapVideoToSourceNode(video)
	if err != nil {
		return nil, err
	}

	// 验证节点配置
	if err := b.mapper.ValidateVideoNode(node); err != nil {
		return nil, fmt.Errorf("视频源节点验证失败: %v", err)
	}

	return node, nil
}

// BuildDetectorNode 构建算法检测节点
func (b *pipelineBuilder) BuildDetectorNode(algorithm *Algorithm, binding *VideoAlgorithmBinding) (*PipelineNode, error) {
	if algorithm == nil {
		return nil, fmt.Errorf("算法不能为空")
	}
	if binding == nil {
		return nil, fmt.Errorf("绑定关系不能为空")
	}

	node, err := b.mapper.MapAlgorithmToDetectorNode(algorithm, binding)
	if err != nil {
		return nil, err
	}

	// 验证节点配置
	if err := b.mapper.ValidateDetectorNode(node); err != nil {
		return nil, fmt.Errorf("检测器节点验证失败: %v", err)
	}

	return node, nil
}

// BuildConnections 构建连接关系
func (b *pipelineBuilder) BuildConnections(videoNodeID, detectorNodeID string, fixedNodeIDs []string) ([]*PipelineConnection, error) {
	if videoNodeID == "" {
		return nil, fmt.Errorf("视频源节点ID不能为空")
	}
	if detectorNodeID == "" {
		return nil, fmt.Errorf("检测器节点ID不能为空")
	}

	connections := make([]*PipelineConnection, 0)
	connectionID := 1

	// 1. 视频源 → 检测器
	connections = append(connections, &PipelineConnection{
		ID:   connectionID,
		From: videoNodeID,
		To:   detectorNodeID,
	})
	connectionID++

	// 2. 检测器 → OSD显示
	if containsString(fixedNodeIDs, "osd_display") {
		connections = append(connections, &PipelineConnection{
			ID:   connectionID,
			From: detectorNodeID,
			To:   "osd_display",
		})
		connectionID++

		// 3. OSD → 告警处理
		if containsString(fixedNodeIDs, "alert_handler") {
			connections = append(connections, &PipelineConnection{
				ID:   connectionID,
				From: "osd_display",
				To:   "alert_handler",
			})
			connectionID++
		}

		// 4. OSD → 屏幕输出
		if containsString(fixedNodeIDs, "screen_output") {
			connections = append(connections, &PipelineConnection{
				ID:   connectionID,
				From: "osd_display",
				To:   "screen_output",
			})
			connectionID++
		}
	} else {
		// 如果没有OSD节点，直接连接到输出节点
		if containsString(fixedNodeIDs, "alert_handler") {
			connections = append(connections, &PipelineConnection{
				ID:   connectionID,
				From: detectorNodeID,
				To:   "alert_handler",
			})
			connectionID++
		}

		if containsString(fixedNodeIDs, "screen_output") {
			connections = append(connections, &PipelineConnection{
				ID:   connectionID,
				From: detectorNodeID,
				To:   "screen_output",
			})
			connectionID++
		}
	}

	return connections, nil
}

// validateCompleteConfig 验证完整配置
func (b *pipelineBuilder) validateCompleteConfig(config *CompletePipelineConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	if config.ID == "" {
		return fmt.Errorf("管道ID不能为空")
	}

	if config.Name == "" {
		return fmt.Errorf("管道名称不能为空")
	}

	if len(config.Nodes) == 0 {
		return fmt.Errorf("至少需要一个节点")
	}

	if len(config.Connections) == 0 {
		return fmt.Errorf("至少需要一个连接")
	}

	// 验证节点ID唯一性
	nodeIDs := make(map[string]bool)
	for _, node := range config.Nodes {
		if node.ID == "" {
			return fmt.Errorf("节点ID不能为空")
		}
		if nodeIDs[node.ID] {
			return fmt.Errorf("节点ID重复: %s", node.ID)
		}
		nodeIDs[node.ID] = true
	}

	// 验证连接关系的有效性
	for _, conn := range config.Connections {
		// 验证From节点存在
		fromID := ""
		switch v := conn.From.(type) {
		case string:
			fromID = v
		case []string:
			if len(v) > 0 {
				fromID = v[0] // 取第一个作为验证
			}
		default:
			return fmt.Errorf("连接%d的From字段类型无效", conn.ID)
		}

		if fromID != "" && !nodeIDs[fromID] {
			return fmt.Errorf("连接%d的源节点不存在: %s", conn.ID, fromID)
		}

		// 验证To节点存在
		if conn.To != "" && !nodeIDs[conn.To] {
			return fmt.Errorf("连接%d的目标节点不存在: %s", conn.ID, conn.To)
		}
	}

	return nil
}

// GetNodeByID 根据ID获取节点
func (b *pipelineBuilder) GetNodeByID(config *CompletePipelineConfig, nodeID string) *PipelineNode {
	if config == nil {
		return nil
	}

	for _, node := range config.Nodes {
		if node.ID == nodeID {
			return node
		}
	}
	return nil
}

// GetConnectionsByFromNode 获取指定源节点的所有连接
func (b *pipelineBuilder) GetConnectionsByFromNode(config *CompletePipelineConfig, fromNodeID string) []*PipelineConnection {
	if config == nil {
		return nil
	}

	connections := make([]*PipelineConnection, 0)
	for _, conn := range config.Connections {
		switch v := conn.From.(type) {
		case string:
			if v == fromNodeID {
				connections = append(connections, conn)
			}
		case []string:
			for _, id := range v {
				if id == fromNodeID {
					connections = append(connections, conn)
					break
				}
			}
		}
	}
	return connections
}

// GetConnectionsByToNode 获取指定目标节点的所有连接
func (b *pipelineBuilder) GetConnectionsByToNode(config *CompletePipelineConfig, toNodeID string) []*PipelineConnection {
	if config == nil {
		return nil
	}

	connections := make([]*PipelineConnection, 0)
	for _, conn := range config.Connections {
		if conn.To == toNodeID {
			connections = append(connections, conn)
		}
	}
	return connections
}

// containsString 检查字符串切片是否包含指定字符串
func containsString(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// === 新增：综合管道构建方法 ===

// BuildComprehensiveConfig 构建综合管道配置
func (b *pipelineBuilder) BuildComprehensiveConfig(bindings []*VideoAlgorithmBinding) (*CompletePipelineConfig, error) {
	if len(bindings) == 0 {
		return nil, fmt.Errorf("绑定关系列表不能为空")
	}

	// 1. 加载固定模板
	template, err := b.templateManager.LoadTemplate()
	if err != nil {
		return nil, fmt.Errorf("加载模板失败: %v", err)
	}

	// 2. 分析绑定关系
	analysis, err := b.AnalyzeBindings(bindings)
	if err != nil {
		return nil, fmt.Errorf("分析绑定关系失败: %v", err)
	}

	// 3. 构建综合动态内容
	dynamic, err := b.buildComprehensiveDynamicContent(analysis)
	if err != nil {
		return nil, fmt.Errorf("构建综合动态内容失败: %v", err)
	}

	// 4. 合并模板和动态内容
	config, err := b.mergeComprehensiveTemplate(template, dynamic)
	if err != nil {
		return nil, fmt.Errorf("合并配置失败: %v", err)
	}

	// 5. 验证最终配置
	if err := b.validateCompleteConfig(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	return config, nil
}

// AnalyzeBindings 分析绑定关系
func (b *pipelineBuilder) AnalyzeBindings(bindings []*VideoAlgorithmBinding) (*BindingAnalysis, error) {
	analysis := &BindingAnalysis{
		UniqueVideos:     make(map[int64]*Video),
		UniqueAlgorithms: make(map[int64]*Algorithm),
		ActiveBindings:   make([]*VideoAlgorithmBinding, 0),
		BindingMatrix:    make(map[int64]map[int64]*VideoAlgorithmBinding),
	}

	// 分析所有绑定关系
	for _, binding := range bindings {
		if binding == nil {
			continue
		}

		// 只处理激活的绑定关系
		if binding.Status != "active" {
			continue
		}

		analysis.ActiveBindings = append(analysis.ActiveBindings, binding)

		// 收集唯一的视频源
		analysis.UniqueVideos[binding.VideoID] = &binding.Video

		// 收集唯一的算法
		analysis.UniqueAlgorithms[binding.AlgorithmID] = &binding.Algorithm

		// 构建绑定矩阵
		if analysis.BindingMatrix[binding.VideoID] == nil {
			analysis.BindingMatrix[binding.VideoID] = make(map[int64]*VideoAlgorithmBinding)
		}
		analysis.BindingMatrix[binding.VideoID][binding.AlgorithmID] = binding
	}

	if len(analysis.ActiveBindings) == 0 {
		return nil, fmt.Errorf("没有找到激活的绑定关系")
	}

	return analysis, nil
}

// buildComprehensiveDynamicContent 构建综合动态内容
func (b *pipelineBuilder) buildComprehensiveDynamicContent(analysis *BindingAnalysis) (*ComprehensiveDynamicContent, error) {
	dynamic := &ComprehensiveDynamicContent{
		ID:            "comprehensive_pipeline",
		Name:          fmt.Sprintf("综合管道 (%d个视频源, %d个算法)", len(analysis.UniqueVideos), len(analysis.UniqueAlgorithms)),
		Description:   fmt.Sprintf("包含%d个绑定关系的综合算法执行管道", len(analysis.ActiveBindings)),
		VideoNodes:    make([]*PipelineNode, 0),
		DetectorNodes: make([]*PipelineNode, 0),
		BindingMatrix: analysis.BindingMatrix,
	}

	// 构建视频源节点
	for _, video := range analysis.UniqueVideos {
		videoNode, err := b.BuildVideoSourceNode(video)
		if err != nil {
			return nil, fmt.Errorf("构建视频源节点失败 (VideoID: %d): %v", video.ID, err)
		}
		dynamic.VideoNodes = append(dynamic.VideoNodes, videoNode)
	}

	// 构建算法检测节点（为每个算法创建一个节点）
	for _, algorithm := range analysis.UniqueAlgorithms {
		// 为每个算法找到一个代表性的绑定关系来获取参数
		var representativeBinding *VideoAlgorithmBinding
		for _, binding := range analysis.ActiveBindings {
			if binding.AlgorithmID == algorithm.ID {
				representativeBinding = binding
				break
			}
		}

		if representativeBinding != nil {
			detectorNode, err := b.BuildDetectorNode(algorithm, representativeBinding)
			if err != nil {
				return nil, fmt.Errorf("构建检测器节点失败 (AlgorithmID: %d): %v", algorithm.ID, err)
			}
			dynamic.DetectorNodes = append(dynamic.DetectorNodes, detectorNode)
		}
	}

	// 构建连接关系
	fixedNodeIDs := []string{"alert_handler", "screen_output", "osd_display"}
	connections, err := b.BuildComprehensiveConnections(analysis, fixedNodeIDs)
	if err != nil {
		return nil, fmt.Errorf("构建连接关系失败: %v", err)
	}
	dynamic.Connections = connections

	return dynamic, nil
}

// BuildComprehensiveConnections 构建综合连接关系
func (b *pipelineBuilder) BuildComprehensiveConnections(analysis *BindingAnalysis, fixedNodeIDs []string) ([]*PipelineConnection, error) {
	connections := make([]*PipelineConnection, 0)
	connectionID := 1

	// 1. 视频源 → 算法检测器的连接（基于绑定矩阵）
	for videoID, algorithmMap := range analysis.BindingMatrix {
		videoNodeID := fmt.Sprintf("video_src_%d", videoID)

		for algorithmID := range algorithmMap {
			detectorNodeID := fmt.Sprintf("detector_%d", algorithmID)

			connections = append(connections, &PipelineConnection{
				ID:   connectionID,
				From: videoNodeID,
				To:   detectorNodeID,
			})
			connectionID++
		}
	}

	// 2. 算法检测器 → 固定节点的连接
	for algorithmID := range analysis.UniqueAlgorithms {
		detectorNodeID := fmt.Sprintf("detector_%d", algorithmID)

		// 检测器 → OSD显示
		if containsString(fixedNodeIDs, "osd_display") {
			connections = append(connections, &PipelineConnection{
				ID:   connectionID,
				From: detectorNodeID,
				To:   "osd_display",
			})
			connectionID++
		} else {
			// 如果没有OSD节点，直接连接到输出节点
			if containsString(fixedNodeIDs, "alert_handler") {
				connections = append(connections, &PipelineConnection{
					ID:   connectionID,
					From: detectorNodeID,
					To:   "alert_handler",
				})
				connectionID++
			}

			if containsString(fixedNodeIDs, "screen_output") {
				connections = append(connections, &PipelineConnection{
					ID:   connectionID,
					From: detectorNodeID,
					To:   "screen_output",
				})
				connectionID++
			}
		}
	}

	// 3. OSD → 输出节点的连接（如果有OSD节点）
	if containsString(fixedNodeIDs, "osd_display") {
		if containsString(fixedNodeIDs, "alert_handler") {
			connections = append(connections, &PipelineConnection{
				ID:   connectionID,
				From: "osd_display",
				To:   "alert_handler",
			})
			connectionID++
		}

		if containsString(fixedNodeIDs, "screen_output") {
			connections = append(connections, &PipelineConnection{
				ID:   connectionID,
				From: "osd_display",
				To:   "screen_output",
			})
			connectionID++
		}
	}

	return connections, nil
}

// mergeComprehensiveTemplate 合并综合模板和动态内容
func (b *pipelineBuilder) mergeComprehensiveTemplate(template *PipelineTemplate, dynamic *ComprehensiveDynamicContent) (*CompletePipelineConfig, error) {
	if template == nil {
		return nil, fmt.Errorf("模板不能为空")
	}
	if dynamic == nil {
		return nil, fmt.Errorf("动态内容不能为空")
	}

	// 创建完整配置
	config := &CompletePipelineConfig{
		ID:          dynamic.ID,
		Name:        dynamic.Name,
		Description: dynamic.Description,
		Version:     template.Version,
		Author:      template.Author,
		Globals:     make(map[string]interface{}),
		Nodes:       make([]*PipelineNode, 0),
		Connections: make([]*PipelineConnection, 0),
	}

	// 复制全局变量
	for k, v := range template.Globals {
		config.Globals[k] = v
	}

	// 添加固定节点
	for _, node := range template.FixedNodes {
		config.Nodes = append(config.Nodes, &PipelineNode{
			ID:     node.ID,
			Type:   node.Type,
			Params: copyParams(node.Params),
		})
	}

	// 添加视频源节点
	for _, videoNode := range dynamic.VideoNodes {
		config.Nodes = append(config.Nodes, &PipelineNode{
			ID:     videoNode.ID,
			Type:   videoNode.Type,
			Params: copyParams(videoNode.Params),
		})
	}

	// 添加检测器节点
	for _, detectorNode := range dynamic.DetectorNodes {
		config.Nodes = append(config.Nodes, &PipelineNode{
			ID:     detectorNode.ID,
			Type:   detectorNode.Type,
			Params: copyParams(detectorNode.Params),
		})
	}

	// 添加连接关系
	for _, conn := range dynamic.Connections {
		config.Connections = append(config.Connections, &PipelineConnection{
			ID:   conn.ID,
			From: conn.From,
			To:   conn.To,
		})
	}

	return config, nil
}
