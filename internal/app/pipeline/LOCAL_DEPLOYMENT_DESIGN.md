# 本地部署模式设计与实现

## 设计理念

当pipeline平台与边缘平台部署在同一设备上时，直接操作本地文件系统比HTTP调用更高效、更可靠。这种设计具有以下优势：

### 优势分析

1. **性能优势**：避免网络开销，直接文件操作更快
2. **可靠性**：不依赖网络连接，减少故障点
3. **简化部署**：单机部署时更简单
4. **资源节约**：减少网络资源消耗
5. **离线能力**：支持完全离线环境运行

## 架构设计

### 配置结构

```yaml
# 管道配置
pipeline:
  enabled: true
  
  # 部署模式：local(本地部署) 或 remote(远程部署)
  deployment_mode: "local"
  
  # 本地部署配置
  local:
    config_storage_path: "./data/pipelines"      # 管道配置文件存储路径
    status_storage_path: "./data/pipeline-status" # 管道状态文件存储路径
    videopipe_executable: "./bin/videopipe"      # VideoPipe可执行文件路径
    videopipe_workdir: "./data/videopipe"        # VideoPipe工作目录
    process_timeout: 30s                         # 管道进程超时时间
    status_check_interval: 5s                    # 状态检查间隔
  
  # 远程部署配置
  remote:
    api_endpoint: "http://localhost:8080/api/v1"
    api_key: "${PIPELINE_API_KEY}"
    timeout: 30s
    retry_count: 3
```

### 核心组件

#### 1. LocalPipelineClient
本地管道客户端，实现 `PipelineClient` 接口：

```go
type LocalPipelineClient struct {
    configStoragePath   string        // 配置文件存储路径
    statusStoragePath   string        // 状态文件存储路径
    videoPipeExecutable string        // VideoPipe可执行文件路径
    videoPipeWorkdir    string        // VideoPipe工作目录
    processTimeout      time.Duration // 进程超时时间
    statusCheckInterval time.Duration // 状态检查间隔
}
```

#### 2. 工厂模式
根据配置自动选择合适的客户端：

```go
func CreatePipelineClient(config PipelineConfig) PipelineClient {
    switch config.DeploymentMode {
    case "local":
        return NewLocalPipelineClient(config)
    case "remote":
        return NewPipelineClient(config)
    default:
        return NewPipelineClient(config) // 默认远程模式
    }
}
```

## 实现细节

### 文件存储结构

```
data/
├── pipelines/                    # 管道配置文件目录
│   ├── comprehensive_pipeline.json
│   ├── binding_1_pipeline.json
│   └── binding_2_pipeline.json
├── pipeline-status/              # 管道状态文件目录
│   ├── comprehensive_pipeline_status.json
│   ├── binding_1_pipeline_status.json
│   └── binding_2_pipeline_status.json
└── videopipe/                    # VideoPipe工作目录
    ├── logs/
    ├── temp/
    └── output/
```

### 核心方法实现

#### 1. 创建管道
```go
func (c *LocalPipelineClient) CreateCompletePipeline(config *CompletePipelineConfig) error {
    // 1. 验证配置
    if err := c.ValidatePipelineConfig(config); err != nil {
        return err
    }
    
    // 2. 保存配置文件
    configPath := filepath.Join(c.configStoragePath, fmt.Sprintf("%s.json", config.ID))
    configData, _ := json.MarshalIndent(config, "", "  ")
    ioutil.WriteFile(configPath, configData, 0644)
    
    // 3. 创建初始状态
    status := &PipelineStatus{
        PipelineID: config.ID,
        State:      "created",
        StartTime:  time.Now().Format("2006-01-02T15:04:05Z"),
        // ...
    }
    
    return c.saveStatus(config.ID, status)
}
```

#### 2. 启动管道
```go
func (c *LocalPipelineClient) StartPipeline(pipelineID string) error {
    // 1. 检查配置文件是否存在
    configPath := filepath.Join(c.configStoragePath, fmt.Sprintf("%s.json", pipelineID))
    if _, err := os.Stat(configPath); os.IsNotExist(err) {
        return fmt.Errorf("管道配置文件不存在: %s", pipelineID)
    }
    
    // 2. 启动VideoPipe进程（可扩展）
    // 这里可以调用 exec.Command 启动实际的VideoPipe进程
    
    // 3. 更新状态为运行中
    status := &PipelineStatus{
        PipelineID: pipelineID,
        State:      "running",
        StartTime:  time.Now().Format("2006-01-02T15:04:05Z"),
        Nodes:      c.generateMockNodes(), // 生成节点状态
        // ...
    }
    
    return c.saveStatus(pipelineID, status)
}
```

#### 3. 获取状态
```go
func (c *LocalPipelineClient) GetPipelineStatus(pipelineID string) (*PipelineStatus, error) {
    statusPath := filepath.Join(c.statusStoragePath, fmt.Sprintf("%s_status.json", pipelineID))
    
    // 读取状态文件
    statusData, err := ioutil.ReadFile(statusPath)
    if err != nil {
        return nil, fmt.Errorf("读取管道状态文件失败: %v", err)
    }
    
    var status PipelineStatus
    json.Unmarshal(statusData, &status)
    
    // 如果管道正在运行，更新运行时间
    if status.State == "running" {
        startTime, _ := time.Parse("2006-01-02T15:04:05Z", status.StartTime)
        status.UptimeSeconds = int(time.Since(startTime).Seconds())
    }
    
    return &status, nil
}
```

## 使用方式

### 1. 配置切换
只需修改配置文件中的 `deployment_mode`：

```yaml
# 本地模式
deployment_mode: "local"

# 远程模式  
deployment_mode: "remote"
```

### 2. 代码使用
代码无需修改，工厂函数会自动选择合适的客户端：

```go
// 自动根据配置选择本地或远程客户端
pipelineClient := pipeline.CreatePipelineClient(pipelineConfig)

// 使用方式完全一致
err := pipelineClient.CreateCompletePipeline(config)
err = pipelineClient.StartPipeline("comprehensive_pipeline")
status, err := pipelineClient.GetPipelineStatus("comprehensive_pipeline")
```

### 3. 服务器集成
服务器启动时自动选择合适的客户端：

```go
// 在 server.go 中
pipelineClient := pipeline.CreatePipelineClient(pipelineConfig)
pipelineService := pipeline.NewPipelineService(pipelineClient, builder, pipelineConfig)
```

## 扩展能力

### 1. 进程管理
可以扩展本地客户端来实际管理VideoPipe进程：

```go
func (c *LocalPipelineClient) StartPipeline(pipelineID string) error {
    // 启动实际的VideoPipe进程
    cmd := exec.Command(c.videoPipeExecutable, 
        "--config", configPath,
        "--workdir", c.videoPipeWorkdir)
    
    if err := cmd.Start(); err != nil {
        return fmt.Errorf("启动VideoPipe进程失败: %v", err)
    }
    
    // 保存进程ID以便后续管理
    // ...
}
```

### 2. 健康检查
可以添加定期健康检查：

```go
func (c *LocalPipelineClient) healthCheck() {
    ticker := time.NewTicker(c.statusCheckInterval)
    for range ticker.C {
        // 检查进程状态
        // 更新状态文件
        // 处理异常情况
    }
}
```

### 3. 日志管理
可以集成日志收集和管理：

```go
func (c *LocalPipelineClient) collectLogs(pipelineID string) {
    logPath := filepath.Join(c.videoPipeWorkdir, "logs", pipelineID+".log")
    // 读取和处理日志
}
```

## 优势总结

1. **统一接口**：本地和远程模式使用相同的接口，代码无需修改
2. **配置驱动**：通过配置文件轻松切换部署模式
3. **高性能**：本地模式避免网络开销，响应更快
4. **高可靠**：不依赖网络连接，减少故障点
5. **易扩展**：可以轻松添加进程管理、健康检查等功能
6. **向后兼容**：完全兼容现有的远程模式

这种设计既保持了系统的灵活性，又提供了本地部署的高性能和高可靠性，是一个非常合理的架构选择。
