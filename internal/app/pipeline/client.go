package pipeline

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

// pipelineClient VideoPipe API客户端实现
type pipelineClient struct {
	baseURL    string
	apiKey     string
	timeout    time.Duration
	retryCount int
	httpClient *http.Client
}

// NewPipelineClient 创建新的管道API客户端（远程模式）
func NewPipelineClient(config PipelineConfig) PipelineClient {
	return &pipelineClient{
		baseURL:    config.Remote.APIEndpoint,
		apiKey:     config.Remote.APIKey,
		timeout:    config.Remote.Timeout,
		retryCount: config.Remote.RetryCount,
		httpClient: &http.Client{
			Timeout: config.Remote.Timeout,
		},
	}
}

// CreateCompletePipeline 创建完整管道
func (c *pipelineClient) CreateCompletePipeline(config *CompletePipelineConfig) error {
	if config == nil {
		return fmt.Errorf("管道配置不能为空")
	}

	url := fmt.Sprintf("%s/api/v1/pipelines/complete", c.baseURL)

	// 序列化配置
	jsonData, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 重试机制
	var lastErr error
	for i := 0; i <= c.retryCount; i++ {
		if i > 0 {
			// 指数退避
			waitTime := time.Duration(i) * time.Second
			time.Sleep(waitTime)
		}

		err := c.doRequest("POST", url, jsonData, nil)
		if err == nil {
			return nil
		}
		lastErr = err
	}

	return fmt.Errorf("创建管道失败，重试%d次后仍然失败: %v", c.retryCount, lastErr)
}

// DeletePipeline 删除管道
func (c *pipelineClient) DeletePipeline(pipelineID string) error {
	if pipelineID == "" {
		return fmt.Errorf("管道ID不能为空")
	}

	url := fmt.Sprintf("%s/api/v1/pipelines/%s", c.baseURL, pipelineID)

	var lastErr error
	for i := 0; i <= c.retryCount; i++ {
		if i > 0 {
			waitTime := time.Duration(i) * time.Second
			time.Sleep(waitTime)
		}

		err := c.doRequest("DELETE", url, nil, nil)
		if err == nil {
			return nil
		}
		lastErr = err
	}

	return fmt.Errorf("删除管道失败: %v", lastErr)
}

// StartPipeline 启动管道
func (c *pipelineClient) StartPipeline(pipelineID string) error {
	if pipelineID == "" {
		return fmt.Errorf("管道ID不能为空")
	}

	url := fmt.Sprintf("%s/api/v1/pipelines/%s/start", c.baseURL, pipelineID)

	var lastErr error
	for i := 0; i <= c.retryCount; i++ {
		if i > 0 {
			waitTime := time.Duration(i) * time.Second
			time.Sleep(waitTime)
		}

		err := c.doRequest("POST", url, nil, nil)
		if err == nil {
			return nil
		}
		lastErr = err
	}

	return fmt.Errorf("启动管道失败: %v", lastErr)
}

// StopPipeline 停止管道
func (c *pipelineClient) StopPipeline(pipelineID string) error {
	if pipelineID == "" {
		return fmt.Errorf("管道ID不能为空")
	}

	url := fmt.Sprintf("%s/api/v1/pipelines/%s/stop", c.baseURL, pipelineID)

	var lastErr error
	for i := 0; i <= c.retryCount; i++ {
		if i > 0 {
			waitTime := time.Duration(i) * time.Second
			time.Sleep(waitTime)
		}

		err := c.doRequest("POST", url, nil, nil)
		if err == nil {
			return nil
		}
		lastErr = err
	}

	return fmt.Errorf("停止管道失败: %v", lastErr)
}

// GetPipelineStatus 获取管道状态
func (c *pipelineClient) GetPipelineStatus(pipelineID string) (*PipelineStatus, error) {
	if pipelineID == "" {
		return nil, fmt.Errorf("管道ID不能为空")
	}

	url := fmt.Sprintf("%s/api/v1/pipelines/%s/status", c.baseURL, pipelineID)

	// VideoPipe API 返回的响应格式
	var apiResponse struct {
		Status string         `json:"status"`
		Data   PipelineStatus `json:"data"`
	}
	var lastErr error

	for i := 0; i <= c.retryCount; i++ {
		if i > 0 {
			waitTime := time.Duration(i) * time.Second
			time.Sleep(waitTime)
		}

		err := c.doRequest("GET", url, nil, &apiResponse)
		if err == nil {
			if apiResponse.Status == "success" {
				return &apiResponse.Data, nil
			}
			return nil, fmt.Errorf("API返回错误状态: %s", apiResponse.Status)
		}
		lastErr = err
	}

	return nil, fmt.Errorf("获取管道状态失败: %v", lastErr)
}

// ValidatePipelineConfig 验证管道配置
func (c *pipelineClient) ValidatePipelineConfig(config *CompletePipelineConfig) error {
	if config == nil {
		return fmt.Errorf("管道配置不能为空")
	}

	url := fmt.Sprintf("%s/api/v1/pipelines/validate", c.baseURL)

	jsonData, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	return c.doRequest("POST", url, jsonData, nil)
}

// doRequest 执行HTTP请求
func (c *pipelineClient) doRequest(method, url string, body []byte, response interface{}) error {
	var req *http.Request
	var err error

	if body != nil {
		req, err = http.NewRequest(method, url, bytes.NewBuffer(body))
	} else {
		req, err = http.NewRequest(method, url, nil)
	}

	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	if c.apiKey != "" {
		req.Header.Set("X-API-Key", c.apiKey)
		req.Header.Set("Authorization", "Bearer "+c.apiKey)
	}
	req.Header.Set("User-Agent", "ECP-Pipeline-Client/1.0")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		// 尝试解析错误响应
		var apiResp APIResponse
		if json.Unmarshal(respBody, &apiResp) == nil && apiResp.Message != "" {
			return fmt.Errorf("API调用失败 [%d]: %s", resp.StatusCode, apiResp.Message)
		}
		return fmt.Errorf("API调用失败 [%d]: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应（如果需要）
	if response != nil {
		if err := json.Unmarshal(respBody, response); err != nil {
			return fmt.Errorf("解析响应失败: %v", err)
		}
	}

	return nil
}

// Ping 测试API连接
func (c *pipelineClient) Ping() error {
	url := fmt.Sprintf("%s/api/v1/ping", c.baseURL)
	return c.doRequest("GET", url, nil, nil)
}

// GetAPIVersion 获取API版本信息
func (c *pipelineClient) GetAPIVersion() (string, error) {
	url := fmt.Sprintf("%s/api/v1/version", c.baseURL)

	var response struct {
		Version string `json:"version"`
	}

	err := c.doRequest("GET", url, nil, &response)
	if err != nil {
		return "", err
	}

	return response.Version, nil
}

// ListPipelines 获取管道列表
func (c *pipelineClient) ListPipelines() ([]PipelineStatus, error) {
	url := fmt.Sprintf("%s/api/v1/pipelines", c.baseURL)

	var response []PipelineStatus
	err := c.doRequest("GET", url, nil, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// GetPipelineConfig 获取管道配置
func (c *pipelineClient) GetPipelineConfig(pipelineID string) (*CompletePipelineConfig, error) {
	if pipelineID == "" {
		return nil, fmt.Errorf("管道ID不能为空")
	}

	url := fmt.Sprintf("%s/api/v1/pipelines/%s/config", c.baseURL, pipelineID)

	var response CompletePipelineConfig
	err := c.doRequest("GET", url, nil, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// UpdatePipelineConfig 更新管道配置
func (c *pipelineClient) UpdatePipelineConfig(pipelineID string, config *CompletePipelineConfig) error {
	if pipelineID == "" {
		return fmt.Errorf("管道ID不能为空")
	}
	if config == nil {
		return fmt.Errorf("管道配置不能为空")
	}

	url := fmt.Sprintf("%s/api/v1/pipelines/%s/config", c.baseURL, pipelineID)

	jsonData, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	return c.doRequest("PUT", url, jsonData, nil)
}
