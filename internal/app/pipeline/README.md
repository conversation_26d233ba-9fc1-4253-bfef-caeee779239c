# 管道系统使用指南

## 概述

管道系统是边缘计算平台的核心组件，负责根据 `VideoAlgorithmBinding` 表中的绑定关系创建完整的视频算法执行管道。系统使用综合管道模式，根据所有激活的绑定关系创建一个统一的综合管道。

## 核心组件

### 1. PipelineService - 管道服务
主要接口，负责管道的生命周期管理。

### 2. PipelineBuilder - 管道构建器
负责根据绑定关系构建完整的管道配置。

### 3. DataMapper - 数据映射器
负责将数据库中的视频源和算法信息映射为管道节点配置。

### 4. TemplateManager - 模板管理器
负责加载和合并固定模板与动态内容。

### 5. PipelineClient - 管道客户端
负责与 VideoPipe API 进行通信。

## VideoAlgorithmBinding 表结构

```go
type VideoAlgorithmBinding struct {
    ID              int64     // 绑定关系ID
    VideoID         int64     // 视频源ID
    AlgorithmID     int64     // 算法ID
    DetectionArea   string    // 检测区域（JSON格式多边形坐标）
    AlertInterval   int       // 告警间隔（秒）
    AlertWindow     int       // 告警窗口长度（秒）
    AlertThreshold  float64   // 告警阈值（0-1）
    VoiceContent    string    // 语音播报内容
    DangerLevel     string    // 危险等级：info, warning, error
    ExtensionFields string    // 算法扩展字段（JSON格式）
    Status          string    // 绑定状态：active, inactive
    
    // 关联数据
    Video     Video     // 视频源信息
    Algorithm Algorithm // 算法信息
}
```

## 使用方法

### 1. 初始化管道服务

```go
// 1. 创建配置
pipelineConfig := pipeline.PipelineConfig{
    DeploymentMode: "remote", // 或 "local"
    TemplatePath:   "./configs/pipeline-template.yaml",
    NodeMapping: pipeline.NodeMapping{
        VideoSource: map[string]string{
            "rtsp":    "vp_rtsp_src_node",
            "gb28181": "vp_gb28181_src_node",
            "file":    "vp_file_src_node",
        },
        Algorithms: map[string]string{
            "face_detection":   "vp_yunet_face_detector_node",
            "object_detection": "vp_yolo_detector_node",
            "person_detection": "vp_person_detector_node",
        },
    },
    Local: pipeline.LocalConfig{
        ConfigStoragePath:   "./data/pipelines",
        StatusStoragePath:   "./data/pipeline-status",
        VideoPipeExecutable: "./bin/videopipe",
        VideoPipeWorkdir:    "./data/videopipe",
        ProcessTimeout:      "30s",
        StatusCheckInterval: "5s",
    },
    Remote: pipeline.RemoteConfig{
        APIEndpoint: "http://localhost:8080/api/v1",
        APIKey:      "your-api-key",
        Timeout:     30 * time.Second,
        RetryCount:  3,
    },
}

// 2. 创建组件
templateManager := pipeline.NewTemplateManager(pipelineConfig.TemplatePath)
dataMapper := pipeline.NewDataMapper(pipelineConfig.NodeMapping)
pipelineBuilder := pipeline.NewPipelineBuilder(templateManager, dataMapper)
// 根据配置自动选择本地或远程客户端
pipelineClient := pipeline.CreatePipelineClient(pipelineConfig)

// 3. 创建管道服务
pipelineService := pipeline.NewPipelineService(pipelineClient, pipelineBuilder, pipelineConfig)
```

### 2. 综合管道操作

#### 创建综合管道
```go
// 获取所有激活的绑定关系
bindings := getAllActiveBindings()

// 转换为管道包的数据结构
pipelineBindings := make([]*pipeline.VideoAlgorithmBinding, len(bindings))
for i, binding := range bindings {
    pipelineBindings[i] = convertToPipelineBinding(binding)
}

// 创建综合管道
err := pipelineService.CreateComprehensivePipeline(pipelineBindings)
if err != nil {
    log.Printf("创建综合管道失败: %v", err)
}
```

#### 启动综合管道
```go
err := pipelineService.StartComprehensivePipeline()
if err != nil {
    log.Printf("启动综合管道失败: %v", err)
}
```

#### 停止综合管道
```go
err := pipelineService.StopComprehensivePipeline()
if err != nil {
    log.Printf("停止综合管道失败: %v", err)
}
```

#### 获取综合管道状态
```go
status, err := pipelineService.GetComprehensivePipelineStatus()
if err != nil {
    log.Printf("获取综合管道状态失败: %v", err)
} else {
    fmt.Printf("综合管道状态: %s\n", status.Status)
}
```

### 3. 绑定关系事件处理

系统提供了全量绑定关系变更的事件处理机制：

```go
// 全量绑定关系变更事件
err := pipelineService.OnAllBindingsChanged(bindings)
```

## 数据转换

由于 pipeline 包使用独立的数据结构，需要进行数据转换：

```go
func convertToPipelineBinding(dbBinding *binding.VideoAlgorithmBinding) *pipeline.VideoAlgorithmBinding {
    return &pipeline.VideoAlgorithmBinding{
        ID:              dbBinding.ID,
        VideoID:         dbBinding.VideoID,
        AlgorithmID:     dbBinding.AlgorithmID,
        DetectionArea:   dbBinding.DetectionArea,
        AlertInterval:   dbBinding.AlertInterval,
        AlertWindow:     dbBinding.AlertWindow,
        AlertThreshold:  dbBinding.AlertThreshold,
        VoiceContent:    dbBinding.VoiceContent,
        DangerLevel:     dbBinding.DangerLevel,
        ExtensionFields: dbBinding.ExtensionFields,
        Status:          dbBinding.Status,
        Video: pipeline.Video{
            ID:          dbBinding.Video.ID,
            Name:        dbBinding.Video.Name,
            Type:        dbBinding.Video.Type,
            Protocol:    dbBinding.Video.Protocol,
            URL:         dbBinding.Video.URL,
            Username:    dbBinding.Video.Username,
            Password:    dbBinding.Video.Password,
            CameraID:    dbBinding.Video.CameraID,
            CameraIP:    dbBinding.Video.CameraIP,
            Port:        dbBinding.Video.Port,
            Brand:       dbBinding.Video.Brand,
            StreamMode:  dbBinding.Video.StreamMode,
        },
        Algorithm: pipeline.Algorithm{
            ID:          dbBinding.Algorithm.ID,
            Name:        dbBinding.Algorithm.Name,
            Description: dbBinding.Algorithm.Description,
            Version:     dbBinding.Algorithm.Version,
            Path:        dbBinding.Algorithm.Path,
            Status:      dbBinding.Algorithm.Status,
        },
    }
}
```

## 管道配置生成过程

### 综合管道配置生成

1. **分析绑定关系**：分析所有激活的绑定关系，提取唯一的视频源和算法
2. **构建绑定矩阵**：创建视频源与算法的多对多关系矩阵
3. **去重节点创建**：为每个唯一的视频源和算法创建节点
4. **复杂连接构建**：根据绑定矩阵创建复杂的连接关系
5. **合并和验证**：合并配置并验证

## 节点类型映射

### 视频源节点映射
- `rtsp` → `vp_rtsp_src_node`
- `gb28181` → `vp_gb28181_src_node`
- `onvif` → `vp_onvif_src_node`
- `file` → `vp_file_src_node`

### 算法节点映射
- `face_detection` → `vp_yunet_face_detector_node`
- `object_detection` → `vp_yolo_detector_node`
- `person_detection` → `vp_person_detector_node`
- `vehicle_detection` → `vp_vehicle_detector_node`
- `helmet_detection` → `vp_helmet_detector_node`
- `safety_detection` → `vp_safety_detector_node`

## 配置参数说明

### 视频源节点参数
```yaml
# RTSP 视频源
rtsp_url: "rtsp://admin:password@*************:554/stream1"
username: "admin"
password: "password"
channel_index: 0
resize_ratio: 0.6

# GB28181 视频源
device_id: "44010200491180000001"
channel_id: 1
server_ip: "*************"
server_port: 5060

# 文件视频源
file_path: "/path/to/video.mp4"
loop: true
```

### 算法检测节点参数
```yaml
model_path: "./models/face_detection.onnx"
confidence_threshold: 0.8
detection_area: '{"points": [[100,100], [200,100], [200,200], [100,200]]}'
alert_interval: 60
alert_window: 300
danger_level: "warning"
voice_content: "检测到人脸"
algorithm_version: "1.0.0"

# 扩展字段（从 ExtensionFields JSON 解析）
nms_threshold: 0.3
top_k: 5000
```

## 错误处理

系统提供了完善的错误处理机制：

1. **配置验证错误**：配置格式不正确或缺少必需参数
2. **API 通信错误**：与 VideoPipe API 通信失败
3. **状态同步错误**：管道状态同步失败
4. **重试机制**：支持失败重试，可配置重试次数

## 完整使用示例

参考以下测试文件了解完整的使用流程：

- `cmd/test-db-pipeline/main.go` - 基本管道操作示例
- `cmd/test-db-pipeline-with-data/main.go` - 包含测试数据的完整示例
- `cmd/test-comprehensive-pipeline/main.go` - 综合管道操作示例

## 注意事项

1. **数据转换**：使用前需要将数据库模型转换为 pipeline 包的数据结构
2. **配置文件**：确保 `configs/pipeline-template.yaml` 文件存在且格式正确
3. **API 端点**：确保 VideoPipe API 服务正常运行
4. **权限认证**：配置正确的 API 密钥
5. **资源管理**：及时清理不需要的管道以释放资源

## 故障排查

1. **检查配置文件**：验证 `configs/config.yaml` 和 `configs/pipeline-template.yaml`
2. **检查 API 连接**：确认 VideoPipe API 服务状态
3. **查看日志**：检查详细的错误日志信息
4. **验证数据**：确认绑定关系数据的完整性和正确性
5. **测试连接**：使用测试程序验证各组件功能

## API 接口说明

### 综合管道 API

- `POST /api/comprehensive-pipeline/create` - 创建综合管道
- `GET /api/comprehensive-pipeline/status` - 获取综合管道状态
- `PUT /api/comprehensive-pipeline/start` - 启动综合管道
- `PUT /api/comprehensive-pipeline/stop` - 停止综合管道
- `POST /api/comprehensive-pipeline/rebuild` - 重建综合管道
- `DELETE /api/comprehensive-pipeline` - 删除综合管道

## 最佳实践

1. **使用综合管道**：对于多个绑定关系，推荐使用综合管道以提高资源利用率
2. **合理配置阈值**：根据实际需求调整告警阈值和间隔
3. **监控管道状态**：定期检查管道运行状态，及时处理异常
4. **优化检测区域**：合理设置检测区域以提高检测精度
5. **资源清理**：删除不需要的绑定关系时，及时清理对应的管道

## 扩展开发

如需扩展管道系统功能，可以：

1. **添加新的节点类型**：在 `mapper.go` 中添加新的节点映射
2. **扩展配置参数**：在数据转换过程中添加新的参数处理
3. **自定义模板**：修改 `configs/pipeline-template.yaml` 添加自定义固定节点
4. **增强错误处理**：在各组件中添加更详细的错误处理逻辑

## 快速开始示例

以下是一个完整的使用示例，展示如何从数据库中获取绑定关系并创建管道：

```go
package main

import (
    "log"
    "time"

    "ecp/internal/app/binding"
    "ecp/internal/app/pipeline"
    "ecp/internal/pkg/config"
    "ecp/internal/pkg/database"
)

func main() {
    // 1. 加载配置
    cfg, err := config.LoadConfig("configs/config.yaml")
    if err != nil {
        log.Fatalf("加载配置失败: %v", err)
    }

    // 2. 连接数据库
    dbConfig := &database.Config{Path: cfg.Database.Path}
    db, err := database.NewDB(dbConfig)
    if err != nil {
        log.Fatalf("连接数据库失败: %v", err)
    }

    // 3. 创建绑定服务
    bindingService := binding.NewBindingService(db.DB)

    // 4. 获取激活的绑定关系
    bindings, err := bindingService.GetActiveBindings()
    if err != nil {
        log.Fatalf("获取绑定关系失败: %v", err)
    }

    // 5. 初始化管道服务
    timeout, _ := time.ParseDuration(cfg.Pipeline.Timeout)
    pipelineConfig := pipeline.PipelineConfig{
        APIEndpoint:  cfg.Pipeline.APIEndpoint,
        APIKey:       cfg.Pipeline.APIKey,
        Timeout:      timeout,
        RetryCount:   cfg.Pipeline.RetryCount,
        TemplatePath: cfg.Pipeline.TemplatePath,
        NodeMapping: pipeline.NodeMapping{
            VideoSource: cfg.Pipeline.NodeMapping.VideoSource,
            Algorithms:  cfg.Pipeline.NodeMapping.Algorithms,
        },
    }

    templateManager := pipeline.NewTemplateManager(pipelineConfig.TemplatePath)
    dataMapper := pipeline.NewDataMapper(pipelineConfig.NodeMapping)
    pipelineBuilder := pipeline.NewPipelineBuilder(templateManager, dataMapper)
    pipelineClient := pipeline.NewPipelineClient(pipelineConfig)
    pipelineService := pipeline.NewPipelineService(pipelineClient, pipelineBuilder, pipelineConfig)

    // 6. 转换数据格式
    pipelineBindings := make([]*pipeline.VideoAlgorithmBinding, len(bindings))
    for i, binding := range bindings {
        pipelineBindings[i] = convertToPipelineBinding(binding)
    }

    // 7. 创建综合管道
    err = pipelineService.CreateComprehensivePipeline(pipelineBindings)
    if err != nil {
        log.Fatalf("创建综合管道失败: %v", err)
    }

    // 8. 启动管道
    err = pipelineService.StartComprehensivePipeline()
    if err != nil {
        log.Fatalf("启动综合管道失败: %v", err)
    }

    log.Println("管道创建并启动成功！")
}

// 数据转换函数
func convertToPipelineBinding(dbBinding *binding.VideoAlgorithmBinding) *pipeline.VideoAlgorithmBinding {
    return &pipeline.VideoAlgorithmBinding{
        ID:              dbBinding.ID,
        VideoID:         dbBinding.VideoID,
        AlgorithmID:     dbBinding.AlgorithmID,
        DetectionArea:   dbBinding.DetectionArea,
        AlertInterval:   dbBinding.AlertInterval,
        AlertWindow:     dbBinding.AlertWindow,
        AlertThreshold:  dbBinding.AlertThreshold,
        VoiceContent:    dbBinding.VoiceContent,
        DangerLevel:     dbBinding.DangerLevel,
        ExtensionFields: dbBinding.ExtensionFields,
        Status:          dbBinding.Status,
        Video: pipeline.Video{
            ID:       dbBinding.Video.ID,
            Name:     dbBinding.Video.Name,
            Type:     dbBinding.Video.Type,
            Protocol: dbBinding.Video.Protocol,
            URL:      dbBinding.Video.URL,
            Username: dbBinding.Video.Username,
            Password: dbBinding.Video.Password,
        },
        Algorithm: pipeline.Algorithm{
            ID:   dbBinding.Algorithm.ID,
            Name: dbBinding.Algorithm.Name,
            Path: dbBinding.Algorithm.Path,
        },
    }
}
```

这个示例展示了完整的管道创建流程，从数据库获取绑定关系到最终启动管道的全过程。
