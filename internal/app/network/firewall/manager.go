package firewall

import (
	"fmt"
	"os/exec"
	"regexp"
	"strings"
	"sync"
	"time"

	"ecp/internal/app/network/models"
)

// Manager 防火墙管理器
type Manager struct {
	iptablesPath string
	backupPath   string
	mutex        sync.RWMutex
	ruleCache    map[string]*models.FirewallRule
}

// NewManager 创建防火墙管理器
func NewManager(iptablesPath, backupPath string) *Manager {
	return &Manager{
		iptablesPath: iptablesPath,
		backupPath:   backupPath,
		ruleCache:    make(map[string]*models.FirewallRule),
	}
}

// GetRules 获取防火墙规则
func (m *Manager) GetRules() ([]*models.FirewallRule, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 执行iptables -L命令获取规则
	cmd := exec.Command(m.iptablesPath, "-L", "-n", "--line-numbers")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("获取iptables规则失败: %v", err)
	}

	rules, err := m.parseIptablesOutput(string(output))
	if err != nil {
		return nil, fmt.Errorf("解析iptables输出失败: %v", err)
	}

	return rules, nil
}

// AddRule 添加防火墙规则
func (m *Manager) AddRule(rule *models.FirewallRule) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 构建iptables命令
	args := m.buildIptablesArgs("A", rule)

	// 执行命令
	cmd := exec.Command(m.iptablesPath, args...)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("添加防火墙规则失败: %v", err)
	}

	// 更新缓存
	rule.ID = m.generateRuleID()
	rule.CreatedAt = time.Now()
	m.ruleCache[rule.ID] = rule

	return nil
}

// DeleteRule 删除防火墙规则
func (m *Manager) DeleteRule(ruleID string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 从缓存中获取规则
	rule, exists := m.ruleCache[ruleID]
	if !exists {
		return fmt.Errorf("规则 %s 不存在", ruleID)
	}

	// 构建删除命令
	args := m.buildIptablesArgs("D", rule)

	// 执行命令
	cmd := exec.Command(m.iptablesPath, args...)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("删除防火墙规则失败: %v", err)
	}

	// 从缓存中删除
	delete(m.ruleCache, ruleID)

	return nil
}

// GetStatus 获取防火墙状态
func (m *Manager) GetStatus() (*models.FirewallStatus, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 检查iptables是否可用
	cmd := exec.Command(m.iptablesPath, "-L")
	err := cmd.Run()
	enabled := err == nil

	// 获取默认策略
	defaultPolicy := m.getDefaultPolicy()

	// 获取规则数量
	rules, _ := m.GetRules()
	ruleCount := len(rules)

	return &models.FirewallStatus{
		Enabled:       enabled,
		DefaultPolicy: defaultPolicy,
		RuleCount:     ruleCount,
		LastUpdated:   time.Now(),
	}, nil
}

// parseIptablesOutput 解析iptables输出
func (m *Manager) parseIptablesOutput(output string) ([]*models.FirewallRule, error) {
	var rules []*models.FirewallRule
	lines := strings.Split(output, "\n")

	currentChain := ""
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 检查是否是链标题行
		if strings.HasPrefix(line, "Chain") {
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				currentChain = parts[1]
			}
			continue
		}

		// 跳过空行和标题行
		if line == "" || strings.HasPrefix(line, "num") || strings.HasPrefix(line, "target") {
			continue
		}

		// 解析规则行
		rule := m.parseRuleLine(line, currentChain)
		if rule != nil {
			rules = append(rules, rule)
		}
	}

	return rules, nil
}

// parseRuleLine 解析单行规则
func (m *Manager) parseRuleLine(line, chain string) *models.FirewallRule {
	fields := strings.Fields(line)
	if len(fields) < 4 {
		return nil
	}

	rule := &models.FirewallRule{
		ID:        m.generateRuleID(),
		Chain:     chain,
		Target:    fields[1],
		Protocol:  fields[2],
		Source:    "0.0.0.0/0",
		Enabled:   true,
		CreatedAt: time.Now(),
	}

	// 解析更多字段（简化版本）
	if len(fields) > 3 {
		rule.Source = fields[3]
	}
	if len(fields) > 4 {
		rule.Destination = fields[4]
	}

	return rule
}

// buildIptablesArgs 构建iptables命令参数
func (m *Manager) buildIptablesArgs(action string, rule *models.FirewallRule) []string {
	args := []string{"-" + action, rule.Chain}

	// 添加协议
	if rule.Protocol != "" && rule.Protocol != "all" {
		args = append(args, "-p", rule.Protocol)
	}

	// 添加源地址
	if rule.Source != "" && rule.Source != "0.0.0.0/0" {
		args = append(args, "-s", rule.Source)
	}

	// 添加目标地址
	if rule.Destination != "" && rule.Destination != "0.0.0.0/0" {
		args = append(args, "-d", rule.Destination)
	}

	// 添加源端口
	if rule.SourcePort != "" {
		args = append(args, "--sport", rule.SourcePort)
	}

	// 添加目标端口
	if rule.DestPort != "" {
		args = append(args, "--dport", rule.DestPort)
	}

	// 添加接口
	if rule.Interface != "" {
		args = append(args, "-i", rule.Interface)
	}

	// 添加目标动作
	args = append(args, "-j", rule.Target)

	// 添加注释
	if rule.Comment != "" {
		args = append(args, "-m", "comment", "--comment", rule.Comment)
	}

	return args
}

// generateRuleID 生成规则ID
func (m *Manager) generateRuleID() string {
	return fmt.Sprintf("rule_%d", time.Now().UnixNano())
}

// getDefaultPolicy 获取默认策略
func (m *Manager) getDefaultPolicy() string {
	cmd := exec.Command(m.iptablesPath, "-L", "-n")
	output, err := cmd.Output()
	if err != nil {
		return "ACCEPT"
	}

	// 解析默认策略
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "Chain INPUT") {
			re := regexp.MustCompile(`policy (\w+)`)
			matches := re.FindStringSubmatch(line)
			if len(matches) > 1 {
				return matches[1]
			}
		}
	}

	return "ACCEPT"
}
