# 网络管理模块

## 概述

网络管理模块负责Linux系统的网络配置、监控、诊断和管理功能。提供完整的网络管理解决方案，包括网络接口配置、流量监控、防火墙管理等功能。

## 功能特性

### 1. 网络配置管理
- **网络接口管理**：查看和配置网络接口
- **IP地址配置**：支持静态IP和DHCP配置
- **DNS配置**：DNS服务器设置和管理
- **路由管理**：默认网关和静态路由配置
- **网络代理**：HTTP/HTTPS代理配置

### 2. 网络监控
- **实时状态监控**：网络接口状态、连接状态
- **流量统计**：实时和历史流量数据
- **网络质量检测**：延迟、丢包率、带宽测试
- **连接数统计**：TCP/UDP连接数监控

### 3. 防火墙管理
- **iptables规则管理**：端口开放、访问控制
- **安全策略配置**：白名单、黑名单管理
- **服务端口管理**：常用服务端口配置

### 4. 网络诊断
- **连通性测试**：ping、traceroute工具
- **端口扫描**：检测开放端口
- **故障诊断**：自动诊断网络问题

## 模块结构

```
internal/app/network/
├── README.md                 # 模块说明文档
├── types.go                  # 数据类型定义
├── service.go                # 网络服务接口
├── config/                   # 网络配置管理
│   ├── interface.go          # 网络接口配置
│   ├── dns.go               # DNS配置
│   ├── route.go             # 路由配置
│   └── proxy.go             # 代理配置
├── monitor/                  # 网络监控
│   ├── status.go            # 状态监控
│   ├── traffic.go           # 流量监控
│   ├── quality.go           # 网络质量检测
│   └── connection.go        # 连接监控
├── firewall/                 # 防火墙管理
│   ├── iptables.go          # iptables管理
│   ├── rules.go             # 规则管理
│   └── security.go          # 安全策略
├── diagnostic/               # 网络诊断
│   ├── ping.go              # ping测试
│   ├── traceroute.go        # 路由跟踪
│   ├── portscan.go          # 端口扫描
│   └── troubleshoot.go      # 故障诊断
└── utils/                    # 工具函数
    ├── command.go           # 系统命令执行
    ├── parser.go            # 输出解析
    └── validator.go         # 配置验证
```

## API接口设计

### 网络配置 API
- `GET /api/network/interfaces` - 获取网络接口列表
- `POST /api/network/interfaces/{name}/config` - 配置网络接口
- `GET /api/network/dns` - 获取DNS配置
- `POST /api/network/dns` - 设置DNS配置
- `GET /api/network/routes` - 获取路由表
- `POST /api/network/routes` - 添加路由

### 网络监控 API
- `GET /api/network/status` - 获取网络状态
- `GET /api/network/traffic` - 获取流量统计
- `GET /api/network/quality` - 获取网络质量
- `GET /api/network/connections` - 获取连接统计

### 防火墙管理 API
- `GET /api/network/firewall/rules` - 获取防火墙规则
- `POST /api/network/firewall/rules` - 添加防火墙规则
- `DELETE /api/network/firewall/rules/{id}` - 删除防火墙规则

### 网络诊断 API
- `POST /api/network/diagnostic/ping` - 执行ping测试
- `POST /api/network/diagnostic/traceroute` - 执行路由跟踪
- `POST /api/network/diagnostic/portscan` - 执行端口扫描

## 使用示例

### 获取网络接口信息
```go
interfaces, err := networkService.GetNetworkInterfaces()
if err != nil {
    log.Printf("获取网络接口失败: %v", err)
    return
}

for _, iface := range interfaces {
    fmt.Printf("接口: %s, 状态: %s, IP: %s\n", 
        iface.Name, iface.Status, iface.IPAddress)
}
```

### 配置静态IP
```go
config := &NetworkInterfaceConfig{
    Name:      "eth0",
    Method:    "static",
    IPAddress: "*************",
    Netmask:   "*************",
    Gateway:   "***********",
}

err := networkService.ConfigureInterface(config)
if err != nil {
    log.Printf("配置网络接口失败: %v", err)
}
```

### 执行网络诊断
```go
result, err := networkService.PingTest("*******", 4)
if err != nil {
    log.Printf("ping测试失败: %v", err)
    return
}

fmt.Printf("平均延迟: %v, 丢包率: %.2f%%\n", 
    result.AvgLatency, result.PacketLoss)
```

## 安全考虑

1. **权限控制**：网络配置操作需要管理员权限
2. **输入验证**：严格验证所有网络配置参数
3. **操作审计**：记录所有网络配置变更操作
4. **备份恢复**：配置变更前自动备份原配置

## 部署要求

- Linux操作系统（Ubuntu 18.04+, CentOS 7+）
- root权限或sudo权限
- 网络管理工具：ip, ifconfig, iptables等
- 系统服务：NetworkManager或systemd-networkd
