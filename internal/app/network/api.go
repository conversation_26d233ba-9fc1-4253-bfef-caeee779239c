package network

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"ecp/internal/app/network/models"
)

// API 网络管理API
type API struct {
	service NetworkService
}

// NewAPI 创建网络API实例
func NewAPI(service NetworkService) *API {
	return &API{
		service: service,
	}
}

// RegisterRoutes 注册路由
func (api *API) RegisterRoutes(r *gin.RouterGroup) {
	network := r.Group("/network")
	{
		// 网络配置API
		network.GET("/interfaces", api.GetInterfaces)
		network.POST("/interfaces/:name/config", api.ConfigureInterface)
		network.GET("/dns", api.GetDNSConfig)
		network.POST("/dns", api.SetDNSConfig)
		network.GET("/routes", api.GetRoutes)
		network.POST("/routes", api.AddRoute)
		network.PUT("/routes", api.UpdateRoute)
		network.DELETE("/routes", api.DeleteRoute)

		// 网络监控API
		network.GET("/status", api.GetNetworkStatus)
		network.GET("/traffic", api.GetTrafficStats)
		network.GET("/quality", api.GetNetworkQuality)
		network.GET("/connections", api.GetConnectionStats)

		// 防火墙API
		firewall := network.Group("/firewall")
		{
			firewall.GET("/status", api.GetFirewallStatus)
			firewall.GET("/rules", api.GetFirewallRules)
			firewall.POST("/rules", api.AddFirewallRule)
			firewall.DELETE("/rules/:id", api.DeleteFirewallRule)
		}

		// 网络诊断API
		diagnostic := network.Group("/diagnostic")
		{
			diagnostic.POST("/ping", api.PingTest)
			diagnostic.POST("/traceroute", api.TracerouteTest)
			diagnostic.POST("/portscan", api.PortScan)
			diagnostic.POST("/diagnose", api.DiagnoseNetwork)

			// SSE流式输出API
			diagnostic.GET("/ping/stream", api.PingTestStream)
			diagnostic.GET("/traceroute/stream", api.TracerouteTestStream)

			// 网络监控SSE API
			network.GET("/monitor/stream", api.NetworkMonitorStream)
		}
	}
}

// GetInterfaces 获取网络接口列表
func (api *API) GetInterfaces(c *gin.Context) {
	interfaces, err := api.service.GetNetworkInterfaces()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取网络接口失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    interfaces,
	})
}

// ConfigureInterface 配置网络接口
func (api *API) ConfigureInterface(c *gin.Context) {
	name := c.Param("name")

	var config models.NetworkInterfaceConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	config.Name = name
	if err := api.service.ConfigureInterface(&config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "配置网络接口失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "网络接口配置成功",
	})
}

// GetDNSConfig 获取DNS配置
func (api *API) GetDNSConfig(c *gin.Context) {
	config, err := api.service.GetDNSConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取DNS配置失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// SetDNSConfig 设置DNS配置
func (api *API) SetDNSConfig(c *gin.Context) {
	var config models.DNSConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := api.service.SetDNSConfig(&config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "设置DNS配置失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "DNS配置设置成功",
	})
}

// GetRoutes 获取路由表
func (api *API) GetRoutes(c *gin.Context) {
	routes, err := api.service.GetRoutes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取路由表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    routes,
	})
}

// AddRoute 添加路由
func (api *API) AddRoute(c *gin.Context) {
	var route models.Route
	if err := c.ShouldBindJSON(&route); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := api.service.AddRoute(&route); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "添加路由失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "路由添加成功",
	})
}

// UpdateRoute 更新路由
func (api *API) UpdateRoute(c *gin.Context) {
	var route models.Route
	if err := c.ShouldBindJSON(&route); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := api.service.UpdateRoute(&route); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新路由失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "路由更新成功",
	})
}

// DeleteRoute 删除路由
func (api *API) DeleteRoute(c *gin.Context) {
	var route models.Route
	if err := c.ShouldBindJSON(&route); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := api.service.DeleteRoute(&route); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除路由失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "路由删除成功",
	})
}

// GetNetworkStatus 获取网络状态
func (api *API) GetNetworkStatus(c *gin.Context) {
	status, err := api.service.GetNetworkStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取网络状态失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    status,
	})
}

// GetTrafficStats 获取流量统计
func (api *API) GetTrafficStats(c *gin.Context) {
	stats, err := api.service.GetTrafficStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取流量统计失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// GetNetworkQuality 获取网络质量
func (api *API) GetNetworkQuality(c *gin.Context) {
	target := c.Query("target")
	if target == "" {
		target = "8.8.8.8" // 默认目标
	}

	quality, err := api.service.GetNetworkQuality(target)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取网络质量失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    quality,
	})
}

// GetConnectionStats 获取连接统计
func (api *API) GetConnectionStats(c *gin.Context) {
	stats, err := api.service.GetConnectionStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取连接统计失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// GetFirewallStatus 获取防火墙状态
func (api *API) GetFirewallStatus(c *gin.Context) {
	status, err := api.service.GetFirewallStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取防火墙状态失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    status,
	})
}

// GetFirewallRules 获取防火墙规则
func (api *API) GetFirewallRules(c *gin.Context) {
	rules, err := api.service.GetFirewallRules()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取防火墙规则失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rules,
	})
}

// AddFirewallRule 添加防火墙规则
func (api *API) AddFirewallRule(c *gin.Context) {
	var rule models.FirewallRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := api.service.AddFirewallRule(&rule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "添加防火墙规则失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "防火墙规则添加成功",
	})
}

// DeleteFirewallRule 删除防火墙规则
func (api *API) DeleteFirewallRule(c *gin.Context) {
	ruleID := c.Param("id")

	if err := api.service.DeleteFirewallRule(ruleID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除防火墙规则失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "防火墙规则删除成功",
	})
}

// PingTest 执行ping测试
func (api *API) PingTest(c *gin.Context) {
	var req struct {
		Target string `json:"target" binding:"required"`
		Count  int    `json:"count"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if req.Count <= 0 {
		req.Count = 4 // 默认ping 4次
	}

	result, err := api.service.PingTest(req.Target, req.Count)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "ping测试失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// PingTestStream Ping测试流式输出
func (api *API) PingTestStream(c *gin.Context) {
	target := c.Query("target")
	countStr := c.DefaultQuery("count", "4")

	if target == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "目标地址不能为空",
		})
		return
	}

	count, err := strconv.Atoi(countStr)
	if err != nil {
		count = 4
	}

	// 设置SSE头
	c.Header("Content-Type", "text/event-stream; charset=utf-8")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// 创建输出通道
	outputChan := make(chan string, 100)
	resultChan := make(chan interface{}, 1)
	errorChan := make(chan error, 1)

	// 启动ping测试
	go api.service.PingTestStream(target, count, outputChan, resultChan, errorChan)

	// 发送SSE数据
	for {
		select {
		case output := <-outputChan:
			api.sendSSEMessage(c, "output", output)
			c.Writer.Flush()
		case result := <-resultChan:
			api.sendSSEMessage(c, "result", result)
			c.Writer.Flush()
			api.sendSSEMessage(c, "close", "")
			return
		case err := <-errorChan:
			api.sendSSEMessage(c, "error", err.Error())
			c.Writer.Flush()
			api.sendSSEMessage(c, "close", "")
			return
		case <-c.Request.Context().Done():
			return
		}
	}
}

// TracerouteTest 执行路由跟踪测试
func (api *API) TracerouteTest(c *gin.Context) {
	var req struct {
		Target string `json:"target" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	result, err := api.service.TracerouteTest(req.Target)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "路由跟踪测试失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// TracerouteTestStream 路由跟踪测试流式输出
func (api *API) TracerouteTestStream(c *gin.Context) {
	target := c.Query("target")

	if target == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "目标地址不能为空",
		})
		return
	}

	// 设置SSE头
	c.Header("Content-Type", "text/event-stream; charset=utf-8")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// 创建输出通道
	outputChan := make(chan string, 100)
	resultChan := make(chan interface{}, 1)
	errorChan := make(chan error, 1)

	// 启动traceroute测试
	go api.service.TracerouteTestStream(target, outputChan, resultChan, errorChan)

	// 发送SSE数据
	for {
		select {
		case output := <-outputChan:
			api.sendSSEMessage(c, "output", output)
			c.Writer.Flush()
		case result := <-resultChan:
			api.sendSSEMessage(c, "result", result)
			c.Writer.Flush()
			api.sendSSEMessage(c, "close", "")
			return
		case err := <-errorChan:
			api.sendSSEMessage(c, "error", err.Error())
			c.Writer.Flush()
			api.sendSSEMessage(c, "close", "")
			return
		case <-c.Request.Context().Done():
			return
		}
	}
}

// PortScan 执行端口扫描
func (api *API) PortScan(c *gin.Context) {
	var req struct {
		Target string `json:"target" binding:"required"`
		Ports  []int  `json:"ports" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	result, err := api.service.PortScan(req.Target, req.Ports)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "端口扫描失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// DiagnoseNetwork 执行网络诊断
func (api *API) DiagnoseNetwork(c *gin.Context) {
	result, err := api.service.DiagnoseNetwork()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "网络诊断失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// NetworkMonitorStream 网络监控数据流式输出
func (api *API) NetworkMonitorStream(c *gin.Context) {
	// 设置SSE头
	c.Header("Content-Type", "text/event-stream; charset=utf-8")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("X-Accel-Buffering", "no") // 禁用Nginx缓冲

	// 创建输出通道
	dataChan := make(chan interface{}, 100)
	errorChan := make(chan error, 10)
	done := make(chan bool, 1)

	// 创建一个带超时的上下文，设置为10分钟
	ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Minute)
	defer cancel()

	// 替换原始请求的上下文
	c.Request = c.Request.WithContext(ctx)

	fmt.Printf("[SSE] 启动网络监控数据收集，客户端: %s\n", c.ClientIP())

	// 启动网络监控数据收集
	go api.service.NetworkMonitorStream(dataChan, errorChan, done)

	// 发送初始连接确认消息
	api.sendSSEMessage(c, "connected", map[string]interface{}{
		"message": "网络监控连接已建立",
		"time":    time.Now().Unix(),
	})
	c.Writer.Flush()
	fmt.Printf("[SSE] 连接确认消息已发送\n")

	// 发送SSE数据
	messageCount := 0
	lastMessageTime := time.Now()

	for {
		select {
		case data := <-dataChan:
			messageCount++
			lastMessageTime = time.Now()
			fmt.Printf("[SSE] 收到数据消息 #%d，准备发送\n", messageCount)

			// 发送数据消息
			api.sendSSEMessage(c, "data", data)
			c.Writer.Flush()

			fmt.Printf("[SSE] 数据消息 #%d 发送完成\n", messageCount)

		case err := <-errorChan:
			fmt.Printf("[SSE] 收到错误消息: %v\n", err)
			api.sendSSEMessage(c, "error", err.Error())
			c.Writer.Flush()
			fmt.Printf("[SSE] 错误消息发送完成\n")

		case <-done:
			fmt.Printf("[SSE] 收到完成信号，关闭连接\n")
			api.sendSSEMessage(c, "close", "")
			return

		case <-c.Request.Context().Done():
			// 客户端断开连接，通知停止数据收集
			contextErr := c.Request.Context().Err()
			fmt.Printf("[SSE] 客户端断开连接，原因: %v，通知停止数据收集\n", contextErr)
			fmt.Printf("[SSE] 连接持续时间: %v，发送消息数: %d\n", time.Since(lastMessageTime), messageCount)

			// 确保done channel能够发送信号
			select {
			case done <- true:
				fmt.Printf("[SSE] 停止信号已发送\n")
			default:
				fmt.Printf("[SSE] 停止信号发送失败，channel可能已满\n")
			}

			// 等待一小段时间确保goroutine能够接收到信号
			time.Sleep(100 * time.Millisecond)
			return
		}
	}
}

// sendSSEMessage 发送SSE消息
func (api *API) sendSSEMessage(c *gin.Context, eventType string, data interface{}) {
	message := map[string]interface{}{
		"type":    eventType,
		"content": data,
	}

	jsonData, _ := json.Marshal(message)
	c.Writer.WriteString(fmt.Sprintf("data: %s\n\n", jsonData))
}
