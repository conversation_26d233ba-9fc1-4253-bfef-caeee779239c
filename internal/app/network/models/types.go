package models

import (
	"time"
)

// NetworkInterface 网络接口信息
type NetworkInterface struct {
	Name         string    `json:"name"`          // 接口名称
	DisplayName  string    `json:"display_name"`  // 显示名称
	Type         string    `json:"type"`          // 接口类型 (ethernet, wireless, loopback)
	Status       string    `json:"status"`        // 状态 (up, down, unknown)
	IPAddress    string    `json:"ip_address"`    // IP地址
	Netmask      string    `json:"netmask"`       // 子网掩码
	Gateway      string    `json:"gateway"`       // 网关
	MACAddress   string    `json:"mac_address"`   // MAC地址
	MTU          int       `json:"mtu"`           // MTU大小
	Speed        string    `json:"speed"`         // 连接速度
	Duplex       string    `json:"duplex"`        // 双工模式
	Method       string    `json:"method"`        // 配置方法 (static, dhcp)
	DNSServers   []string  `json:"dns_servers"`   // DNS服务器
	LastUpdated  time.Time `json:"last_updated"`  // 最后更新时间
}

// NetworkInterfaceConfig 网络接口配置
type NetworkInterfaceConfig struct {
	Name       string   `json:"name" validate:"required"`       // 接口名称
	Method     string   `json:"method" validate:"required"`     // 配置方法 (static, dhcp)
	IPAddress  string   `json:"ip_address"`                     // IP地址
	Netmask    string   `json:"netmask"`                        // 子网掩码
	Gateway    string   `json:"gateway"`                        // 网关
	DNSServers []string `json:"dns_servers"`                    // DNS服务器
	MTU        int      `json:"mtu"`                            // MTU大小
	AutoStart  bool     `json:"auto_start"`                     // 开机自启
}

// DNSConfig DNS配置
type DNSConfig struct {
	Nameservers   []string `json:"nameservers"`    // DNS服务器列表
	SearchDomains []string `json:"search_domains"` // 搜索域
	Options       []string `json:"options"`        // DNS选项
}

// Route 路由信息
type Route struct {
	Destination string `json:"destination"` // 目标网络
	Gateway     string `json:"gateway"`     // 网关
	Interface   string `json:"interface"`   // 接口
	Metric      int    `json:"metric"`      // 优先级
	Type        string `json:"type"`        // 路由类型 (default, static)
}

// NetworkStatus 网络状态
type NetworkStatus struct {
	Interfaces     []*NetworkInterface `json:"interfaces"`      // 网络接口状态
	DefaultGateway string              `json:"default_gateway"` // 默认网关
	DNSServers     []string            `json:"dns_servers"`     // DNS服务器
	InternetAccess bool                `json:"internet_access"` // 互联网访问状态
	LastChecked    time.Time           `json:"last_checked"`    // 最后检查时间
}

// TrafficStats 流量统计
type TrafficStats struct {
	Interface string    `json:"interface"`  // 接口名称
	RxBytes   uint64    `json:"rx_bytes"`   // 接收字节数
	TxBytes   uint64    `json:"tx_bytes"`   // 发送字节数
	RxPackets uint64    `json:"rx_packets"` // 接收包数
	TxPackets uint64    `json:"tx_packets"` // 发送包数
	RxErrors  uint64    `json:"rx_errors"`  // 接收错误数
	TxErrors  uint64    `json:"tx_errors"`  // 发送错误数
	RxDropped uint64    `json:"rx_dropped"` // 接收丢弃数
	TxDropped uint64    `json:"tx_dropped"` // 发送丢弃数
	RxSpeed   float64   `json:"rx_speed"`   // 接收速度 (bytes/s)
	TxSpeed   float64   `json:"tx_speed"`   // 发送速度 (bytes/s)
	Timestamp time.Time `json:"timestamp"`  // 时间戳
}

// NetworkQuality 网络质量
type NetworkQuality struct {
	Target     string        `json:"target"`      // 测试目标
	Latency    time.Duration `json:"latency"`     // 延迟
	PacketLoss float64       `json:"packet_loss"` // 丢包率
	Jitter     time.Duration `json:"jitter"`      // 抖动
	Bandwidth  float64       `json:"bandwidth"`   // 带宽 (Mbps)
	TestTime   time.Time     `json:"test_time"`   // 测试时间
}

// ConnectionStats 连接统计
type ConnectionStats struct {
	TCPConnections   int       `json:"tcp_connections"`    // TCP连接数
	UDPConnections   int       `json:"udp_connections"`    // UDP连接数
	ListeningPorts   []int     `json:"listening_ports"`    // 监听端口
	EstablishedConns int       `json:"established_conns"`  // 已建立连接数
	TimeWaitConns    int       `json:"time_wait_conns"`    // TIME_WAIT连接数
	LastUpdated      time.Time `json:"last_updated"`       // 最后更新时间
}

// FirewallRule 防火墙规则
type FirewallRule struct {
	ID          string    `json:"id"`           // 规则ID
	Chain       string    `json:"chain"`        // 链名称 (INPUT, OUTPUT, FORWARD)
	Target      string    `json:"target"`       // 目标动作 (ACCEPT, DROP, REJECT)
	Protocol    string    `json:"protocol"`     // 协议 (tcp, udp, icmp, all)
	Source      string    `json:"source"`       // 源地址
	Destination string    `json:"destination"`  // 目标地址
	SourcePort  string    `json:"source_port"`  // 源端口
	DestPort    string    `json:"dest_port"`    // 目标端口
	Interface   string    `json:"interface"`    // 接口
	Comment     string    `json:"comment"`      // 注释
	Enabled     bool      `json:"enabled"`      // 是否启用
	CreatedAt   time.Time `json:"created_at"`   // 创建时间
}

// FirewallStatus 防火墙状态
type FirewallStatus struct {
	Enabled       bool      `json:"enabled"`        // 是否启用
	DefaultPolicy string    `json:"default_policy"` // 默认策略
	RuleCount     int       `json:"rule_count"`     // 规则数量
	LastUpdated   time.Time `json:"last_updated"`   // 最后更新时间
}

// PingResult ping测试结果
type PingResult struct {
	Target      string        `json:"target"`       // 测试目标
	PacketsSent int           `json:"packets_sent"` // 发送包数
	PacketsRecv int           `json:"packets_recv"` // 接收包数
	PacketLoss  float64       `json:"packet_loss"`  // 丢包率
	MinLatency  time.Duration `json:"min_latency"`  // 最小延迟
	MaxLatency  time.Duration `json:"max_latency"`  // 最大延迟
	AvgLatency  time.Duration `json:"avg_latency"`  // 平均延迟
	StdDev      time.Duration `json:"std_dev"`      // 标准差
	TestTime    time.Time     `json:"test_time"`    // 测试时间
}

// TracerouteResult traceroute测试结果
type TracerouteResult struct {
	Target   string           `json:"target"`    // 测试目标
	Hops     []*TracerouteHop `json:"hops"`      // 跳数信息
	TestTime time.Time        `json:"test_time"` // 测试时间
}

// TracerouteHop traceroute跳数信息
type TracerouteHop struct {
	Hop      int           `json:"hop"`      // 跳数
	IP       string        `json:"ip"`       // IP地址
	Hostname string        `json:"hostname"` // 主机名
	Latency  time.Duration `json:"latency"`  // 延迟
}

// PortScanResult 端口扫描结果
type PortScanResult struct {
	Target    string    `json:"target"`     // 扫描目标
	OpenPorts []int     `json:"open_ports"` // 开放端口
	TestTime  time.Time `json:"test_time"`  // 测试时间
}

// DiagnosticResult 诊断结果
type DiagnosticResult struct {
	Issues      []string  `json:"issues"`      // 发现的问题
	Suggestions []string  `json:"suggestions"` // 建议
	TestTime    time.Time `json:"test_time"`   // 测试时间
}
