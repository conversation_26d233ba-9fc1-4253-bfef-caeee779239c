package network

import (
	"ecp/internal/app/network/models"
)

// NetworkService 网络管理服务接口
type NetworkService interface {
	// 网络配置管理
	GetNetworkInterfaces() ([]*models.NetworkInterface, error)
	ConfigureInterface(config *models.NetworkInterfaceConfig) error
	GetDNSConfig() (*models.DNSConfig, error)
	SetDNSConfig(config *models.DNSConfig) error
	GetRoutes() ([]*models.Route, error)
	AddRoute(route *models.Route) error
	UpdateRoute(route *models.Route) error
	DeleteRoute(route *models.Route) error

	// 网络监控
	GetNetworkStatus() (*models.NetworkStatus, error)
	GetTrafficStats() ([]*models.TrafficStats, error)
	GetNetworkQuality(target string) (*models.NetworkQuality, error)
	GetConnectionStats() (*models.ConnectionStats, error)

	// 防火墙管理
	GetFirewallRules() ([]*models.FirewallRule, error)
	AddFirewallRule(rule *models.FirewallRule) error
	DeleteFirewallRule(ruleID string) error
	GetFirewallStatus() (*models.FirewallStatus, error)

	// 网络诊断
	PingTest(target string, count int) (*models.PingResult, error)
	TracerouteTest(target string) (*models.TracerouteResult, error)
	PortScan(target string, ports []int) (*models.PortScanResult, error)
	DiagnoseNetwork() (*models.DiagnosticResult, error)

	// 流式诊断
	PingTestStream(target string, count int, outputChan chan<- string, resultChan chan<- interface{}, errorChan chan<- error)
	TracerouteTestStream(target string, outputChan chan<- string, resultChan chan<- interface{}, errorChan chan<- error)

	// 网络监控流式数据
	NetworkMonitorStream(dataChan chan<- interface{}, errorChan chan<- error, done <-chan bool)
}
