package network

import (
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"ecp/internal/app/network/config"
	"ecp/internal/app/network/diagnostic"
	"ecp/internal/app/network/firewall"
	"ecp/internal/app/network/models"
	"ecp/internal/app/network/monitor"
)

// networkService 网络管理服务实现
type networkService struct {
	configManager     *config.Manager
	monitorManager    *monitor.Manager
	firewallManager   *firewall.Manager
	diagnosticManager *diagnostic.Manager
	mutex             sync.RWMutex
}

// NetworkConfig 网络服务配置
type NetworkConfig struct {
	// 监控配置
	MonitorInterval   time.Duration `yaml:"monitor_interval"`    // 监控间隔
	TrafficSampleRate int           `yaml:"traffic_sample_rate"` // 流量采样率

	// 诊断配置
	PingTimeout       time.Duration `yaml:"ping_timeout"`        // ping超时时间
	TracerouteMaxHops int           `yaml:"traceroute_max_hops"` // traceroute最大跳数
	PortScanTimeout   time.Duration `yaml:"port_scan_timeout"`   // 端口扫描超时

	// 防火墙配置
	IptablesPath string `yaml:"iptables_path"` // iptables路径
	BackupPath   string `yaml:"backup_path"`   // 备份路径

	// 系统配置
	NetworkConfigPath string `yaml:"network_config_path"` // 网络配置路径
	DNSConfigPath     string `yaml:"dns_config_path"`     // DNS配置路径
	RouteTablePath    string `yaml:"route_table_path"`    // 路由表路径
}

// NewNetworkService 创建网络管理服务
func NewNetworkService(cfg NetworkConfig) NetworkService {
	// 设置默认配置
	if cfg.MonitorInterval == 0 {
		cfg.MonitorInterval = 5 * time.Second
	}
	if cfg.TrafficSampleRate == 0 {
		cfg.TrafficSampleRate = 1
	}
	if cfg.PingTimeout == 0 {
		cfg.PingTimeout = 5 * time.Second
	}
	if cfg.TracerouteMaxHops == 0 {
		cfg.TracerouteMaxHops = 30
	}
	if cfg.PortScanTimeout == 0 {
		cfg.PortScanTimeout = 10 * time.Second
	}
	if cfg.IptablesPath == "" {
		cfg.IptablesPath = "/sbin/iptables"
	}
	if cfg.BackupPath == "" {
		cfg.BackupPath = "/etc/ecp/network/backup"
	}
	if cfg.NetworkConfigPath == "" {
		cfg.NetworkConfigPath = "/etc/netplan"
	}
	if cfg.DNSConfigPath == "" {
		cfg.DNSConfigPath = "/etc/resolv.conf"
	}
	if cfg.RouteTablePath == "" {
		cfg.RouteTablePath = "/proc/net/route"
	}

	return &networkService{
		configManager:     config.NewManager(cfg.NetworkConfigPath, cfg.DNSConfigPath),
		monitorManager:    monitor.NewManager(cfg.MonitorInterval, cfg.TrafficSampleRate),
		firewallManager:   firewall.NewManager(cfg.IptablesPath, cfg.BackupPath),
		diagnosticManager: diagnostic.NewManager(cfg.PingTimeout, cfg.TracerouteMaxHops, cfg.PortScanTimeout),
	}
}

// GetNetworkInterfaces 获取网络接口列表
func (ns *networkService) GetNetworkInterfaces() ([]*models.NetworkInterface, error) {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()

	return ns.configManager.GetInterfaces()
}

// ConfigureInterface 配置网络接口
func (ns *networkService) ConfigureInterface(config *models.NetworkInterfaceConfig) error {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()

	log.Printf("配置网络接口: %s", config.Name)

	// 验证配置
	if err := ns.validateInterfaceConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	// 备份当前配置
	if err := ns.configManager.BackupConfig(config.Name); err != nil {
		log.Printf("备份配置失败: %v", err)
	}

	// 应用配置
	if err := ns.configManager.ConfigureInterface(config); err != nil {
		return fmt.Errorf("配置接口失败: %v", err)
	}

	log.Printf("网络接口 %s 配置成功", config.Name)
	return nil
}

// GetDNSConfig 获取DNS配置
func (ns *networkService) GetDNSConfig() (*models.DNSConfig, error) {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()

	return ns.configManager.GetDNSConfig()
}

// SetDNSConfig 设置DNS配置
func (ns *networkService) SetDNSConfig(config *models.DNSConfig) error {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()

	log.Printf("设置DNS配置: %v", config.Nameservers)

	// 验证DNS服务器
	for _, dnsServer := range config.Nameservers {
		if err := ns.validateIPAddress(dnsServer); err != nil {
			return fmt.Errorf("无效的DNS服务器地址 %s: %v", dnsServer, err)
		}
	}

	return ns.configManager.SetDNSConfig(config)
}

// GetRoutes 获取路由表
func (ns *networkService) GetRoutes() ([]*models.Route, error) {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()

	return ns.configManager.GetRoutes()
}

// AddRoute 添加路由
func (ns *networkService) AddRoute(route *models.Route) error {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()

	log.Printf("添加路由: %s -> %s", route.Destination, route.Gateway)

	// 验证路由配置
	if err := ns.validateRoute(route); err != nil {
		return fmt.Errorf("路由验证失败: %v", err)
	}

	return ns.configManager.AddRoute(route)
}

// UpdateRoute 更新路由
func (ns *networkService) UpdateRoute(route *models.Route) error {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()

	log.Printf("更新路由: %s -> %s", route.Destination, route.Gateway)

	// 验证路由配置
	if err := ns.validateRoute(route); err != nil {
		return fmt.Errorf("路由验证失败: %v", err)
	}

	return ns.configManager.UpdateRoute(route)
}

// DeleteRoute 删除路由
func (ns *networkService) DeleteRoute(route *models.Route) error {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()

	log.Printf("删除路由: %s -> %s", route.Destination, route.Gateway)
	return ns.configManager.DeleteRoute(route)
}

// GetNetworkStatus 获取网络状态
func (ns *networkService) GetNetworkStatus() (*models.NetworkStatus, error) {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()

	return ns.monitorManager.GetNetworkStatus()
}

// GetTrafficStats 获取流量统计
func (ns *networkService) GetTrafficStats() ([]*models.TrafficStats, error) {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()

	return ns.monitorManager.GetTrafficStats()
}

// GetNetworkQuality 获取网络质量
func (ns *networkService) GetNetworkQuality(target string) (*models.NetworkQuality, error) {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()

	return ns.monitorManager.GetNetworkQuality(target)
}

// GetConnectionStats 获取连接统计
func (ns *networkService) GetConnectionStats() (*models.ConnectionStats, error) {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()

	return ns.monitorManager.GetConnectionStats()
}

// GetFirewallRules 获取防火墙规则
func (ns *networkService) GetFirewallRules() ([]*models.FirewallRule, error) {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()

	return ns.firewallManager.GetRules()
}

// AddFirewallRule 添加防火墙规则
func (ns *networkService) AddFirewallRule(rule *models.FirewallRule) error {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()

	log.Printf("添加防火墙规则: %s %s:%s -> %s:%s",
		rule.Protocol, rule.Source, rule.SourcePort,
		rule.Destination, rule.DestPort)

	// 验证规则
	if err := ns.validateFirewallRule(rule); err != nil {
		return fmt.Errorf("防火墙规则验证失败: %v", err)
	}

	return ns.firewallManager.AddRule(rule)
}

// DeleteFirewallRule 删除防火墙规则
func (ns *networkService) DeleteFirewallRule(ruleID string) error {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()

	log.Printf("删除防火墙规则: %s", ruleID)
	return ns.firewallManager.DeleteRule(ruleID)
}

// GetFirewallStatus 获取防火墙状态
func (ns *networkService) GetFirewallStatus() (*models.FirewallStatus, error) {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()

	return ns.firewallManager.GetStatus()
}

// PingTest 执行ping测试
func (ns *networkService) PingTest(target string, count int) (*models.PingResult, error) {
	return ns.diagnosticManager.Ping(target, count)
}

// PingTestStream 执行ping测试流式输出
func (ns *networkService) PingTestStream(target string, count int, outputChan chan<- string, resultChan chan<- interface{}, errorChan chan<- error) {
	ns.diagnosticManager.PingStream(target, count, outputChan, resultChan, errorChan)
}

// TracerouteTest 执行traceroute测试
func (ns *networkService) TracerouteTest(target string) (*models.TracerouteResult, error) {
	return ns.diagnosticManager.Traceroute(target)
}

// TracerouteTestStream 执行traceroute测试流式输出
func (ns *networkService) TracerouteTestStream(target string, outputChan chan<- string, resultChan chan<- interface{}, errorChan chan<- error) {
	ns.diagnosticManager.TracerouteStream(target, outputChan, resultChan, errorChan)
}

// NetworkMonitorStream 网络监控数据流式输出
func (ns *networkService) NetworkMonitorStream(dataChan chan<- interface{}, errorChan chan<- error, done <-chan bool) {
	ticker := time.NewTicker(2 * time.Second) // 每2秒推送一次数据
	defer func() {
		ticker.Stop()
		fmt.Printf("[Monitor] 定时器已停止\n")
	}()

	// 心跳定时器，每10秒发送一次心跳，防止连接超时
	heartbeatTicker := time.NewTicker(10 * time.Second)
	defer func() {
		heartbeatTicker.Stop()
		fmt.Printf("[Monitor] 心跳定时器已停止\n")
	}()

	fmt.Printf("[Monitor] 开始网络监控流，PID: %d\n", os.Getpid())

	// 立即发送第一次数据
	monitorData, err := ns.collectNetworkMonitorData()
	if err != nil {
		fmt.Printf("[Monitor] 初始数据收集失败: %v\n", err)
		select {
		case errorChan <- err:
		case <-done:
			fmt.Printf("[Monitor] 收到停止信号，退出初始化\n")
			return
		}
	} else {
		fmt.Printf("[Monitor] 初始数据收集成功，发送数据\n")
		select {
		case dataChan <- monitorData:
			fmt.Printf("[Monitor] 初始数据发送成功\n")
		case <-done:
			fmt.Printf("[Monitor] 收到停止信号，退出初始化\n")
			return
		}
	}

	fmt.Printf("[Monitor] 进入定时循环\n")

	// 使用标志来确保循环能够正确退出
	running := true

	for running {
		select {
		case <-ticker.C:
			if !running {
				fmt.Printf("[Monitor] 检测到停止标志，跳过数据收集\n")
				continue
			}

			fmt.Printf("[Monitor] 定时器触发，开始收集数据\n")

			// 收集和发送数据
			if !ns.collectAndSendData(dataChan, errorChan, done) {
				fmt.Printf("[Monitor] 数据收集过程中收到停止信号，退出循环\n")
				running = false
				return
			}

		case <-heartbeatTicker.C:
			if !running {
				fmt.Printf("[Monitor] 检测到停止标志，跳过心跳发送\n")
				continue
			}

			fmt.Printf("[Monitor] 发送心跳消息\n")

			// 发送心跳消息
			heartbeat := map[string]interface{}{
				"type":      "heartbeat",
				"timestamp": time.Now().Unix(),
				"message":   "连接正常",
			}

			select {
			case dataChan <- heartbeat:
				fmt.Printf("[Monitor] 心跳消息发送成功\n")
			case <-done:
				fmt.Printf("[Monitor] 心跳发送时收到停止信号，退出循环\n")
				running = false
				return
			}

		case <-done:
			fmt.Printf("[Monitor] 收到停止信号，设置停止标志并退出监控循环\n")
			running = false
			return
		}
	}

	fmt.Printf("[Monitor] 监控循环已完全退出\n")
}

// collectAndSendData 收集并发送监控数据，返回false表示应该停止
func (ns *networkService) collectAndSendData(dataChan chan<- interface{}, errorChan chan<- error, done <-chan bool) bool {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("[Monitor] 数据收集发生panic: %v\n", r)
			select {
			case errorChan <- fmt.Errorf("数据收集panic: %v", r):
			case <-done:
				fmt.Printf("[Monitor] 在panic处理中收到停止信号\n")
			}
		}
	}()

	// 收集网络监控数据
	monitorData, err := ns.collectNetworkMonitorData()
	if err != nil {
		fmt.Printf("[Monitor] 数据收集失败: %v\n", err)
		select {
		case errorChan <- err:
			fmt.Printf("[Monitor] 错误已发送，继续下次收集\n")
			return true // 继续运行
		case <-done:
			fmt.Printf("[Monitor] 在错误处理中收到停止信号\n")
			return false // 停止运行
		}
	}

	fmt.Printf("[Monitor] 数据收集成功，准备发送\n")

	// 发送数据
	select {
	case dataChan <- monitorData:
		fmt.Printf("[Monitor] 数据发送成功\n")
		return true // 继续运行
	case <-done:
		fmt.Printf("[Monitor] 在数据发送中收到停止信号\n")
		return false // 停止运行
	}
}

// collectNetworkMonitorData 收集网络监控数据
func (ns *networkService) collectNetworkMonitorData() (map[string]interface{}, error) {
	fmt.Printf("[Monitor] 开始收集网络监控数据\n")

	// 同时获取网络状态、流量统计和网络质量
	statusResult, err := ns.GetNetworkStatus()
	if err != nil {
		fmt.Printf("[Monitor] 获取网络状态失败: %v\n", err)
		return nil, fmt.Errorf("获取网络状态失败: %w", err)
	}
	fmt.Printf("[Monitor] 网络状态获取成功，接口数量: %d\n", len(statusResult.Interfaces))

	trafficStats, err := ns.GetTrafficStats()
	if err != nil {
		fmt.Printf("[Monitor] 获取流量统计失败: %v\n", err)
		return nil, fmt.Errorf("获取流量统计失败: %w", err)
	}
	fmt.Printf("[Monitor] 流量统计获取成功，接口数量: %d\n", len(trafficStats))

	// 网络质量获取失败不影响其他数据，使用默认值
	qualityResult := &models.NetworkQuality{
		Target:  "8.8.8.8",
		Latency: 0,
	}

	// 同步获取网络质量，但设置超时
	qualityDone := make(chan bool, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("[Monitor] 网络质量检测panic: %v\n", r)
			}
			qualityDone <- true
		}()

		if result, err := ns.GetNetworkQuality("8.8.8.8"); err == nil {
			qualityResult.Latency = result.Latency
			fmt.Printf("[Monitor] 网络质量获取成功: %d ms\n", result.Latency/1000000)
		} else {
			fmt.Printf("[Monitor] 网络质量获取失败: %v\n", err)
		}
	}()

	// 等待网络质量检测完成，但最多等待1秒
	select {
	case <-qualityDone:
		fmt.Printf("[Monitor] 网络质量检测完成\n")
	case <-time.After(1 * time.Second):
		fmt.Printf("[Monitor] 网络质量检测超时，使用默认值\n")
	}

	// 合并状态信息和流量统计
	statusMap := make(map[string]*models.NetworkInterface)
	for _, iface := range statusResult.Interfaces {
		statusMap[iface.Name] = iface
	}

	var interfaceStats []map[string]interface{}
	var totalRxSpeed, totalTxSpeed float64

	for _, stat := range trafficStats {
		statusInfo := statusMap[stat.Interface]

		interfaceData := map[string]interface{}{
			"interface":  stat.Interface,
			"rx_bytes":   stat.RxBytes,
			"tx_bytes":   stat.TxBytes,
			"rx_packets": stat.RxPackets,
			"tx_packets": stat.TxPackets,
			"rx_errors":  stat.RxErrors,
			"tx_errors":  stat.TxErrors,
			"rx_speed":   stat.RxSpeed,
			"tx_speed":   stat.TxSpeed,
		}

		if statusInfo != nil {
			interfaceData["status"] = statusInfo.Status
			interfaceData["display_name"] = statusInfo.DisplayName
			interfaceData["type"] = statusInfo.Type
			interfaceData["ip_address"] = statusInfo.IPAddress
			interfaceData["mac_address"] = statusInfo.MACAddress
			interfaceData["method"] = statusInfo.Method
		} else {
			interfaceData["status"] = "unknown"
			interfaceData["display_name"] = stat.Interface
			interfaceData["type"] = "unknown"
			interfaceData["ip_address"] = ""
			interfaceData["mac_address"] = ""
			interfaceData["method"] = "unknown"
		}

		interfaceStats = append(interfaceStats, interfaceData)
		totalRxSpeed += stat.RxSpeed
		totalTxSpeed += stat.TxSpeed
	}

	return map[string]interface{}{
		"timestamp":       time.Now().Unix(),
		"upload_speed":    totalTxSpeed,
		"download_speed":  totalRxSpeed,
		"latency":         qualityResult.Latency / 1000000, // 转换为毫秒
		"interface_stats": interfaceStats,
	}, nil
}

// PortScan 执行端口扫描
func (ns *networkService) PortScan(target string, ports []int) (*models.PortScanResult, error) {
	return ns.diagnosticManager.PortScan(target, ports)
}

// DiagnoseNetwork 执行网络诊断
func (ns *networkService) DiagnoseNetwork() (*models.DiagnosticResult, error) {
	return ns.diagnosticManager.DiagnoseNetwork()
}

// 验证函数
func (ns *networkService) validateInterfaceConfig(config *models.NetworkInterfaceConfig) error {
	if config.Name == "" {
		return fmt.Errorf("接口名称不能为空")
	}

	if config.Method != "static" && config.Method != "dhcp" {
		return fmt.Errorf("无效的配置方法: %s", config.Method)
	}

	if config.Method == "static" {
		if config.IPAddress == "" {
			return fmt.Errorf("静态配置必须指定IP地址")
		}
		if err := ns.validateIPAddress(config.IPAddress); err != nil {
			return fmt.Errorf("无效的IP地址: %v", err)
		}
	}

	return nil
}

func (ns *networkService) validateRoute(route *models.Route) error {
	if route.Destination == "" {
		return fmt.Errorf("目标网络不能为空")
	}
	if route.Gateway == "" {
		return fmt.Errorf("网关不能为空")
	}
	return nil
}

func (ns *networkService) validateFirewallRule(rule *models.FirewallRule) error {
	if rule.Chain == "" {
		return fmt.Errorf("链名称不能为空")
	}
	if rule.Target == "" {
		return fmt.Errorf("目标动作不能为空")
	}
	return nil
}

func (ns *networkService) validateIPAddress(ip string) error {
	// 简单的IP地址验证，实际项目中应使用更严格的验证
	if ip == "" {
		return fmt.Errorf("IP地址不能为空")
	}
	return nil
}
