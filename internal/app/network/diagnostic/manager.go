package diagnostic

import (
	"bufio"
	"fmt"
	"math"
	"net"
	"os/exec"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"ecp/internal/app/network/models"
)

// Manager 网络诊断管理器
type Manager struct {
	pingTimeout       time.Duration
	tracerouteMaxHops int
	portScanTimeout   time.Duration
	mutex             sync.RWMutex
}

// NewManager 创建诊断管理器
func NewManager(pingTimeout time.Duration, tracerouteMaxHops int, portScanTimeout time.Duration) *Manager {
	return &Manager{
		pingTimeout:       pingTimeout,
		tracerouteMaxHops: tracerouteMaxHops,
		portScanTimeout:   portScanTimeout,
	}
}

// Ping 执行ping测试
func (m *Manager) Ping(target string, count int) (*models.PingResult, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 构建ping命令
	var cmd *exec.Cmd
	if isWindows() {
		cmd = exec.Command("ping", "-n", strconv.Itoa(count), target)
	} else {
		cmd = exec.Command("ping", "-c", strconv.Itoa(count), target)
	}

	// 设置超时
	cmd.WaitDelay = m.pingTimeout

	// 执行命令
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("ping命令执行失败: %v", err)
	}

	// 解析ping输出
	result, err := m.parsePingOutput(string(output), target, count)
	if err != nil {
		return nil, fmt.Errorf("解析ping输出失败: %v", err)
	}

	return result, nil
}

// Traceroute 执行路由跟踪
func (m *Manager) Traceroute(target string) (*models.TracerouteResult, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 构建traceroute命令
	var cmd *exec.Cmd
	if isWindows() {
		cmd = exec.Command("tracert", target)
	} else {
		cmd = exec.Command("traceroute", target)
	}

	// 执行命令
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("traceroute命令执行失败: %v", err)
	}

	// 解析输出
	hops := m.parseTracerouteOutput(string(output))

	return &models.TracerouteResult{
		Target:   target,
		Hops:     hops,
		TestTime: time.Now(),
	}, nil
}

// PortScan 执行端口扫描
func (m *Manager) PortScan(target string, ports []int) (*models.PortScanResult, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var openPorts []int

	// 并发扫描端口
	portChan := make(chan int, len(ports))
	resultChan := make(chan int, len(ports))

	// 启动工作协程
	for i := 0; i < 10; i++ { // 限制并发数
		go func() {
			for port := range portChan {
				if m.isPortOpen(target, port) {
					resultChan <- port
				} else {
					resultChan <- -1
				}
			}
		}()
	}

	// 发送端口到通道
	go func() {
		for _, port := range ports {
			portChan <- port
		}
		close(portChan)
	}()

	// 收集结果
	for i := 0; i < len(ports); i++ {
		if port := <-resultChan; port > 0 {
			openPorts = append(openPorts, port)
		}
	}

	return &models.PortScanResult{
		Target:    target,
		OpenPorts: openPorts,
		TestTime:  time.Now(),
	}, nil
}

// DiagnoseNetwork 执行网络诊断
func (m *Manager) DiagnoseNetwork() (*models.DiagnosticResult, error) {
	var issues []string
	var suggestions []string

	// 检查网络连接
	if !m.checkInternetConnectivity() {
		issues = append(issues, "无法连接到互联网")
		suggestions = append(suggestions, "检查网络连接和DNS设置")
	}

	// 检查DNS解析
	if !m.checkDNSResolution() {
		issues = append(issues, "DNS解析失败")
		suggestions = append(suggestions, "检查DNS服务器配置")
	}

	// 检查默认网关
	if !m.checkDefaultGateway() {
		issues = append(issues, "无法访问默认网关")
		suggestions = append(suggestions, "检查网关配置和网络连接")
	}

	return &models.DiagnosticResult{
		Issues:      issues,
		Suggestions: suggestions,
		TestTime:    time.Now(),
	}, nil
}

// isWindows 检查是否为Windows系统
func isWindows() bool {
	return runtime.GOOS == "windows"
}

// parsePingOutput 解析ping输出
func (m *Manager) parsePingOutput(output, target string, count int) (*models.PingResult, error) {
	lines := strings.Split(output, "\n")

	result := &models.PingResult{
		Target:      target,
		PacketsSent: count,
		PacketsRecv: 0,
		PacketLoss:  100.0,
		TestTime:    time.Now(),
	}

	var latencies []time.Duration

	// 解析每行输出
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// Linux/Unix ping输出格式
		if strings.Contains(line, "time=") {
			result.PacketsRecv++

			// 提取延迟时间
			re := regexp.MustCompile(`time=([0-9.]+)\s*ms`)
			matches := re.FindStringSubmatch(line)
			if len(matches) > 1 {
				if latency, err := strconv.ParseFloat(matches[1], 64); err == nil {
					latencies = append(latencies, time.Duration(latency*float64(time.Millisecond)))
				}
			}
		}

		// Windows ping输出格式
		if strings.Contains(line, "时间=") || strings.Contains(line, "time<") {
			result.PacketsRecv++

			// 提取延迟时间
			re := regexp.MustCompile(`时间[<=]([0-9]+)ms|time[<=]([0-9]+)ms`)
			matches := re.FindStringSubmatch(line)
			for i := 1; i < len(matches); i++ {
				if matches[i] != "" {
					if latency, err := strconv.ParseFloat(matches[i], 64); err == nil {
						latencies = append(latencies, time.Duration(latency*float64(time.Millisecond)))
					}
					break
				}
			}
		}
	}

	// 计算统计信息
	if len(latencies) > 0 {
		result.PacketLoss = float64(count-result.PacketsRecv) / float64(count) * 100

		// 计算最小、最大、平均延迟
		min := latencies[0]
		max := latencies[0]
		var sum time.Duration

		for _, latency := range latencies {
			if latency < min {
				min = latency
			}
			if latency > max {
				max = latency
			}
			sum += latency
		}

		result.MinLatency = min
		result.MaxLatency = max
		result.AvgLatency = sum / time.Duration(len(latencies))

		// 计算标准差
		var variance float64
		avgMs := float64(result.AvgLatency) / float64(time.Millisecond)
		for _, latency := range latencies {
			latencyMs := float64(latency) / float64(time.Millisecond)
			variance += (latencyMs - avgMs) * (latencyMs - avgMs)
		}
		variance /= float64(len(latencies))
		result.StdDev = time.Duration(math.Sqrt(variance) * float64(time.Millisecond))
	}

	return result, nil
}

// parseTracerouteOutput 解析traceroute输出
func (m *Manager) parseTracerouteOutput(output string) []*models.TracerouteHop {
	var hops []*models.TracerouteHop
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 解析跳数信息（简化版本）
		fields := strings.Fields(line)
		if len(fields) >= 3 {
			if hopNum, err := strconv.Atoi(fields[0]); err == nil {
				hop := &models.TracerouteHop{
					Hop: hopNum,
				}

				// 查找IP地址
				for _, field := range fields[1:] {
					if net.ParseIP(field) != nil {
						hop.IP = field
						break
					}
				}

				// 查找延迟时间
				re := regexp.MustCompile(`([0-9.]+)\s*ms`)
				matches := re.FindStringSubmatch(line)
				if len(matches) > 1 {
					if latency, err := strconv.ParseFloat(matches[1], 64); err == nil {
						hop.Latency = time.Duration(latency * float64(time.Millisecond))
					}
				}

				hops = append(hops, hop)
			}
		}
	}

	return hops
}

// isPortOpen 检查端口是否开放
func (m *Manager) isPortOpen(host string, port int) bool {
	address := fmt.Sprintf("%s:%d", host, port)
	conn, err := net.DialTimeout("tcp", address, m.portScanTimeout)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// checkInternetConnectivity 检查互联网连接
func (m *Manager) checkInternetConnectivity() bool {
	conn, err := net.DialTimeout("tcp", "*******:53", 3*time.Second)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// checkDNSResolution 检查DNS解析
func (m *Manager) checkDNSResolution() bool {
	_, err := net.LookupHost("google.com")
	return err == nil
}

// checkDefaultGateway 检查默认网关
func (m *Manager) checkDefaultGateway() bool {
	// 简化实现：尝试连接常见的网关地址
	gateways := []string{"***********", "***********", "********"}

	for _, gateway := range gateways {
		conn, err := net.DialTimeout("tcp", gateway+":80", 2*time.Second)
		if err == nil {
			conn.Close()
			return true
		}
	}

	return false
}

// PingStream 执行ping测试流式输出
func (m *Manager) PingStream(target string, count int, outputChan chan<- string, resultChan chan<- interface{}, errorChan chan<- error) {
	defer close(outputChan)
	defer close(resultChan)
	defer close(errorChan)

	// 构建ping命令
	var cmd *exec.Cmd
	if isWindows() {
		// Windows下使用chcp 65001设置UTF-8编码，然后执行ping命令
		cmd = exec.Command("cmd", "/c", "chcp 65001 >nul && ping", "-n", strconv.Itoa(count), target)
	} else {
		cmd = exec.Command("ping", "-c", strconv.Itoa(count), target)
	}

	// 创建管道获取实时输出
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		errorChan <- fmt.Errorf("创建输出管道失败: %v", err)
		return
	}

	// 启动命令
	if err := cmd.Start(); err != nil {
		errorChan <- fmt.Errorf("启动ping命令失败: %v", err)
		return
	}

	// 读取实时输出
	scanner := bufio.NewScanner(stdout)
	var allOutput []string

	for scanner.Scan() {
		line := scanner.Text()
		allOutput = append(allOutput, line)
		outputChan <- line
	}

	// 等待命令完成
	if err := cmd.Wait(); err != nil {
		errorChan <- fmt.Errorf("ping命令执行失败: %v", err)
		return
	}

	// 解析最终结果
	result, err := m.parsePingOutput(strings.Join(allOutput, "\n"), target, count)
	if err != nil {
		errorChan <- fmt.Errorf("解析ping输出失败: %v", err)
		return
	}

	resultChan <- result
}

// TracerouteStream 执行traceroute测试流式输出
func (m *Manager) TracerouteStream(target string, outputChan chan<- string, resultChan chan<- interface{}, errorChan chan<- error) {
	defer close(outputChan)
	defer close(resultChan)
	defer close(errorChan)

	// 构建traceroute命令
	var cmd *exec.Cmd
	if isWindows() {
		// Windows下使用chcp 65001设置UTF-8编码，然后执行tracert命令
		cmd = exec.Command("cmd", "/c", "chcp 65001 >nul && tracert", target)
	} else {
		cmd = exec.Command("traceroute", target)
	}

	// 创建管道获取实时输出
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		errorChan <- fmt.Errorf("创建输出管道失败: %v", err)
		return
	}

	// 启动命令
	if err := cmd.Start(); err != nil {
		errorChan <- fmt.Errorf("启动traceroute命令失败: %v", err)
		return
	}

	// 读取实时输出
	scanner := bufio.NewScanner(stdout)
	var allOutput []string

	for scanner.Scan() {
		line := scanner.Text()
		allOutput = append(allOutput, line)
		outputChan <- line
	}

	// 等待命令完成
	if err := cmd.Wait(); err != nil {
		errorChan <- fmt.Errorf("traceroute命令执行失败: %v", err)
		return
	}

	// 解析最终结果
	hops := m.parseTracerouteOutput(strings.Join(allOutput, "\n"))
	result := &models.TracerouteResult{
		Target:   target,
		Hops:     hops,
		TestTime: time.Now(),
	}

	resultChan <- result
}
