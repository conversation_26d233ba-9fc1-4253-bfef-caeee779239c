package monitor

import (
	"bufio"
	"fmt"
	"net"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"ecp/internal/app/network/models"

	psnet "github.com/shirou/gopsutil/v3/net"
)

// Manager 网络监控管理器
type Manager struct {
	interval     time.Duration
	sampleRate   int
	mutex        sync.RWMutex
	lastStats    map[string]*models.TrafficStats
	isMonitoring bool
	stopChan     chan struct{}
}

// NewManager 创建监控管理器
func NewManager(interval time.Duration, sampleRate int) *Manager {
	return &Manager{
		interval:     interval,
		sampleRate:   sampleRate,
		lastStats:    make(map[string]*models.TrafficStats),
		isMonitoring: false,
		stopChan:     make(chan struct{}),
	}
}

// StartMonitoring 开始监控
func (m *Manager) StartMonitoring() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.isMonitoring {
		return
	}

	m.isMonitoring = true
	go m.monitorLoop()
}

// StopMonitoring 停止监控
func (m *Manager) StopMonitoring() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.isMonitoring {
		return
	}

	m.isMonitoring = false
	close(m.stopChan)
	m.stopChan = make(chan struct{})
}

// monitorLoop 监控循环
func (m *Manager) monitorLoop() {
	ticker := time.NewTicker(m.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.updateTrafficStats()
		case <-m.stopChan:
			return
		}
	}
}

// updateTrafficStats 更新流量统计
func (m *Manager) updateTrafficStats() {
	// 直接调用GetTrafficStats来更新统计信息
	m.GetTrafficStats()
}

// GetNetworkStatus 获取网络状态
func (m *Manager) GetNetworkStatus() (*models.NetworkStatus, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 获取网络接口信息
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("获取网络接口失败: %v", err)
	}

	var networkInterfaces []*models.NetworkInterface
	for _, iface := range interfaces {
		// 获取接口状态
		status := m.getInterfaceStatus(iface)

		// 获取IP地址信息
		var ipAddress string
		addrs, err := iface.Addrs()
		if err == nil {
			for _, addr := range addrs {
				if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
					if ipnet.IP.To4() != nil {
						ipAddress = ipnet.IP.String()
						break
					}
				}
			}
		}

		// 获取接口详细信息
		netInterface := &models.NetworkInterface{
			Name:        iface.Name,
			DisplayName: m.getInterfaceDisplayName(iface.Name),
			Type:        getInterfaceType(iface.Name),
			Status:      status,
			IPAddress:   ipAddress,
			MACAddress:  iface.HardwareAddr.String(),
			MTU:         iface.MTU,
			Method:      m.getInterfaceMethod(iface.Name, ipAddress),
			DNSServers:  m.getInterfaceDNSServers(iface.Name),
			LastUpdated: time.Now(),
		}

		networkInterfaces = append(networkInterfaces, netInterface)
	}

	// 获取默认网关
	defaultGateway := m.getDefaultGateway()

	// 获取DNS服务器
	dnsServers := m.getDNSServers()

	// 检查互联网连接
	internetAccess := m.checkInternetAccess()

	return &models.NetworkStatus{
		Interfaces:     networkInterfaces,
		DefaultGateway: defaultGateway,
		DNSServers:     dnsServers,
		InternetAccess: internetAccess,
		LastChecked:    time.Now(),
	}, nil
}

// getInterfaceType 获取接口类型
func getInterfaceType(name string) string {
	if strings.HasPrefix(name, "eth") || strings.HasPrefix(name, "en") {
		return "ethernet"
	} else if strings.HasPrefix(name, "wlan") || strings.HasPrefix(name, "wl") {
		return "wireless"
	} else if strings.HasPrefix(name, "lo") {
		return "loopback"
	}
	return "unknown"
}

// getDefaultGateway 获取默认网关
func (m *Manager) getDefaultGateway() string {
	file, err := os.Open("/proc/net/route")
	if err != nil {
		return ""
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		fields := strings.Fields(line)
		if len(fields) >= 3 && fields[1] == "00000000" {
			// 解析网关IP
			gatewayHex := fields[2]
			if len(gatewayHex) == 8 {
				ip := make([]byte, 4)
				for i := 0; i < 4; i++ {
					val, _ := strconv.ParseUint(gatewayHex[i*2:(i+1)*2], 16, 8)
					ip[3-i] = byte(val)
				}
				return fmt.Sprintf("%d.%d.%d.%d", ip[0], ip[1], ip[2], ip[3])
			}
		}
	}
	return ""
}

// getDNSServers 获取DNS服务器
func (m *Manager) getDNSServers() []string {
	var dnsServers []string
	file, err := os.Open("/etc/resolv.conf")
	if err != nil {
		return dnsServers
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if strings.HasPrefix(line, "nameserver") {
			fields := strings.Fields(line)
			if len(fields) >= 2 {
				dnsServers = append(dnsServers, fields[1])
			}
		}
	}
	return dnsServers
}

// checkInternetAccess 检查互联网连接
func (m *Manager) checkInternetAccess() bool {
	conn, err := net.DialTimeout("tcp", "*******:53", 3*time.Second)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// getInterfaceStatus 获取接口状态
func (m *Manager) getInterfaceStatus(iface net.Interface) string {
	// 检查接口是否启用
	if iface.Flags&net.FlagUp == 0 {
		return "down"
	}

	// 检查接口是否有有效的IP地址
	addrs, err := iface.Addrs()
	if err != nil {
		return "unknown"
	}

	hasValidIP := false
	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				hasValidIP = true
				break
			}
		}
	}

	if !hasValidIP && iface.Name != "lo" && !strings.HasPrefix(iface.Name, "lo") {
		return "no-ip"
	}

	return "up"
}

// getInterfaceDisplayName 获取接口显示名称
func (m *Manager) getInterfaceDisplayName(name string) string {
	// 可以根据接口名称返回更友好的显示名称
	displayNames := map[string]string{
		"lo":     "本地回环",
		"eth0":   "以太网 1",
		"eth1":   "以太网 2",
		"wlan0":  "无线网络 1",
		"wlan1":  "无线网络 2",
		"enp0s3": "以太网适配器",
		"wlp2s0": "无线网络适配器",
	}

	if displayName, exists := displayNames[name]; exists {
		return displayName
	}

	// 根据接口类型生成显示名称
	if strings.HasPrefix(name, "eth") || strings.HasPrefix(name, "en") {
		return fmt.Sprintf("以太网 (%s)", name)
	} else if strings.HasPrefix(name, "wlan") || strings.HasPrefix(name, "wl") {
		return fmt.Sprintf("无线网络 (%s)", name)
	} else if strings.HasPrefix(name, "lo") {
		return fmt.Sprintf("本地回环 (%s)", name)
	}

	return name
}

// getInterfaceMethod 获取接口配置方法
func (m *Manager) getInterfaceMethod(name, ipAddress string) string {
	// 简单的启发式判断
	if ipAddress == "" {
		return "none"
	}

	// 检查是否是静态IP（通过检查常见的静态IP模式）
	if strings.HasPrefix(ipAddress, "192.168.") ||
		strings.HasPrefix(ipAddress, "10.") ||
		strings.HasPrefix(ipAddress, "172.") {
		// 进一步检查是否可能是DHCP分配的
		// 这里可以添加更复杂的逻辑来判断
		return "dhcp" // 大多数情况下是DHCP
	}

	return "static"
}

// getInterfaceDNSServers 获取接口DNS服务器
func (m *Manager) getInterfaceDNSServers(name string) []string {
	// 返回系统DNS服务器（简化实现）
	// 实际应该根据接口获取特定的DNS配置
	return m.getDNSServers()
}

// GetTrafficStats 获取流量统计
func (m *Manager) GetTrafficStats() ([]*models.TrafficStats, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 使用gopsutil获取网络IO统计
	ioCounters, err := psnet.IOCounters(true)
	if err != nil {
		return nil, fmt.Errorf("获取网络IO统计失败: %v", err)
	}

	var stats []*models.TrafficStats
	for _, counter := range ioCounters {
		// 计算速度（需要与上次统计比较）
		var rxSpeed, txSpeed float64
		if lastStat, exists := m.lastStats[counter.Name]; exists {
			timeDiff := time.Since(lastStat.Timestamp).Seconds()
			if timeDiff > 0 {
				rxSpeed = float64(counter.BytesRecv-lastStat.RxBytes) / timeDiff
				txSpeed = float64(counter.BytesSent-lastStat.TxBytes) / timeDiff
			}
		}

		stat := &models.TrafficStats{
			Interface: counter.Name,
			RxBytes:   counter.BytesRecv,
			TxBytes:   counter.BytesSent,
			RxPackets: counter.PacketsRecv,
			TxPackets: counter.PacketsSent,
			RxErrors:  counter.Errin,
			TxErrors:  counter.Errout,
			RxDropped: counter.Dropin,
			TxDropped: counter.Dropout,
			RxSpeed:   rxSpeed,
			TxSpeed:   txSpeed,
			Timestamp: time.Now(),
		}

		// 更新缓存
		m.lastStats[counter.Name] = stat

		stats = append(stats, stat)
	}

	return stats, nil
}

// getInterfaceStats 获取接口统计信息
func (m *Manager) getInterfaceStats(interfaceName string) (*models.TrafficStats, error) {
	file, err := os.Open("/proc/net/dev")
	if err != nil {
		return nil, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if strings.Contains(line, interfaceName+":") {
			fields := strings.Fields(line)
			if len(fields) >= 17 {
				rxBytes, _ := strconv.ParseUint(fields[1], 10, 64)
				rxPackets, _ := strconv.ParseUint(fields[2], 10, 64)
				rxErrors, _ := strconv.ParseUint(fields[3], 10, 64)
				rxDropped, _ := strconv.ParseUint(fields[4], 10, 64)
				txBytes, _ := strconv.ParseUint(fields[9], 10, 64)
				txPackets, _ := strconv.ParseUint(fields[10], 10, 64)
				txErrors, _ := strconv.ParseUint(fields[11], 10, 64)
				txDropped, _ := strconv.ParseUint(fields[12], 10, 64)

				// 计算速度（需要与上次统计比较）
				var rxSpeed, txSpeed float64
				if lastStat, exists := m.lastStats[interfaceName]; exists {
					timeDiff := time.Since(lastStat.Timestamp).Seconds()
					if timeDiff > 0 {
						rxSpeed = float64(rxBytes-lastStat.RxBytes) / timeDiff
						txSpeed = float64(txBytes-lastStat.TxBytes) / timeDiff
					}
				}

				stat := &models.TrafficStats{
					Interface: interfaceName,
					RxBytes:   rxBytes,
					TxBytes:   txBytes,
					RxPackets: rxPackets,
					TxPackets: txPackets,
					RxErrors:  rxErrors,
					TxErrors:  txErrors,
					RxDropped: rxDropped,
					TxDropped: txDropped,
					RxSpeed:   rxSpeed,
					TxSpeed:   txSpeed,
					Timestamp: time.Now(),
				}

				// 更新缓存
				m.lastStats[interfaceName] = stat

				return stat, nil
			}
		}
	}

	return nil, fmt.Errorf("接口 %s 未找到", interfaceName)
}

// GetNetworkQuality 获取网络质量
func (m *Manager) GetNetworkQuality(target string) (*models.NetworkQuality, error) {
	// 执行ping测试来获取网络质量指标
	start := time.Now()

	// 简单的连接测试
	conn, err := net.DialTimeout("tcp", target+":80", 5*time.Second)
	latency := time.Since(start)

	var packetLoss float64
	if err != nil {
		packetLoss = 100.0
		latency = 0
	} else {
		conn.Close()
		packetLoss = 0.0
	}

	// 模拟抖动计算（实际应该通过多次测试计算）
	jitter := time.Duration(float64(latency) * 0.1)

	// 模拟带宽测试（实际应该通过数据传输测试）
	bandwidth := 100.0 // Mbps

	return &models.NetworkQuality{
		Target:     target,
		Latency:    latency,
		PacketLoss: packetLoss,
		Jitter:     jitter,
		Bandwidth:  bandwidth,
		TestTime:   time.Now(),
	}, nil
}

// GetConnectionStats 获取连接统计
func (m *Manager) GetConnectionStats() (*models.ConnectionStats, error) {
	tcpConns, udpConns, err := m.getConnectionCounts()
	if err != nil {
		return nil, err
	}

	listeningPorts, err := m.getListeningPorts()
	if err != nil {
		return nil, err
	}

	establishedConns, timeWaitConns, err := m.getTCPConnectionStates()
	if err != nil {
		return nil, err
	}

	return &models.ConnectionStats{
		TCPConnections:   tcpConns,
		UDPConnections:   udpConns,
		ListeningPorts:   listeningPorts,
		EstablishedConns: establishedConns,
		TimeWaitConns:    timeWaitConns,
		LastUpdated:      time.Now(),
	}, nil
}

// getConnectionCounts 获取连接数量
func (m *Manager) getConnectionCounts() (int, int, error) {
	tcpFile, err := os.Open("/proc/net/tcp")
	if err != nil {
		return 0, 0, err
	}
	defer tcpFile.Close()

	tcpCount := 0
	scanner := bufio.NewScanner(tcpFile)
	for scanner.Scan() {
		tcpCount++
	}
	tcpCount-- // 减去标题行

	udpFile, err := os.Open("/proc/net/udp")
	if err != nil {
		return tcpCount, 0, err
	}
	defer udpFile.Close()

	udpCount := 0
	scanner = bufio.NewScanner(udpFile)
	for scanner.Scan() {
		udpCount++
	}
	udpCount-- // 减去标题行

	return tcpCount, udpCount, nil
}

// getListeningPorts 获取监听端口
func (m *Manager) getListeningPorts() ([]int, error) {
	var ports []int

	file, err := os.Open("/proc/net/tcp")
	if err != nil {
		return ports, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Scan() // 跳过标题行

	for scanner.Scan() {
		line := scanner.Text()
		fields := strings.Fields(line)
		if len(fields) >= 4 && fields[3] == "0A" { // 0A = LISTEN状态
			localAddr := fields[1]
			if colonIndex := strings.LastIndex(localAddr, ":"); colonIndex != -1 {
				portHex := localAddr[colonIndex+1:]
				if port, err := strconv.ParseInt(portHex, 16, 32); err == nil {
					ports = append(ports, int(port))
				}
			}
		}
	}

	return ports, nil
}

// getTCPConnectionStates 获取TCP连接状态统计
func (m *Manager) getTCPConnectionStates() (int, int, error) {
	file, err := os.Open("/proc/net/tcp")
	if err != nil {
		return 0, 0, err
	}
	defer file.Close()

	established := 0
	timeWait := 0

	scanner := bufio.NewScanner(file)
	scanner.Scan() // 跳过标题行

	for scanner.Scan() {
		line := scanner.Text()
		fields := strings.Fields(line)
		if len(fields) >= 4 {
			state := fields[3]
			switch state {
			case "01": // ESTABLISHED
				established++
			case "06": // TIME_WAIT
				timeWait++
			}
		}
	}

	return established, timeWait, nil
}
