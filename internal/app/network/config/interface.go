package config

import (
	"bufio"
	"fmt"
	"io/ioutil"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"ecp/internal/app/network/models"
)

// Manager 网络配置管理器
type Manager struct {
	networkConfigPath string
	dnsConfigPath     string
}

// NewManager 创建配置管理器
func NewManager(networkConfigPath, dnsConfigPath string) *Manager {
	return &Manager{
		networkConfigPath: networkConfigPath,
		dnsConfigPath:     dnsConfigPath,
	}
}

// GetInterfaces 获取网络接口列表
func (m *Manager) GetInterfaces() ([]*models.NetworkInterface, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("获取网络接口失败: %v", err)
	}

	var result []*models.NetworkInterface
	for _, iface := range interfaces {
		netInterface, err := m.parseInterface(&iface)
		if err != nil {
			continue // 跳过解析失败的接口
		}
		result = append(result, netInterface)
	}

	return result, nil
}

// parseInterface 解析网络接口信息
func (m *Manager) parseInterface(iface *net.Interface) (*models.NetworkInterface, error) {
	netInterface := &models.NetworkInterface{
		Name:        iface.Name,
		DisplayName: iface.Name,
		MACAddress:  iface.HardwareAddr.String(),
		MTU:         iface.MTU,
		LastUpdated: time.Now(),
	}

	// 判断接口类型
	if strings.HasPrefix(iface.Name, "eth") {
		netInterface.Type = "ethernet"
	} else if strings.HasPrefix(iface.Name, "lo") {
		netInterface.Type = "loopback"
	} else {
		netInterface.Type = "unknown"
	}

	// 获取接口状态
	if iface.Flags&net.FlagUp != 0 {
		netInterface.Status = "up"
	} else {
		netInterface.Status = "down"
	}

	// 获取IP地址信息
	addrs, err := iface.Addrs()
	if err == nil && len(addrs) > 0 {
		for _, addr := range addrs {
			if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
				if ipnet.IP.To4() != nil {
					netInterface.IPAddress = ipnet.IP.String()
					netInterface.Netmask = net.IP(ipnet.Mask).String()
					break
				}
			}
		}
	}

	// 获取网关信息
	gateway, err := m.getDefaultGateway(iface.Name)
	if err == nil {
		netInterface.Gateway = gateway
	}

	// 获取连接速度和双工模式
	speed, duplex := m.getInterfaceSpeed(iface.Name)
	netInterface.Speed = speed
	netInterface.Duplex = duplex

	// 获取配置方法
	method := m.getConfigMethod(iface.Name)
	netInterface.Method = method

	// 获取DNS服务器
	dnsServers := m.getDNSServers(iface.Name)
	netInterface.DNSServers = dnsServers

	return netInterface, nil
}

// ConfigureInterface 配置网络接口
func (m *Manager) ConfigureInterface(config *models.NetworkInterfaceConfig) error {
	// 检测系统使用的网络管理工具
	if m.isNetplanAvailable() {
		return m.configureWithNetplan(config)
	} else if m.isNetworkManagerAvailable() {
		return m.configureWithNetworkManager(config)
	} else {
		return m.configureWithIfconfig(config)
	}
}

// isNetplanAvailable 检查是否可用netplan
func (m *Manager) isNetplanAvailable() bool {
	_, err := exec.LookPath("netplan")
	if err != nil {
		return false
	}

	// 检查netplan配置目录是否存在
	if _, err := os.Stat(m.networkConfigPath); os.IsNotExist(err) {
		return false
	}

	return true
}

// isNetworkManagerAvailable 检查是否可用NetworkManager
func (m *Manager) isNetworkManagerAvailable() bool {
	_, err := exec.LookPath("nmcli")
	return err == nil
}

// configureWithNetplan 使用netplan配置网络
func (m *Manager) configureWithNetplan(config *models.NetworkInterfaceConfig) error {
	configFile := filepath.Join(m.networkConfigPath, "01-ecp-config.yaml")

	var yamlContent string
	if config.Method == "dhcp" {
		yamlContent = fmt.Sprintf(`network:
  version: 2
  ethernets:
    %s:
      dhcp4: true
      dhcp6: false
`, config.Name)
	} else {
		yamlContent = fmt.Sprintf(`network:
  version: 2
  ethernets:
    %s:
      dhcp4: false
      dhcp6: false
      addresses:
        - %s/%s
      gateway4: %s
`, config.Name, config.IPAddress, m.cidrFromNetmask(config.Netmask), config.Gateway)

		if len(config.DNSServers) > 0 {
			yamlContent += "      nameservers:\n        addresses:\n"
			for _, dns := range config.DNSServers {
				yamlContent += fmt.Sprintf("          - %s\n", dns)
			}
		}
	}

	// 写入配置文件
	if err := ioutil.WriteFile(configFile, []byte(yamlContent), 0644); err != nil {
		return fmt.Errorf("写入netplan配置失败: %v", err)
	}

	// 应用配置
	cmd := exec.Command("netplan", "apply")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("应用netplan配置失败: %v", err)
	}

	return nil
}

// configureWithNetworkManager 使用NetworkManager配置网络
func (m *Manager) configureWithNetworkManager(config *models.NetworkInterfaceConfig) error {
	// 删除现有连接
	cmd := exec.Command("nmcli", "connection", "delete", config.Name)
	cmd.Run() // 忽略错误，可能连接不存在

	var args []string
	if config.Method == "dhcp" {
		args = []string{
			"connection", "add",
			"type", "ethernet",
			"con-name", config.Name,
			"ifname", config.Name,
			"autoconnect", "yes",
		}
	} else {
		args = []string{
			"connection", "add",
			"type", "ethernet",
			"con-name", config.Name,
			"ifname", config.Name,
			"autoconnect", "yes",
			"ip4", fmt.Sprintf("%s/%s", config.IPAddress, m.cidrFromNetmask(config.Netmask)),
			"gw4", config.Gateway,
		}

		if len(config.DNSServers) > 0 {
			args = append(args, "ipv4.dns", strings.Join(config.DNSServers, ","))
		}
	}

	cmd = exec.Command("nmcli", args...)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("配置NetworkManager失败: %v", err)
	}

	// 激活连接
	cmd = exec.Command("nmcli", "connection", "up", config.Name)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("激活网络连接失败: %v", err)
	}

	return nil
}

// configureWithIfconfig 使用ifconfig配置网络
func (m *Manager) configureWithIfconfig(config *models.NetworkInterfaceConfig) error {
	if config.Method == "dhcp" {
		// 使用dhclient获取IP
		cmd := exec.Command("dhclient", config.Name)
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("DHCP配置失败: %v", err)
		}
	} else {
		// 配置静态IP
		cmd := exec.Command("ifconfig", config.Name, config.IPAddress, "netmask", config.Netmask, "up")
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("配置静态IP失败: %v", err)
		}

		// 配置网关
		if config.Gateway != "" {
			cmd = exec.Command("route", "add", "default", "gw", config.Gateway, config.Name)
			if err := cmd.Run(); err != nil {
				return fmt.Errorf("配置网关失败: %v", err)
			}
		}
	}

	return nil
}

// BackupConfig 备份网络配置
func (m *Manager) BackupConfig(interfaceName string) error {
	backupDir := "/etc/ecp/network/backup"
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %v", err)
	}

	timestamp := time.Now().Format("20060102-150405")
	_ = filepath.Join(backupDir, fmt.Sprintf("%s-%s.backup", interfaceName, timestamp))

	// 这里应该备份相关的网络配置文件
	// 具体实现取决于系统使用的网络管理工具

	return nil
}

// GetDNSConfig 获取DNS配置
func (m *Manager) GetDNSConfig() (*models.DNSConfig, error) {
	file, err := os.Open(m.dnsConfigPath)
	if err != nil {
		return nil, fmt.Errorf("打开DNS配置文件失败: %v", err)
	}
	defer file.Close()

	config := &models.DNSConfig{
		Nameservers:   []string{},
		SearchDomains: []string{},
		Options:       []string{},
	}

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if strings.HasPrefix(line, "nameserver") {
			fields := strings.Fields(line)
			if len(fields) >= 2 {
				config.Nameservers = append(config.Nameservers, fields[1])
			}
		} else if strings.HasPrefix(line, "search") {
			fields := strings.Fields(line)
			if len(fields) >= 2 {
				config.SearchDomains = append(config.SearchDomains, fields[1:]...)
			}
		} else if strings.HasPrefix(line, "options") {
			fields := strings.Fields(line)
			if len(fields) >= 2 {
				config.Options = append(config.Options, fields[1:]...)
			}
		}
	}

	return config, nil
}

// SetDNSConfig 设置DNS配置
func (m *Manager) SetDNSConfig(config *models.DNSConfig) error {
	var content strings.Builder

	for _, ns := range config.Nameservers {
		content.WriteString(fmt.Sprintf("nameserver %s\n", ns))
	}

	if len(config.SearchDomains) > 0 {
		content.WriteString(fmt.Sprintf("search %s\n", strings.Join(config.SearchDomains, " ")))
	}

	if len(config.Options) > 0 {
		content.WriteString(fmt.Sprintf("options %s\n", strings.Join(config.Options, " ")))
	}

	return ioutil.WriteFile(m.dnsConfigPath, []byte(content.String()), 0644)
}

// GetRoutes 获取路由表
func (m *Manager) GetRoutes() ([]*models.Route, error) {
	cmd := exec.Command("ip", "route", "show")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("获取路由表失败: %v", err)
	}

	var routes []*models.Route
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		route := m.parseRoute(line)
		if route != nil {
			routes = append(routes, route)
		}
	}

	return routes, nil
}

// AddRoute 添加路由
func (m *Manager) AddRoute(route *models.Route) error {
	var cmd *exec.Cmd
	if route.Type == "default" {
		cmd = exec.Command("ip", "route", "add", "default", "via", route.Gateway, "dev", route.Interface)
	} else {
		cmd = exec.Command("ip", "route", "add", route.Destination, "via", route.Gateway, "dev", route.Interface)
	}

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("添加路由失败: %v", err)
	}

	return nil
}

// UpdateRoute 更新路由
func (m *Manager) UpdateRoute(route *models.Route) error {
	// 对于Linux系统，更新路由通常是先删除再添加
	// 这里简化处理，实际项目中可能需要更复杂的逻辑

	// 先尝试删除旧路由（忽略错误，因为可能不存在）
	_ = m.DeleteRoute(route)

	// 添加新路由
	return m.AddRoute(route)
}

// DeleteRoute 删除路由
func (m *Manager) DeleteRoute(route *models.Route) error {
	var cmd *exec.Cmd
	if route.Type == "default" {
		cmd = exec.Command("ip", "route", "del", "default", "via", route.Gateway, "dev", route.Interface)
	} else {
		cmd = exec.Command("ip", "route", "del", route.Destination, "via", route.Gateway, "dev", route.Interface)
	}

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("删除路由失败: %v", err)
	}

	return nil
}

// parseRoute 解析路由信息
func (m *Manager) parseRoute(line string) *models.Route {
	fields := strings.Fields(line)
	if len(fields) < 3 {
		return nil
	}

	route := &models.Route{}

	if fields[0] == "default" {
		route.Type = "default"
		route.Destination = "0.0.0.0/0"
		// 查找 via 关键字
		for i, field := range fields {
			if field == "via" && i+1 < len(fields) {
				route.Gateway = fields[i+1]
			} else if field == "dev" && i+1 < len(fields) {
				route.Interface = fields[i+1]
			}
		}
	} else {
		route.Type = "static"
		route.Destination = fields[0]
		// 查找 via 关键字
		for i, field := range fields {
			if field == "via" && i+1 < len(fields) {
				route.Gateway = fields[i+1]
			} else if field == "dev" && i+1 < len(fields) {
				route.Interface = fields[i+1]
			}
		}
	}

	return route
}

// 辅助函数
func (m *Manager) getDefaultGateway(interfaceName string) (string, error) {
	cmd := exec.Command("ip", "route", "show", "default")
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, interfaceName) {
			fields := strings.Fields(line)
			for i, field := range fields {
				if field == "via" && i+1 < len(fields) {
					return fields[i+1], nil
				}
			}
		}
	}

	return "", fmt.Errorf("未找到默认网关")
}

func (m *Manager) getInterfaceSpeed(interfaceName string) (string, string) {
	speedFile := fmt.Sprintf("/sys/class/net/%s/speed", interfaceName)
	duplexFile := fmt.Sprintf("/sys/class/net/%s/duplex", interfaceName)

	speed := "unknown"
	duplex := "unknown"

	if data, err := ioutil.ReadFile(speedFile); err == nil {
		speed = strings.TrimSpace(string(data)) + " Mbps"
	}

	if data, err := ioutil.ReadFile(duplexFile); err == nil {
		duplex = strings.TrimSpace(string(data))
	}

	return speed, duplex
}

func (m *Manager) getConfigMethod(interfaceName string) string {
	// 检查是否使用DHCP
	cmd := exec.Command("ps", "aux")
	output, err := cmd.Output()
	if err != nil {
		return "unknown"
	}

	if strings.Contains(string(output), "dhclient "+interfaceName) {
		return "dhcp"
	}

	return "static"
}

func (m *Manager) getDNSServers(interfaceName string) []string {
	file, err := os.Open("/etc/resolv.conf")
	if err != nil {
		return nil
	}
	defer file.Close()

	var dnsServers []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if strings.HasPrefix(line, "nameserver") {
			fields := strings.Fields(line)
			if len(fields) >= 2 {
				dnsServers = append(dnsServers, fields[1])
			}
		}
	}

	return dnsServers
}

func (m *Manager) cidrFromNetmask(netmask string) string {
	// 简单的子网掩码转CIDR实现
	switch netmask {
	case "255.255.255.0":
		return "24"
	case "255.255.0.0":
		return "16"
	case "255.0.0.0":
		return "8"
	default:
		return "24" // 默认值
	}
}
