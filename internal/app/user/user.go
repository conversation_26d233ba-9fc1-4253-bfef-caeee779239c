package user

import (
	"errors"
	"fmt"
	"time"

	"ecp/internal/pkg/config"
	"ecp/internal/pkg/database"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/mlogclub/simple/sqls"
	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct {
	db *gorm.DB
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	Mobile   string `json:"mobile"`
}

// TokenResponse 令牌响应
type TokenResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	TokenType    string    `json:"token_type"`
	UserID       int64     `json:"user_id"`
	Username     string    `json:"username"`
	Name         string    `json:"name"`
	Role         string    `json:"role"`
}

// Claims JWT声明
type Claims struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// NewUserService 创建用户服务
func NewUserService(db *gorm.DB) *UserService {
	return &UserService{
		db: db,
	}
}

// Login 用户登录
func (s *UserService) Login(req *LoginRequest) (*TokenResponse, error) {
	// 查找用户
	var user database.User
	err := s.db.Where("username = ? AND status = ?", req.Username, "active").First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在或已被禁用")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 验证密码
	if !user.CheckPassword(req.Password) {
		return nil, errors.New("密码错误")
	}

	// 更新最后登录时间
	s.db.Model(&user).Updates(map[string]interface{}{
		"last_login_at": time.Now(),
	})

	// 生成令牌
	return s.generateTokens(&user)
}

// Register 用户注册
func (s *UserService) Register(req *RegisterRequest) (*database.User, error) {
	// 检查用户名是否已存在
	var count int64
	if err := s.db.Model(&database.User{}).Where("username = ?", req.Username).Count(&count).Error; err != nil {
		return nil, fmt.Errorf("检查用户名失败: %w", err)
	}
	if count > 0 {
		return nil, errors.New("用户名已存在")
	}

	// 创建新用户
	user := &database.User{
		Username: req.Username,
		Password: req.Password,
		Name:     req.Name,
		Email:    req.Email,
		Mobile:   req.Mobile,
		Role:     "user", // 默认角色
		Status:   "active",
	}

	// 密码加密
	if err := user.HashPassword(); err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 保存到数据库
	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	return user, nil
}

// RefreshToken 刷新令牌
func (s *UserService) RefreshToken(refreshToken string) (*TokenResponse, error) {
	// 查找拥有此刷新令牌的用户
	var user database.User
	err := s.db.Where("refresh_token = ? AND status = ?", refreshToken, "active").First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("无效的刷新令牌")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 生成新令牌
	return s.generateTokens(&user)
}

// GetByID 根据ID获取用户
func (s *UserService) GetByID(id int64) (*database.User, error) {
	if id <= 0 {
		return nil, errors.New("无效的用户ID")
	}

	var user database.User
	err := s.db.First(&user, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return &user, nil
}

// Query 查询用户列表
func (s *UserService) Query(username, role, status string, page, pageSize int) ([]*database.User, *sqls.Paging, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	var users []*database.User
	paging := &sqls.Paging{
		Page:  page,
		Limit: pageSize,
		Total: 0,
	}

	// 构建查询
	query := s.db.Model(&database.User{})
	if username != "" {
		query = query.Where("username LIKE ?", "%"+username+"%")
	}
	if role != "" {
		query = query.Where("role = ?", role)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 计算总数
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return nil, paging, fmt.Errorf("计算用户总数失败: %w", err)
	}
	paging.Total = count

	// 查询数据
	if err := query.Order("id DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&users).Error; err != nil {
		return nil, paging, fmt.Errorf("查询用户列表失败: %w", err)
	}

	return users, paging, nil
}

// Update 更新用户
func (s *UserService) Update(user *database.User) error {
	if user.ID <= 0 {
		return errors.New("无效的用户ID")
	}

	// 查找原始用户
	var existingUser database.User
	if err := s.db.First(&existingUser, user.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 更新字段
	updates := map[string]interface{}{
		"name":   user.Name,
		"email":  user.Email,
		"mobile": user.Mobile,
		"role":   user.Role,
		"status": user.Status,
	}

	// 如果提供了新密码，更新密码
	if user.Password != "" {
		user.HashPassword()
		updates["password"] = user.Password
	}

	// 执行更新
	if err := s.db.Model(&existingUser).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新用户失败: %w", err)
	}

	return nil
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(userID int64, oldPassword, newPassword string) error {
	if userID <= 0 {
		return errors.New("无效的用户ID")
	}

	// 查找用户
	var user database.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 验证旧密码
	if !user.CheckPassword(oldPassword) {
		return errors.New("原密码错误")
	}

	// 更新密码
	user.Password = newPassword
	if err := user.HashPassword(); err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}

	// 保存到数据库
	if err := s.db.Model(&user).Update("password", user.Password).Error; err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	return nil
}

// Delete 删除用户
func (s *UserService) Delete(id int64) error {
	if id <= 0 {
		return errors.New("无效的用户ID")
	}

	// 检查是否为最后一个管理员
	var adminCount int64
	if err := s.db.Model(&database.User{}).Where("role = ? AND id != ?", "admin", id).Count(&adminCount).Error; err != nil {
		return fmt.Errorf("检查管理员数量失败: %w", err)
	}

	// 查询要删除的用户
	var user database.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 如果是最后一个管理员，禁止删除
	if user.Role == "admin" && adminCount == 0 {
		return errors.New("不能删除最后一个管理员")
	}

	// 执行删除
	if err := s.db.Delete(&database.User{}, id).Error; err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}

	return nil
}

// generateTokens 生成访问令牌和刷新令牌
func (s *UserService) generateTokens(user *database.User) (*TokenResponse, error) {
	cfg := config.GetConfig()

	// 设置访问令牌过期时间
	expiresAt := time.Now().Add(time.Duration(cfg.Auth.TokenExpiry) * time.Hour)

	// 创建JWT声明
	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "ecp",
			Subject:   fmt.Sprintf("%d", user.ID),
		},
	}

	// 生成访问令牌
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	accessToken, err := token.SignedString([]byte(cfg.Auth.JWTSecret))
	if err != nil {
		return nil, fmt.Errorf("生成访问令牌失败: %w", err)
	}

	// 生成刷新令牌
	refreshToken := uuid.New().String()

	// 更新用户的刷新令牌
	if err := s.db.Model(user).Update("refresh_token", refreshToken).Error; err != nil {
		return nil, fmt.Errorf("更新刷新令牌失败: %w", err)
	}

	return &TokenResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
		TokenType:    "Bearer",
		UserID:       user.ID,
		Username:     user.Username,
		Name:         user.Name,
		Role:         user.Role,
	}, nil
}

// ParseToken 解析JWT令牌
func (s *UserService) ParseToken(tokenString string) (*Claims, error) {
	// 解析令牌
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return []byte(config.GetConfig().Auth.JWTSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("解析令牌失败: %w", err)
	}

	// 验证令牌
	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的令牌")
}
