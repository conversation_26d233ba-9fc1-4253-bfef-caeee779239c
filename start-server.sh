#!/bin/bash

# ECP边缘计算平台服务器启动脚本

echo "=========================================="
echo "  ECP边缘计算平台服务器启动"
echo "=========================================="

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go 1.19+ 版本"
    echo "   下载地址: https://golang.org/dl/"
    exit 1
fi

echo "✅ Go 版本: $(go version)"

# 检查可执行文件是否存在
if [ ! -f "ecp-server" ]; then
    echo "❌ 可执行文件 ecp-server 不存在"
    echo "正在编译..."
    go build -o ecp-server ./cmd/ecp
    if [ $? -ne 0 ]; then
        echo "❌ 编译失败"
        exit 1
    fi
    echo "✅ 编译成功"
fi

# 检查配置文件
if [ ! -f "configs/config.yaml" ]; then
    echo "❌ 配置文件不存在: configs/config.yaml"
    exit 1
fi

# 创建数据目录
mkdir -p data/{videos,thumbnails,algorithms,alerts,images}

echo "✅ 环境检查完成"
echo ""
echo "🚀 启动服务器..."
echo "   配置文件: configs/config.yaml"
echo "   数据目录: data/"
echo "   Web访问: http://localhost:8080/app"
echo "   API文档: http://localhost:8080/swagger/index.html"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=========================================="

# 启动服务器
./ecp-server --config=configs/config.yaml

echo ""
echo "服务器已停止"
