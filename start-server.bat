@echo off
chcp 65001 >nul
title ECP边缘计算平台服务器

echo ==========================================
echo   ECP边缘计算平台服务器启动
echo ==========================================

:: 检查可执行文件是否存在
if not exist "ecp-server.exe" (
    echo ❌ 可执行文件 ecp-server.exe 不存在
    echo 正在编译...
    go build -o ecp-server.exe ./cmd/ecp
    if %errorlevel% neq 0 (
        echo ❌ 编译失败
        pause
        exit /b 1
    )
    echo ✅ 编译成功
)

:: 检查配置文件
if not exist "configs\config.yaml" (
    echo ❌ 配置文件不存在: configs\config.yaml
    pause
    exit /b 1
)

:: 创建数据目录
if not exist "data" mkdir data
if not exist "data\videos" mkdir data\videos
if not exist "data\thumbnails" mkdir data\thumbnails
if not exist "data\algorithms" mkdir data\algorithms
if not exist "data\alerts" mkdir data\alerts
if not exist "data\images" mkdir data\images

echo ✅ 环境检查完成
echo.
echo 🚀 启动服务器...
echo    配置文件: configs\config.yaml
echo    数据目录: data\
echo    Web访问: http://localhost:8080/app
echo    API文档: http://localhost:8080/swagger/index.html
echo.
echo 按 Ctrl+C 停止服务器
echo ==========================================

:: 启动服务器
ecp-server.exe --config=configs\config.yaml

echo.
echo 服务器已停止
pause
