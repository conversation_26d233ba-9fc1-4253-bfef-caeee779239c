# ECP告警联动系统 - Web界面部署指南

## 概述

本文档介绍如何部署ECP告警联动系统的Web管理界面，包括开发环境和生产环境的部署方式。

## 环境要求

### 基础环境
- **Node.js**: 18.0.0 或更高版本
- **pnpm**: 8.0.0 或更高版本（推荐）或 npm 9.0.0+
- **现代浏览器**: Chrome 88+, Firefox 85+, Safari 14+, Edge 88+

### 系统要求
- **内存**: 最少 2GB RAM
- **存储**: 最少 1GB 可用空间
- **网络**: 稳定的网络连接

## 开发环境部署

### 1. 快速启动（推荐）

**Linux/Mac 用户:**
```bash
# 克隆项目后进入web目录
cd web

# 给启动脚本执行权限
chmod +x start-linkage.sh

# 启动开发服务器
./start-linkage.sh
```

**Windows 用户:**
```cmd
# 进入web目录
cd web

# 运行启动脚本
start-linkage.bat
```

### 2. 手动启动

```bash
# 进入前端项目目录
cd web/app

# 安装依赖
pnpm install

# 复制环境配置文件
cp .env.example .env.development

# 编辑环境配置（可选）
# 修改 .env.development 中的API地址等配置

# 启动开发服务器
pnpm dev
```

### 3. 访问应用

开发服务器启动后，访问以下地址：
- **主页**: http://localhost:5173
- **联动监控**: http://localhost:5173/linkage/dashboard
- **联动规则**: http://localhost:5173/linkage/rules
- **联动设备**: http://localhost:5173/linkage/devices
- **执行记录**: http://localhost:5173/linkage/executions
- **系统设置**: http://localhost:5173/linkage/settings

## 生产环境部署

### 1. 构建生产版本

```bash
# 进入前端项目目录
cd web/app

# 安装依赖
pnpm install

# 创建生产环境配置
cp .env.example .env.production

# 编辑生产环境配置
# 修改 .env.production 中的配置，特别是API地址
# VITE_APP_BASE_API=http://your-api-server:port

# 构建生产版本
pnpm build
```

### 2. 部署方式

#### 方式一：集成到Go服务（推荐）

构建完成后，`dist` 目录会生成在 `web/` 目录下。Go服务会自动嵌入这些静态文件。

```bash
# 构建Go服务（在项目根目录）
go build -o ecp-server

# 启动服务
./ecp-server

# 访问Web界面
# http://your-server:port/app
```

#### 方式二：独立Web服务器部署

如果需要独立部署前端，可以使用Nginx等Web服务器：

**Nginx配置示例:**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/web/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://your-backend-server:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 方式三：Docker部署

**Dockerfile示例:**
```dockerfile
# 构建阶段
FROM node:18-alpine as builder

WORKDIR /app
COPY web/app/package*.json ./
COPY web/app/pnpm-lock.yaml ./

RUN npm install -g pnpm
RUN pnpm install

COPY web/app/ ./
RUN pnpm build

# 生产阶段
FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

**构建和运行:**
```bash
# 构建镜像
docker build -t ecp-linkage-web .

# 运行容器
docker run -d -p 80:80 ecp-linkage-web
```

## 环境配置

### 开发环境配置 (.env.development)
```env
VITE_APP_TITLE=ECP告警联动系统
VITE_APP_BASE_API=http://localhost:8080
VITE_APP_ENV=development
VITE_DEBUG=true
```

### 生产环境配置 (.env.production)
```env
VITE_APP_TITLE=ECP告警联动系统
VITE_APP_BASE_API=http://your-api-server:port
VITE_APP_ENV=production
VITE_DEBUG=false
```

## 性能优化

### 1. 构建优化
```bash
# 分析构建包大小
pnpm build --analyze

# 启用压缩
pnpm build --minify
```

### 2. 服务器优化

**Nginx优化配置:**
```nginx
# 启用gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# HTML文件不缓存
location ~* \.html$ {
    expires -1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
}
```

## 监控和日志

### 1. 应用监控

在生产环境中，建议配置以下监控：
- **性能监控**: 页面加载时间、API响应时间
- **错误监控**: JavaScript错误、API错误
- **用户行为**: 页面访问统计、功能使用情况

### 2. 日志配置

前端日志会输出到浏览器控制台，生产环境建议：
- 关闭调试日志：设置 `VITE_DEBUG=false`
- 配置错误上报服务
- 定期检查浏览器控制台错误

## 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   pnpm store prune
   rm -rf node_modules
   pnpm install
   ```

2. **构建失败**
   ```bash
   # 检查Node.js版本
   node -v  # 应该 >= 18.0.0
   
   # 检查内存使用
   # 如果内存不足，可以增加Node.js内存限制
   export NODE_OPTIONS="--max-old-space-size=4096"
   pnpm build
   ```

3. **API请求失败**
   - 检查 `.env` 文件中的API地址配置
   - 确认后端服务正常运行
   - 检查网络连接和防火墙设置

4. **页面空白**
   - 检查浏览器控制台错误
   - 确认静态资源路径正确
   - 检查路由配置

### 调试技巧

1. **开启调试模式**
   ```env
   VITE_DEBUG=true
   ```

2. **查看网络请求**
   - 打开浏览器开发者工具
   - 切换到Network标签
   - 检查API请求和响应

3. **查看应用状态**
   - 使用Vue DevTools浏览器扩展
   - 检查组件状态和数据流

## 安全考虑

### 1. 生产环境安全

- **HTTPS**: 生产环境必须使用HTTPS
- **CSP**: 配置内容安全策略
- **认证**: 确保API接口有适当的认证机制
- **权限**: 实施基于角色的访问控制

### 2. 配置安全

- 不要在前端代码中暴露敏感信息
- 使用环境变量管理配置
- 定期更新依赖包，修复安全漏洞

## 更新和维护

### 1. 依赖更新
```bash
# 检查过期依赖
pnpm outdated

# 更新依赖
pnpm update

# 更新主要版本（谨慎操作）
pnpm update --latest
```

### 2. 版本发布

1. 更新版本号：修改 `package.json` 中的版本
2. 构建生产版本：`pnpm build`
3. 测试功能完整性
4. 部署到生产环境
5. 验证部署结果

## 支持和帮助

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查浏览器控制台错误信息
3. 查看服务器日志
4. 参考项目的详细文档：`app/README-LINKAGE.md`

---

**注意**: 本部署指南基于当前版本编写，具体配置可能需要根据实际环境调整。
