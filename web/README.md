# Web前端集成说明

本目录用于存放边缘计算平台的Web前端代码，包含完整的告警联动系统管理界面。

## 目录结构

- `app/` - Vue3应用源码目录
- `dist/` - 前端构建输出目录（构建后生成）
- `start-linkage.sh` - Linux/Mac启动脚本
- `start-linkage.bat` - Windows启动脚本
- `static/` - 静态资源文件（可选）
- `template/` - HTML模板文件（可选）

## 告警联动系统功能

### 🎯 核心功能模块
- **联动监控**: 实时监控仪表板，展示系统状态和执行统计
- **联动规则**: 可视化规则配置，支持复杂条件和多设备联动
- **联动设备**: 多协议设备管理（MQTT、Modbus、RS485）
- **执行记录**: 完整的执行历史和统计分析
- **系统设置**: 系统参数配置和维护操作

### 📱 界面特性
- 响应式设计，支持桌面端、平板端、移动端
- 现代化UI设计，基于Element Plus组件库
- 实时数据更新和可视化图表
- 完整的权限控制和操作日志

## 开发说明

1. 前端使用Vue3 + Vite + TypeScript + Element Plus开发
2. 前端源码位于`app`目录下
3. 构建后的静态资源会输出到`dist`目录，并被嵌入到Go二进制文件中

## 快速启动

### 方式一：使用启动脚本（推荐）

**Linux/Mac用户:**
```bash
# 给脚本执行权限
chmod +x start-linkage.sh

# 启动开发服务器
./start-linkage.sh
```

**Windows用户:**
```cmd
# 双击运行或命令行执行
start-linkage.bat
```

### 方式二：手动启动

```bash
# 进入前端项目目录
cd app

# 安装依赖
pnpm install

# 开发模式
pnpm run dev

# 构建生产版本
pnpm run build
```

构建完成后，`dist`目录将生成在`web`目录下，而不是`app`目录内。

## 访问方式

### 开发环境
- 前端开发服务器: `http://localhost:5173`
- 后端API服务: `http://localhost:8080`
- 联动系统入口: `http://localhost:5173/linkage/dashboard`

### 生产环境
构建后的前端应用将通过Go服务提供，访问路径为：`http://服务器IP:端口/app`

## 联动系统页面导航

- **联动监控**: `/linkage/dashboard` - 实时监控和统计
- **联动规则**: `/linkage/rules` - 规则管理和配置
- **联动设备**: `/linkage/devices` - 设备管理和控制
- **执行记录**: `/linkage/executions` - 执行历史和分析
- **系统设置**: `/linkage/settings` - 系统配置和维护

## 技术要求

- Node.js 18+
- pnpm 包管理器
- 现代浏览器（Chrome 88+, Firefox 85+, Safari 14+, Edge 88+）

## 注意事项

1. 前端路由基础路径已设置为`/app/`
2. 前端API请求会被自动代理到后端API
3. 修改前端代码后需要重新构建并重启Go服务才能看到更改
4. 联动系统需要后端API支持，确保后端服务正常运行
5. 开发时建议使用启动脚本，会自动检查环境和依赖

## 更多文档

详细的功能说明和使用指南请参考：`app/README-LINKAGE.md`
