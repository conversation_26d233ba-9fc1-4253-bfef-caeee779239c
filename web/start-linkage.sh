#!/bin/bash

# ECP告警联动系统 Web界面启动脚本

echo "=========================================="
echo "  ECP告警联动系统 Web管理界面"
echo "=========================================="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18+ 版本"
    echo "   下载地址: https://nodejs.org/"
    exit 1
fi

# 检查Node.js版本
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 版本过低，需要 18+ 版本，当前版本: $(node -v)"
    exit 1
fi

echo "✅ Node.js 版本: $(node -v)"

# 检查pnpm是否安装
if ! command -v pnpm &> /dev/null; then
    echo "⚠️  pnpm 未安装，正在安装..."
    npm install -g pnpm
    if [ $? -ne 0 ]; then
        echo "❌ pnpm 安装失败，请手动安装: npm install -g pnpm"
        exit 1
    fi
fi

echo "✅ pnpm 版本: $(pnpm -v)"

# 进入应用目录
cd "$(dirname "$0")/app"

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 正在安装依赖..."
    pnpm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
else
    echo "✅ 依赖已安装"
fi

# 检查告警联动所需的特殊依赖
echo "🔍 检查告警联动依赖..."
if ! grep -q '"echarts"' package.json; then
    echo "⚠️  缺少 echarts 依赖，正在安装..."
    pnpm add echarts@^5.4.3 dayjs@^1.11.10
    if [ $? -ne 0 ]; then
        echo "❌ 告警联动依赖安装失败"
        echo "请手动运行: ./install-linkage-deps.sh"
        exit 1
    fi
    echo "✅ 告警联动依赖安装完成"
fi

# 检查环境变量文件
if [ ! -f ".env.development" ]; then
    echo "📝 创建开发环境配置文件..."
    cat > .env.development << EOF
# 开发环境配置
VITE_APP_TITLE=ECP告警联动系统
VITE_APP_BASE_API=http://localhost:8080
VITE_APP_ENV=development
EOF
    echo "✅ 环境配置文件已创建"
fi

echo ""
echo "🚀 启动开发服务器..."
echo "   访问地址: http://localhost:5173"
echo "   API地址: http://localhost:8080"
echo ""
echo "📋 功能模块:"
echo "   - 联动监控: /linkage/dashboard"
echo "   - 联动规则: /linkage/rules"
echo "   - 联动设备: /linkage/devices"
echo "   - 执行记录: /linkage/executions"
echo "   - 系统设置: /linkage/settings"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=========================================="

# 启动开发服务器
pnpm dev
