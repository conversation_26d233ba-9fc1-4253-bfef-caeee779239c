@echo off
chcp 65001 >nul
title ECP告警联动系统 Web管理界面

echo ==========================================
echo   ECP告警联动系统 Web管理界面
echo ==========================================

:: 检查Node.js是否安装
node -v >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+ 版本
    echo    下载地址: https://nodejs.org/
    pause
    exit /b 1
)

:: 获取Node.js版本
for /f "tokens=1 delims=v" %%i in ('node -v') do set NODE_VERSION=%%i
for /f "tokens=1 delims=." %%i in ("%NODE_VERSION:~1%") do set MAJOR_VERSION=%%i

if %MAJOR_VERSION% lss 18 (
    echo ❌ Node.js 版本过低，需要 18+ 版本
    node -v
    pause
    exit /b 1
)

echo ✅ Node.js 版本: 
node -v

:: 检查pnpm是否安装
pnpm -v >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  pnpm 未安装，正在安装...
    npm install -g pnpm
    if %errorlevel% neq 0 (
        echo ❌ pnpm 安装失败，请手动安装: npm install -g pnpm
        pause
        exit /b 1
    )
)

echo ✅ pnpm 版本:
pnpm -v

:: 进入应用目录
cd /d "%~dp0app"

:: 检查是否已安装依赖
if not exist "node_modules" (
    echo 📦 正在安装依赖...
    pnpm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已安装
)

:: 检查告警联动所需的特殊依赖
echo 🔍 检查告警联动依赖...
findstr /C:"echarts" package.json >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  缺少 echarts 依赖，正在安装...
    pnpm add echarts@^5.4.3 dayjs@^1.11.10
    if %errorlevel% neq 0 (
        echo ❌ 告警联动依赖安装失败
        echo 请手动运行: install-linkage-deps.bat
        pause
        exit /b 1
    )
    echo ✅ 告警联动依赖安装完成
)

:: 检查环境变量文件
if not exist ".env.development" (
    echo 📝 创建开发环境配置文件...
    (
        echo # 开发环境配置
        echo VITE_APP_TITLE=ECP告警联动系统
        echo VITE_APP_BASE_API=http://localhost:8080
        echo VITE_APP_ENV=development
    ) > .env.development
    echo ✅ 环境配置文件已创建
)

echo.
echo 🚀 启动开发服务器...
echo    访问地址: http://localhost:5173
echo    API地址: http://localhost:8080
echo.
echo 📋 功能模块:
echo    - 联动监控: /linkage/dashboard
echo    - 联动规则: /linkage/rules
echo    - 联动设备: /linkage/devices
echo    - 执行记录: /linkage/executions
echo    - 系统设置: /linkage/settings
echo.
echo 按 Ctrl+C 停止服务
echo ==========================================

:: 启动开发服务器
pnpm dev

pause
