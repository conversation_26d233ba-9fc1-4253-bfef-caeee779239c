# 环境配置文件示例
# 复制此文件为 .env.development (开发环境) 或 .env.production (生产环境)

# 应用标题
VITE_APP_TITLE=ECP告警联动系统

# API基础地址
# 开发环境通常为: http://localhost:8080
# 生产环境根据实际部署地址配置
VITE_APP_BASE_API=http://localhost:8080

# 应用环境
# development: 开发环境
# production: 生产环境
VITE_APP_ENV=development

# 是否启用Mock数据 (开发环境可选)
VITE_USE_MOCK=false

# WebSocket地址 (如果需要实时通信)
VITE_WS_URL=ws://localhost:8080/ws

# 上传文件大小限制 (MB)
VITE_UPLOAD_SIZE_LIMIT=10

# 是否启用调试模式
VITE_DEBUG=true

# 系统版本号
VITE_APP_VERSION=1.0.0
