# 告警联动页面无法打开 - 快速修复

## 🚨 问题现象

访问告警联动页面时出现错误：
```
Failed to resolve import "echarts" from "src/views/linkage/dashboard/index.vue"
```

## ⚡ 快速解决方案

### 方案一：使用自动安装脚本（推荐）

**Windows用户：**
```cmd
cd web/app
install-linkage-deps.bat
```

**Linux/Mac用户：**
```bash
cd web/app
chmod +x install-linkage-deps.sh
./install-linkage-deps.sh
```

### 方案二：手动安装依赖

```bash
cd web/app

# 使用 pnpm（推荐）
pnpm add echarts dayjs

# 或使用 npm
npm install echarts dayjs

# 或使用 yarn
yarn add echarts dayjs
```

### 方案三：使用更新的启动脚本

新的启动脚本会自动检查和安装依赖：

**Windows：**
```cmd
cd web
start-linkage.bat
```

**Linux/Mac：**
```bash
cd web
./start-linkage.sh
```

## 🔧 安装后操作

1. **重启开发服务器**：
   ```bash
   # 停止当前服务器 (Ctrl+C)
   # 然后重新启动
   pnpm dev
   ```

2. **验证安装**：
   访问 http://localhost:5173/linkage/dashboard

## 📋 需要安装的依赖

| 依赖包 | 版本 | 用途 |
|--------|------|------|
| echarts | ^5.4.3 | 数据可视化图表（执行趋势图、设备状态图） |
| dayjs | ^1.11.10 | 日期时间处理（时间格式化、计算） |

## 🎯 验证页面

安装完成后，以下页面应该可以正常访问：

- ✅ **联动监控**: http://localhost:5173/linkage/dashboard
- ✅ **联动规则**: http://localhost:5173/linkage/rules
- ✅ **联动设备**: http://localhost:5173/linkage/devices
- ✅ **执行记录**: http://localhost:5173/linkage/executions
- ✅ **系统设置**: http://localhost:5173/linkage/settings

## 🛠️ 故障排除

### 如果安装失败

1. **清除缓存**：
   ```bash
   pnpm store prune
   # 或
   npm cache clean --force
   ```

2. **删除 node_modules 重新安装**：
   ```bash
   rm -rf node_modules package-lock.json
   pnpm install
   ```

3. **检查网络连接**：
   确保可以访问 npm 仓库

### 如果页面仍然报错

1. **检查 package.json**：
   确认依赖已正确添加：
   ```json
   {
     "dependencies": {
       "echarts": "^5.4.3",
       "dayjs": "^1.11.10"
     }
   }
   ```

2. **清除浏览器缓存**：
   - 按 F12 打开开发者工具
   - 右键刷新按钮，选择"清空缓存并硬性重新加载"

3. **检查控制台错误**：
   - 打开浏览器开发者工具
   - 查看 Console 标签页的错误信息

## 📞 获取帮助

如果问题仍然存在，请：

1. 检查 Node.js 版本（需要 18+）
2. 检查包管理器版本
3. 查看完整的错误日志
4. 参考 `INSTALL-DEPENDENCIES.md` 获取详细说明

## 🔄 预防措施

为避免类似问题：

1. 使用项目提供的启动脚本
2. 定期更新依赖
3. 在团队中同步 package.json 变更
4. 使用锁定文件（package-lock.json）
