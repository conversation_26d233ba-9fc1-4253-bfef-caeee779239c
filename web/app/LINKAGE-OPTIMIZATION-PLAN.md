# 告警联动功能优化计划

## 🎯 优化目标

基于前后端代码逻辑梳理，确保告警联动功能完全符合需求，前后端接口匹配，界面美观易用。

## 🔍 发现的问题

### 1. 前端数据类型不匹配
**问题**: 前端API定义中某些字段类型与后端不一致

**影响**: 可能导致数据传输错误或类型转换问题

**解决方案**:
```typescript
// 修正前端API类型定义
export interface LinkageRule {
  id?: string;
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  conditions: RuleConditions;  // 改为对象类型，而非字符串
  actions: LinkageAction[];    // 改为数组类型，而非字符串
  created_at?: string;
  updated_at?: string;
}

export interface RuleConditions {
  video_ids?: number[];
  algorithm_ids?: number[];
  levels?: string[];
  types?: string[];
  time_range?: TimeRange;
  expression?: string;
}
```

### 2. 缺少实时状态更新
**问题**: 设备状态和执行记录需要手动刷新

**影响**: 用户体验不佳，无法实时了解系统状态

**解决方案**:
- 实现WebSocket连接用于实时数据推送
- 添加自动刷新机制
- 优化状态指示器的视觉反馈

### 3. 错误处理不够完善
**问题**: 某些异常情况缺少用户友好的错误提示

**影响**: 用户无法理解错误原因，影响使用体验

**解决方案**:
- 完善错误码映射和国际化
- 添加操作确认对话框
- 改进表单验证提示

## 🛠️ 具体优化方案

### 1. 前端API类型优化

#### 修正LinkageRule接口
```typescript
// 文件: web/app/src/api/linkage.ts
export interface LinkageRule {
  id?: string;
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  conditions: RuleConditions;  // 对象类型
  actions: LinkageAction[];    // 数组类型
  created_at?: string;
  updated_at?: string;
}

export interface RuleConditions {
  video_ids?: number[];
  algorithm_ids?: number[];
  levels?: string[];
  types?: string[];
  time_range?: {
    start_time: string;
    end_time: string;
    weekdays: number[];
  };
  expression?: string;
}

export interface LinkageAction {
  device_id: string;
  command: string;
  params: Record<string, any>;
  delay: number;
}
```

### 2. 实时状态更新机制

#### WebSocket集成
```typescript
// 文件: web/app/src/utils/websocket.ts
class LinkageWebSocket {
  private ws: WebSocket | null = null;
  private callbacks: Map<string, Function[]> = new Map();

  connect() {
    this.ws = new WebSocket('ws://localhost:8080/api/linkage/ws');
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
  }

  subscribe(event: string, callback: Function) {
    if (!this.callbacks.has(event)) {
      this.callbacks.set(event, []);
    }
    this.callbacks.get(event)!.push(callback);
  }

  private handleMessage(data: any) {
    const callbacks = this.callbacks.get(data.type) || [];
    callbacks.forEach(callback => callback(data.payload));
  }
}
```

#### 设备状态实时更新
```vue
<!-- 文件: web/app/src/views/linkage/devices/index.vue -->
<script setup lang="ts">
import { LinkageWebSocket } from '@/utils/websocket';

const ws = new LinkageWebSocket();

onMounted(() => {
  ws.connect();
  ws.subscribe('device_status_update', (data: any) => {
    // 更新设备状态
    const device = deviceList.value.find(d => d.id === data.device_id);
    if (device) {
      device.status = data.status;
      device.last_seen = data.last_seen;
    }
  });
});
</script>
```

### 3. 用户体验优化

#### 操作确认对话框
```vue
<!-- 改进删除操作 -->
<script setup lang="ts">
const handleDelete = async (row: LinkageRule) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除规则"${row.name}"吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );
    
    await LinkageRuleAPI.delete(row.id!);
    ElMessage.success('删除成功');
    await getList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};
</script>
```

#### 表单验证优化
```vue
<!-- 改进规则表单验证 -->
<script setup lang="ts">
const rules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请设置优先级', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '优先级范围 1-100', trigger: 'blur' }
  ],
  'conditions.video_ids': [
    { required: true, message: '请选择至少一个视频源', trigger: 'change' }
  ],
  'actions': [
    { required: true, message: '请配置至少一个联动动作', trigger: 'change' }
  ]
};
</script>
```

### 4. 界面美观性提升

#### 状态指示器优化
```vue
<!-- 设备状态显示优化 -->
<template>
  <el-table-column label="设备状态" width="120" align="center">
    <template #default="{ row }">
      <div class="status-indicator">
        <el-badge 
          :value="row.error_count > 0 ? row.error_count : ''" 
          :hidden="row.error_count === 0"
          type="danger"
        >
          <el-tag 
            :type="getStatusType(row.status)"
            :effect="row.status === 'online' ? 'light' : 'plain'"
          >
            <el-icon class="status-icon">
              <component :is="getStatusIcon(row.status)" />
            </el-icon>
            {{ getStatusText(row.status) }}
          </el-tag>
        </el-badge>
      </div>
    </template>
  </el-table-column>
</template>

<style scoped>
.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  margin-right: 4px;
}

.el-tag {
  border-radius: 12px;
  padding: 4px 12px;
}
</style>
```

#### 图表样式优化
```typescript
// 执行趋势图表配置优化
const chartOptions = {
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(50, 50, 50, 0.9)',
    borderColor: '#409EFF',
    borderWidth: 1,
    textStyle: {
      color: '#fff'
    }
  },
  legend: {
    data: ['成功', '失败'],
    textStyle: {
      color: '#606266'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisLine: {
      lineStyle: {
        color: '#E4E7ED'
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#E4E7ED'
      }
    }
  },
  series: [
    {
      name: '成功',
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#67C23A'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
          ]
        }
      }
    }
  ]
};
```

### 5. 后端API优化

#### 添加批量操作接口
```go
// 文件: internal/app/linkage/api.go
// BatchDeleteRules 批量删除规则
func (api *API) BatchDeleteRules(c *gin.Context) {
    var req BatchDeleteRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, web.JsonErrorMsg("无效的请求数据"))
        return
    }
    
    var errors []string
    for _, id := range req.IDs {
        if err := api.ruleManager.DeleteRule(id); err != nil {
            errors = append(errors, fmt.Sprintf("删除规则 %s 失败: %v", id, err))
        }
    }
    
    if len(errors) > 0 {
        c.JSON(http.StatusPartialContent, gin.H{
            "code": "PARTIAL_SUCCESS",
            "message": "部分删除成功",
            "errors": errors,
        })
        return
    }
    
    c.JSON(http.StatusOK, web.JsonMsg("批量删除成功"))
}
```

#### 添加WebSocket支持
```go
// 文件: internal/app/linkage/websocket.go
func (api *API) HandleWebSocket(c *gin.Context) {
    conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
    if err != nil {
        log.Printf("WebSocket升级失败: %v", err)
        return
    }
    defer conn.Close()
    
    client := &WebSocketClient{
        conn: conn,
        send: make(chan []byte, 256),
    }
    
    api.wsHub.register <- client
    
    go client.writePump()
    go client.readPump()
}
```

## 📅 实施计划

### 第一阶段 (1周)
- [ ] 修正前端API类型定义
- [ ] 完善表单验证规则
- [ ] 优化错误处理机制

### 第二阶段 (1周)
- [ ] 实现WebSocket实时更新
- [ ] 添加操作确认对话框
- [ ] 优化状态指示器样式

### 第三阶段 (1周)
- [ ] 完善图表样式和交互
- [ ] 添加批量操作功能
- [ ] 优化移动端适配

### 第四阶段 (1周)
- [ ] 性能优化和测试
- [ ] 用户体验测试和调优
- [ ] 文档更新和部署

## 🎯 预期效果

优化完成后，告警联动功能将具备：

1. **更好的用户体验**: 实时状态更新、友好的错误提示、流畅的操作流程
2. **更高的可靠性**: 完善的错误处理、数据类型匹配、异常情况覆盖
3. **更美观的界面**: 现代化的UI设计、直观的状态指示、响应式布局
4. **更强的功能性**: 批量操作、实时监控、高级配置选项

通过这些优化，告警联动功能将成为ECP平台的核心亮点功能。
