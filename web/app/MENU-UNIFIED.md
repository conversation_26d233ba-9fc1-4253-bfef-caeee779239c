# 菜单配置统一管理说明

## 🎯 目标

将告警联动菜单配置完全统一到 `menu.ts` 中管理，与其他菜单配置保持一致，实现菜单与路由的完全分离。

## ✅ 已完成的统一工作

### 1. 菜单配置统一格式

**位置**: `web/app/src/api/menu.ts`

所有菜单配置现在都使用统一的格式：

```javascript
{
  path: '/linkage',
  component: 'Layout',
  redirect: '/linkage/dashboard',
  name: 'Linkage',
  meta: {
    title: '告警联动',
    icon: 'el-icon-Connection'  // 统一使用 el-icon-* 格式
  },
  children: [
    {
      path: 'dashboard',
      component: 'linkage/dashboard/index',
      name: 'LinkageDashboard',
      meta: {
        title: '联动监控',
        icon: 'el-icon-Monitor',
        keepAlive: true
      },
      children: []
    },
    // ... 其他子菜单
  ]
}
```

### 2. 图标格式统一

所有菜单图标都使用 `el-icon-*` 格式，与项目中其他菜单保持一致：

| 菜单项 | 图标 | 说明 |
|--------|------|------|
| 告警联动 | `el-icon-Connection` | 主菜单图标 |
| 联动监控 | `el-icon-Monitor` | 监控仪表板 |
| 联动规则 | `el-icon-Setting` | 规则配置 |
| 联动设备 | `el-icon-Cpu` | 设备管理 |
| 执行记录 | `el-icon-Document` | 记录查看 |
| 系统设置 | `el-icon-Tools` | 系统配置 |

### 3. 路由配置简化

**位置**: `web/app/src/router/index.ts`

路由配置现在只包含路由功能相关的信息，移除了所有菜单显示相关的meta信息：

```javascript
{
  path: "/linkage",
  component: Layout,
  redirect: "/linkage/dashboard",
  // 移除了 meta 中的 title 和 icon
  children: [
    {
      path: "dashboard",
      component: () => import("@/views/linkage/dashboard/index.vue"),
      name: "LinkageDashboard",
      meta: {
        // 只保留路由功能需要的 meta
        keepAlive: true,
      },
    },
    {
      path: "rules/create",
      component: () => import("@/views/linkage/rules/form.vue"),
      name: "CreateLinkageRule",
      meta: {
        // 只保留路由功能需要的 meta
        hidden: true,
        activeMenu: "/linkage/rules",
      },
    },
    // ... 其他路由
  ],
}
```

## 📋 配置对比

### 菜单配置 (menu.ts)
- ✅ 负责菜单显示
- ✅ 包含 title、icon 等显示信息
- ✅ 控制菜单的层级结构
- ✅ 决定菜单项的显示/隐藏

### 路由配置 (router/index.ts)
- ✅ 负责页面路由
- ✅ 包含组件导入和路径匹配
- ✅ 控制页面缓存 (keepAlive)
- ✅ 控制菜单高亮 (activeMenu)

## 🔧 统一后的优势

### 1. 职责分离
- **菜单系统**: 专注于菜单显示逻辑
- **路由系统**: 专注于页面导航逻辑

### 2. 维护便利
- **菜单修改**: 只需修改 `menu.ts`
- **路由修改**: 只需修改 `router/index.ts`
- **避免重复**: 不需要在两个地方维护相同信息

### 3. 格式一致
- **图标格式**: 统一使用 `el-icon-*` 格式
- **配置结构**: 与其他菜单配置完全一致
- **命名规范**: 遵循项目统一的命名约定

## 🎯 验证方法

### 1. 菜单显示验证
- ✅ 左侧菜单中的告警联动项正常显示
- ✅ 图标显示正确
- ✅ 子菜单展开/收起正常
- ✅ 菜单项点击跳转正常

### 2. 路由功能验证
- ✅ 所有页面路由正常工作
- ✅ 新增/编辑按钮跳转正常
- ✅ 页面缓存功能正常
- ✅ 菜单高亮功能正常

### 3. 配置一致性验证
- ✅ 告警联动菜单格式与其他菜单一致
- ✅ 图标格式与项目规范一致
- ✅ 路由配置简洁明确

## 📝 维护指南

### 新增菜单项
1. 在 `menu.ts` 中添加菜单配置
2. 在 `router/index.ts` 中添加对应路由
3. 确保图标使用 `el-icon-*` 格式

### 修改菜单显示
1. 只需修改 `menu.ts` 中的配置
2. 不需要修改路由配置

### 修改路由逻辑
1. 只需修改 `router/index.ts` 中的配置
2. 不需要修改菜单配置

## 🚨 注意事项

1. **保持同步**: 菜单配置和路由配置的路径要保持一致
2. **图标规范**: 新增菜单必须使用 `el-icon-*` 格式的图标
3. **命名一致**: 路由名称和菜单名称要保持对应关系
4. **隐藏路由**: 表单页面等隐藏路由不需要在菜单中配置

## 🔄 后续优化

1. **类型检查**: 可以添加 TypeScript 类型检查确保配置一致性
2. **自动化验证**: 可以添加脚本验证菜单和路由配置的一致性
3. **文档生成**: 可以根据配置自动生成菜单文档

通过这次统一，告警联动菜单现在完全符合项目的菜单管理规范，与其他菜单配置保持一致！
