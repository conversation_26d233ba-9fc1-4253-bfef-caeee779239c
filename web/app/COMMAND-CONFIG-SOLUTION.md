# 联动规则执行命令配置完整解决方案

## 📍 问题定位

用户询问：**新增联动规则时，联动动作区域的执行命令下拉框的选项是在哪里配置的？**

## 🔍 答案总结

### 1. 执行命令选项配置位置

**文件**: `web/app/src/views/linkage/rules/form.vue`  
**方法**: `getAvailableCommands(deviceId: string)`  
**行号**: 461-488

```typescript
const getAvailableCommands = (deviceId: string) => {
  const device = getDeviceById(deviceId)
  if (!device) return []
  
  // 根据设备协议返回可用命令
  const commandMap: Record<string, any[]> = {
    mqtt: [
      { label: '开启', value: 'turn_on' },
      { label: '关闭', value: 'turn_off' },
      { label: '闪烁', value: 'blink' },
      { label: '设置亮度', value: 'set_brightness' }
    ],
    modbus: [
      { label: '写入单个线圈', value: 'write_single_coil' },
      { label: '写入单个寄存器', value: 'write_single_register' },
      { label: '写入多个线圈', value: 'write_multiple_coils' },
      { label: '写入多个寄存器', value: 'write_multiple_registers' }
    ],
    rs485: [
      { label: '发送十六进制数据', value: 'raw_hex' },
      { label: 'Modbus读取', value: 'modbus_read' },
      { label: 'Modbus写入', value: 'modbus_write' },
      { label: '自定义命令', value: 'custom' }
    ]
  }
  
  return commandMap[device.protocol] || []
}
```

### 2. 工作原理

1. **设备选择**: 用户选择联动设备
2. **协议识别**: 系统根据设备的 `protocol` 字段识别协议类型
3. **命令过滤**: `getAvailableCommands()` 根据协议返回对应命令列表
4. **动态更新**: 设备变更时，命令选项自动更新

## 🛠️ 发现并解决的问题

### 问题：参数配置组件缺失

在调研过程中发现，联动动作还有一个"命令参数"配置区域，但相关的 `ParamEditor` 组件并未实现，导致参数配置功能无法使用。

### 解决方案：创建完整的参数编辑器

**新增文件**: `web/app/src/components/linkage/ParamEditor.vue`

#### 功能特性

1. **多种参数类型支持**:
   - 数字输入 (`number`)
   - 文本输入 (`string`)
   - 文本域 (`textarea`)
   - 开关 (`boolean`)
   - 选择器 (`select`)
   - 颜色选择器 (`color`)
   - JSON编辑器 (`json`)

2. **协议特定参数配置**:
   - **MQTT协议**: 亮度值、闪烁间隔、闪烁次数等
   - **Modbus协议**: 线圈地址、寄存器地址、数值等
   - **RS485协议**: 十六进制数据、自定义命令等

3. **智能默认值和验证**:
   - 根据参数类型设置合理默认值
   - 数值范围验证
   - JSON格式验证
   - 必填项验证

#### 使用示例

```vue
<ParamEditor
  v-model="action.params"
  :device="getDeviceById(action.device_id)"
  :command="action.command"
/>
```

## 📋 协议命令详细配置

### MQTT协议命令

| 命令 | 参数 | 类型 | 说明 |
|------|------|------|------|
| `turn_on` | 无 | - | 开启设备 |
| `turn_off` | 无 | - | 关闭设备 |
| `blink` | `interval`<br>`count` | `number`<br>`number` | 闪烁间隔(ms)<br>闪烁次数 |
| `set_brightness` | `brightness` | `number` | 亮度值(0-100) |

### Modbus协议命令

| 命令 | 参数 | 类型 | 说明 |
|------|------|------|------|
| `write_single_coil` | `address`<br>`value` | `number`<br>`boolean` | 线圈地址<br>线圈值(ON/OFF) |
| `write_single_register` | `address`<br>`value` | `number`<br>`number` | 寄存器地址<br>寄存器值 |
| `write_multiple_coils` | 待扩展 | - | 批量写入线圈 |
| `write_multiple_registers` | 待扩展 | - | 批量写入寄存器 |

### RS485协议命令

| 命令 | 参数 | 类型 | 说明 |
|------|------|------|------|
| `raw_hex` | `data` | `string` | 十六进制数据 |
| `modbus_read` | 待扩展 | - | Modbus读取 |
| `modbus_write` | 待扩展 | - | Modbus写入 |
| `custom` | `command`<br>`params` | `string`<br>`json` | 自定义命令<br>命令参数 |

## 🔧 如何修改配置

### 1. 添加新命令

在 `getAvailableCommands` 方法中添加：

```typescript
mqtt: [
  // 现有命令...
  { label: '重启设备', value: 'restart' },
  { label: '获取状态', value: 'get_status' }
]
```

### 2. 添加命令参数

在 `ParamEditor.vue` 的 `configMap` 中添加：

```typescript
mqtt: {
  restart: {
    delay: {
      label: '重启延迟(秒)',
      type: 'number',
      min: 0,
      max: 60,
      placeholder: '请输入重启延迟时间',
      description: '设备重启前的延迟时间'
    }
  }
}
```

### 3. 添加新协议

```typescript
// 在 getAvailableCommands 中添加
http: [
  { label: 'GET请求', value: 'http_get' },
  { label: 'POST请求', value: 'http_post' }
]

// 在 ParamEditor 中添加对应参数配置
http: {
  http_post: {
    url: {
      label: '请求URL',
      type: 'string',
      placeholder: 'http://example.com/api',
      required: true
    },
    body: {
      label: '请求体',
      type: 'json',
      placeholder: '{"key": "value"}',
      description: 'POST请求的JSON数据'
    }
  }
}
```

## ✅ 完成的工作

1. **✅ 定位配置位置**: 明确了执行命令选项的配置位置和方法
2. **✅ 分析工作原理**: 解释了命令选项的动态加载机制
3. **✅ 发现缺失功能**: 识别出参数配置组件缺失的问题
4. **✅ 创建参数编辑器**: 实现了完整的 `ParamEditor` 组件
5. **✅ 集成到表单**: 将参数编辑器集成到联动规则表单中
6. **✅ 提供扩展指南**: 详细说明了如何添加新命令和参数

## 🎯 使用效果

修复后，用户在创建联动规则时：

1. **选择设备** → 系统自动加载该设备协议支持的命令
2. **选择命令** → 系统自动显示该命令需要的参数配置界面
3. **配置参数** → 根据参数类型提供相应的输入控件
4. **保存规则** → 命令和参数一起保存到规则配置中

## 📝 后续优化建议

1. **动态配置**: 将命令配置移到后端API或配置文件中
2. **参数验证**: 增强参数验证逻辑和错误提示
3. **命令预览**: 添加命令执行预览功能
4. **批量配置**: 支持批量配置相同类型的设备命令
5. **模板功能**: 提供常用命令配置模板

现在联动规则的执行命令配置功能已经完整可用！
