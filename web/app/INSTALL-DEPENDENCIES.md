# 安装缺失依赖说明

## 问题描述

告警联动页面无法打开，出现以下错误：
```
Failed to resolve import "echarts" from "src/views/linkage/dashboard/index.vue". Does the file exist?
```

## 原因分析

告警联动功能需要以下依赖包，但项目中尚未安装：
- `echarts` - 用于数据可视化图表
- `dayjs` - 用于日期时间处理

## 解决方案

### 方法一：使用 pnpm 安装（推荐）

```bash
cd web/app
pnpm install echarts dayjs
```

### 方法二：使用 npm 安装

```bash
cd web/app
npm install echarts dayjs
```

### 方法三：使用 yarn 安装

```bash
cd web/app
yarn add echarts dayjs
```

## 依赖说明

### echarts (^5.4.3)
- **用途**: 数据可视化图表库
- **功能**: 
  - 联动监控页面的执行趋势图
  - 设备状态分布饼图
  - 实时数据展示图表
- **官网**: https://echarts.apache.org/

### dayjs (^1.11.10)
- **用途**: 轻量级日期时间处理库
- **功能**:
  - 时间格式化显示
  - 日期计算和比较
  - 时间范围处理
- **官网**: https://day.js.org/

## 安装后验证

安装完成后，重新启动开发服务器：

```bash
# 停止当前服务器 (Ctrl+C)
# 然后重新启动
pnpm dev
```

## 完整的依赖列表

告警联动功能需要的所有依赖：

```json
{
  "dependencies": {
    "echarts": "^5.4.3",
    "dayjs": "^1.11.10",
    "element-plus": "^2.8.0",
    "vue": "^3.4.38",
    "vue-router": "^4.4.3",
    "axios": "^1.7.4"
  }
}
```

## 故障排除

### 1. 如果安装失败

```bash
# 清除缓存
pnpm store prune
# 或
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf node_modules
pnpm install
```

### 2. 如果仍然报错

检查 package.json 中是否正确添加了依赖：

```json
"dependencies": {
  "echarts": "^5.4.3",
  "dayjs": "^1.11.10"
}
```

### 3. 版本兼容性

如果遇到版本兼容问题，可以尝试以下版本：

```bash
pnpm add echarts@5.4.3 dayjs@1.11.10
```

## 注意事项

1. **Node.js 版本**: 确保使用 Node.js 18+ 版本
2. **包管理器**: 推荐使用 pnpm，与项目配置一致
3. **重启服务**: 安装依赖后需要重启开发服务器
4. **缓存清理**: 如果问题持续，清理浏览器缓存

## 预防措施

为避免类似问题，建议：

1. 在添加新功能前检查依赖需求
2. 及时更新 package.json 文件
3. 在团队中同步依赖变更
4. 使用 package-lock.json 锁定版本
