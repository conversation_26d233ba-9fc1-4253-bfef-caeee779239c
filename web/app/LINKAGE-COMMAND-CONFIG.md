# 联动规则执行命令配置说明

## 📍 配置位置

联动规则新增/编辑页面中"联动动作"区域的"执行命令"下拉框选项配置在：

**文件位置**: `web/app/src/views/linkage/rules/form.vue`  
**方法名称**: `getAvailableCommands(deviceId: string)`  
**行号**: 461-488

## 🔧 当前配置

### 代码实现
```typescript
const getAvailableCommands = (deviceId: string) => {
  const device = getDeviceById(deviceId)
  if (!device) return []
  
  // 根据设备协议返回可用命令
  const commandMap: Record<string, any[]> = {
    mqtt: [
      { label: '开启', value: 'turn_on' },
      { label: '关闭', value: 'turn_off' },
      { label: '闪烁', value: 'blink' },
      { label: '设置亮度', value: 'set_brightness' }
    ],
    modbus: [
      { label: '写入单个线圈', value: 'write_single_coil' },
      { label: '写入单个寄存器', value: 'write_single_register' },
      { label: '写入多个线圈', value: 'write_multiple_coils' },
      { label: '写入多个寄存器', value: 'write_multiple_registers' }
    ],
    rs485: [
      { label: '发送十六进制数据', value: 'raw_hex' },
      { label: 'Modbus读取', value: 'modbus_read' },
      { label: 'Modbus写入', value: 'modbus_write' },
      { label: '自定义命令', value: 'custom' }
    ]
  }
  
  return commandMap[device.protocol] || []
}
```

### 协议命令映射

#### MQTT协议命令
| 显示名称 | 命令值 | 说明 |
|---------|--------|------|
| 开启 | `turn_on` | 开启设备 |
| 关闭 | `turn_off` | 关闭设备 |
| 闪烁 | `blink` | 设备闪烁 |
| 设置亮度 | `set_brightness` | 调节亮度 |

#### Modbus协议命令
| 显示名称 | 命令值 | 说明 |
|---------|--------|------|
| 写入单个线圈 | `write_single_coil` | 写入单个线圈状态 |
| 写入单个寄存器 | `write_single_register` | 写入单个寄存器值 |
| 写入多个线圈 | `write_multiple_coils` | 批量写入线圈状态 |
| 写入多个寄存器 | `write_multiple_registers` | 批量写入寄存器值 |

#### RS485协议命令
| 显示名称 | 命令值 | 说明 |
|---------|--------|------|
| 发送十六进制数据 | `raw_hex` | 发送原始十六进制数据 |
| Modbus读取 | `modbus_read` | 通过RS485进行Modbus读取 |
| Modbus写入 | `modbus_write` | 通过RS485进行Modbus写入 |
| 自定义命令 | `custom` | 自定义协议命令 |

## 🔄 工作原理

1. **设备选择**: 用户在"联动设备"下拉框中选择设备
2. **协议识别**: 系统根据选中设备的 `protocol` 字段识别协议类型
3. **命令过滤**: `getAvailableCommands()` 方法根据协议类型返回对应的命令列表
4. **动态更新**: 当设备选择改变时，执行命令下拉框会自动更新可用选项

## 📝 如何修改命令配置

### 1. 添加新命令

在对应协议的数组中添加新的命令对象：

```typescript
mqtt: [
  { label: '开启', value: 'turn_on' },
  { label: '关闭', value: 'turn_off' },
  { label: '闪烁', value: 'blink' },
  { label: '设置亮度', value: 'set_brightness' },
  // 新增命令
  { label: '重启设备', value: 'restart' },
  { label: '获取状态', value: 'get_status' }
]
```

### 2. 添加新协议

在 `commandMap` 中添加新的协议配置：

```typescript
const commandMap: Record<string, any[]> = {
  mqtt: [...],
  modbus: [...],
  rs485: [...],
  // 新增协议
  http: [
    { label: 'GET请求', value: 'http_get' },
    { label: 'POST请求', value: 'http_post' },
    { label: 'PUT请求', value: 'http_put' },
    { label: 'DELETE请求', value: 'http_delete' }
  ]
}
```

### 3. 修改现有命令

直接修改对应命令的 `label` 或 `value`：

```typescript
modbus: [
  { label: '写单线圈', value: 'write_single_coil' }, // 修改显示名称
  { label: '写单寄存器', value: 'write_single_register' },
  // ...
]
```

## 🚀 优化建议

### 1. 动态配置化

将命令配置移到配置文件或后端API中，实现动态配置：

```typescript
// 建议的优化方案
const getAvailableCommands = async (deviceId: string) => {
  const device = getDeviceById(deviceId)
  if (!device) return []
  
  try {
    // 从后端API获取协议命令配置
    const response = await request({
      url: `/api/linkage/protocols/${device.protocol}/commands`,
      method: 'get'
    })
    return response.data
  } catch (error) {
    console.error('获取协议命令失败:', error)
    return []
  }
}
```

### 2. 配置文件管理

创建专门的配置文件：

```typescript
// src/config/linkage-commands.ts
export const PROTOCOL_COMMANDS = {
  mqtt: [
    { label: '开启', value: 'turn_on', params: [] },
    { label: '关闭', value: 'turn_off', params: [] },
    { label: '设置亮度', value: 'set_brightness', params: ['brightness'] }
  ],
  // ...
}
```

### 3. 参数配置支持

为每个命令配置所需的参数：

```typescript
const commandMap: Record<string, any[]> = {
  mqtt: [
    { 
      label: '开启', 
      value: 'turn_on',
      params: [] // 无参数
    },
    { 
      label: '设置亮度', 
      value: 'set_brightness',
      params: [
        { name: 'brightness', type: 'number', min: 0, max: 100, required: true }
      ]
    }
  ]
}
```

## 🔗 相关文件

### 前端文件
- **规则表单**: `web/app/src/views/linkage/rules/form.vue` (命令配置)
- **设备管理**: `web/app/src/views/linkage/devices/index.vue` (设备协议)
- **API定义**: `web/app/src/api/linkage.ts` (接口类型)

### 后端文件
- **协议适配器**: `internal/app/linkage/adapters/` (命令执行)
- **设备管理**: `internal/app/linkage/device_manager.go` (设备信息)
- **API接口**: `internal/app/linkage/api.go` (接口实现)

## 📋 注意事项

1. **协议一致性**: 前端配置的命令值必须与后端协议适配器支持的命令一致
2. **参数匹配**: 命令参数配置要与后端期望的参数格式匹配
3. **错误处理**: 添加新命令时要考虑错误处理和用户提示
4. **国际化**: 如果支持多语言，命令显示名称需要国际化处理
5. **版本兼容**: 修改命令配置时要考虑向后兼容性

## ⚠️ 重要发现

### 参数配置组件缺失

在联动动作配置中，还有一个 **命令参数配置** 区域，但相关组件 `ParamEditor` 并未实际实现：

**位置**: `web/app/src/views/linkage/rules/form.vue` 第 286-292 行
```vue
<!-- 命令参数 -->
<div v-if="action.command" class="command-params">
  <el-divider content-position="left">命令参数</el-divider>
  <component
    :is="getParamComponent(action)"
    v-model="action.params"
    :device="getDeviceById(action.device_id)"
    :command="action.command"
  />
</div>
```

**问题**: `getParamComponent()` 方法返回 `'ParamEditor'`，但该组件不存在，会导致参数配置功能无法使用。

### 解决方案

需要创建对应的参数编辑组件，建议创建以下组件：

1. **`ParamEditor.vue`** - 通用参数编辑器
2. **`MqttParamEditor.vue`** - MQTT协议参数编辑器
3. **`ModbusParamEditor.vue`** - Modbus协议参数编辑器
4. **`Rs485ParamEditor.vue`** - RS485协议参数编辑器

## 🛠️ 完整解决方案

### 1. 创建通用参数编辑器

```vue
<!-- src/components/linkage/ParamEditor.vue -->
<template>
  <div class="param-editor">
    <el-form :model="localParams" label-width="100px">
      <div v-for="(config, key) in paramConfigs" :key="key">
        <el-form-item :label="config.label">
          <!-- 数字输入 -->
          <el-input-number
            v-if="config.type === 'number'"
            v-model="localParams[key]"
            :min="config.min"
            :max="config.max"
            :placeholder="config.placeholder"
          />
          <!-- 文本输入 -->
          <el-input
            v-else-if="config.type === 'string'"
            v-model="localParams[key]"
            :placeholder="config.placeholder"
          />
          <!-- 开关 -->
          <el-switch
            v-else-if="config.type === 'boolean'"
            v-model="localParams[key]"
          />
          <!-- 选择器 -->
          <el-select
            v-else-if="config.type === 'select'"
            v-model="localParams[key]"
            :placeholder="config.placeholder"
          >
            <el-option
              v-for="option in config.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>
```

### 2. 更新命令配置

修改 `getAvailableCommands` 方法，为每个命令添加参数配置：

```typescript
const getAvailableCommands = (deviceId: string) => {
  const device = getDeviceById(deviceId)
  if (!device) return []

  const commandMap: Record<string, any[]> = {
    mqtt: [
      {
        label: '开启',
        value: 'turn_on',
        params: {} // 无参数
      },
      {
        label: '设置亮度',
        value: 'set_brightness',
        params: {
          brightness: {
            label: '亮度值',
            type: 'number',
            min: 0,
            max: 100,
            placeholder: '请输入亮度值(0-100)'
          }
        }
      }
    ],
    // ... 其他协议
  }

  return commandMap[device.protocol] || []
}
```

## 🎯 总结

执行命令下拉框的选项目前是在前端硬编码配置的，位于 `form.vue` 文件的 `getAvailableCommands` 方法中。同时发现了一个重要问题：**命令参数配置组件 `ParamEditor` 缺失**，需要创建相应的组件来完善参数配置功能。

建议优先解决参数编辑器组件缺失的问题，然后再考虑将命令配置优化为动态配置方式。
