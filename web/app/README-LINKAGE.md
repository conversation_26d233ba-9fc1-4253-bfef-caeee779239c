# ECP告警联动系统 - Web管理界面

## 概述

ECP告警联动系统Web管理界面是一个基于Vue 3 + TypeScript + Element Plus的现代化管理平台，提供了完整的告警联动规则配置、设备管理、执行监控等功能。

## 功能特性

### 🎯 联动监控仪表板
- **实时统计展示**: 总执行次数、成功率、在线设备数、活跃规则数
- **数据可视化**: 执行趋势图、设备状态分布图
- **系统状态监控**: 引擎状态、队列长度、协议适配器状态
- **最近执行记录**: 实时显示最新的联动执行情况
- **自动刷新**: 30秒自动刷新数据，保持实时性

### 📋 联动规则管理
- **规则列表**: 支持搜索、排序、分页的规则管理
- **规则创建/编辑**: 可视化的规则配置界面
- **触发条件配置**: 
  - 视频源选择
  - 算法类型选择
  - 告警等级和类型配置
  - 时间范围限制
  - 自定义表达式条件
- **联动动作配置**: 
  - 多设备支持
  - 命令参数配置
  - 延迟执行设置
- **规则测试**: 模拟告警数据测试规则执行
- **批量操作**: 支持批量启用/禁用/删除规则

### 🖥️ 联动设备管理
- **设备列表**: 完整的设备信息展示和管理
- **设备添加/编辑**: 支持多种协议的设备配置
- **协议支持**:
  - **MQTT**: 支持TLS、认证、QoS配置
  - **Modbus**: 支持TCP/RTU/ASCII模式
  - **RS485**: 支持自定义协议和Modbus RTU
- **设备控制**: 实时设备控制和命令执行
- **连接测试**: 设备连接状态测试
- **状态监控**: 实时设备在线状态监控

### 📊 执行记录查看
- **执行历史**: 完整的联动执行记录查询
- **统计分析**: 成功率、平均耗时等统计信息
- **详细信息**: 执行参数、错误信息等详细记录
- **重试功能**: 失败记录的重新执行
- **数据清理**: 历史记录的定期清理
- **数据导出**: 执行记录的导出功能

### ⚙️ 系统设置
- **系统配置**: 引擎参数、并发数、超时时间等配置
- **协议配置**: 各协议的全局参数配置
- **系统操作**: 引擎重启、记录清理等维护操作
- **配置备份**: 系统配置的导入导出功能

## 技术架构

### 前端技术栈
- **Vue 3**: 采用Composition API的现代化框架
- **TypeScript**: 类型安全的JavaScript超集
- **Element Plus**: 企业级UI组件库
- **Vue Router 4**: 官方路由管理器
- **Pinia**: 轻量级状态管理
- **Axios**: HTTP客户端
- **ECharts**: 数据可视化图表库
- **Day.js**: 轻量级日期处理库

### 项目结构
```
web/app/src/
├── api/                    # API接口
│   └── linkage.ts         # 联动系统API
├── components/            # 公共组件
│   └── Pagination/        # 分页组件
├── plugins/               # 插件配置
│   └── components.ts      # 全局组件注册
├── router/                # 路由配置
├── styles/                # 样式文件
│   ├── index.scss         # 主样式文件
│   └── element-plus.scss  # Element Plus样式覆盖
├── types/                 # 类型定义
├── utils/                 # 工具函数
└── views/                 # 页面组件
    └── linkage/           # 联动系统页面
        ├── dashboard/     # 监控仪表板
        ├── rules/         # 规则管理
        ├── devices/       # 设备管理
        ├── executions/    # 执行记录
        └── settings/      # 系统设置
```

## 页面功能详解

### 1. 联动监控 (`/linkage/dashboard`)
- 系统概览和实时监控
- 关键指标统计卡片
- 执行趋势和设备状态图表
- 系统健康状态监控
- 最近执行记录快速查看

### 2. 联动规则 (`/linkage/rules`)
- 规则列表管理
- 规则创建和编辑 (`/linkage/rules/create`, `/linkage/rules/edit/:id`)
- 规则启用/禁用
- 规则测试功能
- 批量操作支持

### 3. 联动设备 (`/linkage/devices`)
- 设备列表管理
- 设备添加和编辑 (`/linkage/devices/create`, `/linkage/devices/edit/:id`)
- 设备连接测试
- 实时设备控制
- 设备状态监控

### 4. 执行记录 (`/linkage/executions`)
- 执行历史查询
- 执行统计分析
- 记录详情查看
- 失败记录重试
- 数据清理和导出

### 5. 系统设置 (`/linkage/settings`)
- 系统参数配置
- 协议全局配置
- 系统维护操作
- 配置备份管理

## API接口

### 联动规则API
- `GET /api/linkage/rules` - 获取规则列表
- `POST /api/linkage/rules` - 创建规则
- `PUT /api/linkage/rules/:id` - 更新规则
- `DELETE /api/linkage/rules/:id` - 删除规则
- `PUT /api/linkage/rules/:id/enable` - 启用规则
- `PUT /api/linkage/rules/:id/disable` - 禁用规则
- `POST /api/linkage/rules/:id/test` - 测试规则

### 联动设备API
- `GET /api/linkage/devices` - 获取设备列表
- `POST /api/linkage/devices` - 创建设备
- `PUT /api/linkage/devices/:id` - 更新设备
- `DELETE /api/linkage/devices/:id` - 删除设备
- `GET /api/linkage/devices/:id/status` - 获取设备状态
- `POST /api/linkage/devices/:id/test` - 测试设备连接
- `POST /api/linkage/devices/:id/control` - 控制设备

### 执行记录API
- `GET /api/linkage/executions` - 获取执行记录
- `GET /api/linkage/executions/:id` - 获取执行详情
- `POST /api/linkage/executions/:id/retry` - 重试执行
- `GET /api/linkage/executions/stats` - 获取执行统计

### 系统监控API
- `GET /api/linkage/statistics` - 获取系统统计
- `GET /api/linkage/health` - 获取健康状态
- `GET /api/linkage/config` - 获取系统配置
- `PUT /api/linkage/config` - 更新系统配置
- `POST /api/linkage/restart` - 重启引擎
- `POST /api/linkage/cleanup` - 清理记录

## 使用说明

### 1. 创建联动规则
1. 进入"联动规则"页面
2. 点击"新增规则"按钮
3. 填写规则基本信息（名称、描述、优先级）
4. 配置触发条件（视频源、算法、告警等级等）
5. 添加联动动作（选择设备、命令、参数）
6. 保存并启用规则

### 2. 添加联动设备
1. 进入"联动设备"页面
2. 点击"添加设备"按钮
3. 填写设备基本信息
4. 选择协议类型并配置连接参数
5. 测试设备连接
6. 保存设备配置

### 3. 监控执行情况
1. 在"联动监控"页面查看实时统计
2. 在"执行记录"页面查看详细历史
3. 对失败的执行记录进行重试
4. 定期清理历史记录

### 4. 系统维护
1. 在"系统设置"页面调整系统参数
2. 配置各协议的全局参数
3. 定期重启引擎和清理数据
4. 备份和恢复系统配置

## 响应式设计

界面采用响应式设计，完美适配：
- **桌面端**: 1200px以上，完整功能展示
- **平板端**: 768px-1200px，适配布局调整
- **移动端**: 768px以下，简化操作界面

## 浏览器兼容性

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 开发和部署

### 开发环境启动
```bash
cd web/app
npm install
npm run dev
```

### 生产环境构建
```bash
npm run build
```

### 环境配置
- 开发环境API地址: `http://localhost:8080`
- 生产环境API地址: 根据实际部署配置

## 注意事项

1. **权限控制**: 系统操作需要相应权限
2. **数据备份**: 重要配置建议定期备份
3. **性能监控**: 大量设备时注意性能表现
4. **网络要求**: 需要稳定的网络连接
5. **浏览器缓存**: 更新后建议清除浏览器缓存
