# 联动页面路由修复说明

## 🚨 问题现象

点击"新增规则"和"添加设备"按钮时出现以下错误：
```
No match found for location with path "/linkage/rules/create"
No match found for location with path "/linkage/devices/create"
```

## 🔍 问题原因

用户手动修改了 `web/app/src/router/index.ts` 文件，将整个告警联动路由配置注释掉了，导致相关路由无法匹配。

## ✅ 修复方案

### 1. 恢复路由配置

已将 `router/index.ts` 中被注释的告警联动路由配置恢复：

```javascript
// 告警联动路由 - 菜单配置已移至 menu.ts
{
  path: "/linkage",
  component: Layout,
  redirect: "/linkage/dashboard",
  meta: {
    title: "告警联动",
    icon: "connection",
  },
  children: [
    {
      path: "dashboard",
      component: () => import("@/views/linkage/dashboard/index.vue"),
      name: "LinkageDashboard",
      meta: {
        title: "联动监控",
        keepAlive: true,
      },
    },
    {
      path: "rules",
      component: () => import("@/views/linkage/rules/index.vue"),
      name: "LinkageRules",
      meta: {
        title: "联动规则",
        keepAlive: true,
      },
    },
    {
      path: "rules/create",
      component: () => import("@/views/linkage/rules/form.vue"),
      name: "CreateLinkageRule",
      meta: {
        title: "创建规则",
        hidden: true,
        activeMenu: "/linkage/rules",
      },
    },
    {
      path: "rules/edit/:id",
      component: () => import("@/views/linkage/rules/form.vue"),
      name: "EditLinkageRule",
      meta: {
        title: "编辑规则",
        hidden: true,
        activeMenu: "/linkage/rules",
      },
    },
    {
      path: "devices",
      component: () => import("@/views/linkage/devices/index.vue"),
      name: "LinkageDevices",
      meta: {
        title: "联动设备",
        keepAlive: true,
      },
    },
    {
      path: "devices/create",
      component: () => import("@/views/linkage/devices/form.vue"),
      name: "CreateLinkageDevice",
      meta: {
        title: "添加设备",
        hidden: true,
        activeMenu: "/linkage/devices",
      },
    },
    {
      path: "devices/edit/:id",
      component: () => import("@/views/linkage/devices/form.vue"),
      name: "EditLinkageDevice",
      meta: {
        title: "编辑设备",
        hidden: true,
        activeMenu: "/linkage/devices",
      },
    },
    {
      path: "executions",
      component: () => import("@/views/linkage/executions/index.vue"),
      name: "LinkageExecutions",
      meta: {
        title: "执行记录",
        keepAlive: true,
      },
    },
    {
      path: "settings",
      component: () => import("@/views/linkage/settings/index.vue"),
      name: "LinkageSettings",
      meta: {
        title: "系统设置",
      },
    },
  ],
}
```

## 🎯 验证方法

修复后，以下功能应该正常工作：

### 1. 页面访问
- ✅ http://localhost:5173/linkage/dashboard - 联动监控
- ✅ http://localhost:5173/linkage/rules - 联动规则列表
- ✅ http://localhost:5173/linkage/rules/create - 创建规则表单
- ✅ http://localhost:5173/linkage/rules/edit/1 - 编辑规则表单
- ✅ http://localhost:5173/linkage/devices - 联动设备列表
- ✅ http://localhost:5173/linkage/devices/create - 添加设备表单
- ✅ http://localhost:5173/linkage/devices/edit/1 - 编辑设备表单
- ✅ http://localhost:5173/linkage/executions - 执行记录
- ✅ http://localhost:5173/linkage/settings - 系统设置

### 2. 按钮功能
- ✅ 联动规则页面的"新增规则"按钮
- ✅ 联动设备页面的"添加设备"按钮
- ✅ 列表页面的"编辑"按钮
- ✅ 表单页面的"返回"按钮

### 3. 菜单导航
- ✅ 左侧菜单的告警联动展开/收起
- ✅ 各子菜单项的点击跳转
- ✅ 当前页面的菜单高亮显示

## 🔧 路由结构说明

### 主路由
- `/linkage` - 告警联动主路由，重定向到 dashboard

### 子路由
| 路径 | 组件 | 名称 | 说明 |
|------|------|------|------|
| `dashboard` | `dashboard/index.vue` | LinkageDashboard | 联动监控 |
| `rules` | `rules/index.vue` | LinkageRules | 联动规则列表 |
| `rules/create` | `rules/form.vue` | CreateLinkageRule | 创建规则表单 |
| `rules/edit/:id` | `rules/form.vue` | EditLinkageRule | 编辑规则表单 |
| `devices` | `devices/index.vue` | LinkageDevices | 联动设备列表 |
| `devices/create` | `devices/form.vue` | CreateLinkageDevice | 添加设备表单 |
| `devices/edit/:id` | `devices/form.vue` | EditLinkageDevice | 编辑设备表单 |
| `executions` | `executions/index.vue` | LinkageExecutions | 执行记录 |
| `settings` | `settings/index.vue` | LinkageSettings | 系统设置 |

### 隐藏路由
表单页面（create/edit）设置了 `hidden: true`，不会在菜单中显示，但可以通过编程方式访问。

## 📝 注意事项

1. **不要注释路由配置**: 路由配置是页面访问的基础，注释后会导致页面无法访问
2. **菜单与路由分离**: 菜单配置在 `menu.ts` 中，路由配置在 `router/index.ts` 中，两者需要保持一致
3. **隐藏路由**: 表单页面使用 `hidden: true` 和 `activeMenu` 来实现菜单高亮
4. **路由命名**: 每个路由都有唯一的 `name` 属性，用于编程导航

## 🛠️ 故障排除

如果修复后仍有问题：

1. **清除浏览器缓存**: 按 F12 → Network → 勾选 "Disable cache"
2. **重启开发服务器**: Ctrl+C 停止，然后 `pnpm dev` 重启
3. **检查控制台错误**: 查看浏览器开发者工具的 Console 标签
4. **验证文件存在**: 确认所有组件文件都存在且路径正确

## 🔄 预防措施

1. **谨慎修改路由**: 修改路由配置前先备份
2. **测试完整性**: 修改后测试所有相关页面
3. **版本控制**: 使用 Git 跟踪配置文件变更
4. **团队协作**: 路由变更需要团队同步
