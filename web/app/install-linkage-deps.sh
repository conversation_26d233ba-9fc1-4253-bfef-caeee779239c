#!/bin/bash

# 安装告警联动功能所需依赖

echo "=========================================="
echo "  安装告警联动功能所需依赖"
echo "=========================================="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18+ 版本"
    echo "   下载地址: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js 版本: $(node -v)"

# 检查pnpm是否安装
if ! command -v pnpm &> /dev/null; then
    echo "⚠️  pnpm 未安装，正在安装..."
    npm install -g pnpm
    if [ $? -ne 0 ]; then
        echo "❌ pnpm 安装失败，将使用 npm 安装依赖"
        USE_NPM=true
    fi
fi

if [ "$USE_NPM" != "true" ]; then
    echo "✅ pnpm 版本: $(pnpm -v)"
fi

echo ""
echo "📦 正在安装告警联动依赖..."
echo "   - echarts (图表库)"
echo "   - dayjs (日期处理库)"

# 尝试使用pnpm安装
if [ "$USE_NPM" != "true" ]; then
    pnpm add echarts@^5.4.3 dayjs@^1.11.10
    if [ $? -ne 0 ]; then
        echo "❌ pnpm 安装失败，尝试使用 npm"
        USE_NPM=true
    else
        echo "✅ 依赖安装成功！"
    fi
fi

# 如果pnpm失败，使用npm
if [ "$USE_NPM" = "true" ]; then
    echo ""
    echo "📦 使用 npm 安装依赖..."
    npm install echarts@^5.4.3 dayjs@^1.11.10
    if [ $? -ne 0 ]; then
        echo "❌ npm 安装失败"
        echo ""
        echo "请手动执行以下命令："
        echo "npm install echarts dayjs"
        exit 1
    fi
    echo "✅ 依赖安装成功！"
fi

echo ""
echo "=========================================="
echo "  安装完成！"
echo "=========================================="
echo ""
echo "📋 已安装的依赖："
echo "   ✅ echarts - 数据可视化图表库"
echo "   ✅ dayjs - 日期时间处理库"
echo ""
echo "🚀 下一步操作："
echo "   1. 重启开发服务器 (Ctrl+C 停止，然后 pnpm dev)"
echo "   2. 访问告警联动页面测试功能"
echo ""
echo "📍 告警联动页面地址："
echo "   - 联动监控: http://localhost:5173/linkage/dashboard"
echo "   - 联动规则: http://localhost:5173/linkage/rules"
echo "   - 联动设备: http://localhost:5173/linkage/devices"
echo "   - 执行记录: http://localhost:5173/linkage/executions"
echo "   - 系统设置: http://localhost:5173/linkage/settings"
echo ""
echo "=========================================="
