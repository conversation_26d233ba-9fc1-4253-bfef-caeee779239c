# Modbus命令参数配置缺失问题修复

## 🚨 问题描述

用户反馈：**控制命令选择 modbus读取和写入时，显示"此命令无需配置参数"**

## 🔍 问题分析

### 问题定位

**文件**: `web/app/src/components/linkage/ParamEditor.vue`  
**问题**: 参数配置映射中缺少部分命令的配置

### 问题根源

在设备列表页面 (`devices/index.vue`) 中定义了以下 Modbus 命令：

```typescript
modbus: [
  { label: '写入单个线圈', value: 'write_single_coil' },      // ✅ 有参数配置
  { label: '写入单个寄存器', value: 'write_single_register' }, // ✅ 有参数配置
  { label: '读取线圈', value: 'read_coils' },                 // ❌ 缺少参数配置
  { label: '读取寄存器', value: 'read_holding_registers' }     // ❌ 缺少参数配置
]
```

但在 `ParamEditor` 组件的 `configMap` 中，只配置了前两个命令的参数，后两个读取命令没有参数配置，导致显示"此命令无需配置参数"。

同样，RS485 协议的 `modbus_read` 和 `modbus_write` 命令也缺少参数配置。

## ✅ 修复方案

### 1. 添加 Modbus 读取命令参数配置

#### read_coils (读取线圈)
```typescript
read_coils: {
  address: {
    label: '起始地址',
    type: 'number',
    min: 0,
    max: 65535,
    placeholder: '请输入起始地址',
    description: '要读取的线圈起始地址',
    required: true
  },
  count: {
    label: '读取数量',
    type: 'number',
    min: 1,
    max: 2000,
    placeholder: '请输入读取数量',
    description: '要读取的线圈数量(1-2000)',
    required: true
  }
}
```

#### read_holding_registers (读取保持寄存器)
```typescript
read_holding_registers: {
  address: {
    label: '起始地址',
    type: 'number',
    min: 0,
    max: 65535,
    placeholder: '请输入起始地址',
    description: '要读取的寄存器起始地址',
    required: true
  },
  count: {
    label: '读取数量',
    type: 'number',
    min: 1,
    max: 125,
    placeholder: '请输入读取数量',
    description: '要读取的寄存器数量(1-125)',
    required: true
  }
}
```

### 2. 添加 RS485 Modbus 命令参数配置

#### modbus_read (Modbus读取)
```typescript
modbus_read: {
  device_addr: {
    label: '设备地址',
    type: 'number',
    min: 1,
    max: 247,
    description: 'Modbus设备地址(1-247)',
    required: true
  },
  function_code: {
    label: '功能码',
    type: 'select',
    options: [
      { label: '01 - 读取线圈状态', value: 1 },
      { label: '02 - 读取离散输入', value: 2 },
      { label: '03 - 读取保持寄存器', value: 3 },
      { label: '04 - 读取输入寄存器', value: 4 }
    ],
    required: true
  },
  address: {
    label: '起始地址',
    type: 'number',
    min: 0,
    max: 65535,
    description: '要读取的起始地址',
    required: true
  },
  count: {
    label: '读取数量',
    type: 'number',
    min: 1,
    max: 2000,
    description: '要读取的数据数量',
    required: true
  }
}
```

#### modbus_write (Modbus写入)
```typescript
modbus_write: {
  device_addr: {
    label: '设备地址',
    type: 'number',
    min: 1,
    max: 247,
    description: 'Modbus设备地址(1-247)',
    required: true
  },
  function_code: {
    label: '功能码',
    type: 'select',
    options: [
      { label: '05 - 写入单个线圈', value: 5 },
      { label: '06 - 写入单个寄存器', value: 6 },
      { label: '15 - 写入多个线圈', value: 15 },
      { label: '16 - 写入多个寄存器', value: 16 }
    ],
    required: true
  },
  address: {
    label: '起始地址',
    type: 'number',
    min: 0,
    max: 65535,
    description: '要写入的起始地址',
    required: true
  },
  value: {
    label: '写入值',
    type: 'string',
    placeholder: '请输入写入值，多个值用逗号分隔',
    description: '要写入的值，多个值用逗号分隔',
    required: true
  }
}
```

## 📋 完整的命令参数配置

### Modbus 协议命令

| 命令 | 参数 | 说明 |
|------|------|------|
| `write_single_coil` | `address`, `value` | 写入单个线圈 |
| `write_single_register` | `address`, `value` | 写入单个寄存器 |
| `read_coils` | `address`, `count` | 读取线圈状态 |
| `read_holding_registers` | `address`, `count` | 读取保持寄存器 |

### RS485 协议命令

| 命令 | 参数 | 说明 |
|------|------|------|
| `raw_hex` | `data` | 发送十六进制数据 |
| `modbus_read` | `device_addr`, `function_code`, `address`, `count` | Modbus读取操作 |
| `modbus_write` | `device_addr`, `function_code`, `address`, `value` | Modbus写入操作 |
| `custom` | `command`, `params` | 自定义命令 |

## 🎯 修复效果

### 修复前
- ❌ 选择"读取线圈"命令：显示"此命令无需配置参数"
- ❌ 选择"读取寄存器"命令：显示"此命令无需配置参数"
- ❌ 选择"Modbus读取"命令：显示"此命令无需配置参数"
- ❌ 选择"Modbus写入"命令：显示"此命令无需配置参数"

### 修复后
- ✅ 选择"读取线圈"命令：显示起始地址和读取数量参数
- ✅ 选择"读取寄存器"命令：显示起始地址和读取数量参数
- ✅ 选择"Modbus读取"命令：显示设备地址、功能码、起始地址、读取数量参数
- ✅ 选择"Modbus写入"命令：显示设备地址、功能码、起始地址、写入值参数

## 🧪 测试验证

### 测试步骤

1. **进入设备列表页面**
   - 访问 `/linkage/devices`

2. **选择 Modbus 设备进行控制**
   - 点击协议为 "modbus" 的设备的"控制"按钮

3. **测试读取命令**
   - 选择"读取线圈"命令
   - 应该显示"起始地址"和"读取数量"参数输入框
   - 选择"读取寄存器"命令
   - 应该显示"起始地址"和"读取数量"参数输入框

4. **选择 RS485 设备进行控制**
   - 点击协议为 "rs485" 的设备的"控制"按钮

5. **测试 Modbus 命令**
   - 选择"Modbus读取"命令
   - 应该显示设备地址、功能码、起始地址、读取数量参数
   - 选择"Modbus写入"命令
   - 应该显示设备地址、功能码、起始地址、写入值参数

### 预期结果

所有命令都应该显示相应的参数配置界面，不再出现"此命令无需配置参数"的提示。

## 🚨 注意事项

### 1. 参数验证
- 地址范围：0-65535
- 设备地址范围：1-247
- 读取数量限制：线圈最多2000个，寄存器最多125个

### 2. 功能码说明
- **读取功能码**：01(线圈), 02(离散输入), 03(保持寄存器), 04(输入寄存器)
- **写入功能码**：05(单个线圈), 06(单个寄存器), 15(多个线圈), 16(多个寄存器)

### 3. 数据格式
- 写入值支持多个值，用逗号分隔
- 十六进制数据用空格分隔

## 🎉 总结

通过这次修复：
- ✅ **补全了缺失的参数配置**: 为所有 Modbus 和 RS485 命令添加了完整的参数配置
- ✅ **提升了用户体验**: 所有命令都能正确显示参数配置界面
- ✅ **增强了功能完整性**: 支持完整的 Modbus 读写操作
- ✅ **规范了参数格式**: 提供了详细的参数说明和验证规则

现在所有的 Modbus 和 RS485 命令都应该能正确显示参数配置界面了！
