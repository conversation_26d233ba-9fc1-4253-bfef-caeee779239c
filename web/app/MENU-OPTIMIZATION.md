# 菜单优化说明

## 优化内容

### 1. 菜单样式修复

**问题**: 菜单悬停和选中的样式被自定义样式覆盖，与原始设计不一致。

**解决方案**: 
- 移除了 `element-plus.scss` 中的自定义菜单样式
- 恢复使用原始的CSS变量和主题样式
- 保持与项目整体设计风格的一致性

**修改文件**:
- `web/app/src/styles/element-plus.scss` - 移除自定义菜单样式

### 2. 菜单配置迁移

**问题**: 告警联动菜单配置在路由文件中，与其他菜单配置不一致。

**解决方案**:
- 将告警联动菜单配置从 `router/index.ts` 迁移到 `api/menu.ts`
- 保持菜单配置的统一管理
- 路由文件只保留路由定义，不包含菜单显示信息

**修改文件**:
- `web/app/src/api/menu.ts` - 添加告警联动菜单配置
- `web/app/src/router/index.ts` - 移除菜单配置，保留路由定义

## 菜单配置详情

### 告警联动菜单结构

```javascript
{
  path: '/linkage',
  component: 'Layout',
  redirect: '/linkage/dashboard',
  name: 'Linkage',
  meta: {
    title: '告警联动',
    icon: 'el-icon-Connection'
  },
  children: [
    {
      path: 'dashboard',
      component: 'linkage/dashboard/index',
      name: 'LinkageDashboard',
      meta: {
        title: '联动监控',
        icon: 'el-icon-Monitor',
        keepAlive: true
      }
    },
    {
      path: 'rules',
      component: 'linkage/rules/index',
      name: 'LinkageRules',
      meta: {
        title: '联动规则',
        icon: 'el-icon-Setting',
        keepAlive: true
      }
    },
    {
      path: 'devices',
      component: 'linkage/devices/index',
      name: 'LinkageDevices',
      meta: {
        title: '联动设备',
        icon: 'el-icon-Cpu',
        keepAlive: true
      }
    },
    {
      path: 'executions',
      component: 'linkage/executions/index',
      name: 'LinkageExecutions',
      meta: {
        title: '执行记录',
        icon: 'el-icon-Document',
        keepAlive: true
      }
    },
    {
      path: 'settings',
      component: 'linkage/settings/index',
      name: 'LinkageSettings',
      meta: {
        title: '系统设置',
        icon: 'el-icon-Tools'
      }
    }
  ]
}
```

### 图标使用

所有菜单图标统一使用 Element Plus 图标，前缀为 `el-icon-`，与项目中其他菜单保持一致：

- **告警联动**: `el-icon-Connection` - 连接图标，表示系统间的联动
- **联动监控**: `el-icon-Monitor` - 监控图标，表示实时监控
- **联动规则**: `el-icon-Setting` - 设置图标，表示规则配置
- **联动设备**: `el-icon-Cpu` - CPU图标，表示设备管理
- **执行记录**: `el-icon-Document` - 文档图标，表示记录查看
- **系统设置**: `el-icon-Tools` - 工具图标，表示系统配置

**注意**: 图标格式已统一为 `el-icon-*` 格式，与算法管理、告警记录等其他菜单的图标格式保持一致。

## 样式变量

菜单样式使用以下CSS变量（定义在 `variables.scss` 中）：

```scss
:root {
  --menu-background: #304156;        // 菜单背景色
  --menu-text: #bfcbd9;             // 菜单文字颜色
  --menu-active-text: var(--el-menu-active-color); // 菜单激活文字颜色
  --menu-hover: #263445;            // 菜单悬停背景色
}
```

## 接口扩展

为支持详情页面的菜单高亮，扩展了 `Meta` 接口：

```typescript
export interface Meta {
  // ... 其他属性
  /** 当前路由为详情页时，需要高亮的菜单 */
  activeMenu?: string;
}
```

## 验证方法

1. **菜单样式验证**:
   - 检查菜单项悬停时的背景色是否为深色
   - 检查菜单项选中时是否有蓝色左边框
   - 检查菜单文字颜色是否正常

2. **菜单配置验证**:
   - 确认告警联动菜单在左侧导航中正常显示
   - 确认所有子菜单项都能正常访问
   - 确认图标显示正确

3. **路由功能验证**:
   - 测试所有联动相关页面的路由跳转
   - 测试详情页面的菜单高亮功能
   - 测试页面缓存功能（keepAlive）

## 注意事项

1. **样式一致性**: 不要在 `element-plus.scss` 中添加自定义菜单样式，应使用主题变量
2. **配置统一**: 新增菜单应在 `menu.ts` 中配置，而不是在路由文件中
3. **图标规范**: 统一使用 Element Plus 图标，保持视觉一致性
4. **缓存配置**: 列表页面建议开启 `keepAlive`，详情页面不开启

## 后续维护

1. 如需修改菜单样式，应修改 `variables.scss` 中的CSS变量
2. 如需新增菜单，应在 `menu.ts` 中添加配置
3. 如需修改图标，应使用 Element Plus 图标库中的图标
4. 定期检查菜单配置与路由配置的一致性
