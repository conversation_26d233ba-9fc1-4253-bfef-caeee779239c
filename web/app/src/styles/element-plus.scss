// Element Plus 样式覆盖

// 全局变量覆盖
:root {
  --el-color-primary: #409eff;
  --el-color-success: #67c23a;
  --el-color-warning: #e6a23c;
  --el-color-danger: #f56c6c;
  --el-color-error: #f56c6c;
  --el-color-info: #909399;
}

// 表格样式
.el-table {
  .el-table__header-wrapper {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 500;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 卡片样式
.el-card {
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  
  .el-card__header {
    padding: 18px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      span {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

// 表单样式
.el-form {
  .el-form-item {
    margin-bottom: 22px;
    
    .el-form-item__label {
      color: #606266;
      font-weight: 500;
    }
    
    .el-form-item__content {
      .form-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
        line-height: 1.4;
      }
    }
  }
}

// 按钮样式
.el-button {
  &.el-button--primary {
    background-color: #409eff;
    border-color: #409eff;
    
    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }
  }
  
  &.el-button--success {
    background-color: #67c23a;
    border-color: #67c23a;
    
    &:hover {
      background-color: #85ce61;
      border-color: #85ce61;
    }
  }
  
  &.el-button--warning {
    background-color: #e6a23c;
    border-color: #e6a23c;
    
    &:hover {
      background-color: #ebb563;
      border-color: #ebb563;
    }
  }
  
  &.el-button--danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
    
    &:hover {
      background-color: #f78989;
      border-color: #f78989;
    }
  }
}

// 标签样式
.el-tag {
  &.el-tag--success {
    background-color: #f0f9ff;
    border-color: #c6e2ff;
    color: #67c23a;
  }
  
  &.el-tag--warning {
    background-color: #fdf6ec;
    border-color: #f5dab1;
    color: #e6a23c;
  }
  
  &.el-tag--danger {
    background-color: #fef0f0;
    border-color: #fbc4c4;
    color: #f56c6c;
  }
  
  &.el-tag--info {
    background-color: #f4f4f5;
    border-color: #d3d4d6;
    color: #909399;
  }
}

// 对话框样式
.el-dialog {
  .el-dialog__header {
    padding: 20px 20px 10px;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .el-dialog__body {
    padding: 20px;
    color: #606266;
    font-size: 14px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px;
    text-align: right;
  }
}

// 分页样式
.el-pagination {
  margin-top: 20px;
  text-align: center;
  
  .el-pagination__total {
    color: #606266;
  }
  
  .el-pager li {
    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}

// 菜单样式 - 移除自定义样式，使用原始主题变量
// 原始菜单样式已在 variables.scss 和 SidebarMenuItem.vue 中定义

// 开关样式
.el-switch {
  &.is-checked {
    .el-switch__core {
      background-color: #409eff;
      border-color: #409eff;
    }
  }
}

// 输入框样式
.el-input {
  .el-input__inner {
    &:focus {
      border-color: #409eff;
    }
  }
}

.el-select {
  .el-input {
    .el-input__inner {
      &:focus {
        border-color: #409eff;
      }
    }
  }
}

// 时间选择器样式
.el-date-editor {
  &.el-input {
    .el-input__inner {
      &:focus {
        border-color: #409eff;
      }
    }
  }
}

// 上传组件样式
.el-upload {
  .el-upload-dragger {
    &:hover {
      border-color: #409eff;
    }
  }
}

// 描述列表样式
.el-descriptions {
  .el-descriptions__header {
    margin-bottom: 20px;
    
    .el-descriptions__title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .el-descriptions__body {
    .el-descriptions__table {
      .el-descriptions__cell {
        &.is-bordered-label {
          background-color: #fafafa;
          font-weight: 500;
        }
      }
    }
  }
}

// 空状态样式
.el-empty {
  .el-empty__description {
    color: #909399;
  }
}

// 警告框样式
.el-alert {
  &.el-alert--success {
    background-color: #f0f9ff;
    border-color: #c6e2ff;
    
    .el-alert__title {
      color: #67c23a;
    }
  }
  
  &.el-alert--warning {
    background-color: #fdf6ec;
    border-color: #f5dab1;
    
    .el-alert__title {
      color: #e6a23c;
    }
  }
  
  &.el-alert--error {
    background-color: #fef0f0;
    border-color: #fbc4c4;
    
    .el-alert__title {
      color: #f56c6c;
    }
  }
  
  &.el-alert--info {
    background-color: #f4f4f5;
    border-color: #d3d4d6;
    
    .el-alert__title {
      color: #909399;
    }
  }
}

// 标签页样式
.el-tabs {
  .el-tabs__header {
    .el-tabs__nav-wrap {
      &::after {
        background-color: #e4e7ed;
      }
    }
    
    .el-tabs__item {
      &.is-active {
        color: #409eff;
      }
      
      &:hover {
        color: #409eff;
      }
    }
    
    .el-tabs__active-bar {
      background-color: #409eff;
    }
  }
}

// 分割线样式
.el-divider {
  &.el-divider--horizontal {
    margin: 24px 0;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .el-card {
    .el-card__header {
      padding: 15px;
    }
    
    .el-card__body {
      padding: 15px;
    }
  }
  
  .el-form {
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .el-dialog {
    width: 90% !important;
    margin-top: 5vh !important;
  }
  
  .el-table {
    font-size: 12px;
  }
  
  .el-pagination {
    .el-pagination__sizes,
    .el-pagination__total {
      display: none;
    }
  }
}
