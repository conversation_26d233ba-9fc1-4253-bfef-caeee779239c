import AuthAPI, { type LoginData } from "@/api/auth";
import User<PERSON><PERSON>, { type UserInfo } from "@/api/user";
import { resetRouter } from "@/router";
import { store } from "@/store";
import { TOKEN_KEY } from "@/enums/CacheEnum";

export const useUserStore = defineStore("user", () => {
  const user = ref<UserInfo>({
    roles: [],
    perms: [],
  });

  /**
   * 登录
   *
   * @param {LoginData}
   * @returns
   */
  function login(loginData: LoginData) {
    return new Promise<void>((resolve, reject) => {
      AuthAPI.login(loginData)
        .then((response) => {
          if (response.success) {
            const { token_type, access_token } = response.data;
            localStorage.setItem(TOKEN_KEY, token_type + " " + access_token);
            // 保存用户基本信息
            const { user_id, username, name, role } = response.data;
            Object.assign(user.value, { 
              userId: user_id,
              username,
              nickname: name,
              roles: [role]
            });
          resolve();
          } else {
            reject(response.message || '登录失败');
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // 获取信息(用户昵称、头像、角色集合、权限集合)
  function getUserInfo() {
    return new Promise<UserInfo>((resolve, reject) => {
      // 如果已经有用户信息，直接返回
      if (user.value.username) {
        resolve(user.value);
        return;
      }
      
      UserAPI.getInfo()
        .then((response: any) => {
          if (response && response.success && response.data) {
            // 处理边缘计算平台API响应
            const userData = response.data;
            const userInfo: UserInfo = {
              userId: userData.id,
              username: userData.username,
              nickname: userData.name,
              roles: [userData.role], // 将角色字符串转换为数组
              perms: [], // 权限列表，暂时为空
              avatar: '' // 头像，暂时为空
            };
            
            Object.assign(user.value, userInfo);
            resolve(user.value);
          } else {
            reject("获取用户信息失败");
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // user logout
  function logout() {
    return new Promise<void>((resolve, reject) => {
      // 直接清除本地存储的token，不调用后端API
      try {
          localStorage.setItem(TOKEN_KEY, "");
          location.reload(); // 清空路由
          resolve();
      } catch (error) {
          reject(error);
      }
    });
  }

  // remove token
  function resetToken() {
    return new Promise<void>((resolve) => {
      localStorage.setItem(TOKEN_KEY, "");
      resetRouter();
      resolve();
    });
  }

  return {
    user,
    login,
    getUserInfo,
    logout,
    resetToken,
  };
});

/**
 * 用于在组件外部（如在Pinia Store 中）使用 Pinia 提供的 store 实例。
 * 官方文档解释了如何在组件外部使用 Pinia Store：
 * https://pinia.vuejs.org/core-concepts/outside-component-usage.html#using-a-store-outside-of-a-component
 */
export function useUserStoreHook() {
  return useUserStore(store);
}
