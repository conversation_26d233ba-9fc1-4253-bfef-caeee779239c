import { ElMessage } from 'element-plus'

/**
 * 联动系统SSE客户端
 * 用于接收服务器端事件推送
 */
export class LinkageSSE {
  private eventSources: Map<string, EventSource> = new Map()
  private callbacks: Map<string, Function[]> = new Map()
  private reconnectTimers: Map<string, number> = new Map()
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000

  constructor(private baseUrl: string = '/api/linkage/stream') {}

  /**
   * 连接设备状态流
   */
  connectDeviceStatus(callback: Function): void {
    this.connect('devices/status', callback)
  }

  /**
   * 连接执行记录流
   */
  connectExecutionRecords(callback: Function): void {
    this.connect('executions', callback)
  }

  /**
   * 连接系统告警流
   */
  connectSystemAlerts(callback: Function): void {
    this.connect('alerts', callback)
  }

  /**
   * 连接统计数据流
   */
  connectStatistics(callback: Function): void {
    this.connect('statistics', callback)
  }

  /**
   * 连接所有事件流
   */
  connectAll(callback: Function): void {
    this.connect('all', callback)
  }

  /**
   * 连接带过滤器的事件流
   */
  connectWithFilter(filters: {
    types?: string[]
    device_id?: string
    rule_id?: string
  }, callback: Function): void {
    const params = new URLSearchParams()
    
    if (filters.types && filters.types.length > 0) {
      params.append('types', filters.types.join(','))
    }
    if (filters.device_id) {
      params.append('device_id', filters.device_id)
    }
    if (filters.rule_id) {
      params.append('rule_id', filters.rule_id)
    }

    const endpoint = `filter?${params.toString()}`
    this.connect(endpoint, callback)
  }

  /**
   * 通用连接方法
   */
  private connect(endpoint: string, callback: Function): void {
    // 如果已经连接，先断开
    if (this.eventSources.has(endpoint)) {
      this.disconnect(endpoint)
    }

    const url = `${this.baseUrl}/${endpoint}`
    console.log(`连接SSE流: ${url}`)

    const eventSource = new EventSource(url)
    this.eventSources.set(endpoint, eventSource)

    // 注册回调
    if (!this.callbacks.has(endpoint)) {
      this.callbacks.set(endpoint, [])
    }
    this.callbacks.get(endpoint)!.push(callback)

    // 连接成功
    eventSource.addEventListener('connected', (event) => {
      console.log(`SSE连接成功: ${endpoint}`, JSON.parse(event.data))
      this.clearReconnectTimer(endpoint)
    })

    // 接收数据
    eventSource.addEventListener('data', (event) => {
      try {
        const data = JSON.parse(event.data)
        console.log(`收到SSE数据: ${endpoint}`, data)
        
        // 调用所有回调函数
        const callbacks = this.callbacks.get(endpoint) || []
        callbacks.forEach(cb => {
          try {
            cb(data)
          } catch (error) {
            console.error('SSE回调执行失败:', error)
          }
        })
      } catch (error) {
        console.error('SSE数据解析失败:', error)
      }
    })

    // 心跳消息
    eventSource.addEventListener('heartbeat', (event) => {
      const data = JSON.parse(event.data)
      console.log(`收到心跳: ${endpoint}`, data.message)
    })

    // 错误处理
    eventSource.addEventListener('error', (event) => {
      console.error(`SSE连接错误: ${endpoint}`, event)
      
      if (eventSource.readyState === EventSource.CLOSED) {
        console.log(`SSE连接已关闭: ${endpoint}`)
        this.scheduleReconnect(endpoint, callback)
      }
    })

    // 连接关闭
    eventSource.addEventListener('close', () => {
      console.log(`SSE连接关闭: ${endpoint}`)
      this.eventSources.delete(endpoint)
    })
  }

  /**
   * 断开指定连接
   */
  disconnect(endpoint: string): void {
    const eventSource = this.eventSources.get(endpoint)
    if (eventSource) {
      eventSource.close()
      this.eventSources.delete(endpoint)
      this.callbacks.delete(endpoint)
      this.clearReconnectTimer(endpoint)
      console.log(`SSE连接已断开: ${endpoint}`)
    }
  }

  /**
   * 断开所有连接
   */
  disconnectAll(): void {
    this.eventSources.forEach((eventSource, endpoint) => {
      eventSource.close()
      this.clearReconnectTimer(endpoint)
    })
    this.eventSources.clear()
    this.callbacks.clear()
    console.log('所有SSE连接已断开')
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(endpoint: string, callback: Function): void {
    if (this.reconnectTimers.has(endpoint)) {
      return // 已经在重连中
    }

    console.log(`${this.reconnectInterval}ms后重连SSE: ${endpoint}`)
    
    const timer = window.setTimeout(() => {
      this.reconnectTimers.delete(endpoint)
      this.connect(endpoint, callback)
    }, this.reconnectInterval)

    this.reconnectTimers.set(endpoint, timer)
  }

  /**
   * 清除重连定时器
   */
  private clearReconnectTimer(endpoint: string): void {
    const timer = this.reconnectTimers.get(endpoint)
    if (timer) {
      clearTimeout(timer)
      this.reconnectTimers.delete(endpoint)
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): Record<string, number> {
    const status: Record<string, number> = {}
    this.eventSources.forEach((eventSource, endpoint) => {
      status[endpoint] = eventSource.readyState
    })
    return status
  }

  /**
   * 检查是否有活跃连接
   */
  hasActiveConnections(): boolean {
    return Array.from(this.eventSources.values()).some(
      eventSource => eventSource.readyState === EventSource.OPEN
    )
  }
}

// 创建全局实例
export const linkageSSE = new LinkageSSE()

// SSE连接状态常量
export const SSE_READY_STATE = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSED: 2
} as const

// 事件类型常量
export const SSE_EVENT_TYPES = {
  DEVICE_STATUS_UPDATE: 'device_status_update',
  EXECUTION_RECORD: 'execution_record',
  RULE_STATUS_CHANGE: 'rule_status_change',
  SYSTEM_ALERT: 'system_alert',
  STATISTICS_UPDATE: 'statistics_update'
} as const

// 事件数据接口
export interface DeviceStatusUpdateEvent {
  device_id: string
  status: string
  last_seen: string
  error_count: number
  response_time?: number
}

export interface ExecutionRecordEvent {
  id: string
  rule_id: string
  device_id: string
  action: string
  status: string
  result: string
  executed_at: string
  duration: number
}

export interface RuleStatusChangeEvent {
  rule_id: string
  enabled: boolean
  changed_by: string
  changed_at: string
}

export interface SystemAlertEvent {
  level: string
  message: string
  component: string
  timestamp: string
}

export interface StatisticsUpdateEvent {
  total_executions: number
  success_executions: number
  failed_executions: number
  success_rate: number
  online_devices: number
  offline_devices: number
  active_rules: number
}
