import { ElMessage } from 'element-plus'
import type { WebSocketMessage } from '@/api/linkage'

/**
 * 告警联动WebSocket客户端
 * 用于实时接收设备状态更新、执行记录等信息
 */
export class LinkageWebSocket {
  private ws: WebSocket | null = null
  private callbacks: Map<string, Function[]> = new Map()
  private reconnectTimer: number | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000
  private isConnecting = false

  constructor(private url: string = 'ws://localhost:8080/api/linkage/ws') {}

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
        resolve()
        return
      }

      this.isConnecting = true

      try {
        this.ws = new WebSocket(this.url)

        this.ws.onopen = () => {
          console.log('告警联动WebSocket连接成功')
          this.isConnecting = false
          this.reconnectAttempts = 0
          this.clearReconnectTimer()
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data)
            this.handleMessage(message)
          } catch (error) {
            console.error('WebSocket消息解析失败:', error)
          }
        }

        this.ws.onclose = (event) => {
          console.log('告警联动WebSocket连接关闭:', event.code, event.reason)
          this.isConnecting = false
          this.ws = null
          
          // 非正常关闭时尝试重连
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect()
          }
        }

        this.ws.onerror = (error) => {
          console.error('告警联动WebSocket连接错误:', error)
          this.isConnecting = false
          reject(error)
        }
      } catch (error) {
        this.isConnecting = false
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  disconnect() {
    this.clearReconnectTimer()
    if (this.ws) {
      this.ws.close(1000, '主动断开连接')
      this.ws = null
    }
  }

  /**
   * 订阅消息类型
   */
  subscribe(eventType: string, callback: Function) {
    if (!this.callbacks.has(eventType)) {
      this.callbacks.set(eventType, [])
    }
    this.callbacks.get(eventType)!.push(callback)
  }

  /**
   * 取消订阅
   */
  unsubscribe(eventType: string, callback?: Function) {
    if (!this.callbacks.has(eventType)) return

    if (callback) {
      const callbacks = this.callbacks.get(eventType)!
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    } else {
      this.callbacks.delete(eventType)
    }
  }

  /**
   * 发送消息
   */
  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket未连接，无法发送消息')
    }
  }

  /**
   * 获取连接状态
   */
  get isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage) {
    const callbacks = this.callbacks.get(message.type) || []
    callbacks.forEach(callback => {
      try {
        callback(message.payload)
      } catch (error) {
        console.error('WebSocket消息处理回调执行失败:', error)
      }
    })
  }

  /**
   * 安排重连
   */
  private scheduleReconnect() {
    if (this.reconnectTimer) return

    this.reconnectAttempts++
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连...`)
    
    this.reconnectTimer = window.setTimeout(() => {
      this.reconnectTimer = null
      this.connect().catch(() => {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          ElMessage.error('WebSocket连接失败，请刷新页面重试')
        }
      })
    }, delay)
  }

  /**
   * 清除重连定时器
   */
  private clearReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }
}

// 创建全局实例
export const linkageWebSocket = new LinkageWebSocket()

// 消息类型常量
export const MESSAGE_TYPES = {
  DEVICE_STATUS_UPDATE: 'device_status_update',
  EXECUTION_RECORD: 'execution_record',
  RULE_STATUS_CHANGE: 'rule_status_change',
  SYSTEM_ALERT: 'system_alert',
  STATISTICS_UPDATE: 'statistics_update'
} as const

// 设备状态更新消息
export interface DeviceStatusUpdateMessage {
  device_id: string
  status: string
  last_seen: string
  error_count: number
  response_time?: number
}

// 执行记录消息
export interface ExecutionRecordMessage {
  id: number
  rule_id: string
  device_id: string
  action: string
  status: string
  result: string
  executed_at: string
}

// 规则状态变更消息
export interface RuleStatusChangeMessage {
  rule_id: string
  enabled: boolean
  changed_by: string
  changed_at: string
}

// 系统告警消息
export interface SystemAlertMessage {
  level: string
  message: string
  component: string
  timestamp: string
}

// 统计信息更新消息
export interface StatisticsUpdateMessage {
  total_executions: number
  success_executions: number
  failed_executions: number
  success_rate: number
  online_devices: number
  offline_devices: number
  active_rules: number
}
