import type { PipelineNode, PipelineConnection, PipelineStatus } from '@/api/pipeline'

// Vue Flow 节点类型
export interface VueFlowNode {
  id: string
  type: string
  label: string
  position: { x: number; y: number }
  data: {
    id: string
    type: string
    typeName: string
    params: Record<string, any>
    state: string
    metrics: Record<string, any>
  }
  class: string
}

// Vue Flow 边类型
export interface VueFlowEdge {
  id: string
  source: string
  target: string
  animated: boolean
  style: Record<string, any>
  label?: string
  type: string
}

/**
 * 将API节点数据转换为Vue Flow节点格式
 * @param apiNodes API返回的节点数据
 * @param statusData 节点状态数据（可选）
 * @param connections 连接关系数据（可选，用于优化布局）
 * @returns Vue Flow格式的节点数组
 */
export function convertToVueFlowNodes(
  apiNodes: PipelineNode[],
  statusData: PipelineStatus | null = null,
  connections: PipelineConnection[] = []
): VueFlowNode[] {
  // 生成节点布局
  const layout = generateNodesLayout(apiNodes, connections)

  return apiNodes.map((node, index) => {
    // 查找节点状态信息
    let nodeStatus: any = null

    if (statusData && statusData.nodes) {
      try {
        // 解析状态数据中的节点状态JSON字符串
        const parsedNodes = JSON.parse(statusData.nodes)
        nodeStatus = parsedNodes.find((n: any) => n.id === node.id)
      } catch (e) {
        console.error('解析节点状态数据失败', e)
      }
    }

    // 创建Vue Flow节点对象
    return {
      id: node.id,
      // 根据节点类型决定使用哪种节点组件
      type: getNodeType(node.type),
      // 显示的文本标签
      label: node.id,
      // 节点的位置（从布局中获取或使用默认位置）
      position: layout[node.id] || { x: 60 * index, y: 60 * (index % 4) },
      // 传递给节点组件的数据
      data: {
        id: node.id,
        type: node.type,
        params: node.params,
        // 节点状态，默认为idle
        state: nodeStatus?.state || 'idle',
        // 节点指标数据
        metrics: nodeStatus?.metrics || {},
        // 添加类型显示名称
        typeName: formatNodeTypeName(node.type)
      },
      // 节点样式类名，根据状态设置不同样式
      class: `node-${nodeStatus?.state || 'idle'}`
    }
  })
}

/**
 * 将API连接数据转换为Vue Flow边格式
 * @param apiConnections API返回的连接数据
 * @returns Vue Flow格式的边数组
 */
export function convertToVueFlowEdges(apiConnections: any[]): VueFlowEdge[] {
  const edges: VueFlowEdge[] = []

  apiConnections.forEach(conn => {
    // 处理简单连接格式（从配置文件生成的格式）
    if (typeof conn.from === 'string' && typeof conn.to === 'string') {
      edges.push({
        id: `edge-${conn.id}`,
        source: conn.from,
        target: conn.to,
        // 设置为true以显示动画效果
        animated: true,
        // 边的样式
        style: {
          stroke: '#6366f1',
          strokeWidth: 2,
          strokeDasharray: '8,4' // 优化虚线效果
        },
        // 标签（可选）
        label: `连接 ${conn.id}`,
        // 类型（可选，如果有自定义边组件）
        type: 'default'
      })
    }
    // 处理复杂连接格式（原有的格式）
    else if (conn.from && typeof conn.from === 'object') {
      // 处理单一源连接
      if (conn.from.is_single && conn.from.source) {
        edges.push({
          id: `edge-${conn.id}`,
          source: conn.from.source,
          target: conn.to,
          animated: true,
          style: {
            stroke: '#6366f1',
            strokeWidth: 2,
            strokeDasharray: '8,4'
          },
          label: `连接 ${conn.id}`,
          type: 'default'
        })
      }
      // 处理多源连接
      else if (!conn.from.is_single && conn.from.sources) {
        conn.from.sources.forEach((source, idx) => {
          edges.push({
            id: `edge-${conn.id}-${idx}`,
            source: source,
            target: conn.to,
            animated: true,
            style: {
              stroke: '#6366f1',
              strokeWidth: 2,
              strokeDasharray: '8,4'
            },
            label: `连接 ${conn.id}-${idx}`,
            type: 'default'
          })
        })
      }
    }
  })
  
  return edges
}

/**
 * 格式化节点类型名称
 * @param nodeType 原始节点类型名
 * @returns 格式化后的名称
 */
function formatNodeTypeName(nodeType: string): string {
  // 移除前缀并将下划线替换为空格
  return nodeType
    .replace(/^vp_/, '')
    .replace(/_/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

/**
 * 根据节点类型确定使用哪种Vue Flow节点组件
 * @param apiNodeType API节点类型
 * @returns Vue Flow节点类型名称
 */
function getNodeType(apiNodeType: string): string {
  // 根据节点类型返回不同的组件
  if (apiNodeType.includes('src') || apiNodeType.includes('input')) {
    return 'sourceNode' // 源节点类型
  }
  if (apiNodeType.includes('detector') || apiNodeType.includes('tracker') || apiNodeType.includes('encoder')) {
    return 'processorNode' // 处理器节点类型
  }
  if (apiNodeType.includes('des') || apiNodeType.includes('output') || apiNodeType.includes('sink')) {
    return 'sinkNode' // 输出节点类型
  }
  // 默认节点类型
  return 'defaultNode'
}

/**
 * 生成节点的布局位置 - 流水线式布局
 * @param nodes 节点数组
 * @param connections 连接关系数组（可选）
 * @returns 包含每个节点ID和其位置的对象
 */
function generateNodesLayout(
  nodes: PipelineNode[],
  connections: PipelineConnection[] = []
): Record<string, { x: number; y: number }> {
  // 流水线式布局参数
  const xSpacing = 250     // 水平间距 - 增加间距避免拥挤
  const ySpacing = 120     // 垂直间距 - 确保节点不重叠
  const startX = 80        // 起始X位置
  const startY = 80        // 起始Y位置

  // 如果有连接关系，使用基于拓扑排序的流水线布局
  if (connections.length > 0) {
    return generatePipelineLayout(nodes, connections, xSpacing, ySpacing, startX, startY)
  }

  // 否则使用简单的类型分组布局
  return generateTypeBasedLayout(nodes, xSpacing, ySpacing, startX, startY)
}

/**
 * 基于类型的简单布局（当没有连接关系时使用）
 */
function generateTypeBasedLayout(
  nodes: PipelineNode[],
  xSpacing: number,
  ySpacing: number,
  startX: number,
  startY: number
): Record<string, { x: number; y: number }> {
  const layout: Record<string, { x: number; y: number }> = {}

  // 按类型分组
  const nodeTypes = {
    source: [] as string[],
    processor: [] as string[],
    sink: [] as string[]
  }

  nodes.forEach(node => {
    if (node.type.includes('src') || node.type.includes('input')) {
      nodeTypes.source.push(node.id)
    } else if (node.type.includes('des') || node.type.includes('output') || node.type.includes('sink')) {
      nodeTypes.sink.push(node.id)
    } else {
      nodeTypes.processor.push(node.id)
    }
  })

  // 计算总的节点数，用于合理分布Y坐标
  let currentY = startY

  // 源节点 - 第一列
  nodeTypes.source.forEach((nodeId) => {
    layout[nodeId] = {
      x: startX,
      y: currentY
    }
    currentY += ySpacing
  })

  // 重置Y坐标为处理节点
  currentY = startY

  // 处理节点 - 第二列
  nodeTypes.processor.forEach((nodeId) => {
    layout[nodeId] = {
      x: startX + xSpacing,
      y: currentY
    }
    currentY += ySpacing
  })

  // 重置Y坐标为输出节点
  currentY = startY

  // 输出节点 - 第三列
  nodeTypes.sink.forEach((nodeId) => {
    layout[nodeId] = {
      x: startX + xSpacing * 2,
      y: currentY
    }
    currentY += ySpacing
  })

  return layout
}

/**
 * 基于拓扑排序的真正流水线布局
 */
function generatePipelineLayout(
  nodes: PipelineNode[],
  connections: PipelineConnection[],
  xSpacing: number,
  ySpacing: number,
  startX: number,
  startY: number
): Record<string, { x: number; y: number }> {
  const layout: Record<string, { x: number; y: number }> = {}

  // 构建邻接表和入度表
  const adjacencyList = new Map<string, string[]>()
  const inDegree = new Map<string, number>()

  // 初始化所有节点
  nodes.forEach(node => {
    adjacencyList.set(node.id, [])
    inDegree.set(node.id, 0)
  })

  // 构建图 - 支持简单连接格式和复杂连接格式
  connections.forEach(conn => {
    // 处理简单连接格式（从配置文件生成的格式）
    if (typeof conn.from === 'string' && typeof conn.to === 'string') {
      adjacencyList.get(conn.from)?.push(conn.to)
      inDegree.set(conn.to, (inDegree.get(conn.to) || 0) + 1)
    }
    // 处理复杂连接格式（原有的格式）
    else if (conn.from && typeof conn.from === 'object') {
      if (conn.from.is_single && conn.from.source) {
        adjacencyList.get(conn.from.source)?.push(conn.to)
        inDegree.set(conn.to, (inDegree.get(conn.to) || 0) + 1)
      } else if (!conn.from.is_single && conn.from.sources) {
        conn.from.sources.forEach(source => {
          adjacencyList.get(source)?.push(conn.to)
          inDegree.set(conn.to, (inDegree.get(conn.to) || 0) + 1)
        })
      }
    }
  })

  // 拓扑排序 - 按层级分组
  const levels: string[][] = []
  const queue: string[] = []
  const tempInDegree = new Map(inDegree)

  // 找到所有入度为0的节点（起始节点）
  nodes.forEach(node => {
    if (tempInDegree.get(node.id) === 0) {
      queue.push(node.id)
    }
  })

  // 按层级处理
  while (queue.length > 0) {
    const currentLevel: string[] = []
    const levelSize = queue.length

    // 处理当前层级的所有节点
    for (let i = 0; i < levelSize; i++) {
      const nodeId = queue.shift()!
      currentLevel.push(nodeId)

      // 更新相邻节点的入度
      adjacencyList.get(nodeId)?.forEach(neighbor => {
        const newInDegree = (tempInDegree.get(neighbor) || 0) - 1
        tempInDegree.set(neighbor, newInDegree)
        if (newInDegree === 0) {
          queue.push(neighbor)
        }
      })
    }

    if (currentLevel.length > 0) {
      levels.push(currentLevel)
    }
  }

  // 如果还有节点没有被处理（可能存在环），按类型添加到相应层级
  const processedNodes = new Set(levels.flat())
  const remainingNodes = nodes.filter(node => !processedNodes.has(node.id))

  if (remainingNodes.length > 0) {
    // 按节点类型分组剩余节点
    const sourceNodes = remainingNodes.filter(node =>
      node.type.includes('src') || node.type.includes('input'))
    const processorNodes = remainingNodes.filter(node =>
      !node.type.includes('src') && !node.type.includes('input') &&
      !node.type.includes('des') && !node.type.includes('output') && !node.type.includes('sink'))
    const sinkNodes = remainingNodes.filter(node =>
      node.type.includes('des') || node.type.includes('output') || node.type.includes('sink'))

    // 将剩余节点添加到适当的层级
    if (sourceNodes.length > 0) {
      if (levels.length === 0) levels.push([])
      levels[0].push(...sourceNodes.map(n => n.id))
    }
    if (processorNodes.length > 0) {
      const midLevel = Math.floor(levels.length / 2) || 0
      if (levels[midLevel]) {
        levels[midLevel].push(...processorNodes.map(n => n.id))
      } else {
        levels.push(processorNodes.map(n => n.id))
      }
    }
    if (sinkNodes.length > 0) {
      levels.push(sinkNodes.map(n => n.id))
    }
  }

  // 根据层级分配位置 - 优化流水线布局
  const maxNodesInLevel = Math.max(...levels.map(level => level.length))
  const totalHeight = (maxNodesInLevel - 1) * ySpacing
  const centerY = startY + totalHeight / 2

  levels.forEach((level, levelIndex) => {
    const x = startX + levelIndex * xSpacing

    // 计算该层级的起始Y位置，使其在垂直方向上居中
    const levelHeight = (level.length - 1) * ySpacing
    const levelStartY = centerY - levelHeight / 2

    level.forEach((nodeId, nodeIndex) => {
      layout[nodeId] = {
        x: x,
        y: Math.max(startY, levelStartY + nodeIndex * ySpacing) // 确保Y坐标不小于startY
      }
    })
  })

  return layout
}