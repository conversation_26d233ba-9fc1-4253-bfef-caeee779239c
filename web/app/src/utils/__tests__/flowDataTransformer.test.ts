import { describe, it, expect } from 'vitest'
import { convertToVueFlowNodes, convertToVueFlowEdges } from '../flowDataTransformer'
import type { PipelineNode, PipelineConnection } from '../../api/pipeline'

describe('flowDataTransformer', () => {
  describe('convertToVueFlowNodes', () => {
    it('应该正确转换简单的线性流水线', () => {
      const nodes: PipelineNode[] = [
        { id: 'src_0', type: 'vp_file_src_node', params: {} },
        { id: 'detector_0', type: 'vp_yunet_face_detector_node', params: {} },
        { id: 'encoder_0', type: 'vp_sface_face_encoder_node', params: {} },
        { id: 'des_0', type: 'vp_screen_des_node', params: {} }
      ]

      const connections: PipelineConnection[] = [
        { id: 'conn_1', from: { is_single: true, source: 'src_0' }, to: 'detector_0' },
        { id: 'conn_2', from: { is_single: true, source: 'detector_0' }, to: 'encoder_0' },
        { id: 'conn_3', from: { is_single: true, source: 'encoder_0' }, to: 'des_0' }
      ]

      const result = convertToVueFlowNodes(nodes, null, connections)

      // 验证节点数量
      expect(result).toHaveLength(4)

      // 验证节点ID
      const nodeIds = result.map(node => node.id)
      expect(nodeIds).toContain('src_0')
      expect(nodeIds).toContain('detector_0')
      expect(nodeIds).toContain('encoder_0')
      expect(nodeIds).toContain('des_0')

      // 验证流水线布局：X坐标应该递增
      const srcNode = result.find(n => n.id === 'src_0')!
      const detectorNode = result.find(n => n.id === 'detector_0')!
      const encoderNode = result.find(n => n.id === 'encoder_0')!
      const desNode = result.find(n => n.id === 'des_0')!

      expect(srcNode.position.x).toBeLessThan(detectorNode.position.x)
      expect(detectorNode.position.x).toBeLessThan(encoderNode.position.x)
      expect(encoderNode.position.x).toBeLessThan(desNode.position.x)
    })

    it('应该正确处理分支流水线', () => {
      const nodes: PipelineNode[] = [
        { id: 'src_0', type: 'vp_file_src_node', params: {} },
        { id: 'detector_0', type: 'vp_yunet_face_detector_node', params: {} },
        { id: 'encoder_0', type: 'vp_sface_face_encoder_node', params: {} },
        { id: 'osd_0', type: 'vp_osd_node', params: {} },
        { id: 'screen_des_0', type: 'vp_screen_des_node', params: {} },
        { id: 'rtmp_des_0', type: 'vp_rtmp_des_node', params: {} }
      ]

      const connections: PipelineConnection[] = [
        { id: 'conn_1', from: { is_single: true, source: 'src_0' }, to: 'detector_0' },
        { id: 'conn_2', from: { is_single: true, source: 'detector_0' }, to: 'encoder_0' },
        { id: 'conn_3', from: { is_single: true, source: 'encoder_0' }, to: 'osd_0' },
        { id: 'conn_4', from: { is_single: true, source: 'osd_0' }, to: 'screen_des_0' },
        { id: 'conn_5', from: { is_single: true, source: 'osd_0' }, to: 'rtmp_des_0' }
      ]

      const result = convertToVueFlowNodes(nodes, null, connections)

      // 验证节点数量
      expect(result).toHaveLength(6)

      // 验证分支节点在同一层级（相同X坐标）
      const screenDesNode = result.find(n => n.id === 'screen_des_0')!
      const rtmpDesNode = result.find(n => n.id === 'rtmp_des_0')!
      
      expect(screenDesNode.position.x).toBe(rtmpDesNode.position.x)
      
      // 验证分支节点有不同的Y坐标（避免堆叠）
      expect(screenDesNode.position.y).not.toBe(rtmpDesNode.position.y)
    })

    it('应该正确处理多源汇聚流水线', () => {
      const nodes: PipelineNode[] = [
        { id: 'src_0', type: 'vp_file_src_node', params: {} },
        { id: 'src_1', type: 'vp_file_src_node', params: {} },
        { id: 'detector_0', type: 'vp_yunet_face_detector_node', params: {} },
        { id: 'motion_0', type: 'vp_motion_detector_node', params: {} },
        { id: 'osd_0', type: 'vp_osd_node', params: {} },
        { id: 'des_0', type: 'vp_screen_des_node', params: {} }
      ]

      const connections: PipelineConnection[] = [
        { id: 'conn_1', from: { is_single: true, source: 'src_0' }, to: 'detector_0' },
        { id: 'conn_2', from: { is_single: true, source: 'src_1' }, to: 'motion_0' },
        { id: 'conn_3', from: { is_single: true, source: 'detector_0' }, to: 'osd_0' },
        { id: 'conn_4', from: { is_single: true, source: 'motion_0' }, to: 'osd_0' },
        { id: 'conn_5', from: { is_single: true, source: 'osd_0' }, to: 'des_0' }
      ]

      const result = convertToVueFlowNodes(nodes, null, connections)

      // 验证节点数量
      expect(result).toHaveLength(6)

      // 验证多源节点在同一层级
      const src0Node = result.find(n => n.id === 'src_0')!
      const src1Node = result.find(n => n.id === 'src_1')!
      
      expect(src0Node.position.x).toBe(src1Node.position.x)
      expect(src0Node.position.y).not.toBe(src1Node.position.y)

      // 验证汇聚节点位置正确
      const osdNode = result.find(n => n.id === 'osd_0')!
      const detector0Node = result.find(n => n.id === 'detector_0')!
      const motion0Node = result.find(n => n.id === 'motion_0')!
      
      expect(osdNode.position.x).toBeGreaterThan(detector0Node.position.x)
      expect(osdNode.position.x).toBeGreaterThan(motion0Node.position.x)
    })
  })

  describe('convertToVueFlowEdges', () => {
    it('应该正确转换连接关系为边', () => {
      const connections: PipelineConnection[] = [
        { id: 'conn_1', from: { is_single: true, source: 'src_0' }, to: 'detector_0' },
        { id: 'conn_2', from: { is_single: true, source: 'detector_0' }, to: 'encoder_0' }
      ]

      const result = convertToVueFlowEdges(connections)

      expect(result).toHaveLength(2)
      
      const edge1 = result.find(e => e.id === 'conn_1')!
      expect(edge1.source).toBe('src_0')
      expect(edge1.target).toBe('detector_0')
      
      const edge2 = result.find(e => e.id === 'conn_2')!
      expect(edge2.source).toBe('detector_0')
      expect(edge2.target).toBe('encoder_0')
    })

    it('应该正确处理多源连接', () => {
      const connections: PipelineConnection[] = [
        { 
          id: 'conn_1', 
          from: { is_single: false, sources: ['src_0', 'src_1'] }, 
          to: 'processor_0' 
        }
      ]

      const result = convertToVueFlowEdges(connections)

      expect(result).toHaveLength(2)
      
      const edge1 = result.find(e => e.source === 'src_0')!
      expect(edge1.target).toBe('processor_0')
      expect(edge1.id).toBe('conn_1_src_0')
      
      const edge2 = result.find(e => e.source === 'src_1')!
      expect(edge2.target).toBe('processor_0')
      expect(edge2.id).toBe('conn_1_src_1')
    })
  })
})
