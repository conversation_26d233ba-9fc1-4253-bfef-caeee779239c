import request from '@/utils/request'

/**
 * 系统管理API
 */
export default class SystemAPI {
  /**
   * 获取系统时间信息
   * @returns 系统时间信息
   */
  static getSystemTime() {
    return request({
      url: '/api/system/time',
      method: 'get'
    })
  }

  /**
   * 设置系统时间
   * @param data 时间设置数据
   * @returns 设置结果
   */
  static setSystemTime(data: any) {
    return request({
      url: '/api/system/time/set',
      method: 'post',
      data
    })
  }

  /**
   * 获取时间状态（包含完整信息）
   * @returns 时间状态信息
   */
  static getTimeStatus() {
    return request({
      url: '/api/system/time/status',
      method: 'get'
    })
  }

  /**
   * 获取NTP配置
   * @returns NTP配置信息
   */
  static getNTPConfig() {
    return request({
      url: '/api/system/time/ntp',
      method: 'get'
    })
  }

  /**
   * 设置NTP配置
   * @param data NTP配置数据
   * @returns 设置结果
   */
  static setNTPConfig(data: any) {
    return request({
      url: '/api/system/time/ntp',
      method: 'post',
      data
    })
  }

  /**
   * 执行NTP同步
   * @param data 同步参数
   * @returns 同步结果
   */
  static syncNTP(data: any = {}) {
    return request({
      url: '/api/system/time/ntp/sync',
      method: 'post',
      data
    })
  }

  /**
   * 获取NTP服务器状态
   * @returns NTP服务器状态列表
   */
  static getNTPServersStatus() {
    return request({
      url: '/api/system/time/ntp/servers',
      method: 'get'
    })
  }

  /**
   * 获取当前时区
   * @returns 时区信息
   */
  static getTimezone() {
    return request({
      url: '/api/system/time/timezone',
      method: 'get'
    })
  }

  /**
   * 设置时区
   * @param data 时区设置数据
   * @returns 设置结果
   */
  static setTimezone(data: any) {
    return request({
      url: '/api/system/time/timezone',
      method: 'post',
      data
    })
  }

  /**
   * 获取可用时区列表
   * @returns 时区列表
   */
  static getAvailableTimezones() {
    return request({
      url: '/api/system/time/timezones',
      method: 'get'
    })
  }
}
