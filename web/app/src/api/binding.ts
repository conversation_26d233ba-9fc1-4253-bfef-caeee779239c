import request from "@/utils/request";

/**
 * 视频源算法绑定API接口
 */
export default class BindingAPI {
  /**
   * 获取绑定列表
   * @param params 查询参数
   * @returns 绑定列表
   */
  static getBindings(params?: any) {
    return request({
      url: '/api/bindings',
      method: 'get',
      params
    });
  }

  /**
   * 添加绑定
   * @param data 绑定数据
   * @returns 添加结果
   */
  static addBinding(data: any) {
    return request({
      url: '/api/bindings',
      method: 'post',
      data
    });
  }

  /**
   * 批量添加绑定
   * @param videoId 视频源ID
   * @param data 绑定数据数组
   * @returns 添加结果
   */
  static batchAddBindings(videoId: number, data: any[]) {
    return request({
      url: `/api/videos/${videoId}/bindings`,
      method: 'post',
      data
    });
  }

  /**
   * 更新绑定
   * @param id 绑定ID
   * @param data 绑定数据
   * @returns 更新结果
   */
  static updateBinding(id: number, data: any) {
    return request({
      url: `/api/bindings/${id}`,
      method: 'put',
      data
    });
  }

  /**
   * 获取指定绑定
   * @param id 绑定ID
   * @returns 绑定详情
   */
  static getBinding(id: number) {
    return request({
      url: `/api/bindings/${id}`,
      method: 'get'
    });
  }

  /**
   * 更新绑定状态
   * @param id 绑定ID
   * @param status 状态
   * @returns 更新结果
   */
  static updateBindingStatus(id: number, status: string) {
    return request({
      url: `/api/bindings/${id}/status`,
      method: 'put',
      data: { status }
    });
  }
} 