import request from "@/utils/request";

/**
 * 告警记录API接口
 */
export default class AlertAPI {
  /**
   * 查询告警记录列表
   * @param params 查询参数
   * @returns 告警记录列表
   */
  static getAlerts(params?: AlertQueryParams) {
    return request({
      url: '/api/alerts/query',
      method: 'get',
      params
    });
  }

  /**
   * 获取单个告警记录
   * @param id 告警记录ID
   * @returns 告警记录详情
   */
  static getAlert(id: number) {
    return request({
      url: `/api/alerts/${id}`,
      method: 'get'
    });
  }

  /**
   * 更新告警状态
   * @param id 告警记录ID
   * @param status 新状态
   * @returns 更新结果
   */
  static updateAlertStatus(id: number, status: string) {
    return request({
      url: `/api/alerts/${id}/status`,
      method: 'put',
      data: { status }
    });
  }

  /**
   * 删除告警记录
   * @param id 告警记录ID
   * @returns 删除结果
   */
  static deleteAlert(id: number) {
    return request({
      url: `/api/alerts/${id}`,
      method: 'delete'
    });
  }

  /**
   * 上传告警图片
   * @param id 告警记录ID
   * @param formData 图片表单数据
   * @returns 上传结果
   */
  static uploadAlertImages(id: number, formData: FormData) {
    return request({
      url: `/api/alerts/${id}/images`,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
}

/**
 * 告警查询参数
 */
export interface AlertQueryParams {
  /** 页码 */
  page?: number;
  /** 每页数量 */
  limit?: number;
  /** 视频源ID */
  videoId?: number;
  /** 算法ID */
  algorithmId?: number;
  /** 视频源名称 */
  videoName?: string;
  /** 危险等级 */
  level?: string;
  /** 告警类型 */
  type?: string;
  /** 处理状态 */
  status?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}

/**
 * 告警记录
 */
export interface Alert {
  /** 告警记录ID */
  id: number;
  /** 告警来源 */
  source: string;
  /** 摄像头IP */
  ip: string;
  /** 危险等级 */
  level: string;
  /** 告警类型 */
  type: string;
  /** 视频源ID */
  video_id: number;
  /** 视频源名称 */
  video_name: string;
  /** 算法ID */
  algorithm_id: number;
  /** 算法名称 */
  algorithm_name: string;
  /** 告警内容 */
  content: string;
  /** 原图地址 */
  original_image: string;
  /** 告警图地址 */
  image_path: string;
  /** 处理状态 */
  status: string;
  /** 创建时间 */
  created_at: string;
  /** 更新时间 */
  updated_at: string;
}

/**
 * 分页响应
 */
export interface PageResult<T> {
  /** 数据列表 */
  list: T[];
  /** 分页信息 */
  paging: {
    /** 当前页 */
    page: number;
    /** 每页数量 */
    limit: number;
    /** 总数 */
    total: number;
  };
}
