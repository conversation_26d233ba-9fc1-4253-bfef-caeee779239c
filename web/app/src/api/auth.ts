import request from "@/utils/request";

class AuthAPI {
  /** 登录 接口*/
  static login(data: LoginData) {
    return request<any, LoginResult>({
      url: "/api/auth/login",
      method: "post",
      data: {
        username: data.username,
        password: data.password
      },
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  /** 注销 接口*/
  static logout() {
    return request({
      url: "/api/auth/logout",
      method: "delete",
    });
  }

  /** 获取验证码 接口*/
  static getCaptcha() {
    return request<any, CaptchaResult>({
      url: "/api/auth/captcha",
      method: "get",
    });
  }
}

export default AuthAPI;

/** 登录请求参数 */
export interface LoginData {
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
  /** 验证码缓存key */
  captchaKey?: string;
  /** 验证码 */
  captchaCode?: string;
}

/** 登录响应 */
export interface LoginResult {
  /** 错误码 */
  errorCode: number;
  /** 描述信息 */
  message: string;
  /** 响应数据 */
  data: {
  /** 访问token */
    access_token: string;
  /** 刷新token */
    refresh_token: string;
    /** 过期时间 */
    expires_at: string;
    /** token类型 */
    token_type: string;
    /** 用户ID */
    user_id: number;
    /** 用户名 */
    username: string;
    /** 显示名称 */
    name: string;
    /** 角色 */
    role: string;
  };
  /** 成功标识 */
  success: boolean;
}

/** 验证码响应 */
export interface CaptchaResult {
  /** 验证码缓存key */
  captchaKey: string;
  /** 验证码图片Base64字符串 */
  captchaBase64: string;
}
