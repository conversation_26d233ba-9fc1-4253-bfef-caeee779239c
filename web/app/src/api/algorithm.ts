import request from "@/utils/request";

/**
 * 算法API接口
 */
export default class AlgorithmAPI {
  /**
   * 获取算法列表
   * @param params 查询参数
   * @returns 算法列表
   */
  static getAlgorithms(params?: any) {
    return request({
      url: '/api/algorithms',
      method: 'get',
      params
    });
  }

  /**
   * 导入算法
   * @param formData 算法表单数据
   * @returns 导入结果
   */
  static importAlgorithm(formData: FormData) {
    return request({
      url: '/api/algorithms',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  /**
   * 激活算法
   * @param id 算法ID
   * @returns 激活结果
   */
  static activateAlgorithm(id: number) {
    return request({
      url: `/api/algorithms/${id}/activate`,
      method: 'put'
    });
  }

  /**
   * 停用算法
   * @param id 算法ID
   * @returns 停用结果
   */
  static deactivateAlgorithm(id: number) {
    return request({
      url: `/api/algorithms/${id}/deactivate`,
      method: 'put'
    });
  }

  /**
   * 获取算法绑定的视频源
   * @param id 算法ID
   * @returns 视频源列表
   */
  static getAlgorithmBindings(id: number) {
    return request({
      url: `/api/algorithms/${id}/bindings`,
      method: 'get'
    });
  }
} 