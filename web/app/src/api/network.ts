import request from '@/utils/request'

/**
 * 网络管理API
 */
export default class NetworkAPI {
  /**
   * 获取网络接口列表
   * @returns 网络接口列表
   */
  static getInterfaces() {
    return request({
      url: '/api/network/interfaces',
      method: 'get'
    })
  }

  /**
   * 配置网络接口
   * @param config 接口配置
   * @returns 配置结果
   */
  static configureInterface(config: any) {
    return request({
      url: `/api/network/interfaces/${config.name}/config`,
      method: 'post',
      data: config
    })
  }

  /**
   * 获取DNS配置
   * @returns DNS配置
   */
  static getDNSConfig() {
    return request({
      url: '/api/network/dns',
      method: 'get'
    })
  }

  /**
   * 设置DNS配置
   * @param config DNS配置
   * @returns 设置结果
   */
  static setDNSConfig(config: any) {
    return request({
      url: '/api/network/dns',
      method: 'post',
      data: config
    })
  }

  /**
   * 获取路由表
   * @returns 路由表
   */
  static getRoutes() {
    return request({
      url: '/api/network/routes',
      method: 'get'
    })
  }

  /**
   * 添加路由
   * @param route 路由信息
   * @returns 添加结果
   */
  static addRoute(route: any) {
    return request({
      url: '/api/network/routes',
      method: 'post',
      data: route
    })
  }

  /**
   * 删除路由
   * @param route 路由信息
   * @returns 删除结果
   */
  static deleteRoute(route: any) {
    return request({
      url: '/api/network/routes',
      method: 'delete',
      data: route
    })
  }

  /**
   * 更新路由
   * @param route 路由信息
   * @returns 更新结果
   */
  static updateRoute(route: any) {
    return request({
      url: '/api/network/routes',
      method: 'put',
      data: route
    })
  }

  /**
   * 获取网络状态
   * @returns 网络状态
   */
  static getNetworkStatus() {
    return request({
      url: '/api/network/status',
      method: 'get'
    })
  }

  /**
   * 获取流量统计
   * @returns 流量统计
   */
  static getTrafficStats() {
    return request({
      url: '/api/network/traffic',
      method: 'get'
    })
  }

  /**
   * 获取网络质量
   * @param target 测试目标
   * @returns 网络质量
   */
  static getNetworkQuality(target: string) {
    return request({
      url: '/api/network/quality',
      method: 'get',
      params: { target }
    })
  }

  /**
   * 获取连接统计
   * @returns 连接统计
   */
  static getConnectionStats() {
    return request({
      url: '/api/network/connections',
      method: 'get'
    })
  }

  /**
   * 获取防火墙规则
   * @returns 防火墙规则列表
   */
  static getFirewallRules() {
    return request({
      url: '/api/network/firewall/rules',
      method: 'get'
    })
  }

  /**
   * 添加防火墙规则
   * @param rule 防火墙规则
   * @returns 添加结果
   */
  static addFirewallRule(rule: any) {
    return request({
      url: '/api/network/firewall/rules',
      method: 'post',
      data: rule
    })
  }

  /**
   * 删除防火墙规则
   * @param ruleId 规则ID
   * @returns 删除结果
   */
  static deleteFirewallRule(ruleId: string) {
    return request({
      url: `/api/network/firewall/rules/${ruleId}`,
      method: 'delete'
    })
  }

  /**
   * 获取防火墙状态
   * @returns 防火墙状态
   */
  static getFirewallStatus() {
    return request({
      url: '/api/network/firewall/status',
      method: 'get'
    })
  }

  /**
   * 执行ping测试
   * @param target 测试目标
   * @param count 测试次数
   * @returns ping测试结果
   */
  static pingTest(target: string, count: number = 4) {
    return request({
      url: '/api/network/diagnostic/ping',
      method: 'post',
      data: { target, count }
    })
  }

  /**
   * 执行traceroute测试
   * @param target 测试目标
   * @returns traceroute测试结果
   */
  static tracerouteTest(target: string) {
    return request({
      url: '/api/network/diagnostic/traceroute',
      method: 'post',
      data: { target }
    })
  }

  /**
   * 执行端口扫描
   * @param target 扫描目标
   * @param ports 端口列表
   * @returns 端口扫描结果
   */
  static portScan(target: string, ports: number[]) {
    return request({
      url: '/api/network/diagnostic/portscan',
      method: 'post',
      data: { target, ports }
    })
  }

  /**
   * 执行网络诊断
   * @returns 诊断结果
   */
  static diagnoseNetwork() {
    return request({
      url: '/api/network/diagnostic/diagnose',
      method: 'post'
    })
  }

  /**
   * 获取网络监控数据
   * @param timeRange 时间范围
   * @returns 监控数据
   */
  static getMonitorData(timeRange: string = '1h') {
    return request({
      url: '/api/network/monitor',
      method: 'get',
      params: { time_range: timeRange }
    })
  }

  /**
   * 导出网络配置
   * @returns 配置文件
   */
  static exportConfig() {
    return request({
      url: '/api/network/config/export',
      method: 'get',
      responseType: 'blob'
    })
  }

  /**
   * 导入网络配置
   * @param file 配置文件
   * @returns 导入结果
   */
  static importConfig(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    
    return request({
      url: '/api/network/config/import',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 重启网络服务
   * @returns 重启结果
   */
  static restartNetworkService() {
    return request({
      url: '/api/network/service/restart',
      method: 'post'
    })
  }

  /**
   * 获取网络服务状态
   * @returns 服务状态
   */
  static getNetworkServiceStatus() {
    return request({
      url: '/api/network/service/status',
      method: 'get'
    })
  }
}
