import request from "@/utils/request";

class MenuAPI {
  /**
   * 获取当前用户的路由列表
   * <p/>
   * 无需传入角色，后端解析token获取角色自行判断是否拥有路由的权限
   *
   * @returns 路由列表
   */
  static getRoutes() {
    // 返回静态菜单数据，适配边缘计算平台
    return Promise.resolve(staticRoutes);
  }
}

// 边缘计算平台静态菜单
const staticRoutes: RouteVO[] = [
  {
    path: '/video',
    component: 'Layout',
    redirect: '/video/camera',
    name: 'Video',
    meta: {
      title: '视频接入',
      icon: 'el-icon-VideoCamera'
    },
    children: [
      {
        path: 'camera',
        component: 'video/camera',
        name: 'VideoCamera',
        meta: {
          title: '摄像头',
          icon: 'el-icon-Camera'
        },
        children: []
      },
      {
        path: 'stream',
        component: 'video/stream',
        name: 'VideoStream',
        meta: {
          title: '视频流',
          icon: 'el-icon-VideoPlay'
        },
        children: []
      },
      {
        path: 'file',
        component: 'video/file',
        name: 'VideoFile',
        meta: {
          title: '视频文件',
          icon: 'el-icon-Document'
        },
        children: []
      }
    ]
  },
  {
    path: '/algorithm',
    component: 'Layout',
    redirect: '/algorithm/list',
    name: 'Algorithm',
    meta: {
      title: '算法管理',
      icon: 'el-icon-Cpu'
    },
    children: [
      {
        path: 'list',
        component: 'algorithm/list',
        name: 'AlgorithmList',
        meta: {
          title: '算法列表',
          icon: 'el-icon-List'
        },
        children: []
      },
      {
        path: 'status',
        component: 'algorithm/status',
        name: 'AlgorithmStatus',
        meta: {
          title: '算法部署',
          icon: 'el-icon-Monitor'
        },
        children: []
      }
    ]
  },
  {
    path: '/alert',
    component: 'Layout',
    redirect: '/alert/list',
    name: 'Alert',
    meta: {
      title: '告警记录',
      icon: 'el-icon-Bell'
    },
    children: [
      {
        path: 'list',
        component: 'alert/list',
        name: 'AlertList',
        meta: {
          title: '告警列表',
          icon: 'el-icon-Warning'
        },
        children: []
      }
    ]
  },
  {
    path: '/linkage',
    component: 'Layout',
    redirect: '/linkage/dashboard',
    name: 'Linkage',
    meta: {
      title: '告警联动',
      icon: 'el-icon-Connection'
    },
    children: [
      // {
      //   path: 'dashboard',
      //   component: 'linkage/dashboard/index',
      //   name: 'LinkageDashboard',
      //   meta: {
      //     title: '联动监控',
      //     icon: 'el-icon-Monitor',
      //     keepAlive: true
      //   },
      //   children: []
      // },
      {
        path: 'rules',
        component: 'linkage/rules/index',
        name: 'LinkageRules',
        meta: {
          title: '联动规则',
          icon: 'el-icon-Setting',
          keepAlive: true
        },
        children: []
      },
      {
        path: 'rules/create',
        component: 'linkage/rules/form',
        name: 'CreateLinkageRule',
        meta: {
          title: '创建规则',
          hidden: true,
          activeMenu: '/linkage/rules'
        },
        children: []
      },
      {
        path: 'rules/edit/:id',
        component: 'linkage/rules/form',
        name: 'EditLinkageRule',
        meta: {
          title: '编辑规则',
          hidden: true,
          activeMenu: '/linkage/rules'
        },
        children: []
      },
      {
        path: 'devices',
        component: 'linkage/devices/index',
        name: 'LinkageDevices',
        meta: {
          title: '联动设备',
          icon: 'el-icon-Cpu',
          keepAlive: true
        },
        children: []
      },
      {
        path: 'devices/create',
        component: 'linkage/devices/form',
        name: 'CreateLinkageDevice',
        meta: {
          title: '添加设备',
          hidden: true,
          activeMenu: '/linkage/devices'
        },
        children: []
      },
      {
        path: 'devices/edit/:id',
        component: 'linkage/devices/form',
        name: 'EditLinkageDevice',
        meta: {
          title: '编辑设备',
          hidden: true,
          activeMenu: '/linkage/devices'
        },
        children: []
      },
      {
        path: 'executions',
        component: 'linkage/executions/index',
        name: 'LinkageExecutions',
        meta: {
          title: '执行记录',
          icon: 'el-icon-Document',
          keepAlive: true
        },
        children: []
      },
      {
        path: 'settings',
        component: 'linkage/settings/index',
        name: 'LinkageSettings',
        meta: {
          title: '联动设置',
          icon: 'el-icon-Tools'
        },
        children: []
      }
    ]
  },
  {
    path: '/system',
    component: 'Layout',
    redirect: '/system/user',
    name: 'System',
    meta: {
      title: '系统设置',
      icon: 'el-icon-Setting'
    },
    children: [
      {
        path: 'user',
        component: 'system/user',
        name: 'User',
        meta: {
          title: '用户信息',
          icon: 'el-icon-User'
        },
        children: []
      },
      {
        path: 'network',
        component: 'system/network/index',
        name: 'NetworkConfig',
        meta: {
          title: '网络配置',
          icon: 'el-icon-Connection'
        },
        children: []
      },
      {
        path: 'time',
        component: 'system/time',
        name: 'SystemTime',
        meta: {
          title: '系统时间',
          icon: 'el-icon-Timer'
        },
        children: []
      }
    ]
  }
];

export default MenuAPI;

/** RouteVO，路由对象 */
export interface RouteVO {
  /** 子路由列表 */
  children: RouteVO[];
  /** 组件路径 */
  component?: string;
  /** 路由属性 */
  meta?: Meta;
  /** 路由名称 */
  name?: string;
  /** 路由路径 */
  path?: string;
  /** 跳转链接 */
  redirect?: string;
}

/** Meta，路由属性 */
export interface Meta {
  /** 【目录】只有一个子路由是否始终显示 */
  alwaysShow?: boolean;
  /** 是否隐藏(true-是 false-否) */
  hidden?: boolean;
  /** ICON */
  icon?: string;
  /** 【菜单】是否开启页面缓存 */
  keepAlive?: boolean;
  /** 路由title */
  title?: string;
  /** 是否固定在 tabs nav */
  affix?: boolean;
  /** 当前路由为详情页时，需要高亮的菜单 */
  activeMenu?: string;
}
