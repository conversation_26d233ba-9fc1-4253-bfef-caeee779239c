import request from "@/utils/request";

// 联动规则相关接口
export interface LinkageRule {
  id?: string;
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  conditions: RuleConditions;
  actions: LinkageAction[];
  created_at?: string;
  updated_at?: string;
}

// 规则条件接口
export interface RuleConditions {
  video_ids?: number[];
  algorithm_ids?: number[];
  levels?: string[];
  types?: string[];
  time_range?: TimeRange;
  expression?: string;
}

// 时间范围接口
export interface TimeRange {
  start_time: string;
  end_time: string;
  weekdays: number[];
}

// 联动动作接口
export interface LinkageAction {
  device_id: string;
  command: string;
  params: Record<string, any>;
  delay: number;
}

export interface LinkageRuleQuery {
  page?: number;
  size?: number;
  name?: string;
  enabled?: boolean;
  sort?: string;
  order?: string;
}

// 联动设备相关接口
export interface LinkageDevice {
  id?: string;
  name: string;
  type: string;
  protocol: string;
  address: string;
  port: number;
  config: DeviceConfig;
  status?: string;
  last_seen?: string;
  error_count?: number;
  created_at?: string;
  updated_at?: string;
}

// 设备配置接口
export interface DeviceConfig {
  timeout?: number;
  retry_count?: number;
  heartbeat_interval?: number;
  [key: string]: any; // 允许协议特定的配置
}

export interface LinkageDeviceQuery {
  page?: number;
  size?: number;
  name?: string;
  type?: string;
  protocol?: string;
  status?: string;
  sort?: string;
  order?: string;
}

// 联动执行记录相关接口
export interface LinkageExecution {
  id?: number;
  rule_id: string;
  device_id: string;
  action: string;
  params?: string;
  status: string;
  duration: number;
  error_message?: string;
  executed_at: string;
}

export interface LinkageExecutionQuery {
  page?: number;
  size?: number;
  rule_id?: string;
  device_id?: string;
  status?: string;
  start_time?: string;
  end_time?: string;
  sort?: string;
  order?: string;
}

// 系统统计信息
export interface LinkageStatistics {
  total_executions: number;
  success_executions: number;
  failed_executions: number;
  success_rate: number;
  average_response_time: number;
  online_devices: number;
  offline_devices: number;
  active_rules: number;
}

// 系统健康状态
export interface LinkageHealthStatus {
  status: string;
  engine_status: string;
  queue_length: number;
  active_rules: number;
  protocol_adapters: Record<string, string>;
}

// 批量操作请求
export interface BatchDeleteRequest {
  ids: string[];
}

// 设备测试连接请求
export interface DeviceTestRequest {
  address: string;
  port: number;
  protocol: string;
  config: DeviceConfig;
}

// 设备测试连接响应
export interface DeviceTestResponse {
  success: boolean;
  message: string;
  response_time?: number;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
}

// 联动规则API
export const LinkageRuleAPI = {
  // 获取规则列表
  getList(params: LinkageRuleQuery) {
    return request<LinkagePageResult<LinkageRule[]>>({
      url: "/api/linkage/rules",
      method: "get",
      params,
    });
  },

  // 获取规则详情
  getDetail(id: string) {
    return request<LinkageRule>({
      url: `/api/linkage/rules/${id}`,
      method: "get",
    });
  },

  // 创建规则
  create(data: LinkageRule) {
    return request({
      url: "/api/linkage/rules",
      method: "post",
      data,
    });
  },

  // 更新规则
  update(id: string, data: LinkageRule) {
    return request({
      url: `/api/linkage/rules/${id}`,
      method: "put",
      data,
    });
  },

  // 删除规则
  delete(id: string) {
    return request({
      url: `/api/linkage/rules/${id}`,
      method: "delete",
    });
  },

  // 启用规则
  enable(id: string) {
    return request({
      url: `/api/linkage/rules/${id}/enable`,
      method: "put",
    });
  },

  // 禁用规则
  disable(id: string) {
    return request({
      url: `/api/linkage/rules/${id}/disable`,
      method: "put",
    });
  },

  // 测试规则
  test(id: string, alertData: any) {
    return request({
      url: `/api/linkage/rules/${id}/test`,
      method: "post",
      data: alertData,
    });
  },

  // 批量删除规则
  batchDelete(ids: string[]) {
    return request({
      url: "/api/linkage/rules/batch",
      method: "delete",
      data: { ids },
    });
  },
};

// 联动设备API
export const LinkageDeviceAPI = {
  // 获取设备列表
  getList(params: LinkageDeviceQuery) {
    return request<LinkagePageResult<LinkageDevice[]>>({
      url: "/api/linkage/devices",
      method: "get",
      params,
    });
  },

  // 获取设备详情
  getDetail(id: string) {
    return request<LinkageDevice>({
      url: `/api/linkage/devices/${id}`,
      method: "get",
    });
  },

  // 创建设备
  create(data: LinkageDevice) {
    return request({
      url: "/api/linkage/devices",
      method: "post",
      data,
    });
  },

  // 更新设备
  update(id: string, data: LinkageDevice) {
    return request({
      url: `/api/linkage/devices/${id}`,
      method: "put",
      data,
    });
  },

  // 删除设备
  delete(id: string) {
    return request({
      url: `/api/linkage/devices/${id}`,
      method: "delete",
    });
  },

  // 获取设备状态
  getStatus(id: string) {
    return request({
      url: `/api/linkage/devices/${id}/status`,
      method: "get",
    });
  },

  // 测试设备连接
  test(id: string) {
    return request<DeviceTestResponse>({
      url: `/api/linkage/devices/${id}/test`,
      method: "post",
    });
  },

  // 测试设备连接（使用配置）
  testConnection(data: DeviceTestRequest) {
    return request<DeviceTestResponse>({
      url: "/api/linkage/devices/test-connection",
      method: "post",
      data,
    });
  },

  // 批量删除设备
  batchDelete(ids: string[]) {
    return request({
      url: "/api/linkage/devices/batch",
      method: "delete",
      data: { ids },
    });
  },

  // 手动控制设备
  control(id: string, command: string, params: any = {}) {
    return request({
      url: `/api/linkage/devices/${id}/control`,
      method: "post",
      data: { command, params },
    });
  },
};

// 联动执行记录API
export const LinkageExecutionAPI = {
  // 获取执行记录列表
  getList(params: LinkageExecutionQuery) {
    return request<LinkagePageResult<LinkageExecution[]>>({
      url: "/api/linkage/executions",
      method: "get",
      params,
    });
  },

  // 获取执行记录详情
  getDetail(id: number) {
    return request<LinkageExecution>({
      url: `/api/linkage/executions/${id}`,
      method: "get",
    });
  },

  // 重新执行
  retry(id: number) {
    return request({
      url: `/api/linkage/executions/${id}/retry`,
      method: "post",
    });
  },

  // 获取执行统计
  getStats(params: { period?: string; start_time?: string; end_time?: string }) {
    return request({
      url: "/api/linkage/executions/stats",
      method: "get",
      params,
    });
  },
};

// 系统监控API
export const LinkageSystemAPI = {
  // 获取系统统计信息
  getStatistics() {
    return request<LinkageStatistics>({
      url: "/api/linkage/statistics",
      method: "get",
    });
  },

  // 获取系统健康状态
  getHealthStatus() {
    return request<LinkageHealthStatus>({
      url: "/api/linkage/health",
      method: "get",
    });
  },

  // 获取实时监控数据
  getRealtimeData() {
    return request({
      url: "/api/linkage/realtime",
      method: "get",
    });
  },

  // 获取系统配置
  getSystemConfig() {
    return request({
      url: "/api/linkage/config",
      method: "get",
    });
  },

  // 更新系统配置
  updateSystemConfig(data: any) {
    return request({
      url: "/api/linkage/config",
      method: "put",
      data,
    });
  },

  // 重启联动引擎
  restartEngine() {
    return request({
      url: "/api/linkage/restart",
      method: "post",
    });
  },

  // 清理执行记录
  cleanupExecutions(days: number = 30) {
    return request({
      url: "/api/linkage/cleanup",
      method: "post",
      data: { days },
    });
  },
};

// 获取告警数据（用于规则测试）
export const AlertAPI = {
  // 获取告警列表
  getList(params: any) {
    return request({
      url: "/api/alerts",
      method: "get",
      params,
    });
  },

  // 获取视频源列表
  getVideoSources() {
    return request({
      url: "/api/videos",
      method: "get",
    });
  },

  // 获取算法列表
  getAlgorithms() {
    return request({
      url: "/api/algorithms",
      method: "get",
    });
  },
};
