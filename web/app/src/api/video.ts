import request from "@/utils/request";

/**
 * 视频API接口
 */
export default class VideoAPI {
  /**
   * 获取视频源列表
   * @param params 查询参数
   * @returns 视频源列表
   */
  static getVideos(params?: any) {
    return request({
      url: '/api/videos',
      method: 'get',
      params
    });
  }

  /**
   * 添加视频源
   * @param data 视频源数据
   * @returns 添加结果
   */
  static addVideo(data: any) {
    return request({
      url: '/api/videos',
      method: 'post',
      data
    });
  }

  /**
   * 更新视频源
   * @param id 视频源ID
   * @param data 视频源数据
   * @returns 更新结果
   */
  static updateVideo(id: number, data: any) {
    return request({
      url: `/api/videos/${id}`,
      method: 'put',
      data
    });
  }

  /**
   * 删除视频源
   * @param id 视频源ID
   * @returns 删除结果
   */
  static deleteVideo(id: number) {
    return request({
      url: `/api/videos/${id}`,
      method: 'delete'
    });
  }

  /**
   * 检测视频源是否在线
   * @param id 视频源ID
   * @returns 检测结果
   */
  static checkVideoStatus(id: number) {
    return request({
      url: `/api/videos/${id}/status`,
      method: 'get'
    });
  }

  /**
   * 视频源抓拍
   * @param id 视频源ID
   * @returns 抓拍结果
   */
  static captureSnapshot(id: number) {
    return request({
      url: `/api/videos/${id}/snapshot`,
      method: 'post'
    });
  }

  /**
   * 获取视频源绑定的算法
   * @param id 视频源ID
   * @returns 绑定的算法列表
   */
  static getVideoBindings(id: number) {
    return request({
      url: `/api/videos/${id}/bindings`,
      method: 'get'
    });
  }

  /**
   * 更新视频源绑定的算法
   * @param id 视频源ID
   * @param data 算法绑定数据数组
   * @returns 更新结果
   */
  static updateVideoBindings(id: number, data: any[]) {
    return request({
      url: `/api/videos/${id}/bindings`,
      method: 'post',
      data
    });
  }
} 