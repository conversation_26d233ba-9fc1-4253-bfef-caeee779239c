import request from '@/utils/request'

// 管道信息接口
export interface PipelineInfo {
  id: string
  name: string
  description: string
  version: string
  author: string
  globals: Record<string, any>
  created_at: string
  updated_at: string
}

// 节点信息接口
export interface PipelineNode {
  id: string
  type: string
  params: Record<string, any>
}

// 连接信息接口
export interface PipelineConnection {
  id: string
  from: {
    is_single: boolean
    source?: string
    sources?: string[]
  }
  to: string
}

// 管道状态接口
export interface PipelineStatus {
  pipeline_id: string
  state: string
  start_time: string
  uptime_seconds: number
  nodes: string // JSON字符串
  global_metrics: Record<string, any>
  errors: any[]
}

// API响应格式（匹配后端实际返回格式）
export interface ApiResponse<T> {
  errorCode: number
  message: string
  data: T
  success: boolean
}

// ===== 综合管道相关接口 =====

/**
 * 创建综合管道
 */
export function createComprehensivePipeline(): Promise<ApiResponse<{ message: string; bindings_count: number }>> {
  return request({
    url: '/api/comprehensive-pipeline/create',
    method: 'post'
  })
}

/**
 * 获取综合管道配置
 */
export function getComprehensivePipelineConfig(): Promise<ApiResponse<any>> {
  return request({
    url: '/api/comprehensive-pipeline/config',
    method: 'get'
  })
}

/**
 * 获取综合管道状态
 */
export function getComprehensivePipelineStatus(): Promise<ApiResponse<PipelineStatus>> {
  return request({
    url: '/api/comprehensive-pipeline/status',
    method: 'get'
  })
}

/**
 * 启动综合管道
 */
export function startComprehensivePipeline(): Promise<ApiResponse<{ message: string }>> {
  return request({
    url: '/api/comprehensive-pipeline/start',
    method: 'put'
  })
}

/**
 * 停止综合管道
 */
export function stopComprehensivePipeline(): Promise<ApiResponse<{ message: string }>> {
  return request({
    url: '/api/comprehensive-pipeline/stop',
    method: 'put'
  })
}

/**
 * 重建综合管道
 */
export function rebuildComprehensivePipeline(): Promise<ApiResponse<{ message: string }>> {
  return request({
    url: '/api/comprehensive-pipeline/rebuild',
    method: 'post'
  })
}

/**
 * 删除综合管道
 */
export function deleteComprehensivePipeline(): Promise<ApiResponse<{ message: string }>> {
  return request({
    url: '/api/comprehensive-pipeline',
    method: 'delete'
  })
}