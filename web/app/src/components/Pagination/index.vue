<template>
  <div class="pagination-container">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="pageSizes"
      :total="total"
      :background="background"
      :layout="layout"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  total: number
  page?: number
  limit?: number
  pageSizes?: number[]
  layout?: string
  background?: boolean
  autoScroll?: boolean
  hidden?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  page: 1,
  limit: 20,
  pageSizes: () => [10, 20, 30, 50],
  layout: 'total, sizes, prev, pager, next, jumper',
  background: true,
  autoScroll: true,
  hidden: false
})

interface Emits {
  (e: 'pagination', data: { page: number; limit: number }): void
  (e: 'update:page', page: number): void
  (e: 'update:limit', limit: number): void
}

const emit = defineEmits<Emits>()

const currentPage = computed({
  get() {
    return props.page
  },
  set(val) {
    emit('update:page', val)
  }
})

const pageSize = computed({
  get() {
    return props.limit
  },
  set(val) {
    emit('update:limit', val)
  }
})

const handleSizeChange = (val: number) => {
  if (currentPage.value * val > props.total) {
    currentPage.value = 1
  }
  emit('pagination', { page: currentPage.value, limit: val })
  if (props.autoScroll) {
    scrollTo(0, 800)
  }
}

const handleCurrentChange = (val: number) => {
  emit('pagination', { page: val, limit: pageSize.value })
  if (props.autoScroll) {
    scrollTo(0, 800)
  }
}

const scrollTo = (element: number, duration: number) => {
  if (typeof window === 'undefined') return
  
  const start = window.pageYOffset
  const change = element - start
  const increment = 20
  let currentTime = 0
  
  const animateScroll = () => {
    currentTime += increment
    const val = Math.easeInOutQuad(currentTime, start, change, duration)
    window.scrollTo(0, val)
    if (currentTime < duration) {
      setTimeout(animateScroll, increment)
    }
  }
  
  animateScroll()
}

// 缓动函数
declare global {
  interface Math {
    easeInOutQuad(t: number, b: number, c: number, d: number): number
  }
}

Math.easeInOutQuad = function (t, b, c, d) {
  t /= d / 2
  if (t < 1) {
    return c / 2 * t * t + b
  }
  t--
  return -c / 2 * (t * (t - 2) - 1) + b
}
</script>

<style lang="scss" scoped>
.pagination-container {
  background: #fff;
  padding: 32px 16px;
  text-align: center;
  
  :deep(.el-pagination) {
    .btn-prev,
    .btn-next {
      background-color: transparent;
      border: 1px solid #dcdfe6;
      color: #606266;
      
      &:hover {
        color: #409eff;
        border-color: #409eff;
      }
      
      &.disabled {
        color: #c0c4cc;
        border-color: #e4e7ed;
        cursor: not-allowed;
        
        &:hover {
          color: #c0c4cc;
          border-color: #e4e7ed;
        }
      }
    }
    
    .el-pager {
      li {
        background-color: transparent;
        border: 1px solid #dcdfe6;
        color: #606266;
        margin: 0 2px;
        
        &:hover {
          color: #409eff;
          border-color: #409eff;
        }
        
        &.active {
          background-color: #409eff;
          border-color: #409eff;
          color: #fff;
          
          &:hover {
            background-color: #409eff;
            border-color: #409eff;
            color: #fff;
          }
        }
      }
    }
    
    .el-pagination__sizes {
      .el-select {
        .el-input {
          .el-input__inner {
            border-color: #dcdfe6;
            
            &:hover {
              border-color: #409eff;
            }
            
            &:focus {
              border-color: #409eff;
            }
          }
        }
      }
    }
    
    .el-pagination__jump {
      .el-input {
        .el-input__inner {
          border-color: #dcdfe6;
          
          &:hover {
            border-color: #409eff;
          }
          
          &:focus {
            border-color: #409eff;
          }
        }
      }
    }
    
    .el-pagination__total {
      color: #606266;
      font-weight: normal;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .pagination-container {
    padding: 20px 10px;
    
    :deep(.el-pagination) {
      .el-pagination__sizes,
      .el-pagination__jump {
        display: none;
      }
      
      .el-pagination__total {
        display: none;
      }
      
      .btn-prev,
      .btn-next {
        padding: 0 8px;
        font-size: 12px;
      }
      
      .el-pager {
        li {
          padding: 0 8px;
          font-size: 12px;
          min-width: 28px;
          height: 28px;
          line-height: 26px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .pagination-container {
    :deep(.el-pagination) {
      .el-pager {
        li {
          padding: 0 6px;
          font-size: 11px;
          min-width: 24px;
          height: 24px;
          line-height: 22px;
          margin: 0 1px;
        }
      }
      
      .btn-prev,
      .btn-next {
        padding: 0 6px;
        font-size: 11px;
        height: 24px;
        line-height: 22px;
      }
    }
  }
}
</style>
