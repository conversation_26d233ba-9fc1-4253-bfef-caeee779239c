<template>
  <div class="pipeline-flow-chart">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="spinner"></div>
      <p>加载管道图...</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <el-button @click="loadPipelineData" type="primary">重试</el-button>
    </div>
    
    <!-- 流程图容器 -->
    <div v-else class="flow-container">
      <VueFlow
        v-model:nodes="nodes"
        v-model:edges="edges"
        :node-types="nodeTypes"
        :default-viewport="{ x: 0, y: 0, zoom: 0.8 }"
        :fit-view-on-init="true"
        :min-zoom="0.3"
        :max-zoom="3"
        :connection-line-style="{ stroke: '#6366f1', strokeWidth: 2 }"
        @node-click="onNodeClick"
        @edge-click="onEdgeClick"
        class="vue-flow"
      >
        <!-- 背景 -->
        <template #background>
          <div class="vue-flow__background" style="background-image: radial-gradient(circle, #aaa 1px, transparent 1px); background-size: 16px 16px;" />
        </template>
      </VueFlow>
      
      <!-- 管道信息面板 -->
      <div v-if="pipelineInfo" class="info-panel">
        <h4>{{ pipelineInfo.name }}</h4>
        <div class="info-item">
          <span class="label">状态:</span>
          <el-tag 
            :type="pipelineStatus?.state === 'running' ? 'success' : 'info'"
            size="small"
          >
            {{ pipelineStatus?.state === 'running' ? '运行中' : '未运行' }}
          </el-tag>
        </div>
        <div v-if="pipelineStatus?.global_metrics" class="global-metrics">
          <div class="metrics-title">全局指标</div>
          <div 
            v-for="(value, key) in pipelineStatus.global_metrics" 
            :key="key"
            class="metric-item"
          >
            <span class="metric-key">{{ formatMetricKey(key) }}:</span>
            <span class="metric-value">{{ formatMetricValue(key, value) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { VueFlow } from '@vue-flow/core'
import {
  getComprehensivePipelineConfig,
  getComprehensivePipelineStatus,
  type PipelineStatus
} from '@/api/pipeline'
import { convertToVueFlowNodes, convertToVueFlowEdges } from '@/utils/flowDataTransformer'
import type { VueFlowNode, VueFlowEdge } from '@/utils/flowDataTransformer'

// 导入自定义节点组件
import SourceNode from './SourceNode.vue'
import ProcessorNode from './ProcessorNode.vue'
import SinkNode from './SinkNode.vue'
import DefaultNode from './DefaultNode.vue'

// 组件属性
interface Props {
  pipelineId: string
  autoRefresh?: boolean
  refreshInterval?: number
}

const props = withDefaults(defineProps<Props>(), {
  autoRefresh: true,
  refreshInterval: 5000
})

// 组件事件
const emit = defineEmits<{
  nodeClick: [node: VueFlowNode]
  edgeClick: [edge: VueFlowEdge]
  error: [message: string]
  dataLoaded: [data: { nodes: VueFlowNode[], edges: VueFlowEdge[] }]
  statusUpdated: [status: PipelineStatus]
}>()

// 响应式数据
const nodes = ref<VueFlowNode[]>([])
const edges = ref<VueFlowEdge[]>([])
const isLoading = ref(true)
const error = ref<string | null>(null)
const pipelineStatus = ref<PipelineStatus | null>(null)

// 从综合管道状态数据中提取节点和连接信息
function extractPipelineData(statusData: PipelineStatus) {
  // 如果管道未部署，返回空数据
  if (statusData.state === 'not_deployed') {
    return { nodes: [], connections: [] }
  }

  // 如果状态数据中包含节点信息，解析它
  let nodes: any[] = []
  let connections: any[] = []

  if (statusData.nodes && statusData.nodes !== '[]') {
    try {
      // 尝试解析节点状态JSON字符串
      const parsedNodes = JSON.parse(statusData.nodes)

      nodes = parsedNodes.map((node: any) => ({
        id: node.id,
        type: node.type || 'default',
        params: node.params || {}
      }))
    } catch (e) {
      console.error('解析节点数据失败:', e)
      // 如果解析失败，创建默认节点
      nodes = [
        { id: 'comprehensive_pipeline', type: 'comprehensive', params: {} }
      ]
    }
  } else {
    // 如果没有节点数据，创建默认的综合管道节点
    nodes = [
      { id: 'comprehensive_pipeline', type: 'comprehensive', params: {} }
    ]
  }

  // 从全局指标中提取连接信息
  if (statusData.global_metrics && statusData.global_metrics.connections) {
    try {
      // 如果连接信息是字符串，解析它
      if (typeof statusData.global_metrics.connections === 'string') {
        connections = JSON.parse(statusData.global_metrics.connections)
        console.log('✅ 解析连接数据成功:', connections)
      } else {
        connections = statusData.global_metrics.connections
        console.log('✅ 直接使用连接数据:', connections)
      }
    } catch (e) {
      console.error('❌ 解析连接数据失败:', e)
      connections = []
    }
  } else {
    console.warn('⚠️ 未找到连接信息')
    connections = []
  }

  return { nodes, connections }
}

// 注册自定义节点类型
const nodeTypes: any = {
  sourceNode: SourceNode,
  processorNode: ProcessorNode,
  sinkNode: SinkNode,
  defaultNode: DefaultNode
}

// 自动刷新定时器
let refreshTimer: number | null = null

// 请求缓存和去重
let isLoadingData = false
let configCache: any = null
let configCacheTime = 0
const CONFIG_CACHE_DURATION = 30000 // 30秒缓存

// 加载管道数据
async function loadPipelineData() {
  // 简单的重复请求防护
  if (isLoadingData) {
    console.log('⚠️ 数据加载中，跳过重复请求')
    return
  }

  try {
    isLoadingData = true
    isLoading.value = true
    error.value = null

    console.log('🚀 开始加载管道数据，管道ID:', props.pipelineId)

    // 优先尝试获取管道配置（包含完整的节点和连接信息）
    let configResponse = null
    const now = Date.now()

    // 尝试获取管道配置（暂时禁用缓存，确保能正常工作）
    try {
      console.log('📡 请求管道配置...')
      configResponse = await getComprehensivePipelineConfig()
      console.log('✅ 成功获取管道配置:', configResponse)
    } catch (configError) {
      console.warn('⚠️ 获取管道配置失败，将使用状态接口:', configError)
    }

    // 获取综合管道状态（用于运行时状态信息）
    console.log('📡 请求管道状态...')
    const statusResponse = await getComprehensivePipelineStatus()

    // 检查响应状态
    if (!statusResponse.success || statusResponse.errorCode !== 0) {
      throw new Error('无法获取综合管道数据')
    }

    pipelineStatus.value = statusResponse.data

    // 优先使用配置数据，如果没有则使用状态数据
    let nodeData: any[] = []
    let connectionData: any[] = []

    if (configResponse && configResponse.success && configResponse.data) {
      // 使用配置数据（更准确和完整）
      console.log('📊 使用配置数据生成流程图')
      nodeData = configResponse.data.nodes || []
      connectionData = configResponse.data.connections || []
      console.log('✅ 配置中的节点数量:', nodeData.length)
      console.log('✅ 配置中的连接数量:', connectionData.length)
    } else {
      // 使用状态数据（向后兼容）
      console.log('📊 使用状态数据生成流程图')
      const extracted = extractPipelineData(statusResponse.data)
      nodeData = extracted.nodes
      connectionData = extracted.connections
      console.log('✅ 状态中的节点数量:', nodeData.length)
      console.log('✅ 状态中的连接数量:', connectionData.length)
    }

    console.log('🔄 最终使用的连接数据:', connectionData)

    // 转换数据为Vue Flow格式
    nodes.value = convertToVueFlowNodes(nodeData, pipelineStatus.value, connectionData)
    edges.value = convertToVueFlowEdges(connectionData)

    console.log('✅ Vue Flow 节点数据:', nodes.value)
    console.log('✅ Vue Flow 边数据:', edges.value)

    // 通知父组件数据加载完成
    emit('dataLoaded', { nodes: nodes.value, edges: edges.value })

    // 通知父组件状态更新
    emit('statusUpdated', pipelineStatus.value)
    
  } catch (err) {
    console.error('加载管道数据失败:', err)
    error.value = err instanceof Error ? err.message : '加载数据失败'
    emit('error', error.value)
  } finally {
    isLoading.value = false
    isLoadingData = false
  }
}

// 更新节点状态（用于自动刷新）
async function updateNodesStatus() {
  try {
    const statusResponse = await getComprehensivePipelineStatus()

    if (statusResponse.success && statusResponse.errorCode === 0) {
      pipelineStatus.value = statusResponse.data
      
      if (statusResponse.data.nodes) {
        try {
          const parsedNodes = JSON.parse(statusResponse.data.nodes)
          
          // 更新节点状态但不改变位置
          nodes.value = nodes.value.map(node => {
            const nodeStatus = parsedNodes.find((n: any) => n.id === node.id)
            if (nodeStatus) {
              return {
                ...node,
                data: {
                  ...node.data,
                  state: nodeStatus.state,
                  metrics: nodeStatus.metrics
                },
                class: `node-${nodeStatus.state || 'idle'}`
              }
            }
            return node
          })
        } catch (e) {
          console.error('解析节点状态数据失败', e)
        }
      }
    }
  } catch (err) {
    console.warn('更新节点状态失败', err)
  }
}

// 事件处理
function onNodeClick(payload: any) {
  emit('nodeClick', payload.node)
}

function onEdgeClick(payload: any) {
  emit('edgeClick', payload.edge)
}

// 自动刷新控制
function startStatusRefresh() {
  if (props.autoRefresh && props.refreshInterval > 0) {
    refreshTimer = window.setInterval(updateNodesStatus, props.refreshInterval)
  }
}

function stopStatusRefresh() {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 格式化指标键名
function formatMetricKey(key: string): string {
  return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

// 格式化指标值
function formatMetricValue(key: string, value: any): string {
  if (key.includes('fps')) {
    return Number(value).toFixed(1) + ' fps'
  }
  if (key.includes('percent')) {
    return Number(value).toFixed(1) + '%'
  }
  if (key.includes('mb')) {
    return Number(value).toFixed(1) + ' MB'
  }
  return String(value)
}

// 监听属性变化
watch(() => props.pipelineId, (newId, oldId) => {
  console.log('🔄 watch 触发:', { newId, oldId })
  if (newId) {
    console.log('🔄 开始加载管道数据，ID:', newId)
    stopStatusRefresh()

    loadPipelineData().then(() => {
      nextTick(() => {
        startStatusRefresh()
      })
    }).catch(err => {
      console.error('❌ 加载管道数据失败:', err)
    })
  }
}, { immediate: true })

watch(() => props.autoRefresh, (newVal) => {
  if (newVal) {
    startStatusRefresh()
  } else {
    stopStatusRefresh()
  }
})

// 生命周期钩子
onMounted(() => {
  // 初始化逻辑由 watch 处理，避免重复调用
  console.log('🚀 PipelineFlowChart 组件已挂载')
})

onBeforeUnmount(() => {
  stopStatusRefresh()
})
</script>

<style scoped>
/* 导入Vue Flow样式 */
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';

.pipeline-flow-chart {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 500px;
  background-color: #fafafa;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #409eff;
  animation: spin 1s ease infinite;
}

.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.error-message {
  color: #f56565;
  margin-bottom: 20px;
  font-size: 16px;
}

.flow-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.vue-flow {
  width: 100%;
  height: 100%;
}

.info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 250px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 5;
}

.info-panel h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #1f2937;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.label {
  font-size: 14px;
  color: #6b7280;
}

.global-metrics {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.metrics-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #374151;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin: 4px 0;
}

.metric-key {
  color: #6b7280;
}

.metric-value {
  font-weight: 600;
  color: #1f2937;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Vue Flow边的样式优化 */
.vue-flow .vue-flow__edge {
  pointer-events: all;
}

.vue-flow .vue-flow__edge-path {
  stroke: #6366f1;
  stroke-width: 2;
  fill: none;
}

.vue-flow .vue-flow__edge:hover .vue-flow__edge-path {
  stroke: #4f46e5;
  stroke-width: 3;
}

.vue-flow .vue-flow__edge.selected .vue-flow__edge-path {
  stroke: #4f46e5;
  stroke-width: 3;
}

/* 边标签样式 */
.vue-flow .vue-flow__edge-text {
  font-size: 10px;
  fill: #6b7280;
}

/* 连接线样式 */
.vue-flow .vue-flow__connection-line {
  stroke: #6366f1;
  stroke-width: 2;
  fill: none;
}
</style> 