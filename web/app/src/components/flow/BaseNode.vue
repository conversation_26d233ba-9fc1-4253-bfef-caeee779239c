<template>
  <div 
    class="custom-node" 
    :class="[nodeStateClass, { selected }]"
  >
    <!-- 左侧连接点（输入） -->
    <Handle 
      type="target" 
      :position="Position.Left" 
      :style="{ background: stateColor, borderColor: '#fff' }"
      class="node-handle-left"
    />
    
    <!-- 节点头部 -->
    <div class="node-header" :style="{ backgroundColor: stateColor }">
      <div class="node-title">{{ data.id }}</div>
      <div class="node-state">{{ stateText }}</div>
    </div>
    
    <!-- 节点内容 -->
    <div class="node-content">
      <div class="node-type">{{ data.typeName || data.type }}</div>
      
      <!-- 关键性能指标 -->
      <div v-if="showMetrics && keyMetrics.length > 0" class="node-metrics">
        <div 
          v-for="metric in keyMetrics" 
          :key="metric.key"
          class="metric-item"
        >
          <span class="metric-key">{{ metric.label }}:</span>
          <span class="metric-value">{{ metric.value }}</span>
        </div>
      </div>
    </div>
    
    <!-- 右侧连接点（输出） -->
    <Handle 
      type="source" 
      :position="Position.Right" 
      :style="{ background: stateColor, borderColor: '#fff' }"
      class="node-handle-right"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'

interface NodeData {
  id: string
  type: string
  typeName: string
  params: Record<string, any>
  state: string
  metrics: Record<string, any>
}

interface Props {
  data: NodeData
  selected?: boolean
  id: string
}

const props = withDefaults(defineProps<Props>(), {
  selected: false
})

// 根据节点状态计算样式类名
const nodeStateClass = computed(() => `node-state-${props.data.state || 'idle'}`)

// 根据节点状态计算状态文本
const stateText = computed(() => {
  switch (props.data.state) {
    case 'running': return '运行中'
    case 'error': return '错误'
    case 'stopped': return '已停止'
    default: return '空闲'
  }
})

// 根据节点状态计算状态颜色
const stateColor = computed(() => {
  switch (props.data.state) {
    case 'running': return '#10b981'
    case 'error': return '#ef4444'
    case 'stopped': return '#6b7280'
    default: return '#9ca3af'
  }
})

// 是否显示指标数据
const showMetrics = computed(() => 
  props.data.state === 'running' && 
  props.data.metrics && 
  Object.keys(props.data.metrics).length > 0
)

// 提取关键性能指标（根据节点类型显示3-4个最相关的指标）
const keyMetrics = computed(() => {
  if (!showMetrics.value) return []
  
  const metrics = props.data.metrics
  const result = []
  const nodeType = props.data.type
  
  // 根据节点类型优先显示相关指标
  if (nodeType.includes('src') || nodeType.includes('input')) {
    // 源节点：优先显示fps、处理帧数、队列状态
    if (metrics.fps) {
      result.push({
        key: 'fps',
        label: 'FPS',
        value: Number(metrics.fps).toFixed(1)
      })
    }
    if (metrics.frames_processed) {
      result.push({
        key: 'frames_processed',
        label: '已处理',
        value: formatLargeNumber(metrics.frames_processed)
      })
    }
    if (metrics.queue_size !== undefined) {
      result.push({
        key: 'queue_size',
        label: '队列',
        value: String(metrics.queue_size)
      })
    }
  } else if (nodeType.includes('detector')) {
    // 检测节点：优先显示FPS、检测数量、处理时间
    if (metrics.inference_fps) {
      result.push({
        key: 'inference_fps',
        label: 'FPS',
        value: Number(metrics.inference_fps).toFixed(1)
      })
    }
    if (metrics.faces_detected !== undefined) {
      result.push({
        key: 'faces_detected',
        label: '检测数',
        value: String(metrics.faces_detected)
      })
    }
    if (metrics.detection_time_ms) {
      result.push({
        key: 'detection_time_ms',
        label: '耗时',
        value: Number(metrics.detection_time_ms).toFixed(1) + 'ms'
      })
    }
  } else if (nodeType.includes('encoder')) {
    // 编码节点：优先显示编码时间、特征提取数
    if (metrics.encoding_time_ms) {
      result.push({
        key: 'encoding_time_ms',
        label: '编码',
        value: Number(metrics.encoding_time_ms).toFixed(1) + 'ms'
      })
    }
    if (metrics.features_extracted !== undefined) {
      result.push({
        key: 'features_extracted',
        label: '特征数',
        value: String(metrics.features_extracted)
      })
    }
  } else if (nodeType.includes('osd')) {
    // OSD节点：优先显示渲染时间
    if (metrics.render_time_ms) {
      result.push({
        key: 'render_time_ms',
        label: '渲染',
        value: Number(metrics.render_time_ms).toFixed(1) + 'ms'
      })
    }
  } else if (nodeType.includes('screen') || nodeType.includes('display')) {
    // 显示节点：优先显示显示FPS
    if (metrics.display_fps) {
      result.push({
        key: 'display_fps',
        label: 'FPS',
        value: Number(metrics.display_fps).toFixed(1)
      })
    }
  } else if (nodeType.includes('rtmp') || nodeType.includes('stream')) {
    // 流媒体节点：优先显示流FPS、码率
    if (metrics.streaming_fps) {
      result.push({
        key: 'streaming_fps',
        label: 'FPS',
        value: Number(metrics.streaming_fps).toFixed(1)
      })
    }
    if (metrics.bitrate_kbps) {
      result.push({
        key: 'bitrate_kbps',
        label: '码率',
        value: Number(metrics.bitrate_kbps).toFixed(0) + 'K'
      })
    }
  }
  
  // 如果按类型没有找到足够的指标，添加通用指标
  if (result.length === 0) {
    // 通用FPS指标
    if (metrics.fps) {
      result.push({
        key: 'fps',
        label: 'FPS',
        value: Number(metrics.fps).toFixed(1)
      })
    } else if (metrics.inference_fps) {
      result.push({
        key: 'inference_fps',
        label: 'FPS',
        value: Number(metrics.inference_fps).toFixed(1)
      })
    } else if (metrics.display_fps) {
      result.push({
        key: 'display_fps',
        label: 'FPS',
        value: Number(metrics.display_fps).toFixed(1)
      })
    } else if (metrics.streaming_fps) {
      result.push({
        key: 'streaming_fps',
        label: 'FPS',
        value: Number(metrics.streaming_fps).toFixed(1)
      })
    }
  }
  
  // 添加其他可用的重要指标（如果空间允许）
  const allMetricKeys = Object.keys(metrics)
  const usedKeys = result.map(r => r.key)
  
  for (const key of allMetricKeys) {
    if (usedKeys.includes(key) || result.length >= 4) break
    
    const value = metrics[key]
    if (key.includes('time_ms')) {
      result.push({
        key,
        label: formatMetricLabel(key),
        value: Number(value).toFixed(1) + 'ms'
      })
    } else if (key.includes('detected') || key.includes('extracted') || key.includes('processed')) {
      result.push({
        key,
        label: formatMetricLabel(key),
        value: String(value)
      })
    } else if (typeof value === 'number' && !key.includes('fps') && !key.includes('kbps')) {
      result.push({
        key,
        label: formatMetricLabel(key),
        value: formatLargeNumber(value)
      })
    }
  }
  
  return result.slice(0, 4) // 最多显示4个指标
})

// 格式化大数字
function formatLargeNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return String(num)
}

// 格式化指标标签
function formatMetricLabel(key: string): string {
  const labelMap: Record<string, string> = {
    'detection_time_ms': '检测',
    'encoding_time_ms': '编码',
    'render_time_ms': '渲染',
    'faces_detected': '人脸',
    'features_extracted': '特征',
    'frames_processed': '帧数',
    'queue_size': '队列',
    'bitrate_kbps': '码率'
  }
  
  return labelMap[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}
</script>

<style scoped>
.custom-node {
  background: white;
  border: 2px solid #ccc;
  border-radius: 6px;
  width: 140px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: sans-serif;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.custom-node:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.custom-node.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

/* Vue Flow Handle 样式重写 */
.node-handle-left,
.node-handle-right {
  width: 10px !important;
  height: 10px !important;
  border: 2px solid #fff !important;
  border-radius: 50% !important;
  transition: all 0.2s ease !important;
}

.node-handle-left:hover,
.node-handle-right:hover {
  transform: scale(1.2) !important;
  border-color: #3b82f6 !important;
}

.node-header {
  padding: 4px 6px;
  background-color: #9ca3af;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 28px;
}

.node-title {
  font-weight: 600;
  font-size: 11px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.node-state {
  font-size: 9px;
  padding: 1px 3px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  margin-left: 4px;
  white-space: nowrap;
}

.node-content {
  padding: 6px;
}

.node-type {
  font-size: 10px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-metrics {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 9px;
  padding: 1px 0;
}

.metric-key {
  color: #6b7280;
  font-weight: 500;
  min-width: 30px;
}

.metric-value {
  font-weight: 600;
  color: #1f2937;
  font-size: 9px;
  text-align: right;
}

/* 节点状态特定样式 */
.node-state-running {
  border-color: #10b981 !important;
}

.node-state-running .node-header {
  background-color: #10b981;
}

.node-state-error {
  border-color: #ef4444 !important;
}

.node-state-error .node-header {
  background-color: #ef4444;
}

.node-state-stopped {
  border-color: #6b7280 !important;
}

.node-state-stopped .node-header {
  background-color: #6b7280;
}

.node-state-idle {
  border-color: #9ca3af !important;
}

.node-state-idle .node-header {
  background-color: #9ca3af;
}
</style> 