<template>
  <BaseNode :data="data" :selected="selected" :id="id" class="sink-node" />
</template>

<script setup lang="ts">
import BaseNode from './BaseNode.vue'

interface NodeData {
  id: string
  type: string
  typeName: string
  params: Record<string, any>
  state: string
  metrics: Record<string, any>
}

interface Props {
  data: NodeData
  selected?: boolean
  id: string
}

defineProps<Props>()
</script>

<style scoped>
/* 输出节点只显示左侧输入连接点 */
.sink-node :deep(.node-handle-right) {
  display: none !important;
}
</style> 