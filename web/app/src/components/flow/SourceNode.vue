<template>
  <BaseNode :data="data" :selected="selected" :id="id" class="source-node" />
</template>

<script setup lang="ts">
import BaseNode from './BaseNode.vue'

interface NodeData {
  id: string
  type: string
  typeName: string
  params: Record<string, any>
  state: string
  metrics: Record<string, any>
}

interface Props {
  data: NodeData
  selected?: boolean
  id: string
}

defineProps<Props>()
</script>

<style scoped>
/* 源节点只显示右侧输出连接点 */
.source-node :deep(.node-handle-left) {
  display: none !important;
}
</style> 