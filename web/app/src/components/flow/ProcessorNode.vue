<template>
  <BaseNode :data="data" :selected="selected" :id="id" class="processor-node" />
</template>

<script setup lang="ts">
import BaseNode from './BaseNode.vue'

interface NodeData {
  id: string
  type: string
  typeName: string
  params: Record<string, any>
  state: string
  metrics: Record<string, any>
}

interface Props {
  data: NodeData
  selected?: boolean
  id: string
}

defineProps<Props>()
</script>

<style scoped>
/* 处理器节点显示左右两个连接点（默认行为，无需额外样式） */
</style> 