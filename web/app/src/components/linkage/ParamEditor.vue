<template>
  <div class="param-editor">
    <div v-if="!hasParams" class="no-params">
      <el-empty description="此命令无需配置参数" :image-size="60" />
    </div>
    
    <el-form v-else :model="localParams" label-width="100px">
      <div v-for="(config, key) in paramConfigs" :key="key" class="param-item">
        <el-form-item :label="config.label" :required="config.required">
          <!-- 数字输入 -->
          <el-input-number
            v-if="config.type === 'number'"
            v-model="localParams[key]"
            :min="config.min || 0"
            :max="config.max || 999999"
            :step="config.step || 1"
            :placeholder="config.placeholder"
            style="width: 100%"
          />
          
          <!-- 文本输入 -->
          <el-input
            v-else-if="config.type === 'string'"
            v-model="localParams[key]"
            :placeholder="config.placeholder"
            :maxlength="config.maxlength"
            :show-word-limit="!!config.maxlength"
          />
          
          <!-- 文本域 -->
          <el-input
            v-else-if="config.type === 'textarea'"
            v-model="localParams[key]"
            type="textarea"
            :rows="config.rows || 3"
            :placeholder="config.placeholder"
            :maxlength="config.maxlength"
            :show-word-limit="!!config.maxlength"
          />
          
          <!-- 开关 -->
          <el-switch
            v-else-if="config.type === 'boolean'"
            v-model="localParams[key]"
            :active-text="config.activeText || '开启'"
            :inactive-text="config.inactiveText || '关闭'"
          />
          
          <!-- 选择器 -->
          <el-select
            v-else-if="config.type === 'select'"
            v-model="localParams[key]"
            :placeholder="config.placeholder"
            style="width: 100%"
          >
            <el-option
              v-for="option in config.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          
          <!-- 颜色选择器 -->
          <el-color-picker
            v-else-if="config.type === 'color'"
            v-model="localParams[key]"
            :predefine="config.predefine"
          />
          
          <!-- JSON编辑器 -->
          <el-input
            v-else-if="config.type === 'json'"
            v-model="localParams[key]"
            type="textarea"
            :rows="4"
            placeholder="请输入JSON格式的参数"
            @blur="validateJson(key)"
          />
          
          <!-- 默认文本输入 -->
          <el-input
            v-else
            v-model="localParams[key]"
            :placeholder="config.placeholder || '请输入参数值'"
          />
          
          <!-- 参数说明 -->
          <div v-if="config.description" class="param-description">
            <el-text type="info">{{ config.description }}</el-text>
          </div>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

interface ParamConfig {
  label: string
  type: 'string' | 'number' | 'boolean' | 'select' | 'textarea' | 'color' | 'json'
  placeholder?: string
  description?: string
  required?: boolean
  min?: number
  max?: number
  step?: number
  rows?: number
  maxlength?: number
  activeText?: string
  inactiveText?: string
  options?: Array<{ label: string; value: any }>
  predefine?: string[]
}

interface Props {
  modelValue: Record<string, any>
  device?: any
  command?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
})

const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>]
}>()

const localParams = ref<Record<string, any>>({})

// 根据设备协议和命令获取参数配置
const paramConfigs = computed<Record<string, ParamConfig>>(() => {
  if (!props.device || !props.command) return {}
  
  const protocol = props.device.protocol
  const command = props.command
  
  // 参数配置映射
  const configMap: Record<string, Record<string, Record<string, ParamConfig>>> = {
    mqtt: {
      set_brightness: {
        brightness: {
          label: '亮度值',
          type: 'number',
          min: 0,
          max: 100,
          placeholder: '请输入亮度值(0-100)',
          description: '设置设备亮度，范围0-100',
          required: true
        }
      },
      blink: {
        interval: {
          label: '闪烁间隔(ms)',
          type: 'number',
          min: 100,
          max: 5000,
          step: 100,
          placeholder: '请输入闪烁间隔',
          description: '设置闪烁间隔时间，单位毫秒'
        },
        count: {
          label: '闪烁次数',
          type: 'number',
          min: 1,
          max: 100,
          placeholder: '请输入闪烁次数',
          description: '设置闪烁次数，0表示持续闪烁'
        }
      }
    },
    modbus: {
      write_single_coil: {
        address: {
          label: '线圈地址',
          type: 'number',
          min: 0,
          max: 65535,
          placeholder: '请输入线圈地址',
          description: '要写入的线圈地址',
          required: true
        },
        value: {
          label: '线圈值',
          type: 'boolean',
          activeText: 'ON',
          inactiveText: 'OFF',
          description: '线圈状态：ON(1) 或 OFF(0)'
        }
      },
      write_single_register: {
        address: {
          label: '寄存器地址',
          type: 'number',
          min: 0,
          max: 65535,
          placeholder: '请输入寄存器地址',
          description: '要写入的寄存器地址',
          required: true
        },
        value: {
          label: '寄存器值',
          type: 'number',
          min: 0,
          max: 65535,
          placeholder: '请输入寄存器值',
          description: '要写入的16位寄存器值',
          required: true
        }
      },
      read_coils: {
        address: {
          label: '起始地址',
          type: 'number',
          min: 0,
          max: 65535,
          placeholder: '请输入起始地址',
          description: '要读取的线圈起始地址',
          required: true
        },
        count: {
          label: '读取数量',
          type: 'number',
          min: 1,
          max: 2000,
          placeholder: '请输入读取数量',
          description: '要读取的线圈数量(1-2000)',
          required: true
        }
      },
      read_holding_registers: {
        address: {
          label: '起始地址',
          type: 'number',
          min: 0,
          max: 65535,
          placeholder: '请输入起始地址',
          description: '要读取的寄存器起始地址',
          required: true
        },
        count: {
          label: '读取数量',
          type: 'number',
          min: 1,
          max: 125,
          placeholder: '请输入读取数量',
          description: '要读取的寄存器数量(1-125)',
          required: true
        }
      }
    },
    rs485: {
      raw_hex: {
        data: {
          label: '十六进制数据',
          type: 'string',
          placeholder: '请输入十六进制数据，如：01 03 00 00 00 01',
          description: '要发送的十六进制数据，用空格分隔',
          required: true
        }
      },
      modbus_read: {
        device_addr: {
          label: '设备地址',
          type: 'number',
          min: 1,
          max: 247,
          placeholder: '请输入设备地址',
          description: 'Modbus设备地址(1-247)',
          required: true
        },
        function_code: {
          label: '功能码',
          type: 'select',
          placeholder: '请选择功能码',
          description: 'Modbus功能码',
          required: true,
          options: [
            { label: '01 - 读取线圈状态', value: 1 },
            { label: '02 - 读取离散输入', value: 2 },
            { label: '03 - 读取保持寄存器', value: 3 },
            { label: '04 - 读取输入寄存器', value: 4 }
          ]
        },
        address: {
          label: '起始地址',
          type: 'number',
          min: 0,
          max: 65535,
          placeholder: '请输入起始地址',
          description: '要读取的起始地址',
          required: true
        },
        count: {
          label: '读取数量',
          type: 'number',
          min: 1,
          max: 2000,
          placeholder: '请输入读取数量',
          description: '要读取的数据数量',
          required: true
        }
      },
      modbus_write: {
        device_addr: {
          label: '设备地址',
          type: 'number',
          min: 1,
          max: 247,
          placeholder: '请输入设备地址',
          description: 'Modbus设备地址(1-247)',
          required: true
        },
        function_code: {
          label: '功能码',
          type: 'select',
          placeholder: '请选择功能码',
          description: 'Modbus功能码',
          required: true,
          options: [
            { label: '05 - 写入单个线圈', value: 5 },
            { label: '06 - 写入单个寄存器', value: 6 },
            { label: '15 - 写入多个线圈', value: 15 },
            { label: '16 - 写入多个寄存器', value: 16 }
          ]
        },
        address: {
          label: '起始地址',
          type: 'number',
          min: 0,
          max: 65535,
          placeholder: '请输入起始地址',
          description: '要写入的起始地址',
          required: true
        },
        value: {
          label: '写入值',
          type: 'string',
          placeholder: '请输入写入值，多个值用逗号分隔',
          description: '要写入的值，多个值用逗号分隔',
          required: true
        }
      },
      custom: {
        command: {
          label: '自定义命令',
          type: 'string',
          placeholder: '请输入自定义命令',
          description: '设备特定的自定义命令',
          required: true
        },
        params: {
          label: '命令参数',
          type: 'json',
          placeholder: '{"param1": "value1", "param2": "value2"}',
          description: '命令参数，JSON格式'
        }
      }
    }
  }
  
  return configMap[protocol]?.[command] || {}
})

// 是否有参数配置
const hasParams = computed(() => Object.keys(paramConfigs.value).length > 0)

// 初始化参数默认值
const initParams = () => {
  const newParams: Record<string, any> = {}
  
  Object.entries(paramConfigs.value).forEach(([key, config]) => {
    if (props.modelValue[key] !== undefined) {
      newParams[key] = props.modelValue[key]
    } else {
      // 设置默认值
      switch (config.type) {
        case 'number':
          newParams[key] = config.min || 0
          break
        case 'boolean':
          newParams[key] = false
          break
        case 'string':
        case 'textarea':
        case 'json':
          newParams[key] = ''
          break
        case 'select':
          newParams[key] = config.options?.[0]?.value || ''
          break
        case 'color':
          newParams[key] = '#409EFF'
          break
        default:
          newParams[key] = ''
      }
    }
  })
  
  localParams.value = newParams
}

// JSON验证
const validateJson = (key: string) => {
  const value = localParams.value[key]
  if (!value) return
  
  try {
    JSON.parse(value)
  } catch (error) {
    ElMessage.error('JSON格式不正确')
    // 可以选择重置为空或保持原值
  }
}

// 监听参数变化
watch(localParams, (newParams) => {
  emit('update:modelValue', { ...newParams })
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (JSON.stringify(newValue) !== JSON.stringify(localParams.value)) {
    Object.assign(localParams.value, newValue)
  }
}, { deep: true })

// 监听命令变化，重新初始化参数
watch(() => [props.device, props.command], () => {
  initParams()
}, { immediate: true })

onMounted(() => {
  initParams()
})
</script>

<style lang="scss" scoped>
.param-editor {
  .no-params {
    text-align: center;
    padding: 20px;
    color: #909399;
  }
  
  .param-item {
    margin-bottom: 16px;
    
    .param-description {
      margin-top: 4px;
      font-size: 12px;
      line-height: 1.4;
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 16px;
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }
}
</style>
