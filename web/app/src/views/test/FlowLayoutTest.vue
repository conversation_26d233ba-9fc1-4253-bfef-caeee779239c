<template>
  <div class="flow-layout-test">
    <h1>流水线布局算法测试</h1>
    
    <div class="test-controls">
      <el-button @click="testSimpleLinear">测试简单线性流水线</el-button>
      <el-button @click="testBranchPipeline">测试分支流水线</el-button>
      <el-button @click="testMultiSourcePipeline">测试多源汇聚流水线</el-button>
      <el-button @click="testComplexPipeline">测试复杂流水线</el-button>
    </div>

    <div class="test-results">
      <h3>当前测试：{{ currentTest }}</h3>
      <div class="layout-info">
        <h4>节点布局信息：</h4>
        <div v-for="node in testNodes" :key="node.id" class="node-info">
          <span class="node-id">{{ node.id }}</span>
          <span class="node-position">X: {{ node.position.x }}, Y: {{ node.position.y }}</span>
          <span class="node-type">{{ node.data.typeName }}</span>
        </div>
      </div>
    </div>

    <div class="flow-container">
      <VueFlow
        v-model:nodes="testNodes"
        v-model:edges="testEdges"
        :node-types="nodeTypes"
        :default-viewport="{ x: 0, y: 0, zoom: 0.8 }"
        :fit-view-on-init="true"
        :min-zoom="0.3"
        :max-zoom="3"
        class="test-vue-flow"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'
import { ElButton } from 'element-plus'
import { convertToVueFlowNodes, convertToVueFlowEdges } from '@/utils/flowDataTransformer'
import type { PipelineNode, PipelineConnection } from '@/api/pipeline'
import type { VueFlowNode, VueFlowEdge } from '@/utils/flowDataTransformer'

// 导入节点组件
import BaseNode from '@/components/flow/BaseNode.vue'
import SourceNode from '@/components/flow/SourceNode.vue'
import ProcessorNode from '@/components/flow/ProcessorNode.vue'
import SinkNode from '@/components/flow/SinkNode.vue'
import DefaultNode from '@/components/flow/DefaultNode.vue'

// 注册节点类型
const nodeTypes: any = {
  sourceNode: SourceNode,
  processorNode: ProcessorNode,
  sinkNode: SinkNode,
  defaultNode: DefaultNode
}

const currentTest = ref('无')
const testNodes = ref<VueFlowNode[]>([])
const testEdges = ref<VueFlowEdge[]>([])

// 测试简单线性流水线
function testSimpleLinear() {
  currentTest.value = '简单线性流水线'
  
  const nodes: PipelineNode[] = [
    { id: 'src_0', type: 'vp_file_src_node', params: {} },
    { id: 'detector_0', type: 'vp_yunet_face_detector_node', params: {} },
    { id: 'encoder_0', type: 'vp_sface_face_encoder_node', params: {} },
    { id: 'des_0', type: 'vp_screen_des_node', params: {} }
  ]

  const connections: PipelineConnection[] = [
    { id: 'conn_1', from: { is_single: true, source: 'src_0' }, to: 'detector_0' },
    { id: 'conn_2', from: { is_single: true, source: 'detector_0' }, to: 'encoder_0' },
    { id: 'conn_3', from: { is_single: true, source: 'encoder_0' }, to: 'des_0' }
  ]

  testNodes.value = convertToVueFlowNodes(nodes, null, connections)
  testEdges.value = convertToVueFlowEdges(connections)
}

// 测试分支流水线
function testBranchPipeline() {
  currentTest.value = '分支流水线'
  
  const nodes: PipelineNode[] = [
    { id: 'src_0', type: 'vp_file_src_node', params: {} },
    { id: 'detector_0', type: 'vp_yunet_face_detector_node', params: {} },
    { id: 'encoder_0', type: 'vp_sface_face_encoder_node', params: {} },
    { id: 'osd_0', type: 'vp_osd_node', params: {} },
    { id: 'screen_des_0', type: 'vp_screen_des_node', params: {} },
    { id: 'rtmp_des_0', type: 'vp_rtmp_des_node', params: {} }
  ]

  const connections: PipelineConnection[] = [
    { id: 'conn_1', from: { is_single: true, source: 'src_0' }, to: 'detector_0' },
    { id: 'conn_2', from: { is_single: true, source: 'detector_0' }, to: 'encoder_0' },
    { id: 'conn_3', from: { is_single: true, source: 'encoder_0' }, to: 'osd_0' },
    { id: 'conn_4', from: { is_single: true, source: 'osd_0' }, to: 'screen_des_0' },
    { id: 'conn_5', from: { is_single: true, source: 'osd_0' }, to: 'rtmp_des_0' }
  ]

  testNodes.value = convertToVueFlowNodes(nodes, null, connections)
  testEdges.value = convertToVueFlowEdges(connections)
}

// 测试多源汇聚流水线
function testMultiSourcePipeline() {
  currentTest.value = '多源汇聚流水线'
  
  const nodes: PipelineNode[] = [
    { id: 'src_0', type: 'vp_file_src_node', params: {} },
    { id: 'src_1', type: 'vp_file_src_node', params: {} },
    { id: 'detector_0', type: 'vp_yunet_face_detector_node', params: {} },
    { id: 'motion_0', type: 'vp_motion_detector_node', params: {} },
    { id: 'osd_0', type: 'vp_osd_node', params: {} },
    { id: 'des_0', type: 'vp_screen_des_node', params: {} }
  ]

  const connections: PipelineConnection[] = [
    { id: 'conn_1', from: { is_single: true, source: 'src_0' }, to: 'detector_0' },
    { id: 'conn_2', from: { is_single: true, source: 'src_1' }, to: 'motion_0' },
    { id: 'conn_3', from: { is_single: true, source: 'detector_0' }, to: 'osd_0' },
    { id: 'conn_4', from: { is_single: true, source: 'motion_0' }, to: 'osd_0' },
    { id: 'conn_5', from: { is_single: true, source: 'osd_0' }, to: 'des_0' }
  ]

  testNodes.value = convertToVueFlowNodes(nodes, null, connections)
  testEdges.value = convertToVueFlowEdges(connections)
}

// 测试复杂流水线（项目中的实际数据）
function testComplexPipeline() {
  currentTest.value = '复杂流水线（实际项目数据）'
  
  const nodes: PipelineNode[] = [
    { id: 'file_src_0', type: 'vp_file_src_node', params: {} },
    { id: 'file_src_1', type: 'vp_file_src_node', params: {} },
    { id: 'yunet_face_detector_0', type: 'vp_yunet_face_detector_node', params: {} },
    { id: 'motion_detector_0', type: 'vp_motion_detector_node', params: {} },
    { id: 'sface_face_encoder_0', type: 'vp_sface_face_encoder_node', params: {} },
    { id: 'osd_0', type: 'vp_osd_node', params: {} },
    { id: 'screen_des_0', type: 'vp_screen_des_node', params: {} },
    { id: 'rtmp_des_0', type: 'vp_rtmp_des_node', params: {} }
  ]

  const connections: PipelineConnection[] = [
    { id: 'conn_1', from: { is_single: true, source: 'file_src_0' }, to: 'yunet_face_detector_0' },
    { id: 'conn_2', from: { is_single: true, source: 'yunet_face_detector_0' }, to: 'sface_face_encoder_0' },
    { id: 'conn_3', from: { is_single: true, source: 'sface_face_encoder_0' }, to: 'osd_0' },
    { id: 'conn_4', from: { is_single: true, source: 'osd_0' }, to: 'screen_des_0' },
    { id: 'conn_5', from: { is_single: true, source: 'osd_0' }, to: 'rtmp_des_0' },
    { id: 'conn_6', from: { is_single: true, source: 'file_src_1' }, to: 'motion_detector_0' },
    { id: 'conn_7', from: { is_single: true, source: 'motion_detector_0' }, to: 'osd_0' }
  ]

  testNodes.value = convertToVueFlowNodes(nodes, null, connections)
  testEdges.value = convertToVueFlowEdges(connections)
}

// 默认加载简单线性流水线
testSimpleLinear()
</script>

<style scoped>
.flow-layout-test {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.test-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-results {
  margin-bottom: 20px;
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
}

.layout-info {
  margin-top: 10px;
}

.node-info {
  display: flex;
  gap: 15px;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.node-id {
  font-weight: bold;
  min-width: 150px;
  color: #2563eb;
}

.node-position {
  min-width: 120px;
  color: #059669;
}

.node-type {
  color: #7c3aed;
}

.flow-container {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.test-vue-flow {
  width: 100%;
  height: 100%;
}
</style>
