<template>
  <div class="request-optimization-test">
    <h2>请求优化测试页面</h2>
    
    <div class="test-controls">
      <el-button @click="clearLogs">清空日志</el-button>
      <el-button @click="reloadComponent">重新加载组件</el-button>
      <el-button @click="toggleComponent">切换组件显示</el-button>
    </div>

    <div class="request-logs">
      <h3>网络请求日志</h3>
      <div class="log-item" v-for="(log, index) in requestLogs" :key="index">
        <span class="timestamp">{{ log.timestamp }}</span>
        <span class="method" :class="log.method.toLowerCase()">{{ log.method }}</span>
        <span class="url">{{ log.url }}</span>
        <span class="status" :class="getStatusClass(log.status)">{{ log.status }}</span>
      </div>
    </div>

    <div class="component-container" v-if="showComponent">
      <PipelineFlowChart
        pipeline-id="comprehensive"
        :auto-refresh="false"
        :refresh-interval="5000"
        @data-loaded="handleDataLoaded"
        @status-updated="handleStatusUpdated"
        @error="handleError"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import PipelineFlowChart from '@/components/flow/PipelineFlowChart.vue'

// 响应式数据
const showComponent = ref(true)
const requestLogs = ref<Array<{
  timestamp: string
  method: string
  url: string
  status: number
}>>([])

// 原始的 fetch 函数
let originalFetch: typeof fetch

// 拦截网络请求
function interceptRequests() {
  originalFetch = window.fetch
  
  window.fetch = async function(input: RequestInfo | URL, init?: RequestInit) {
    const url = typeof input === 'string' ? input : input.toString()
    const method = init?.method || 'GET'
    
    // 只记录我们关心的API请求
    if (url.includes('/api/comprehensive-pipeline/')) {
      const timestamp = new Date().toLocaleTimeString()
      console.log(`🌐 API请求: ${method} ${url}`)
      
      try {
        const response = await originalFetch(input, init)
        
        // 记录请求日志
        requestLogs.value.push({
          timestamp,
          method,
          url: url.replace(window.location.origin, ''),
          status: response.status
        })
        
        return response
      } catch (error) {
        // 记录错误请求
        requestLogs.value.push({
          timestamp,
          method,
          url: url.replace(window.location.origin, ''),
          status: 0
        })
        throw error
      }
    }
    
    return originalFetch(input, init)
  }
}

// 恢复原始的 fetch 函数
function restoreRequests() {
  if (originalFetch) {
    window.fetch = originalFetch
  }
}

// 清空日志
function clearLogs() {
  requestLogs.value = []
  console.clear()
}

// 重新加载组件
function reloadComponent() {
  showComponent.value = false
  setTimeout(() => {
    showComponent.value = true
  }, 100)
}

// 切换组件显示
function toggleComponent() {
  showComponent.value = !showComponent.value
}

// 事件处理
function handleDataLoaded(data: any) {
  console.log('✅ 数据加载完成:', data)
  ElMessage.success('数据加载完成')
}

function handleStatusUpdated(status: any) {
  console.log('📊 状态更新:', status)
}

function handleError(message: string) {
  console.error('❌ 错误:', message)
  ElMessage.error(message)
}

// 获取状态样式类
function getStatusClass(status: number) {
  if (status >= 200 && status < 300) return 'success'
  if (status >= 400) return 'error'
  return 'info'
}

// 生命周期
onMounted(() => {
  interceptRequests()
  console.log('🔍 开始监控网络请求...')
})

onBeforeUnmount(() => {
  restoreRequests()
})
</script>

<style scoped>
.request-optimization-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  margin-bottom: 20px;
}

.test-controls .el-button {
  margin-right: 10px;
}

.request-logs {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  max-height: 300px;
  overflow-y: auto;
}

.request-logs h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.timestamp {
  width: 80px;
  color: #666;
  margin-right: 10px;
}

.method {
  width: 60px;
  font-weight: bold;
  margin-right: 10px;
}

.method.get { color: #67C23A; }
.method.post { color: #409EFF; }
.method.put { color: #E6A23C; }
.method.delete { color: #F56C6C; }

.url {
  flex: 1;
  color: #333;
  margin-right: 10px;
}

.status {
  width: 60px;
  text-align: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
}

.status.success {
  background: #67C23A;
  color: white;
}

.status.error {
  background: #F56C6C;
  color: white;
}

.status.info {
  background: #909399;
  color: white;
}

.component-container {
  border: 2px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background: white;
  min-height: 500px;
}
</style>
