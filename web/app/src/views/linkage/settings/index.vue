<template>
  <div class="app-container">
    <div class="settings-layout">
      <!-- 左侧配置区域 -->
      <div class="config-sidebar">
        <!-- 联动配置 -->
        <div class="config-block">
          <div class="block-header">
            <h4 class="block-title">联动配置</h4>
            <el-button type="primary" size="small" @click="handleSaveConfig" :loading="configLoading">
              保存配置
            </el-button>
          </div>
          
          <div class="block-content">
            <el-form :model="systemConfig" label-width="120px">
            <el-form-item label="联动引擎状态">
              <el-switch
                v-model="systemConfig.enabled"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
            
            <el-form-item label="最大并发执行数">
              <el-input-number
                v-model="systemConfig.max_concurrent_executions"
                :min="1"
                :max="100"
                style="width: 100%"
              />
              <div class="form-tip">同时执行的联动任务数量上限</div>
            </el-form-item>
            
            <el-form-item label="执行超时时间(秒)">
              <el-input-number
                v-model="systemConfig.execution_timeout"
                :min="5"
                :max="300"
                style="width: 100%"
              />
              <div class="form-tip">单个联动任务的最大执行时间</div>
            </el-form-item>
            
            <el-form-item label="重试次数">
              <el-input-number
                v-model="systemConfig.retry_count"
                :min="0"
                :max="10"
                style="width: 100%"
              />
              <div class="form-tip">联动执行失败时的重试次数</div>
            </el-form-item>
            
            <el-form-item label="重试间隔(秒)">
              <el-input-number
                v-model="systemConfig.retry_interval"
                :min="1"
                :max="60"
                style="width: 100%"
              />
              <div class="form-tip">重试之间的等待时间</div>
            </el-form-item>
            
            <el-form-item label="日志级别">
              <el-select v-model="systemConfig.log_level" style="width: 100%">
                <el-option label="DEBUG" value="debug" />
                <el-option label="INFO" value="info" />
                <el-option label="WARN" value="warn" />
                <el-option label="ERROR" value="error" />
              </el-select>
            </el-form-item>
          </el-form>
          </div>
        </div>
        
        <!-- 协议配置 -->
        <div class="config-block">
          <div class="block-header">
            <h4 class="block-title">协议配置</h4>
            <el-button type="primary" size="small" @click="handleSaveProtocols" :loading="protocolLoading">
              保存配置
            </el-button>
          </div>
          
          <div class="block-content">
            <el-tabs v-model="activeProtocol" size="small">
            <!-- MQTT配置 -->
            <el-tab-pane label="MQTT" name="mqtt">
              <el-form :model="protocolConfigs.mqtt" label-width="120px">
                <el-form-item label="启用MQTT">
                  <el-switch v-model="protocolConfigs.mqtt.enabled" />
                </el-form-item>
                <el-form-item label="默认QoS">
                  <el-select v-model="protocolConfigs.mqtt.default_qos" style="width: 100%">
                    <el-option label="0 - 最多一次" :value="0" />
                    <el-option label="1 - 至少一次" :value="1" />
                    <el-option label="2 - 恰好一次" :value="2" />
                  </el-select>
                </el-form-item>
                <el-form-item label="保持连接(秒)">
                  <el-input-number
                    v-model="protocolConfigs.mqtt.keep_alive"
                    :min="10"
                    :max="3600"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="连接超时(秒)">
                  <el-input-number
                    v-model="protocolConfigs.mqtt.connect_timeout"
                    :min="5"
                    :max="60"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="清理会话">
                  <el-switch v-model="protocolConfigs.mqtt.clean_session" />
                </el-form-item>
              </el-form>
            </el-tab-pane>
            
            <!-- Modbus配置 -->
            <el-tab-pane label="Modbus" name="modbus">
              <el-form :model="protocolConfigs.modbus" label-width="120px">
                <el-form-item label="启用Modbus">
                  <el-switch v-model="protocolConfigs.modbus.enabled" />
                </el-form-item>
                <el-form-item label="超时时间(秒)">
                  <el-input-number
                    v-model="protocolConfigs.modbus.timeout"
                    :min="1"
                    :max="60"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="重试次数">
                  <el-input-number
                    v-model="protocolConfigs.modbus.retry_count"
                    :min="0"
                    :max="10"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="默认从站ID">
                  <el-input-number
                    v-model="protocolConfigs.modbus.slave_id"
                    :min="1"
                    :max="247"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-form>
            </el-tab-pane>
            
            <!-- RS485配置 -->
            <el-tab-pane label="RS485" name="rs485">
              <el-form :model="protocolConfigs.rs485" label-width="120px">
                <el-form-item label="启用RS485">
                  <el-switch v-model="protocolConfigs.rs485.enabled" />
                </el-form-item>
                <el-form-item label="超时时间(秒)">
                  <el-input-number
                    v-model="protocolConfigs.rs485.timeout"
                    :min="1"
                    :max="60"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="缓冲区大小">
                  <el-input-number
                    v-model="protocolConfigs.rs485.buffer_size"
                    :min="256"
                    :max="8192"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
          </div>
        </div>
      </div>
      
      <!-- 右侧状态和操作区域 -->
      <div class="status-sidebar">
        <!-- 系统状态 -->
        <div class="status-block">
          <div class="block-header">
            <h4 class="block-title">系统状态</h4>
            <el-button type="success" size="small" @click="handleRefreshStatus">
              <el-icon><Refresh /></el-icon>
              刷新状态
            </el-button>
          </div>
          
          <div class="block-content">
            <div class="status-grid">
            <div class="status-item">
              <span class="status-label">联动引擎</span>
              <el-tag :type="healthStatus.engine_status === 'running' ? 'success' : 'danger'">
                {{ healthStatus.engine_status === 'running' ? '运行中' : '已停止' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">队列长度</span>
              <span class="status-value">{{ healthStatus.queue_length || 0 }}</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">活跃规则数</span>
              <span class="status-value">{{ healthStatus.active_rules || 0 }}</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">协议适配器</span>
              <div class="adapter-status">
                <el-tag 
                  v-for="(status, protocol) in healthStatus.protocol_adapters" 
                  :key="protocol"
                  :type="status === 'healthy' ? 'success' : 'warning'"
                  size="small"
                  class="adapter-tag"
                >
                  {{ protocol.toUpperCase() }}
                </el-tag>
              </div>
            </div>
          </div>
          </div>
        </div>
        
        <!-- 系统操作 -->
        <div class="operations-block">
          <div class="block-header">
            <h4 class="block-title">系统操作</h4>
          </div>
          
          <div class="block-content">
            <div class="operations-grid">
            <div class="operation-item">
              <div class="operation-info">
                <h4>重启联动引擎</h4>
                <p>重新启动联动引擎服务，将中断正在执行的任务</p>
              </div>
              <el-button 
                type="warning" 
                @click="handleRestartEngine"
                :loading="restartLoading"
              >
                重启引擎
              </el-button>
            </div>
            
            <el-divider />
            
            <div class="operation-item">
              <div class="operation-info">
                <h4>清理执行记录</h4>
                <p>清理历史执行记录，释放存储空间</p>
              </div>
              <el-button 
                type="danger" 
                @click="handleCleanupRecords"
              >
                清理记录
              </el-button>
            </div>
            
            <el-divider />
            
            <div class="operation-item">
              <div class="operation-info">
                <h4>导出系统配置</h4>
                <p>导出当前系统配置，用于备份或迁移</p>
              </div>
              <el-button 
                type="info" 
                @click="handleExportConfig"
              >
                导出配置
              </el-button>
            </div>
            
            <el-divider />
            
            <div class="operation-item">
              <div class="operation-info">
                <h4>导入系统配置</h4>
                <p>从备份文件导入系统配置</p>
              </div>
              <el-upload
                ref="uploadRef"
                :auto-upload="false"
                :show-file-list="false"
                accept=".json"
                :on-change="handleImportConfig"
              >
                <el-button type="primary">选择文件</el-button>
              </el-upload>
            </div>
          </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { LinkageSystemAPI, type LinkageHealthStatus } from '@/api/linkage'

defineOptions({
  name: "LinkageSettings",
});

// 响应式数据
const configLoading = ref(false)
const protocolLoading = ref(false)
const restartLoading = ref(false)
const activeProtocol = ref('mqtt')
const uploadRef = ref()

// 系统配置
const systemConfig = reactive({
  enabled: true,
  max_concurrent_executions: 10,
  execution_timeout: 30,
  retry_count: 3,
  retry_interval: 5,
  log_level: 'info'
})

// 协议配置
const protocolConfigs = reactive({
  mqtt: {
    enabled: true,
    default_qos: 1,
    keep_alive: 60,
    connect_timeout: 10,
    clean_session: true
  },
  modbus: {
    enabled: true,
    timeout: 5,
    retry_count: 3,
    slave_id: 1
  },
  rs485: {
    enabled: true,
    timeout: 3,
    buffer_size: 1024
  }
})

// 健康状态
const healthStatus = ref<LinkageHealthStatus>({
  status: 'unknown',
  engine_status: 'unknown',
  queue_length: 0,
  active_rules: 0,
  protocol_adapters: {}
})

// 方法
const loadSystemConfig = async () => {
  try {
    const response = await LinkageSystemAPI.getSystemConfig()
    Object.assign(systemConfig, response.data?.system || {})
    Object.assign(protocolConfigs, response.data?.protocols || {})
  } catch (error) {
    console.error('加载系统配置失败:', error)
    ElMessage.error('加载系统配置失败')
  }
}

const loadHealthStatus = async () => {
  try {
    const response = await LinkageSystemAPI.getHealthStatus()
    healthStatus.value = response.data
  } catch (error) {
    console.error('加载健康状态失败:', error)
  }
}

const handleSaveConfig = async () => {
  configLoading.value = true
  try {
    await LinkageSystemAPI.updateSystemConfig({
      system: systemConfig
    })
    ElMessage.success('系统配置保存成功')
  } catch (error) {
    console.error('保存系统配置失败:', error)
    ElMessage.error('保存系统配置失败')
  } finally {
    configLoading.value = false
  }
}

const handleSaveProtocols = async () => {
  protocolLoading.value = true
  try {
    await LinkageSystemAPI.updateSystemConfig({
      protocols: protocolConfigs
    })
    ElMessage.success('协议配置保存成功')
  } catch (error) {
    console.error('保存协议配置失败:', error)
    ElMessage.error('保存协议配置失败')
  } finally {
    protocolLoading.value = false
  }
}

const handleRefreshStatus = () => {
  loadHealthStatus()
  ElMessage.success('状态已刷新')
}

const handleRestartEngine = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重启联动引擎吗？这将中断正在执行的任务。',
      '确认重启',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    restartLoading.value = true
    await LinkageSystemAPI.restartEngine()
    ElMessage.success('联动引擎重启成功')
    
    // 延迟刷新状态
    setTimeout(() => {
      loadHealthStatus()
    }, 2000)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重启引擎失败:', error)
      ElMessage.error('重启引擎失败')
    }
  } finally {
    restartLoading.value = false
  }
}

const handleCleanupRecords = async () => {
  try {
    const { value } = await ElMessageBox.prompt(
      '请输入要保留的天数（将删除N天前的记录）',
      '清理执行记录',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d+$/,
        inputErrorMessage: '请输入有效的天数',
        inputValue: '30'
      }
    )
    
    const days = parseInt(value)
    await LinkageSystemAPI.cleanupExecutions(days)
    ElMessage.success(`已清理 ${days} 天前的执行记录`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理记录失败:', error)
      ElMessage.error('清理记录失败')
    }
  }
}

const handleExportConfig = async () => {
  try {
    const config = {
      system: systemConfig,
      protocols: protocolConfigs,
      exported_at: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(config, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `linkage-config-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('配置导出成功')
  } catch (error) {
    console.error('导出配置失败:', error)
    ElMessage.error('导出配置失败')
  }
}

const handleImportConfig = async (file: any) => {
  try {
    const text = await file.raw.text()
    const config = JSON.parse(text)
    
    if (config.system) {
      Object.assign(systemConfig, config.system)
    }
    
    if (config.protocols) {
      Object.assign(protocolConfigs, config.protocols)
    }
    
    ElMessage.success('配置导入成功，请保存配置使其生效')
  } catch (error) {
    console.error('导入配置失败:', error)
    ElMessage.error('配置文件格式错误')
  }
}

// 生命周期
onMounted(async () => {
  await loadSystemConfig()
  await loadHealthStatus()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 0;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.settings-layout {
  display: flex;
  min-height: 100vh;
}

.config-sidebar {
  flex: 1;
  background: #ffffff;
  border-right: 1px solid #e2e8f0;
  padding: 24px;
  overflow-y: auto;
}

.status-sidebar {
  width: 400px;
  background: #f1f5f9;
  padding: 24px;
  overflow-y: auto;
}

.config-block,
.status-block,
.operations-block {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.block-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.block-content {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.status-item {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  text-align: center;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
  }
  
  .status-label {
    display: block;
    font-size: 12px;
    color: #64748b;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .status-value {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #0f172a;
  }
  
  .adapter-status {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .adapter-tag {
      font-size: 11px;
      padding: 4px 8px;
      border-radius: 6px;
      font-weight: 500;
    }
  }
}

.operations-grid {
  display: grid;
  gap: 12px;
}

.operation-item {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
  }
  
  .operation-info {
    flex: 1;
    margin-right: 16px;
    
    h4 {
      margin: 0 0 4px 0;
      color: #1e293b;
      font-size: 14px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #64748b;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}

.form-tip {
  font-size: 11px;
  color: #64748b;
  margin-top: 2px;
  line-height: 1.3;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-tabs__content) {
  padding-top: 12px;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: #e2e8f0;
}

:deep(.el-tabs__item) {
  font-weight: 500;
  color: #64748b;
  font-size: 13px;
  padding: 0 16px;
  
  &.is-active {
    color: #3b82f6;
  }
}

:deep(.el-tabs__active-bar) {
  background: #3b82f6;
  height: 2px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  font-size: 12px;
  padding: 6px 12px;
  height: auto;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-1px);
  }
}

:deep(.el-button--primary) {
  background: #3b82f6;
  border-color: #3b82f6;
  
  &:hover {
    background: #2563eb;
    border-color: #2563eb;
  }
}

:deep(.el-button--success) {
  background: #10b981;
  border-color: #10b981;
  
  &:hover {
    background: #059669;
    border-color: #059669;
  }
}

:deep(.el-button--warning) {
  background: #f59e0b;
  border-color: #f59e0b;
  
  &:hover {
    background: #d97706;
    border-color: #d97706;
  }
}

:deep(.el-button--danger) {
  background: #ef4444;
  border-color: #ef4444;
  
  &:hover {
    background: #dc2626;
    border-color: #dc2626;
  }
}

:deep(.el-button--info) {
  background: #6b7280;
  border-color: #6b7280;
  
  &:hover {
    background: #4b5563;
    border-color: #4b5563;
  }
}

:deep(.el-switch) {
  .el-switch__core {
    border-radius: 8px;
    height: 20px;
  }
}

:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  font-size: 11px;
}

:deep(.el-upload) {
  .el-button {
    margin: 0;
  }
}

:deep(.el-input__inner) {
  font-size: 13px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

@media (max-width: 1024px) {
  .settings-layout {
    flex-direction: column;
  }
  
  .config-sidebar {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .status-sidebar {
    width: 100%;
  }
  
  .status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .config-sidebar,
  .status-sidebar {
    padding: 16px;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .operation-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    
    .operation-info {
      margin-right: 0;
    }
  }
}
</style>
