<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="规则名称">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入规则名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryParams.enabled"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增规则
      </el-button>
      <el-button 
        type="danger" 
        :disabled="!multipleSelection.length"
        @click="handleBatchDelete"
      >
        <el-icon><Delete /></el-icon>
        批量删除
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="ruleList"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      stripe
      style="width: 100%;"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column
        prop="name"
        label="规则名称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="description"
        label="描述"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="priority"
        label="优先级"
        width="180"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-tag :type="getPriorityType(row.priority)">
            {{ row.priority }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="enabled"
        label="状态"
        width="180"
        align="center"
      >
        <template #default="{ row }">
          <el-switch
            v-model="row.enabled"
            @change="handleStatusChange(row)"
            :loading="row.statusLoading"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="created_at"
        label="创建时间"
        width="180"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatTime(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            type="success"
            size="small"
            @click="handleTest(row)"
          >
            测试
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <!-- 测试规则对话框 -->
    <el-dialog
      v-model="testDialog.visible"
      title="测试规则"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="testDialog.form" label-width="100px">
        <el-form-item label="告警源">
          <el-input v-model="testDialog.form.source" placeholder="请输入告警源" />
        </el-form-item>
        <el-form-item label="告警等级">
          <el-select v-model="testDialog.form.level" placeholder="请选择告警等级">
            <el-option label="信息" value="info" />
            <el-option label="警告" value="warning" />
            <el-option label="错误" value="error" />
            <el-option label="严重" value="critical" />
          </el-select>
        </el-form-item>
        <el-form-item label="告警类型">
          <el-input v-model="testDialog.form.type" placeholder="请输入告警类型" />
        </el-form-item>
        <el-form-item label="视频ID">
          <el-input-number v-model="testDialog.form.video_id" :min="1" />
        </el-form-item>
        <el-form-item label="算法ID">
          <el-input-number v-model="testDialog.form.algorithm_id" :min="1" />
        </el-form-item>
        <el-form-item label="告警内容">
          <el-input
            v-model="testDialog.form.content"
            type="textarea"
            :rows="3"
            placeholder="请输入告警内容"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="testDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="executeTest" :loading="testDialog.loading">
            执行测试
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 规则表单弹框 -->
    <el-dialog
      v-model="ruleFormDialog.visible"
      title="规则管理"
      width="80%"
      :close-on-click-modal="false"
      destroy-on-close
      @close="handleRuleFormDialogClose"
    >
      <RuleForm
        v-if="ruleFormDialog.visible"
        :rule-id="ruleFormDialog.ruleId"
        :is-edit="ruleFormDialog.isEdit"
        @success="handleRuleFormSuccess"
        @cancel="handleRuleFormCancel"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { LinkageRuleAPI, type LinkageRule, type LinkageRuleQuery } from '@/api/linkage'
import dayjs from 'dayjs'
import RuleForm from './form.vue'

defineOptions({
  name: "LinkageRules",
});

// 响应式数据
const loading = ref(false)
const ruleList = ref<LinkageRule[]>([])
const total = ref(0)
const multipleSelection = ref<LinkageRule[]>([])

// 查询参数
const queryParams = reactive<LinkageRuleQuery>({
  page: 1,
  size: 10,
  name: '',
  enabled: undefined,
  sort: 'created_at',
  order: 'desc'
})

// 测试对话框
const testDialog = reactive({
  visible: false,
  loading: false,
  ruleId: '',
  form: {
    source: '测试摄像头',
    level: 'warning',
    type: '人员检测',
    video_id: 1,
    algorithm_id: 1,
    content: '检测到未授权人员进入'
  }
})

// 规则表单弹框
const ruleFormDialog = reactive({
  visible: false,
  ruleId: null as string | null,
  isEdit: false
})

// 方法
const getList = async () => {
  loading.value = true
  try {
    const response = await LinkageRuleAPI.getList(queryParams)
    console.log('API响应数据:', response) // 调试日志

    // 处理边缘计算平台API的响应格式
    if (response && response.data && Array.isArray(response.data)) {
      // ECP API格式: {errorCode: 0, data: [...], success: true}
      ruleList.value = response.data
      total.value = response.data.length
    } else if (Array.isArray(response)) {
      // 如果直接返回数组
      ruleList.value = response
      total.value = response.length
    } else if (response && response.items) {
      // 如果返回分页格式
      ruleList.value = response.items || []
      total.value = response.total || 0
    } else {
      // 兼容其他格式
      console.warn('未识别的响应格式:', response)
      ruleList.value = []
      total.value = 0
    }

    console.log('规则列表数据:', ruleList.value) // 调试日志
  } catch (error) {
    console.error('获取规则列表失败:', error)
    ElMessage.error('获取规则列表失败')
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  queryParams.page = 1
  getList()
}

const resetQuery = () => {
  queryParams.page = 1
  queryParams.name = ''
  queryParams.enabled = undefined
  getList()
}

const handleAdd = () => {
  ruleFormDialog.ruleId = null
  ruleFormDialog.isEdit = false
  ruleFormDialog.visible = true
}

const handleEdit = (row: LinkageRule) => {
  ruleFormDialog.ruleId = row.id!
  ruleFormDialog.isEdit = true
  ruleFormDialog.visible = true
}

const handleDelete = async (row: LinkageRule) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除规则"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await LinkageRuleAPI.delete(row.id!)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除规则失败:', error)
      ElMessage.error('删除规则失败')
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 条规则吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const promises = multipleSelection.value.map(rule => 
      LinkageRuleAPI.delete(rule.id!)
    )
    
    await Promise.all(promises)
    ElMessage.success('批量删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handleStatusChange = async (row: LinkageRule & { statusLoading?: boolean }) => {
  row.statusLoading = true
  try {
    if (row.enabled) {
      await LinkageRuleAPI.enable(row.id!)
      ElMessage.success('规则已启用')
    } else {
      await LinkageRuleAPI.disable(row.id!)
      ElMessage.success('规则已禁用')
    }
  } catch (error) {
    // 恢复原状态
    row.enabled = !row.enabled
    console.error('更新规则状态失败:', error)
    ElMessage.error('更新规则状态失败')
  } finally {
    row.statusLoading = false
  }
}

const handleTest = (row: LinkageRule) => {
  testDialog.ruleId = row.id!
  testDialog.visible = true
}

const executeTest = async () => {
  testDialog.loading = true
  try {
    await LinkageRuleAPI.test(testDialog.ruleId, testDialog.form)
    ElMessage.success('规则测试执行成功，请查看执行记录')
    testDialog.visible = false
  } catch (error) {
    console.error('规则测试失败:', error)
    ElMessage.error('规则测试失败')
  } finally {
    testDialog.loading = false
  }
}

const handleSelectionChange = (selection: LinkageRule[]) => {
  multipleSelection.value = selection
}

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  queryParams.sort = prop
  queryParams.order = order === 'ascending' ? 'asc' : 'desc'
  getList()
}

const getPriorityType = (priority: number) => {
  if (priority >= 20) return 'danger'
  if (priority >= 10) return 'warning'
  return 'info'
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 表单弹框处理方法
const handleRuleFormDialogClose = () => {
  // 弹框关闭时的处理
}

const handleRuleFormSuccess = () => {
  // 表单提交成功后关闭弹框并刷新列表
  ruleFormDialog.visible = false
  getList()
  ElMessage.success('操作成功')
}

const handleRuleFormCancel = () => {
  // 取消操作，关闭弹框
  ruleFormDialog.visible = false
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.toolbar {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 500;
    }
  }
}
</style>
