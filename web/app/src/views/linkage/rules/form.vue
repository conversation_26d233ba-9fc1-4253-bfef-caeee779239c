<template>
  <div class="rule-form-container">
    <div class="form-layout">
      <!-- 左侧表单区域 -->
      <div class="form-sidebar">
        <!-- 基本信息 -->
        <div class="form-block">
          <div class="block-header">
            <h4 class="block-title">
              <el-icon class="block-icon"><Document /></el-icon>
              基本信息
            </h4>
          </div>
          <div class="block-content">
            <el-form
              ref="formRef"
              :model="form"
              :rules="rules"
              label-width="100px"
              v-loading="loading"
            >
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="规则名称" prop="name">
                    <el-input
                      v-model="form.name"
                      placeholder="请输入规则名称"
                      maxlength="50"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="优先级" prop="priority">
                    <el-input-number
                      v-model="form.priority"
                      :min="1"
                      :max="100"
                      placeholder="数值越大优先级越高"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="规则描述" prop="description">
                <el-input
                  v-model="form.description"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入规则描述"
                  maxlength="200"
                  show-word-limit
                  resize="none"
                />
              </el-form-item>

              <el-form-item label="启用状态">
                <el-switch
                  v-model="form.enabled"
                  active-text="启用"
                  inactive-text="禁用"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 触发条件 -->
        <div class="form-block">
          <div class="block-header">
            <h4 class="block-title">
              <el-icon class="block-icon"><Bell /></el-icon>
              触发条件
            </h4>
          </div>
          <div class="block-content">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="视频源">
                  <el-select
                    v-model="conditions.video_ids"
                    multiple
                    placeholder="请选择视频源"
                    style="width: 100%"
                    collapse-tags
                    collapse-tags-tooltip
                  >
                    <el-option
                      v-for="video in videoSources"
                      :key="video.id"
                      :label="video.name"
                      :value="video.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="算法">
                  <el-select
                    v-model="conditions.algorithm_ids"
                    multiple
                    placeholder="请选择算法"
                    style="width: 100%"
                    collapse-tags
                    collapse-tags-tooltip
                  >
                    <el-option
                      v-for="algorithm in algorithms"
                      :key="algorithm.id"
                      :label="algorithm.name"
                      :value="algorithm.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="告警等级">
                  <el-select
                    v-model="conditions.levels"
                    multiple
                    placeholder="请选择告警等级"
                    style="width: 100%"
                    collapse-tags
                  >
                    <el-option label="信息" value="info" />
                    <el-option label="警告" value="warning" />
                    <el-option label="错误" value="error" />
                    <el-option label="严重" value="critical" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="告警类型">
                  <el-select
                    v-model="conditions.types"
                    multiple
                    allow-create
                    filterable
                    placeholder="请选择或输入告警类型"
                    style="width: 100%"
                    collapse-tags
                    collapse-tags-tooltip
                  >
                    <el-option label="人员检测" value="人员检测" />
                    <el-option label="车辆检测" value="车辆检测" />
                    <el-option label="火灾检测" value="火灾检测" />
                    <el-option label="烟雾检测" value="烟雾检测" />
                    <el-option label="入侵检测" value="入侵检测" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 时间范围 -->
            <div class="time-range-section">
              <el-form-item label="生效时间">
                <el-checkbox
                  v-model="enableTimeRange"
                  @change="handleTimeRangeChange"
                >
                  启用时间范围限制
                </el-checkbox>
              </el-form-item>

              <div v-if="enableTimeRange" class="time-range-config">
                <el-row :gutter="16">
                  <el-col :span="8">
                    <el-form-item label="开始时间">
                      <el-time-picker
                        v-model="timeRange.start_time"
                        format="HH:mm:ss"
                        placeholder="选择开始时间"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="结束时间">
                      <el-time-picker
                        v-model="timeRange.end_time"
                        format="HH:mm:ss"
                        placeholder="选择结束时间"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="生效日期">
                      <el-select
                        v-model="timeRange.weekdays"
                        multiple
                        placeholder="选择生效日期"
                        style="width: 100%"
                        collapse-tags
                      >
                        <el-option label="周一" :value="1" />
                        <el-option label="周二" :value="2" />
                        <el-option label="周三" :value="3" />
                        <el-option label="周四" :value="4" />
                        <el-option label="周五" :value="5" />
                        <el-option label="周六" :value="6" />
                        <el-option label="周日" :value="7" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>

            <!-- 自定义表达式 -->
            <div class="expression-section">
              <el-form-item label="自定义条件">
                <el-input
                  v-model="conditions.expression"
                  type="textarea"
                  :rows="2"
                  placeholder="可选：输入自定义条件表达式，如：level == 'error' && type == '火灾检测'"
                  resize="none"
                />
                <div class="form-tip">
                  <el-icon><InfoFilled /></el-icon>
                  支持的变量：source, level, type, video_id, algorithm_id, content<br>
                  支持的操作符：==, !=, >, <, >=, <=, &&, ||, in
                </div>
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 联动动作 -->
        <div class="form-block">
          <div class="block-header">
            <h4 class="block-title">
              <el-icon class="block-icon"><Connection /></el-icon>
              联动动作
              <el-button type="primary" size="small" @click="addAction">
                <el-icon><Plus /></el-icon>
                添加动作
              </el-button>
            </h4>
          </div>
          <div class="block-content">
            <div v-if="actions.length === 0" class="empty-actions">
              <el-empty description="暂无联动动作，请点击上方按钮添加" :image-size="80">
                <template #image>
                  <el-icon class="empty-icon"><Connection /></el-icon>
                </template>
              </el-empty>
            </div>

            <div v-for="(action, index) in actions" :key="index" class="action-item">
              <div class="action-card">
                <div class="action-header">
                  <div class="action-title">
                    <el-icon class="action-icon"><Operation /></el-icon>
                    <span>动作 {{ index + 1 }}</span>
                  </div>
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeAction(index)"
                  >
                    删除
                  </el-button>
                </div>
                <div class="action-content">
                  <el-row :gutter="16">
                    <el-col :span="8">
                      <el-form-item label="目标设备" :prop="`actions.${index}.device_id`">
                        <el-select
                          v-model="action.device_id"
                          placeholder="请选择设备"
                          style="width: 100%"
                          @change="handleDeviceChange(action, index)"
                          filterable
                        >
                          <el-option
                            v-for="device in devices"
                            :key="device.id"
                            :label="device.name"
                            :value="device.id"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="执行命令" :prop="`actions.${index}.command`">
                        <el-select
                          v-model="action.command"
                          placeholder="请选择命令"
                          style="width: 100%"
                          @change="handleCommandChange(action, index)"
                        >
                          <el-option
                            v-for="cmd in getAvailableCommands(action.device_id)"
                            :key="cmd.value"
                            :label="cmd.label"
                            :value="cmd.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="延迟执行(秒)">
                        <el-input-number
                          v-model="action.delay"
                          :min="0"
                          :max="3600"
                          placeholder="延迟时间"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <!-- 命令参数 -->
                  <div v-if="action.command" class="command-params">
                    <div class="params-header">
                      <el-icon class="params-icon"><Setting /></el-icon>
                      <span>命令参数</span>
                    </div>
                    <div class="params-content">
                      <component
                        :is="getParamComponent(action)"
                        v-model="action.params"
                        :device="getDeviceById(action.device_id)"
                        :command="action.command"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部操作区域 -->
      <div class="form-footer">
        <div class="footer-actions">
          <el-button @click="goBack">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            <el-icon><Check /></el-icon>
            {{ isEdit ? '更新规则' : '创建规则' }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  ArrowLeft,
  Document,
  Bell,
  InfoFilled,
  Connection,
  Plus,
  Operation,
  Delete,
  Close,
  Check
} from '@element-plus/icons-vue'
import { LinkageRuleAPI, LinkageDeviceAPI, AlertAPI, type LinkageRule, type RuleConditions } from '@/api/linkage'
import VideoAPI from '@/api/video'
import dayjs from 'dayjs'
import ParamEditor from '@/components/linkage/ParamEditor.vue'

defineOptions({
  name: "LinkageRuleForm",
});

// Props定义
const props = defineProps<{
  ruleId?: string | null
  isEdit?: boolean
}>()

// Emits定义
const emit = defineEmits<{
  success: []
  cancel: []
}>()

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const formRef = ref()

const isEdit = computed(() => props.isEdit || !!route.params.id)

// 表单数据
const form = reactive<LinkageRule>({
  name: '',
  description: '',
  enabled: true,
  priority: 10,
  conditions: {
    video_ids: [],
    algorithm_ids: [],
    levels: [],
    types: [],
    expression: ''
  },
  actions: []
})

// 条件配置
const conditions = reactive({
  video_ids: [] as number[],
  algorithm_ids: [] as number[],
  levels: [] as string[],
  types: [] as string[],
  expression: ''
})

// 时间范围
const enableTimeRange = ref(false)
const timeRange = reactive({
  start_time: '',
  end_time: '',
  weekdays: [] as number[]
})

// 动作配置
const actions = ref<any[]>([])

// 选项数据
const videoSources = ref<any[]>([])
const algorithms = ref<any[]>([])
const devices = ref<any[]>([])

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请输入优先级', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '优先级范围 1-100', trigger: 'blur' }
  ]
}

// 方法
const loadFormData = async () => {
  if (!isEdit.value) return

  const ruleId = props.ruleId || route.params.id as string
  if (!ruleId) return

  loading.value = true
  try {
    const response = await LinkageRuleAPI.getDetail(ruleId)
    console.log('规则详情API响应:', response) // 调试日志

    // 处理ECP API响应格式
    let data: LinkageRule
    if (response && response.data) {
      // ECP API格式: {errorCode: 0, data: {...}, success: true}
      data = response.data as LinkageRule
    } else {
      // 直接返回数据格式
      data = (response as unknown) as LinkageRule
    }

    console.log('规则详情数据:', data) // 调试日志
    Object.assign(form, data)

    // 解析条件
    if (data.conditions) {
      const conditionsData = typeof data.conditions === 'string' ? JSON.parse(data.conditions) : data.conditions
      Object.assign(conditions, conditionsData)

      if (conditionsData.time_range) {
        enableTimeRange.value = true
        Object.assign(timeRange, conditionsData.time_range)
      }
    }

    // 解析动作
    if (data.actions) {
      actions.value = typeof data.actions === 'string' ? JSON.parse(data.actions) : data.actions
    }
  } catch (error) {
    console.error('加载规则数据失败:', error)
    ElMessage.error('加载规则数据失败')
  } finally {
    loading.value = false
  }
}

const loadOptions = async () => {
  try {
    console.log('🔄 开始加载选项数据...') // 调试日志

    // 并行加载数据，但对失败的API调用进行容错处理
    const [videosResponse, algorithmsResponse, devicesResponse] = await Promise.allSettled([
      VideoAPI.getVideos({ page: 1, limit: 1000 }),
      AlertAPI.getAlgorithms().catch(() => null),
      LinkageDeviceAPI.getList({ page: 1, size: 1000 })
    ])

    console.log('📊 选项数据API响应:', { videosResponse, algorithmsResponse, devicesResponse }) // 调试日志

    // 处理视频源数据
    const videosResult = videosResponse.status === 'fulfilled' ? videosResponse.value : null
    console.log('视频API原始响应:', videosResult) // 调试日志

    if (videosResult && videosResult.data && videosResult.data.results && Array.isArray(videosResult.data.results)) {
      // 处理ECP API格式的响应: {errorCode: 0, data: {results: [...]}, success: true}
      videoSources.value = videosResult.data.results.map((video: any) => ({
        id: video.id,
        name: video.name,
        location: video.description || `${video.protocol}://${video.camera_ip}`,
        protocol: video.protocol,
        camera_ip: video.camera_ip,
        camera_id: video.camera_id,
        status: video.status,
        brand: video.brand,
        url: video.url
      }))
      console.log('✅ 成功解析视频源数据（ECP格式）:', videoSources.value.length, '个')
    } else if (videosResult && videosResult.data && Array.isArray(videosResult.data)) {
      // 处理直接数组格式的响应: {data: [...]}
      videoSources.value = videosResult.data.map((video: any) => ({
        id: video.id,
        name: video.name,
        location: video.description || `${video.protocol}://${video.camera_ip}`,
        protocol: video.protocol,
        camera_ip: video.camera_ip,
        camera_id: video.camera_id,
        status: video.status,
        brand: video.brand,
        url: video.url
      }))
      console.log('✅ 成功解析视频源数据（直接数组格式）:', videoSources.value.length, '个')
    } else if (Array.isArray(videosResult)) {
      // 直接数组格式
      videoSources.value = videosResult.map((video: any) => ({
        id: video.id,
        name: video.name,
        location: video.description || `${video.protocol}://${video.camera_ip}`,
        protocol: video.protocol,
        camera_ip: video.camera_ip,
        camera_id: video.camera_id,
        status: video.status,
        brand: video.brand,
        url: video.url
      }))
      console.log('✅ 成功解析视频源数据（直接数组）:', videoSources.value.length, '个')
    } else {
      // 如果API调用失败，提供空数组
      videoSources.value = []
      console.warn('❌ 无法获取视频源数据，使用空列表。响应结构:', videosResult)
    }

    console.log('📺 最终的视频源数据:', videoSources.value) // 调试日志
    console.log('📺 视频源数量:', videoSources.value.length) // 调试日志

    // 处理算法数据
    const algorithmsResult = algorithmsResponse.status === 'fulfilled' ? algorithmsResponse.value : null
    if (algorithmsResult && algorithmsResult.data && Array.isArray(algorithmsResult.data)) {
      algorithms.value = algorithmsResult.data
    } else if (Array.isArray(algorithmsResult)) {
      algorithms.value = algorithmsResult
    } else {
      // 提供默认的算法选项
      algorithms.value = [
        { id: 1, name: '人员检测', description: '检测画面中的人员' },
        { id: 2, name: '车辆检测', description: '检测画面中的车辆' },
        { id: 3, name: '火灾检测', description: '检测火灾和烟雾' },
        { id: 4, name: '入侵检测', description: '检测非法入侵行为' },
        { id: 5, name: '物品遗留', description: '检测遗留物品' }
      ]
    }

    // 处理设备数据
    const devicesResult = devicesResponse.status === 'fulfilled' ? devicesResponse.value : null
    if (devicesResult && devicesResult.data && Array.isArray(devicesResult.data)) {
      // ECP API格式: {errorCode: 0, data: [...], success: true}
      devices.value = devicesResult.data
    } else if (Array.isArray(devicesResult)) {
      // 直接返回数组格式
      devices.value = devicesResult
    } else if (devicesResult && (devicesResult as any).items) {
      // 分页格式
      devices.value = (devicesResult as any).items || []
    } else {
      devices.value = []
    }

    console.log('✅ 选项数据加载完成:', {
      videoSources: videoSources.value.length + ' 个视频源',
      algorithms: algorithms.value.length + ' 个算法',
      devices: devices.value.length + ' 个设备'
    }) // 调试日志

    console.log('📊 详细选项数据:', {
      videoSources: videoSources.value,
      algorithms: algorithms.value,
      devices: devices.value
    }) // 调试日志
  } catch (error) {
    console.error('加载选项数据失败:', error)
  }
}

const handleTimeRangeChange = (enabled: boolean) => {
  if (!enabled) {
    timeRange.start_time = ''
    timeRange.end_time = ''
    timeRange.weekdays = []
  }
}

const addAction = () => {
  actions.value.push({
    device_id: '',
    command: '',
    params: {},
    delay: 0
  })
}

const removeAction = (index: number) => {
  actions.value.splice(index, 1)
}

const handleDeviceChange = (action: any, _index: number) => {
  // 重置命令和参数
  action.command = ''
  action.params = {}
}

const handleCommandChange = (action: any, _index: number) => {
  // 重置参数
  action.params = {}
}

const getDeviceById = (deviceId: string) => {
  return devices.value.find(d => d.id === deviceId)
}

const getAvailableCommands = (deviceId: string) => {
  const device = getDeviceById(deviceId)
  if (!device) return []
  
  // 根据设备协议返回可用命令
  const commandMap: Record<string, any[]> = {
    mqtt: [
      { label: '开启', value: 'turn_on' },
      { label: '关闭', value: 'turn_off' },
      { label: '闪烁', value: 'blink' },
      { label: '设置亮度', value: 'set_brightness' }
    ],
    modbus: [
      { label: '写入单个线圈', value: 'write_single_coil' },
      { label: '写入单个寄存器', value: 'write_single_register' },
      { label: '写入多个线圈', value: 'write_multiple_coils' },
      { label: '写入多个寄存器', value: 'write_multiple_registers' }
    ],
    rs485: [
      { label: '发送十六进制数据', value: 'raw_hex' },
      { label: 'Modbus读取', value: 'modbus_read' },
      { label: 'Modbus写入', value: 'modbus_write' },
      { label: '自定义命令', value: 'custom' }
    ]
  }
  
  return commandMap[device.protocol] || []
}

const getParamComponent = (_action: any) => {
  // 返回参数配置组件
  // 可以根据设备协议和命令返回不同的组件
  return ParamEditor
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    // 构建条件对象
    const conditionsData: RuleConditions = {
      video_ids: conditions.video_ids,
      algorithm_ids: conditions.algorithm_ids,
      levels: conditions.levels,
      types: conditions.types
    }

    if (conditions.expression) {
      conditionsData.expression = conditions.expression
    }

    if (enableTimeRange.value) {
      conditionsData.time_range = {
        start_time: timeRange.start_time,
        end_time: timeRange.end_time,
        weekdays: timeRange.weekdays
      }
    }

    // 直接赋值对象，不需要JSON.stringify
    form.conditions = conditionsData
    form.actions = actions.value
    
    if (isEdit.value) {
      const ruleId = props.ruleId || route.params.id as string
      await LinkageRuleAPI.update(ruleId, form)
      ElMessage.success('规则更新成功')
    } else {
      await LinkageRuleAPI.create(form)
      ElMessage.success('规则创建成功')
    }
    
    // 如果是在弹框中，发出success事件
    if (props.ruleId !== undefined) {
      emit('success')
    } else {
      goBack()
    }
  } catch (error) {
    console.error('保存规则失败:', error)
    ElMessage.error('保存规则失败')
  } finally {
    submitLoading.value = false
  }
}

const goBack = () => {
  // 如果是在弹框中，发出cancel事件
  if (props.ruleId !== undefined) {
    emit('cancel')
  } else {
    router.push('/linkage/rules')
  }
}

// 生命周期
onMounted(async () => {
  await loadOptions()
  await loadFormData()
})
</script>

<style lang="scss" scoped>
.rule-form-container {
  padding: 0;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.form-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.form-sidebar {
  flex: 1;
  background: #ffffff;
  padding: 24px;
  overflow-y: auto;
}

.form-footer {
  // background: #f1f5f9;
  padding: 20px;
  // border-top: 1px solid #e2e8f0;
}

.form-block {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.block-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;

  .block-icon {
    font-size: 18px;
    color: #3b82f6;
  }
}

.block-content {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.time-range-section {
  .time-range-config {
    margin-top: 12px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
  }
}

.expression-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.form-tip {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  font-size: 11px;
  color: #64748b;
  margin-top: 6px;
  line-height: 1.4;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

.empty-actions {
  text-align: center;
  padding: 40px 0;

  .empty-icon {
    font-size: 48px;
    color: #d1d5db;
  }
}

.action-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .action-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;

    &:hover {
      background: #f8fafc;
      border-color: #cbd5e1;
    }

    .action-header {
      padding: 12px 16px;
      background: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .action-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #374151;

        .action-icon {
          font-size: 14px;
          color: #3b82f6;
        }
      }
    }

    .action-content {
      padding: 16px;
    }
  }
}

.command-params {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;

  .params-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 600;
    color: #374151;

    .params-icon {
      font-size: 14px;
      color: #3b82f6;
    }
  }

  .params-content {
    background: #f8fafc;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
  }
}

.footer-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

// 全局表单样式优化
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.2s ease;
  min-height: 32px;

  &:hover {
    border-color: #3b82f6;
  }
}

:deep(.el-select .el-input__wrapper) {
  min-height: 32px;

  &:hover {
    border-color: #3b82f6;
  }
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
  transition: all 0.2s ease;
  min-height: 32px;

  &:hover {
    border-color: #3b82f6;
  }
}

:deep(.el-input-number) {
  width: 100%;

  .el-input__wrapper {
    min-height: 32px;
  }
}

:deep(.el-switch) {
  &.is-checked .el-switch__core {
    background-color: #3b82f6;
  }
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  font-size: 13px;
  padding: 8px 16px;
  height: auto;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-1px);
  }
}

:deep(.el-button--primary) {
  background: #3b82f6;
  border-color: #3b82f6;
  
  &:hover {
    background: #2563eb;
    border-color: #2563eb;
  }
}

:deep(.el-button--danger) {
  background: #ef4444;
  border-color: #ef4444;
  
  &:hover {
    background: #dc2626;
    border-color: #dc2626;
  }
}

:deep(.el-input__inner) {
  font-size: 13px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

@media (max-width: 640px) {
  .form-sidebar {
    padding: 16px;
  }
  
  .form-footer {
    padding: 16px;
  }
  
  .footer-actions {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
