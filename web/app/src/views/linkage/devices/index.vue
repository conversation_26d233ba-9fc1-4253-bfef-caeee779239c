<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="设备名称">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入设备名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select
            v-model="queryParams.type"
            placeholder="请选择设备类型"
            clearable
            style="width: 150px"
          >
            <el-option label="警报灯" value="警报灯" />
            <el-option label="门禁控制器" value="门禁控制器" />
            <el-option label="报警器" value="报警器" />
            <el-option label="摄像头" value="摄像头" />
            <el-option label="传感器" value="传感器" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="协议类型">
          <el-select
            v-model="queryParams.protocol"
            placeholder="请选择协议"
            clearable
            style="width: 120px"
          >
            <el-option label="MQTT" value="mqtt" />
            <el-option label="Modbus" value="modbus" />
            <el-option label="RS485" value="rs485" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="在线" value="online" />
            <el-option label="离线" value="offline" />
            <el-option label="错误" value="error" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加设备
      </el-button>
      <el-button 
        type="danger" 
        :disabled="!multipleSelection.length"
        @click="handleBatchDelete"
      >
        <el-icon><Delete /></el-icon>
        批量删除
      </el-button>
      <el-button type="success" @click="handleRefreshStatus">
        <el-icon><Refresh /></el-icon>
        刷新状态
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="deviceList"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      stripe
      style="width: 100%;"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column
        prop="name"
        label="设备名称"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        prop="type"
        label="设备类型"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-tag type="info" size="small">{{ row.type }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="protocol"
        label="协议"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="getProtocolType(row.protocol)" size="small">
            {{ row.protocol.toUpperCase() }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="address"
        label="地址"
        width="120"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="port"
        label="端口"
        width="100"
        align="center"
      />
      <el-table-column
        prop="status"
        label="状态"
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <div class="status-cell">
            <span class="status-dot" :class="getStatusClass(row.status)"></span>
            <span>{{ getStatusText(row.status) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="created_at"
        label="创建时间"
        width="180"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatTime(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            type="success"
            size="small"
            @click="handleTest(row)"
            :loading="row.testLoading"
          >
            测试
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="handleControl(row)"
          >
            控制
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <!-- 设备控制对话框 -->
    <el-dialog
      v-model="controlDialog.visible"
      title="设备控制"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="control-panel">
        <div class="device-info">
          <h4>{{ controlDialog.device?.name }}</h4>
          <p>协议: {{ controlDialog.device?.protocol?.toUpperCase() }}</p>
          <p>地址: {{ controlDialog.device?.address }}:{{ controlDialog.device?.port }}</p>
        </div>
        
        <el-divider />
        
        <el-form :model="controlDialog.form" label-width="100px">
          <el-form-item label="控制命令">
            <el-select
              v-model="controlDialog.form.command"
              placeholder="请选择命令"
              style="width: 100%"
              @change="handleCommandChange"
            >
              <el-option
                v-for="cmd in getAvailableCommands(controlDialog.device?.protocol)"
                :key="cmd.value"
                :label="cmd.label"
                :value="cmd.value"
              />
            </el-select>
          </el-form-item>
          
          <!-- 命令参数 -->
          <div v-if="controlDialog.form.command" class="command-params">
            <component
              :is="getControlComponent(controlDialog.device?.protocol, controlDialog.form.command)"
              v-model="controlDialog.form.params"
              :device="controlDialog.device"
              :command="controlDialog.form.command"
            />
          </div>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="controlDialog.visible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="executeControl" 
            :loading="controlDialog.loading"
            :disabled="!controlDialog.form.command"
          >
            执行命令
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设备表单弹框 -->
    <el-dialog
      v-model="deviceFormDialog.visible"
      title="设备管理"
      width="80%"
      :close-on-click-modal="false"
      destroy-on-close
      @close="handleFormDialogClose"
    >
      <DeviceForm
        v-if="deviceFormDialog.visible"
        :device-id="deviceFormDialog.deviceId"
        :is-edit="deviceFormDialog.isEdit"
        @success="handleFormSuccess"
        @cancel="handleFormCancel"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { LinkageDeviceAPI, type LinkageDevice, type LinkageDeviceQuery } from '@/api/linkage'
import { linkageWebSocket } from '@/utils/linkage-websocket'
import { linkageSSE, type DeviceStatusUpdateEvent } from '@/utils/linkage-sse'
import dayjs from 'dayjs'
import ParamEditor from '@/components/linkage/ParamEditor.vue'
import DeviceForm from './form.vue'

defineOptions({
  name: "LinkageDevices",
});

// 响应式数据
const loading = ref(false)
const deviceList = ref<LinkageDevice[]>([])
const total = ref(0)
const multipleSelection = ref<LinkageDevice[]>([])

// 查询参数
const queryParams = reactive<LinkageDeviceQuery>({
  page: 1,
  size: 10,
  name: '',
  type: '',
  protocol: '',
  status: '',
  sort: 'created_at',
  order: 'desc'
})

// 控制对话框
const controlDialog = reactive({
  visible: false,
  loading: false,
  device: null as LinkageDevice | null,
  form: {
    command: '',
    params: {} as any
  }
})

// 设备表单弹框
const deviceFormDialog = reactive({
  visible: false,
  deviceId: null as string | null,
  isEdit: false
})

// 方法
const getList = async () => {
  loading.value = true
  try {
    const response = await LinkageDeviceAPI.getList(queryParams)
    console.log('API响应数据:', response) // 调试日志

    // 处理边缘计算平台API的响应格式
    if (response && response.data && Array.isArray(response.data)) {
      // ECP API格式: {errorCode: 0, data: [...], success: true}
      deviceList.value = response.data
      total.value = response.data.length
    } else if (Array.isArray(response)) {
      // 如果直接返回数组
      deviceList.value = response
      total.value = response.length
    } else if (response && response.items) {
      // 如果返回分页格式
      deviceList.value = response.items || []
      total.value = response.total || 0
    } else {
      // 兼容其他格式
      console.warn('未识别的响应格式:', response)
      deviceList.value = []
      total.value = 0
    }

    console.log('设备列表数据:', deviceList.value) // 调试日志
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败')
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  queryParams.page = 1
  getList()
}

const resetQuery = () => {
  queryParams.page = 1
  queryParams.name = ''
  queryParams.type = ''
  queryParams.protocol = ''
  queryParams.status = ''
  getList()
}

const handleAdd = () => {
  deviceFormDialog.deviceId = null
  deviceFormDialog.isEdit = false
  deviceFormDialog.visible = true
}

const handleEdit = (row: LinkageDevice) => {
  deviceFormDialog.deviceId = row.id!
  deviceFormDialog.isEdit = true
  deviceFormDialog.visible = true
}

const handleDelete = async (row: LinkageDevice) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await LinkageDeviceAPI.delete(row.id!)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除设备失败:', error)
      ElMessage.error('删除设备失败')
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 个设备吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const promises = multipleSelection.value.map(device => 
      LinkageDeviceAPI.delete(device.id!)
    )
    
    await Promise.all(promises)
    ElMessage.success('批量删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handleTest = async (row: LinkageDevice & { testLoading?: boolean }) => {
  row.testLoading = true
  try {
    const response = await LinkageDeviceAPI.test(row.id!)
    console.log('设备测试API响应:', response) // 调试日志

    // 处理ECP API响应格式
    let result
    if (response && response.data) {
      // ECP API格式: {errorCode: 0, data: {...}, success: true}
      result = response.data
    } else {
      // 直接返回数据格式
      result = response
    }

    console.log('设备测试结果:', result) // 调试日志

    if (result && result.success) {
      ElMessage.success(`设备测试成功 - 响应时间: ${result.response_time || 0}ms`)
    } else {
      ElMessage.success('设备测试成功')
    }

    // 刷新设备状态
    getList()
  } catch (error) {
    console.error('设备测试失败:', error)
    ElMessage.error('设备测试失败')
  } finally {
    row.testLoading = false
  }
}

const handleControl = (row: LinkageDevice) => {
  controlDialog.device = row
  controlDialog.form.command = ''
  controlDialog.form.params = {}
  controlDialog.visible = true
}

const handleRefreshStatus = async () => {
  loading.value = true
  try {
    // 刷新所有设备状态
    await Promise.all(
      deviceList.value.map(device => 
        LinkageDeviceAPI.getStatus(device.id!)
      )
    )
    getList()
    ElMessage.success('设备状态已刷新')
  } catch (error) {
    console.error('刷新设备状态失败:', error)
    ElMessage.error('刷新设备状态失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection: LinkageDevice[]) => {
  multipleSelection.value = selection
}

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  queryParams.sort = prop
  queryParams.order = order === 'ascending' ? 'asc' : 'desc'
  getList()
}

const handleCommandChange = () => {
  console.log('命令变更:', controlDialog.form.command) // 调试日志
  console.log('当前设备:', controlDialog.device) // 调试日志
  controlDialog.form.params = {}
}

const executeControl = async () => {
  if (!controlDialog.form.command) {
    ElMessage.warning('请选择控制命令')
    return
  }

  if (!controlDialog.device) {
    ElMessage.error('设备信息不存在')
    return
  }

  controlDialog.loading = true
  try {
    // 优先使用WebSocket发送设备控制命令
    const success = await sendDeviceControlCommand(
      controlDialog.device.id!,
      controlDialog.form.command,
      controlDialog.form.params
    )

    if (success) {
      ElMessage.success('设备控制命令已发送')
      controlDialog.visible = false

      // 刷新设备状态
      getList()
    } else {
      // WebSocket失败时回退到HTTP API
      await LinkageDeviceAPI.control(
        controlDialog.device.id!,
        controlDialog.form.command,
        controlDialog.form.params
      )
      ElMessage.success('命令执行成功')
      controlDialog.visible = false
    }
  } catch (error) {
    console.error('命令执行失败:', error)
    ElMessage.error('命令执行失败')
  } finally {
    controlDialog.loading = false
  }
}

// WebSocket设备控制
const sendDeviceControlCommand = (deviceId: string, command: string, params: any): Promise<boolean> => {
  return new Promise((resolve) => {
    // 确保WebSocket连接
    if (!linkageWebSocket.isConnected) {
      linkageWebSocket.connect().then(() => {
        sendCommand()
      }).catch(() => {
        resolve(false)
      })
    } else {
      sendCommand()
    }

    function sendCommand() {
      // 发送设备控制命令
      linkageWebSocket.send({
        type: 'device_control',
        payload: {
          device_id: deviceId,
          command: command,
          params: params
        }
      })

      // 监听响应
      const responseHandler = (data: any) => {
        if (data.type === 'device_control_response' && data.payload.device_id === deviceId) {
          linkageWebSocket.unsubscribe('device_control_response', responseHandler)
          resolve(data.payload.success)
        }
      }

      linkageWebSocket.subscribe('device_control_response', responseHandler)

      // 设置超时
      setTimeout(() => {
        linkageWebSocket.unsubscribe('device_control_response', responseHandler)
        resolve(false)
      }, 10000) // 10秒超时
    }
  })
}

const getProtocolType = (protocol: string): 'success' | 'warning' | 'info' | 'primary' => {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary'> = {
    mqtt: 'success',
    modbus: 'warning',
    rs485: 'info'
  }
  return typeMap[protocol] || 'info'
}

const getStatusClass = (status: string) => {
  return {
    'status-online': status === 'online',
    'status-offline': status === 'offline',
    'status-error': status === 'error'
  }
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    online: '在线',
    offline: '离线',
    error: '错误'
  }
  return textMap[status] || '未知'
}

const getAvailableCommands = (protocol: string) => {
  const commandMap: Record<string, any[]> = {
    mqtt: [
      { label: '开启', value: 'turn_on' },
      { label: '关闭', value: 'turn_off' },
      { label: '闪烁', value: 'blink' },
      { label: '设置亮度', value: 'set_brightness' }
    ],
    modbus: [
      { label: '写入单个线圈', value: 'write_single_coil' },
      { label: '写入单个寄存器', value: 'write_single_register' },
      { label: '读取线圈', value: 'read_coils' },
      { label: '读取寄存器', value: 'read_holding_registers' }
    ],
    rs485: [
      { label: '发送十六进制数据', value: 'raw_hex' },
      { label: 'Modbus读取', value: 'modbus_read' },
      { label: 'Modbus写入', value: 'modbus_write' }
    ]
  }
  
  return commandMap[protocol] || []
}

const getControlComponent = (protocol: string, command: string) => {
  // 返回参数编辑器组件
  // 可以根据协议和命令返回不同的组件，这里统一使用ParamEditor
  console.log('获取控制组件:', { protocol, command }) // 调试日志
  return ParamEditor
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 设备状态实时更新
const handleDeviceStatusUpdate = (event: DeviceStatusUpdateEvent) => {
  console.log('收到设备状态更新:', event)

  // 查找并更新对应设备的状态
  const deviceIndex = deviceList.value.findIndex((device: LinkageDevice) => device.id === event.device_id)
  if (deviceIndex !== -1) {
    deviceList.value[deviceIndex].status = event.status
    deviceList.value[deviceIndex].last_seen = event.last_seen
    deviceList.value[deviceIndex].error_count = event.error_count
    // 注意：LinkageDevice接口可能没有response_time字段，这里先注释掉
    // if (event.response_time !== undefined) {
    //   deviceList.value[deviceIndex].response_time = event.response_time
    // }
  }
}

const startRealTimeUpdates = () => {
  // 连接设备状态流
  linkageSSE.connectDeviceStatus((data: any) => {
    if (data.type === 'device_status_update') {
      handleDeviceStatusUpdate(data.payload)
    }
  })

  console.log('设备状态实时更新已启动')
}

const stopRealTimeUpdates = () => {
  linkageSSE.disconnect('devices/status')
  console.log('设备状态实时更新已停止')
}

// 表单弹框处理方法
const handleFormDialogClose = () => {
  // 弹框关闭时的处理
}

const handleFormSuccess = () => {
  // 表单提交成功后关闭弹框并刷新列表
  deviceFormDialog.visible = false
  getList()
  ElMessage.success('操作成功')
}

const handleFormCancel = () => {
  // 取消操作，关闭弹框
  deviceFormDialog.visible = false
}

// 生命周期
onMounted(() => {
  getList()
  startRealTimeUpdates()
})

onUnmounted(() => {
  stopRealTimeUpdates()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.toolbar {
  margin-bottom: 20px;
}

.status-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
    
    &.status-online {
      background-color: #67c23a;
    }
    
    &.status-offline {
      background-color: #909399;
    }
    
    &.status-error {
      background-color: #f56c6c;
    }
  }
}

.control-panel {
  .device-info {
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 4px 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.command-params {
  margin-top: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 500;
    }
  }
}
</style>
