<template>
  <div class="device-form-container">
    <div class="form-layout">
      <!-- 左侧表单区域 -->
      <div class="form-sidebar">
        <!-- 基本信息 -->
        <div class="form-block">
          <div class="block-header">
            <h4 class="block-title">
              <el-icon class="block-icon"><Monitor /></el-icon>
              基本信息
            </h4>
          </div>
          <div class="block-content">
            <el-form
              ref="formRef"
              :model="form"
              :rules="rules"
              label-width="100px"
              v-loading="loading"
            >
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="设备名称" prop="name">
                    <el-input
                      v-model="form.name"
                      placeholder="请输入设备名称"
                      maxlength="50"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备类型" prop="type">
                    <el-select
                      v-model="form.type"
                      placeholder="请选择设备类型"
                      style="width: 100%"
                      filterable
                    >
                      <el-option label="警报灯" value="警报灯" />
                      <el-option label="门禁控制器" value="门禁控制器" />
                      <el-option label="报警器" value="报警器" />
                      <el-option label="摄像头" value="摄像头" />
                      <el-option label="传感器" value="传感器" />
                      <el-option label="其他" value="其他" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="设备描述">
                <el-input
                  v-model="form.description"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入设备描述"
                  maxlength="200"
                  show-word-limit
                  resize="none"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 连接配置 -->
        <div class="form-block">
          <div class="block-header">
            <h4 class="block-title">
              <el-icon class="block-icon"><Connection /></el-icon>
              连接配置
            </h4>
          </div>
          <div class="block-content">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="协议类型" prop="protocol">
                  <el-select
                    v-model="form.protocol"
                    placeholder="请选择协议"
                    style="width: 100%"
                    @change="handleProtocolChange"
                  >
                    <el-option label="MQTT" value="mqtt" />
                    <el-option label="Modbus" value="modbus" />
                    <el-option label="RS485" value="rs485" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备地址" prop="address">
                  <el-input
                    v-model="form.address"
                    placeholder="IP地址或串口名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="端口号" prop="port">
                  <el-input-number
                    v-model="form.port"
                    :min="0"
                    :max="65535"
                    placeholder="端口号"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 协议配置 -->
        <div class="form-block">
          <div class="block-header">
            <h4 class="block-title">
              <el-icon class="block-icon"><Setting /></el-icon>
              协议配置
            </h4>
          </div>
          <div class="block-content">
            <!-- MQTT配置 -->
            <div v-if="form.protocol === 'mqtt'" class="protocol-config">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="Broker URL">
                    <el-input
                      v-model="mqttConfig.broker_url"
                      placeholder="tcp://localhost:1883"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="客户端ID">
                    <el-input
                      v-model="mqttConfig.client_id"
                      placeholder="唯一客户端标识"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="用户名">
                    <el-input
                      v-model="mqttConfig.username"
                      placeholder="MQTT用户名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="密码">
                    <el-input
                      v-model="mqttConfig.password"
                      type="password"
                      placeholder="MQTT密码"
                      show-password
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="QoS等级">
                    <el-select v-model="mqttConfig.qos" style="width: 100%">
                      <el-option label="0 - 最多一次" :value="0" />
                      <el-option label="1 - 至少一次" :value="1" />
                      <el-option label="2 - 恰好一次" :value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="保持连接(秒)">
                    <el-input-number
                      v-model="mqttConfig.keep_alive"
                      :min="10"
                      :max="3600"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="启用TLS">
                    <el-switch v-model="mqttConfig.use_tls" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          
            <!-- Modbus配置 -->
            <div v-if="form.protocol === 'modbus'" class="protocol-config">
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="从站ID">
                    <el-input-number
                      v-model="modbusConfig.slave_id"
                      :min="1"
                      :max="247"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="超时时间(秒)">
                    <el-input-number
                      v-model="modbusConfig.timeout"
                      :min="1"
                      :max="60"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="重试次数">
                    <el-input-number
                      v-model="modbusConfig.retry_count"
                      :min="0"
                      :max="10"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="连接模式">
                    <el-select v-model="modbusConfig.mode" style="width: 100%">
                      <el-option label="TCP" value="tcp" />
                      <el-option label="RTU" value="rtu" />
                      <el-option label="ASCII" value="ascii" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="modbusConfig.mode !== 'tcp'">
                  <el-form-item label="串口名称">
                    <el-input
                      v-model="modbusConfig.serial_port"
                      placeholder="如: COM1 或 /dev/ttyUSB0"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            
              <div v-if="modbusConfig.mode !== 'tcp'">
                <el-row :gutter="16">
                  <el-col :span="6">
                    <el-form-item label="波特率">
                      <el-select v-model="modbusConfig.baud_rate" style="width: 100%">
                        <el-option label="9600" :value="9600" />
                        <el-option label="19200" :value="19200" />
                        <el-option label="38400" :value="38400" />
                        <el-option label="57600" :value="57600" />
                        <el-option label="115200" :value="115200" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="数据位">
                      <el-select v-model="modbusConfig.data_bits" style="width: 100%">
                        <el-option label="7" :value="7" />
                        <el-option label="8" :value="8" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="停止位">
                      <el-select v-model="modbusConfig.stop_bits" style="width: 100%">
                        <el-option label="1" :value="1" />
                        <el-option label="2" :value="2" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="校验位">
                      <el-select v-model="modbusConfig.parity" style="width: 100%">
                        <el-option label="无" value="none" />
                        <el-option label="奇校验" value="odd" />
                        <el-option label="偶校验" value="even" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          
            <!-- RS485配置 -->
            <div v-if="form.protocol === 'rs485'" class="protocol-config">
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="串口名称">
                    <el-input
                      v-model="rs485Config.serial_port"
                      placeholder="如: COM1 或 /dev/ttyUSB0"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备地址">
                    <el-input-number
                      v-model="rs485Config.device_addr"
                      :min="1"
                      :max="247"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="协议类型">
                    <el-select v-model="rs485Config.protocol" style="width: 100%">
                      <el-option label="自定义" value="custom" />
                      <el-option label="Modbus RTU" value="modbus-rtu" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="6">
                  <el-form-item label="波特率">
                    <el-select v-model="rs485Config.baud_rate" style="width: 100%">
                      <el-option label="9600" :value="9600" />
                      <el-option label="19200" :value="19200" />
                      <el-option label="38400" :value="38400" />
                      <el-option label="57600" :value="57600" />
                      <el-option label="115200" :value="115200" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="数据位">
                    <el-select v-model="rs485Config.data_bits" style="width: 100%">
                      <el-option label="7" :value="7" />
                      <el-option label="8" :value="8" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="停止位">
                    <el-select v-model="rs485Config.stop_bits" style="width: 100%">
                      <el-option label="1" :value="1" />
                      <el-option label="2" :value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="校验位">
                    <el-select v-model="rs485Config.parity" style="width: 100%">
                      <el-option label="无" value="none" />
                      <el-option label="奇校验" value="odd" />
                      <el-option label="偶校验" value="even" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="超时时间(秒)">
                    <el-input-number
                      v-model="rs485Config.timeout"
                      :min="1"
                      :max="60"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="缓冲区大小">
                    <el-input-number
                      v-model="rs485Config.buffer_size"
                      :min="256"
                      :max="8192"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部操作区域 -->
      <div class="form-footer">
        <div class="footer-actions">
          <el-button @click="goBack">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button type="info" @click="handleTest" :loading="testLoading">
            <el-icon><Connection /></el-icon>
            测试连接
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            <el-icon><Check /></el-icon>
            {{ isEdit ? '更新设备' : '创建设备' }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Monitor,
  Connection,
  Setting,
  Close,
  Check
} from '@element-plus/icons-vue'
import { LinkageDeviceAPI, type LinkageDevice } from '@/api/linkage'

defineOptions({
  name: "LinkageDeviceForm",
});

// Props定义
const props = defineProps<{
  deviceId?: string | null
  isEdit?: boolean
}>()

// Emits定义
const emit = defineEmits<{
  success: []
  cancel: []
}>()

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const testLoading = ref(false)
const formRef = ref()

const isEdit = computed(() => props.isEdit || !!route.params.id)

// 表单数据
const form = reactive<LinkageDevice>({
  name: '',
  type: '',
  protocol: '',
  address: '',
  port: 0,
  config: ''
})

// 协议配置
const mqttConfig = reactive({
  broker_url: '',
  client_id: '',
  username: '',
  password: '',
  qos: 1,
  keep_alive: 60,
  use_tls: false
})

const modbusConfig = reactive({
  slave_id: 1,
  timeout: 5,
  retry_count: 3,
  mode: 'tcp',
  serial_port: '',
  baud_rate: 9600,
  data_bits: 8,
  stop_bits: 1,
  parity: 'none'
})

const rs485Config = reactive({
  serial_port: '',
  device_addr: 1,
  protocol: 'custom',
  baud_rate: 9600,
  data_bits: 8,
  stop_bits: 1,
  parity: 'none',
  timeout: 3,
  buffer_size: 1024
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  protocol: [
    { required: true, message: '请选择协议类型', trigger: 'change' }
  ],
  address: [
    { required: true, message: '请输入设备地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 0, max: 65535, message: '端口号范围 0-65535', trigger: 'blur' }
  ]
}

// 方法
const loadFormData = async () => {
  if (!isEdit.value) return

  const deviceId = props.deviceId || route.params.id as string
  if (!deviceId) return

  loading.value = true
  try {
    const response = await LinkageDeviceAPI.getDetail(deviceId)
    console.log('设备详情API响应:', response) // 调试日志

    // 处理ECP API响应格式
    let data
    if (response && response.data) {
      // ECP API格式: {errorCode: 0, data: {...}, success: true}
      data = response.data
    } else {
      // 直接返回数据格式
      data = response
    }

    console.log('设备详情数据:', data) // 调试日志
    Object.assign(form, data)

    // 解析配置
    if (data.config) {
      const config = typeof data.config === 'string' ? JSON.parse(data.config) : data.config

      if (data.protocol === 'mqtt') {
        Object.assign(mqttConfig, config)
      } else if (data.protocol === 'modbus') {
        Object.assign(modbusConfig, config)
      } else if (data.protocol === 'rs485') {
        Object.assign(rs485Config, config)
      }
    }
  } catch (error) {
    console.error('加载设备数据失败:', error)
    ElMessage.error('加载设备数据失败')
  } finally {
    loading.value = false
  }
}

const handleProtocolChange = () => {
  // 重置端口号
  if (form.protocol === 'mqtt') {
    form.port = 1883
  } else if (form.protocol === 'modbus') {
    form.port = 502
  } else if (form.protocol === 'rs485') {
    form.port = 0
  }
}

const handleTest = async () => {
  try {
    await formRef.value.validate()

    testLoading.value = true

    // 构建配置
    buildConfig()

    // 如果是编辑模式，直接测试现有设备
    if (isEdit.value) {
      const testResponse = await LinkageDeviceAPI.test(route.params.id as string)
      console.log('设备测试API响应:', testResponse) // 调试日志

      // 处理测试结果
      let result
      if (testResponse && testResponse.data) {
        result = testResponse.data
      } else {
        result = testResponse
      }

      if (result && result.success) {
        ElMessage.success(`设备连接测试成功 - 响应时间: ${result.response_time || 0}ms`)
      } else {
        ElMessage.success('设备连接测试成功')
      }
    } else {
      // 创建临时设备进行测试
      const tempDevice = { ...form }
      const createResponse = await LinkageDeviceAPI.create(tempDevice)
      console.log('创建临时设备API响应:', createResponse) // 调试日志

      // 处理创建响应
      let createdDevice
      if (createResponse && createResponse.data) {
        createdDevice = createResponse.data
      } else {
        createdDevice = createResponse
      }

      try {
        const testResponse = await LinkageDeviceAPI.test(createdDevice.id)
        console.log('临时设备测试API响应:', testResponse) // 调试日志

        // 处理测试结果
        let result
        if (testResponse && testResponse.data) {
          result = testResponse.data
        } else {
          result = testResponse
        }

        if (result && result.success) {
          ElMessage.success(`设备连接测试成功 - 响应时间: ${result.response_time || 0}ms`)
        } else {
          ElMessage.success('设备连接测试成功')
        }
      } finally {
        // 删除临时设备
        await LinkageDeviceAPI.delete(createdDevice.id)
      }
    }
  } catch (error) {
    console.error('设备测试失败:', error)
    ElMessage.error('设备连接测试失败')
  } finally {
    testLoading.value = false
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    // 构建配置
    buildConfig()
    
    if (isEdit.value) {
      await LinkageDeviceAPI.update(route.params.id as string, form)
      ElMessage.success('设备更新成功')
    } else {
      await LinkageDeviceAPI.create(form)
      ElMessage.success('设备创建成功')
    }
    
    // 如果是在弹框中，发出success事件
    if (props.deviceId !== undefined) {
      emit('success')
    } else {
      goBack()
    }
  } catch (error) {
    console.error('保存设备失败:', error)
    ElMessage.error('保存设备失败')
  } finally {
    submitLoading.value = false
  }
}

const buildConfig = () => {
  let config = {}
  
  if (form.protocol === 'mqtt') {
    config = { ...mqttConfig }
  } else if (form.protocol === 'modbus') {
    config = { ...modbusConfig }
  } else if (form.protocol === 'rs485') {
    config = { ...rs485Config }
  }
  
  form.config = JSON.stringify(config)
}

const goBack = () => {
  // 如果是在弹框中，发出cancel事件
  if (props.deviceId !== undefined) {
    emit('cancel')
  } else {
    router.push('/linkage/devices')
  }
}

// 生命周期
onMounted(async () => {
  await loadFormData()
})
</script>

<style lang="scss" scoped>
.device-form-container {
  padding: 0;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.form-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.form-sidebar {
  flex: 1;
  background: #ffffff;
  padding: 24px;
  overflow-y: auto;
}

.form-footer {
  //background: #f1f5f9;
  padding: 20px;
  //border-top: 1px solid #e2e8f0;
}

.form-block {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.block-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;

  .block-icon {
    font-size: 18px;
    color: #3b82f6;
  }
}

.block-content {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.protocol-config {
  padding: 16px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  margin-top: 12px;
}

.footer-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

// 全局表单样式优化
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.2s ease;
  min-height: 32px;

  &:hover {
    border-color: #3b82f6;
  }
}

:deep(.el-select .el-input__wrapper) {
  min-height: 32px;

  &:hover {
    border-color: #3b82f6;
  }
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
  transition: all 0.2s ease;
  min-height: 32px;

  &:hover {
    border-color: #3b82f6;
  }
}

:deep(.el-input-number) {
  width: 100%;

  .el-input__wrapper {
    min-height: 32px;
  }
}

:deep(.el-switch) {
  &.is-checked .el-switch__core {
    background-color: #3b82f6;
  }
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  font-size: 13px;
  padding: 8px 16px;
  height: auto;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-1px);
  }
}

:deep(.el-button--primary) {
  background: #3b82f6;
  border-color: #3b82f6;
  
  &:hover {
    background: #2563eb;
    border-color: #2563eb;
  }
}

:deep(.el-button--info) {
  background: #6b7280;
  border-color: #6b7280;
  
  &:hover {
    background: #4b5563;
    border-color: #4b5563;
  }
}

:deep(.el-input__inner) {
  font-size: 13px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

@media (max-width: 640px) {
  .form-sidebar {
    padding: 16px;
  }
  
  .form-footer {
    padding: 16px;
  }
  
  .footer-actions {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
