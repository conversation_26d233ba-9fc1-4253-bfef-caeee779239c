<template>
  <div class="app-container">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon success">
            <svg-icon icon-class="trend-charts" />
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ statistics.total_executions || 0 }}</div>
            <div class="stats-label">总执行次数</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon primary">
            <svg-icon icon-class="success" />
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ statistics.success_rate || 0 }}%</div>
            <div class="stats-label">成功率</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon warning">
            <svg-icon icon-class="monitor" />
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ statistics.online_devices || 0 }}</div>
            <div class="stats-label">在线设备</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon info">
            <svg-icon icon-class="setting" />
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ statistics.active_rules || 0 }}</div>
            <div class="stats-label">活跃规则</div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 执行趋势图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>执行趋势</span>
              <el-button-group size="small">
                <el-button 
                  :type="trendPeriod === '24h' ? 'primary' : ''" 
                  @click="changeTrendPeriod('24h')"
                >
                  24小时
                </el-button>
                <el-button 
                  :type="trendPeriod === '7d' ? 'primary' : ''" 
                  @click="changeTrendPeriod('7d')"
                >
                  7天
                </el-button>
                <el-button 
                  :type="trendPeriod === '30d' ? 'primary' : ''" 
                  @click="changeTrendPeriod('30d')"
                >
                  30天
                </el-button>
              </el-button-group>
            </div>
          </template>
          <div class="chart-container" v-loading="chartLoading">
            <div ref="executionTrendChart" class="chart"></div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 设备状态分布 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>设备状态分布</span>
              <el-button size="small" @click="refreshDeviceStatus">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="chart-container" v-loading="chartLoading">
            <div ref="deviceStatusChart" class="chart"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 系统状态和最近执行 -->
    <el-row :gutter="20" class="bottom-row">
      <!-- 系统健康状态 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="status-card">
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
              <div class="status-indicator" :class="systemStatusClass">
                <span class="status-dot"></span>
                {{ systemStatusText }}
              </div>
            </div>
          </template>
          
          <div class="status-list">
            <div class="status-item">
              <span class="status-label">联动引擎</span>
              <el-tag :type="healthStatus.engine_status === 'running' ? 'success' : 'danger'" size="small">
                {{ healthStatus.engine_status === 'running' ? '运行中' : '已停止' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">队列长度</span>
              <span class="status-value">{{ healthStatus.queue_length || 0 }}</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">平均响应时间</span>
              <span class="status-value">{{ statistics.average_response_time || 0 }}ms</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">协议适配器</span>
              <div class="adapter-status">
                <el-tag 
                  v-for="(status, protocol) in healthStatus.protocol_adapters" 
                  :key="protocol"
                  :type="status === 'healthy' ? 'success' : 'warning'"
                  size="small"
                  class="adapter-tag"
                >
                  {{ protocol.toUpperCase() }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最近执行记录 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="16" :xl="16">
        <el-card class="execution-card">
          <template #header>
            <div class="card-header">
              <span>最近执行记录</span>
              <el-button size="small" @click="$router.push('/linkage/executions')">
                查看全部
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </template>
          
          <div class="execution-list" v-loading="executionLoading">
            <div 
              v-for="execution in recentExecutions" 
              :key="execution.id"
              class="execution-item"
            >
              <div class="execution-info">
                <div class="execution-device">{{ execution.device_name || execution.device_id }}</div>
                <div class="execution-action">{{ execution.action }}</div>
                <div class="execution-time">{{ formatTime(execution.executed_at) }}</div>
              </div>
              <div class="execution-status">
                <el-tag 
                  :type="execution.status === 'success' ? 'success' : 'danger'" 
                  size="small"
                >
                  {{ execution.status === 'success' ? '成功' : '失败' }}
                </el-tag>
                <span class="execution-duration">{{ execution.duration }}ms</span>
              </div>
            </div>
            
            <div v-if="recentExecutions.length === 0" class="empty-state">
              <el-empty description="暂无执行记录" :image-size="80" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { LinkageSystemAPI, LinkageExecutionAPI, type LinkageStatistics, type LinkageHealthStatus, type LinkageExecution } from '@/api/linkage'
import { linkageSSE, type StatisticsUpdateEvent, type ExecutionRecordEvent } from '@/utils/linkage-sse'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

defineOptions({
  name: "LinkageDashboard",
});

// 响应式数据
const chartLoading = ref(false)
const executionLoading = ref(false)
const trendPeriod = ref('24h')
const recentExecutions = ref<LinkageExecution[]>([])

// 统计数据
const statistics = ref<LinkageStatistics>({
  total_executions: 0,
  success_executions: 0,
  failed_executions: 0,
  success_rate: 0,
  average_response_time: 0,
  online_devices: 0,
  offline_devices: 0,
  active_rules: 0
})

// 健康状态
const healthStatus = ref<LinkageHealthStatus>({
  status: 'unknown',
  engine_status: 'unknown',
  queue_length: 0,
  active_rules: 0,
  protocol_adapters: {}
})

// 图表引用
const executionTrendChart = ref<HTMLDivElement>()
const deviceStatusChart = ref<HTMLDivElement>()
let trendChartInstance: echarts.ECharts | null = null
let statusChartInstance: echarts.ECharts | null = null

// 系统状态
const systemStatusClass = computed(() => {
  const status = healthStatus.value.status
  return {
    'status-healthy': status === 'healthy',
    'status-warning': status === 'warning',
    'status-error': status === 'error',
    'status-unknown': status === 'unknown'
  }
})

const systemStatusText = computed(() => {
  const statusMap = {
    'healthy': '正常',
    'warning': '警告',
    'error': '错误',
    'unknown': '未知'
  }
  return statusMap[healthStatus.value.status as keyof typeof statusMap] || '未知'
})

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// 方法
const loadStatistics = async () => {
  try {
    const response = await LinkageSystemAPI.getStatistics()
    console.log('统计数据API响应:', response) // 调试日志

    // 处理ECP API响应格式
    if (response && response.data) {
      // ECP API格式: {errorCode: 0, data: {...}, success: true}
      statistics.value = response.data
    } else {
      // 直接返回数据格式
      statistics.value = response
    }

    console.log('统计数据:', statistics.value) // 调试日志
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadHealthStatus = async () => {
  try {
    const response = await LinkageSystemAPI.getHealthStatus()
    console.log('健康状态API响应:', response) // 调试日志

    // 处理ECP API响应格式
    if (response && response.data) {
      // ECP API格式: {errorCode: 0, data: {...}, success: true}
      healthStatus.value = response.data
    } else {
      // 直接返回数据格式
      healthStatus.value = response
    }

    console.log('健康状态数据:', healthStatus.value) // 调试日志
  } catch (error) {
    console.error('加载健康状态失败:', error)
  }
}

const loadRecentExecutions = async () => {
  try {
    executionLoading.value = true
    const response = await LinkageExecutionAPI.getList({
      page: 1,
      size: 10,
      sort: 'executed_at',
      order: 'desc'
    })
    console.log('最近执行记录API响应:', response) // 调试日志

    // 处理ECP API响应格式
    if (response && response.data && Array.isArray(response.data)) {
      // ECP API格式: {errorCode: 0, data: [...], success: true}
      recentExecutions.value = response.data
    } else if (Array.isArray(response)) {
      // 直接返回数组格式
      recentExecutions.value = response
    } else if (response && response.items) {
      // 分页格式
      recentExecutions.value = response.items || []
    } else {
      recentExecutions.value = []
    }

    console.log('最近执行记录数据:', recentExecutions.value) // 调试日志
  } catch (error) {
    console.error('加载执行记录失败:', error)
    ElMessage.error('加载执行记录失败')
  } finally {
    executionLoading.value = false
  }
}

const loadTrendData = async () => {
  try {
    chartLoading.value = true
    const response = await LinkageExecutionAPI.getStats({
      period: trendPeriod.value
    })
    console.log('趋势数据API响应:', response) // 调试日志

    // 处理ECP API响应格式
    let data
    if (response && response.data) {
      // ECP API格式: {errorCode: 0, data: {...}, success: true}
      data = response.data
    } else {
      // 直接返回数据格式
      data = response
    }

    console.log('趋势数据:', data) // 调试日志
    updateTrendChart(data)
  } catch (error) {
    console.error('加载趋势数据失败:', error)
  } finally {
    chartLoading.value = false
  }
}

const updateTrendChart = (data: any) => {
  if (!trendChartInstance) return
  
  const option = {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['成功', '失败']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.labels || []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '成功',
        type: 'line',
        stack: 'Total',
        smooth: true,
        itemStyle: {
          color: '#67c23a'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(103, 194, 58, 0.3)'
            }, {
              offset: 1, color: 'rgba(103, 194, 58, 0.1)'
            }]
          }
        },
        data: data.success || []
      },
      {
        name: '失败',
        type: 'line',
        stack: 'Total',
        smooth: true,
        itemStyle: {
          color: '#f56c6c'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(245, 108, 108, 0.3)'
            }, {
              offset: 1, color: 'rgba(245, 108, 108, 0.1)'
            }]
          }
        },
        data: data.failed || []
      }
    ]
  }
  
  trendChartInstance.setOption(option)
}

const updateDeviceStatusChart = () => {
  if (!statusChartInstance) return
  
  const online = statistics.value.online_devices || 0
  const offline = statistics.value.offline_devices || 0
  const error = 0 // 暂时没有错误状态统计
  
  const option = {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '设备状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { 
            value: online, 
            name: '在线',
            itemStyle: { color: '#67c23a' }
          },
          { 
            value: offline, 
            name: '离线',
            itemStyle: { color: '#909399' }
          },
          { 
            value: error, 
            name: '错误',
            itemStyle: { color: '#f56c6c' }
          }
        ]
      }
    ]
  }
  
  statusChartInstance.setOption(option)
}

const initCharts = async () => {
  await nextTick()
  
  if (executionTrendChart.value) {
    trendChartInstance = echarts.init(executionTrendChart.value)
  }
  
  if (deviceStatusChart.value) {
    statusChartInstance = echarts.init(deviceStatusChart.value)
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    trendChartInstance?.resize()
    statusChartInstance?.resize()
  })
}

const changeTrendPeriod = (period: string) => {
  trendPeriod.value = period
  loadTrendData()
}

const refreshDeviceStatus = () => {
  loadStatistics().then(() => {
    updateDeviceStatusChart()
    ElMessage.success('设备状态已刷新')
  })
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm:ss')
}

// SSE实时数据处理
const handleStatisticsUpdate = (event: StatisticsUpdateEvent) => {
  console.log('收到统计数据更新:', event)

  // 更新统计数据
  statistics.value = {
    total_executions: event.total_executions,
    success_executions: event.success_executions,
    failed_executions: event.failed_executions,
    success_rate: event.success_rate,
    online_devices: event.online_devices,
    offline_devices: event.offline_devices,
    active_rules: event.active_rules
  }

  // 更新图表
  updateDeviceStatusChart()
  updateTrendChart()
}

const handleExecutionRecord = (event: ExecutionRecordEvent) => {
  console.log('收到执行记录:', event)

  // 添加到最近执行记录列表
  const newExecution: LinkageExecution = {
    id: event.id,
    rule_id: event.rule_id,
    device_id: event.device_id,
    action: event.action,
    status: event.status,
    result: event.result,
    executed_at: event.executed_at,
    duration: event.duration
  }

  // 添加到列表开头，保持最多10条记录
  recentExecutions.value.unshift(newExecution)
  if (recentExecutions.value.length > 10) {
    recentExecutions.value = recentExecutions.value.slice(0, 10)
  }
}

const startRealTimeUpdates = () => {
  // 连接统计数据流
  linkageSSE.connectStatistics((data: any) => {
    if (data.type === 'statistics_update') {
      handleStatisticsUpdate(data.payload)
    }
  })

  // 连接执行记录流
  linkageSSE.connectExecutionRecords((data: any) => {
    if (data.type === 'execution_record') {
      handleExecutionRecord(data.payload)
    }
  })

  console.log('SSE实时数据连接已启动')
}

const stopRealTimeUpdates = () => {
  linkageSSE.disconnectAll()
  console.log('SSE实时数据连接已停止')
}

// 保留定时刷新作为备用方案
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    loadStatistics()
    loadHealthStatus()
    loadRecentExecutions()
    updateDeviceStatusChart()
  }, 60000) // 降低到60秒刷新一次，作为备用
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(async () => {
  await initCharts()

  // 初始加载数据
  await Promise.all([
    loadStatistics(),
    loadHealthStatus(),
    loadRecentExecutions(),
    loadTrendData()
  ])

  updateDeviceStatusChart()

  // 启动实时数据更新
  startRealTimeUpdates()

  // 启动定时刷新作为备用
  startAutoRefresh()
})

onUnmounted(() => {
  // 停止实时数据更新
  stopRealTimeUpdates()

  // 停止定时刷新
  stopAutoRefresh()

  // 清理图表
  trendChartInstance?.dispose()
  statusChartInstance?.dispose()
  window.removeEventListener('resize', () => {})
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  .stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    
    :deep(.svg-icon) {
      font-size: 24px;
      color: white;
    }
    
    &.success {
      background: linear-gradient(135deg, #67c23a, #85ce61);
    }
    
    &.primary {
      background: linear-gradient(135deg, #409eff, #66b1ff);
    }
    
    &.warning {
      background: linear-gradient(135deg, #e6a23c, #ebb563);
    }
    
    &.info {
      background: linear-gradient(135deg, #909399, #a6a9ad);
    }
  }
  
  .stats-content {
    flex: 1;
    
    .stats-number {
      font-size: 28px;
      font-weight: bold;
      color: #303133;
      line-height: 1;
      margin-bottom: 8px;
    }
    
    .stats-label {
      font-size: 14px;
      color: #909399;
    }
  }
}

.charts-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
  
  .chart-container {
    height: 320px;
    
    .chart {
      width: 100%;
      height: 100%;
    }
  }
}

.bottom-row {
  .status-card,
  .execution-card {
    height: 400px;
  }
}

.status-list {
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .status-label {
      font-size: 14px;
      color: #606266;
    }
    
    .status-value {
      font-size: 14px;
      color: #303133;
      font-weight: 500;
    }
    
    .adapter-status {
      display: flex;
      gap: 4px;
      
      .adapter-tag {
        font-size: 12px;
      }
    }
  }
}

.status-indicator {
  display: flex;
  align-items: center;
  font-size: 14px;
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
  }
  
  &.status-healthy {
    color: #67c23a;
    
    .status-dot {
      background-color: #67c23a;
    }
  }
  
  &.status-warning {
    color: #e6a23c;
    
    .status-dot {
      background-color: #e6a23c;
    }
  }
  
  &.status-error {
    color: #f56c6c;
    
    .status-dot {
      background-color: #f56c6c;
    }
  }
  
  &.status-unknown {
    color: #909399;
    
    .status-dot {
      background-color: #909399;
    }
  }
}

.execution-list {
  max-height: 320px;
  overflow-y: auto;
  
  .execution-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .execution-info {
      flex: 1;
      
      .execution-device {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .execution-action {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }
      
      .execution-time {
        font-size: 12px;
        color: #c0c4cc;
      }
    }
    
    .execution-status {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
      
      .execution-duration {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 响应式
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .stats-card {
    padding: 16px;
    
    .stats-icon {
      width: 50px;
      height: 50px;
      margin-right: 12px;
    }
    
    .stats-content {
      .stats-number {
        font-size: 24px;
      }
      
      .stats-label {
        font-size: 12px;
      }
    }
  }
  
  .chart-card {
    height: 300px;
    
    .chart-container {
      height: 220px;
    }
  }
  
  .status-card,
  .execution-card {
    height: auto;
    margin-bottom: 20px;
  }
}
</style>
