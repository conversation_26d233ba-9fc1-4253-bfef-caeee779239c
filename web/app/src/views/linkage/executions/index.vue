<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="规则ID">
          <el-input
            v-model="queryParams.rule_id"
            placeholder="请输入规则ID"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="设备ID">
          <el-input
            v-model="queryParams.device_id"
            placeholder="请输入设备ID"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="执行状态">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="执行中" value="running" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stats-card success">
          <div class="stats-content">
            <div class="stats-number">{{ stats.success_count || 0 }}</div>
            <div class="stats-label">成功执行</div>
          </div>
          <div class="stats-icon">
            <svg-icon icon-class="success" />
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stats-card danger">
          <div class="stats-content">
            <div class="stats-number">{{ stats.failed_count || 0 }}</div>
            <div class="stats-label">执行失败</div>
          </div>
          <div class="stats-icon">
            <svg-icon icon-class="error" />
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stats-card warning">
          <div class="stats-content">
            <div class="stats-number">{{ stats.avg_duration || 0 }}ms</div>
            <div class="stats-label">平均耗时</div>
          </div>
          <div class="stats-icon">
            <svg-icon icon-class="time" />
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stats-card info">
          <div class="stats-content">
            <div class="stats-number">{{ stats.success_rate || 0 }}%</div>
            <div class="stats-label">成功率</div>
          </div>
          <div class="stats-icon">
            <svg-icon icon-class="percentage" />
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="danger" @click="handleCleanup">
        <el-icon><Delete /></el-icon>
        清理记录
      </el-button>
      <el-button type="success" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出记录
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="executionList"
      @sort-change="handleSortChange"
      stripe
      style="width: 100%;"
    >
      <el-table-column
        prop="id"
        label="执行ID"
        width="100"
        align="center"
      />
      <el-table-column
        prop="rule_name"
        label="规则名称"
        min-width="100"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.rule_name || row.rule_id }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="device_name"
        label="目标设备"
        min-width="100"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.device_name || row.device_id }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="action"
        label="执行动作"
        width="100"
        align="center"
      />
      <el-table-column
        prop="status"
        label="执行状态"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="duration"
        label="执行耗时"
        width="150"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          <span :class="getDurationClass(row.duration)">
            {{ row.duration }}ms
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="executed_at"
        label="执行时间"
        width="160"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatTime(row.executed_at) }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="150"
        align="center"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-button
            v-if="row.status === 'failed'"
            type="warning"
            size="small"
            @click="handleRetry(row)"
            :loading="row.retryLoading"
          >
            重试
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="执行详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="detailDialog.data" class="execution-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="执行ID">
            {{ detailDialog.data.id }}
          </el-descriptions-item>
          <el-descriptions-item label="规则ID">
            {{ detailDialog.data.rule_id }}
          </el-descriptions-item>
          <el-descriptions-item label="设备ID">
            {{ detailDialog.data.device_id }}
          </el-descriptions-item>
          <el-descriptions-item label="执行动作">
            {{ detailDialog.data.action }}
          </el-descriptions-item>
          <el-descriptions-item label="执行状态">
            <el-tag :type="getStatusType(detailDialog.data.status)">
              {{ getStatusText(detailDialog.data.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行耗时">
            {{ detailDialog.data.duration }}ms
          </el-descriptions-item>
          <el-descriptions-item label="执行时间" :span="2">
            {{ formatTime(detailDialog.data.executed_at) }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="detailDialog.data.params" class="params-section">
          <h4>执行参数</h4>
          <el-input
            :model-value="formatJSON(detailDialog.data.params)"
            type="textarea"
            :rows="6"
            readonly
          />
        </div>
        
        <div v-if="detailDialog.data.error_message" class="error-section">
          <h4>错误信息</h4>
          <el-alert
            :title="detailDialog.data.error_message"
            type="error"
            :closable="false"
            show-icon
          />
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialog.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 清理对话框 -->
    <el-dialog
      v-model="cleanupDialog.visible"
      title="清理执行记录"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="cleanupDialog.form" label-width="120px">
        <el-form-item label="保留天数">
          <el-input-number
            v-model="cleanupDialog.form.days"
            :min="1"
            :max="365"
            placeholder="保留最近N天的记录"
          />
          <div class="form-tip">
            将删除 {{ cleanupDialog.form.days }} 天前的执行记录
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cleanupDialog.visible = false">取消</el-button>
          <el-button 
            type="danger" 
            @click="executeCleanup" 
            :loading="cleanupDialog.loading"
          >
            确认清理
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { LinkageExecutionAPI, type LinkageExecution, type LinkageExecutionQuery } from '@/api/linkage'
import { linkageSSE, type ExecutionRecordEvent } from '@/utils/linkage-sse'
import dayjs from 'dayjs'

defineOptions({
  name: "LinkageExecutions",
});

// 响应式数据
const loading = ref(false)
const executionList = ref<LinkageExecution[]>([])
const total = ref(0)
const dateRange = ref<[string, string]>()

// 统计数据
const stats = ref({
  success_count: 0,
  failed_count: 0,
  avg_duration: 0,
  success_rate: 0
})

// 查询参数
const queryParams = reactive<LinkageExecutionQuery>({
  page: 1,
  size: 10,
  rule_id: '',
  device_id: '',
  status: '',
  start_time: '',
  end_time: '',
  sort: 'executed_at',
  order: 'desc'
})

// 详情对话框
const detailDialog = reactive({
  visible: false,
  data: null as LinkageExecution | null
})

// 清理对话框
const cleanupDialog = reactive({
  visible: false,
  loading: false,
  form: {
    days: 30
  }
})

// 方法
const getList = async () => {
  loading.value = true
  try {
    const response = await LinkageExecutionAPI.getList(queryParams)
    console.log('API响应数据:', response) // 调试日志

    // 处理边缘计算平台API的响应格式
    if (response && response.data && Array.isArray(response.data)) {
      // ECP API格式: {errorCode: 0, data: [...], success: true}
      executionList.value = response.data
      total.value = response.data.length
    } else if (Array.isArray(response)) {
      // 如果直接返回数组
      executionList.value = response
      total.value = response.length
    } else if (response && response.items) {
      // 如果返回分页格式
      executionList.value = response.items || []
      total.value = response.total || 0
    } else {
      // 兼容其他格式
      console.warn('未识别的响应格式:', response)
      executionList.value = []
      total.value = 0
    }

    console.log('执行记录数据:', executionList.value) // 调试日志
  } catch (error) {
    console.error('获取执行记录失败:', error)
    ElMessage.error('获取执行记录失败')
  } finally {
    loading.value = false
  }
}

const getStats = async () => {
  try {
    const params: any = {}
    if (queryParams.start_time) params.start_time = queryParams.start_time
    if (queryParams.end_time) params.end_time = queryParams.end_time
    
    const data = await LinkageExecutionAPI.getStats(params)
    stats.value = data
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const handleQuery = () => {
  queryParams.page = 1
  
  // 处理时间范围
  if (dateRange.value) {
    queryParams.start_time = dateRange.value[0]
    queryParams.end_time = dateRange.value[1]
  } else {
    queryParams.start_time = ''
    queryParams.end_time = ''
  }
  
  getList()
  getStats()
}

const resetQuery = () => {
  queryParams.page = 1
  queryParams.rule_id = ''
  queryParams.device_id = ''
  queryParams.status = ''
  queryParams.start_time = ''
  queryParams.end_time = ''
  dateRange.value = undefined
  
  getList()
  getStats()
}

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  queryParams.sort = prop
  queryParams.order = order === 'ascending' ? 'asc' : 'desc'
  getList()
}

const handleDetail = async (row: LinkageExecution) => {
  try {
    const response = await LinkageExecutionAPI.getDetail(row.id!)
    console.log('执行详情API响应:', response) // 调试日志

    // 处理ECP API响应格式
    let data
    if (response && response.data) {
      // ECP API格式: {errorCode: 0, data: {...}, success: true}
      data = response.data
    } else {
      // 直接返回数据格式
      data = response
    }

    console.log('执行详情数据:', data) // 调试日志
    detailDialog.data = data
    detailDialog.visible = true
  } catch (error) {
    console.error('获取执行详情失败:', error)
    ElMessage.error('获取执行详情失败')
  }
}

const handleRetry = async (row: LinkageExecution & { retryLoading?: boolean }) => {
  try {
    await ElMessageBox.confirm(
      '确定要重新执行这个联动动作吗？',
      '确认重试',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    row.retryLoading = true
    await LinkageExecutionAPI.retry(row.id!)
    ElMessage.success('重试执行成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重试执行失败:', error)
      ElMessage.error('重试执行失败')
    }
  } finally {
    row.retryLoading = false
  }
}

const handleCleanup = () => {
  cleanupDialog.visible = true
}

const executeCleanup = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要清理 ${cleanupDialog.form.days} 天前的执行记录吗？此操作不可恢复！`,
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    cleanupDialog.loading = true
    // 这里应该调用清理API
    // await LinkageSystemAPI.cleanupExecutions(cleanupDialog.form.days)
    ElMessage.success('执行记录清理成功')
    cleanupDialog.visible = false
    getList()
    getStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理执行记录失败:', error)
      ElMessage.error('清理执行记录失败')
    }
  } finally {
    cleanupDialog.loading = false
  }
}

const handleExport = () => {
  // 导出功能实现
  ElMessage.info('导出功能开发中...')
}

const getStatusType = (status: string): 'success' | 'danger' | 'warning' | 'info' => {
  const typeMap: Record<string, 'success' | 'danger' | 'warning' | 'info'> = {
    success: 'success',
    failed: 'danger',
    running: 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    success: '成功',
    failed: '失败',
    running: '执行中'
  }
  return textMap[status] || '未知'
}

const getDurationClass = (duration: number) => {
  if (duration > 5000) return 'duration-slow'
  if (duration > 2000) return 'duration-normal'
  return 'duration-fast'
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const formatJSON = (jsonStr: string) => {
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2)
  } catch {
    return jsonStr
  }
}

// 执行记录实时更新
const handleExecutionRecord = (event: ExecutionRecordEvent) => {
  console.log('收到新的执行记录:', event)

  // 将新记录添加到列表开头
  const newExecution: LinkageExecution = {
    id: parseInt(event.id), // 转换为数字类型
    rule_id: event.rule_id,
    device_id: event.device_id,
    action: event.action,
    status: event.status,
    duration: event.duration,
    executed_at: event.executed_at,
    error_message: event.status === 'failed' ? event.result : undefined
  }

  executionList.value.unshift(newExecution)

  // 保持列表长度不超过当前页面大小
  if (executionList.value.length > (queryParams.size || 20)) {
    executionList.value = executionList.value.slice(0, queryParams.size || 20)
  }

  // 更新总数
  total.value += 1

  // 刷新统计数据
  getStats()
}

const startRealTimeUpdates = () => {
  // 连接执行记录流
  linkageSSE.connectExecutionRecords((data: any) => {
    if (data.type === 'execution_record') {
      handleExecutionRecord(data.payload)
    }
  })

  console.log('执行记录实时更新已启动')
}

const stopRealTimeUpdates = () => {
  linkageSSE.disconnect('executions')
  console.log('执行记录实时更新已停止')
}

// 生命周期
onMounted(() => {
  getList()
  getStats()
  startRealTimeUpdates()
})

onUnmounted(() => {
  stopRealTimeUpdates()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .stats-content {
    .stats-number {
      font-size: 24px;
      font-weight: bold;
      line-height: 1;
      margin-bottom: 8px;
    }
    
    .stats-label {
      font-size: 14px;
      color: #909399;
    }
  }
  
  .stats-icon {
    font-size: 32px;
    opacity: 0.8;
  }
  
  &.success {
    .stats-number { color: #67c23a; }
    .stats-icon { color: #67c23a; }
  }
  
  &.danger {
    .stats-number { color: #f56c6c; }
    .stats-icon { color: #f56c6c; }
  }
  
  &.warning {
    .stats-number { color: #e6a23c; }
    .stats-icon { color: #e6a23c; }
  }
  
  &.info {
    .stats-number { color: #409eff; }
    .stats-icon { color: #409eff; }
  }
}

.toolbar {
  margin-bottom: 20px;
}

.duration-fast {
  color: #67c23a;
}

.duration-normal {
  color: #e6a23c;
}

.duration-slow {
  color: #f56c6c;
}

.execution-detail {
  .params-section,
  .error-section {
    margin-top: 20px;
    
    h4 {
      margin-bottom: 10px;
      color: #303133;
    }
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 500;
    }
  }
}
</style>
