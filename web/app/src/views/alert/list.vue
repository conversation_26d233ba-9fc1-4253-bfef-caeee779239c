<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-card class="mb-4">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="视频源名称" prop="videoName">
          <el-input
            v-model="queryParams.videoName"
            placeholder="请输入视频源名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="危险等级" prop="level">
          <el-select
            v-model="queryParams.level"
            placeholder="请选择危险等级"
            clearable
          >
            <el-option label="信息" value="info" />
            <el-option label="警告" value="warning" />
            <el-option label="错误" value="error" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择处理状态"
            clearable
          >
            <el-option label="新告警" value="new" />
            <el-option label="已处理" value="processed" />
            <el-option label="已忽略" value="ignored" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon class="mr-1"><i-ep-search /></el-icon>查询
          </el-button>
          <el-button @click="resetQuery">
            <el-icon class="mr-1"><i-ep-refresh /></el-icon>重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 告警列表 -->

    <el-table v-loading="loading" :data="alertList" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="video_name" label="视频源名称" min-width="150" />
      <el-table-column prop="source" label="告警来源" min-width="150" />
      <el-table-column prop="level" label="危险等级" width="100">
        <template #default="scope">
          <el-tag :type="getLevelTagType(scope.row.level)">
            {{ getLevelText(scope.row.level) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="告警类型" min-width="120" />
      <el-table-column prop="algorithm_name" label="算法名称" min-width="150" />
      <el-table-column prop="content" label="告警内容" min-width="200" show-overflow-tooltip />
      <el-table-column prop="status" label="处理状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="检测时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <div class="flex space-x-2">
            <el-tooltip content="查看详情" placement="top">
              <el-button type="primary" circle @click="handleView(scope.row)">
                <el-icon><i-ep-view /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip v-if="scope.row.status === 'new'" content="标记已处理" placement="top">
              <el-button type="success" circle @click="handleProcess(scope.row)">
                <el-icon><i-ep-select /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button type="danger" circle @click="handleDelete(scope.row)">
                <el-icon><i-ep-delete /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 告警详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="告警详情"
      width="800px"
      destroy-on-close
    >
      <div v-if="currentAlert">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="告警ID">
            {{ currentAlert.id }}
          </el-descriptions-item>
          <el-descriptions-item label="视频源名称">
            {{ currentAlert.video_name }}
          </el-descriptions-item>
          <el-descriptions-item label="告警来源">
            {{ currentAlert.source }}
          </el-descriptions-item>
          <el-descriptions-item label="摄像头IP">
            {{ currentAlert.ip }}
          </el-descriptions-item>
          <el-descriptions-item label="危险等级">
            <el-tag :type="getLevelTagType(currentAlert.level)">
              {{ getLevelText(currentAlert.level) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="告警类型">
            {{ currentAlert.type }}
          </el-descriptions-item>
          <el-descriptions-item label="算法名称">
            {{ currentAlert.algorithm_name }}
          </el-descriptions-item>
          <el-descriptions-item label="处理状态">
            <el-tag :type="getStatusTagType(currentAlert.status)">
              {{ getStatusText(currentAlert.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="告警内容" :span="2">
            {{ currentAlert.content }}
          </el-descriptions-item>
          <el-descriptions-item label="检测时间" :span="2">
            {{ formatDateTime(currentAlert.created_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 图片展示 -->
        <div v-if="currentAlert.original_image || currentAlert.image_path" class="image-container">
          <h4>相关图片</h4>
          <div class="image-grid">
            <div v-if="currentAlert.original_image" class="image-item">
              <h5>原图</h5>
              <el-image
                :src="currentAlert.original_image"
                fit="contain"
                style="width: 300px; height: 200px"
                :preview-src-list="[currentAlert.original_image]"
              />
            </div>
            <div v-if="currentAlert.image_path" class="image-item">
              <h5>告警图</h5>
              <el-image
                :src="currentAlert.image_path"
                fit="contain"
                style="width: 300px; height: 200px"
                :preview-src-list="[currentAlert.image_path]"
              />
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            v-if="currentAlert && currentAlert.status === 'new'"
            type="primary"
            @click="handleProcessInDialog"
          >
            标记为已处理
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import AlertAPI, { type Alert, type AlertQueryParams } from '@/api/alert';

defineOptions({
  name: 'AlertList',
  inheritAttrs: false,
});

// 响应式数据
const loading = ref(false);
const alertList = ref<Alert[]>([]);
const total = ref(0);
const detailDialogVisible = ref(false);
const currentAlert = ref<Alert | null>(null);

// 查询参数
const queryParams = reactive<AlertQueryParams>({
  page: 1,
  limit: 20,
  videoName: '',
  level: '',
  status: '',
});

// 查询表单引用
const queryFormRef = ref();

/**
 * 查询告警列表
 */
const getAlertList = async () => {
  loading.value = true;
  try {
    const { data } = await AlertAPI.getAlerts(queryParams);
    alertList.value = data.list || [];
    total.value = data.paging?.total || 0;
  } catch (error) {
    console.error('获取告警列表失败:', error);
    ElMessage.error('获取告警列表失败');
  } finally {
    loading.value = false;
  }
};

/**
 * 搜索
 */
const handleQuery = () => {
  queryParams.page = 1;
  getAlertList();
};

/**
 * 重置查询
 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.page = 1;
  getAlertList();
};

/**
 * 分页大小改变
 */
const handleSizeChange = (size: number) => {
  queryParams.limit = size;
  queryParams.page = 1;
  getAlertList();
};

/**
 * 当前页改变
 */
const handleCurrentChange = (page: number) => {
  queryParams.page = page;
  getAlertList();
};

/**
 * 查看告警详情
 */
const handleView = async (row: Alert) => {
  try {
    const { data } = await AlertAPI.getAlert(row.id);
    currentAlert.value = data;
    detailDialogVisible.value = true;
  } catch (error) {
    console.error('获取告警详情失败:', error);
    ElMessage.error('获取告警详情失败');
  }
};

/**
 * 处理告警
 */
const handleProcess = async (row: Alert) => {
  try {
    await ElMessageBox.confirm('确认将此告警标记为已处理？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    await AlertAPI.updateAlertStatus(row.id, 'processed');
    ElMessage.success('处理成功');
    getAlertList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('处理告警失败:', error);
      ElMessage.error('处理告警失败');
    }
  }
};

/**
 * 在详情对话框中处理告警
 */
const handleProcessInDialog = async () => {
  if (!currentAlert.value) return;

  try {
    await AlertAPI.updateAlertStatus(currentAlert.value.id, 'processed');
    ElMessage.success('处理成功');
    currentAlert.value.status = 'processed';
    detailDialogVisible.value = false;
    getAlertList();
  } catch (error) {
    console.error('处理告警失败:', error);
    ElMessage.error('处理告警失败');
  }
};

/**
 * 删除告警
 */
const handleDelete = async (row: Alert) => {
  try {
    await ElMessageBox.confirm('确认删除此告警记录？删除后无法恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    await AlertAPI.deleteAlert(row.id);
    ElMessage.success('删除成功');
    getAlertList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除告警失败:', error);
      ElMessage.error('删除告警失败');
    }
  }
};

/**
 * 获取危险等级标签类型
 */
const getLevelTagType = (level: string) => {
  switch (level) {
    case 'info':
      return 'info';
    case 'warning':
      return 'warning';
    case 'error':
      return 'danger';
    default:
      return 'info';
  }
};

/**
 * 获取危险等级文本
 */
const getLevelText = (level: string) => {
  switch (level) {
    case 'info':
      return '信息';
    case 'warning':
      return '警告';
    case 'error':
      return '错误';
    default:
      return level;
  }
};

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'new':
      return 'danger';
    case 'processed':
      return 'success';
    case 'ignored':
      return 'info';
    default:
      return 'info';
  }
};

/**
 * 获取状态文本
 */
const getStatusText = (status: string) => {
  switch (status) {
    case 'new':
      return '新告警';
    case 'processed':
      return '已处理';
    case 'ignored':
      return '已忽略';
    default:
      return status;
  }
};

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 组件挂载时获取数据
onMounted(() => {
  getAlertList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.image-container {
  margin-top: 20px;
}

.image-grid {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.image-item {
  text-align: center;
}

.image-item h5 {
  margin: 0 0 10px 0;
  color: #606266;
}
</style>
