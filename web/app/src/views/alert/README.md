# 告警记录模块

这个目录包含告警记录管理相关的前端页面组件。

## 功能特性

### 告警列表页面 (list.vue)
- **查询功能**：支持按视频源名称、危险等级、处理状态进行筛选
- **分页显示**：支持分页查询，可自定义每页显示数量
- **状态管理**：支持告警状态更新（新告警、已处理、已忽略）
- **详情查看**：支持查看告警详细信息和相关图片
- **批量操作**：支持删除告警记录

## 页面结构

```
alert/
├── README.md          # 模块说明文档
└── list.vue          # 告警列表页面
```

## 数据结构

### 告警记录 (Alert)
```typescript
interface Alert {
  id: number;              // 告警记录ID
  source: string;          // 告警来源
  ip: string;              // 摄像头IP
  level: string;           // 危险等级 (info/warning/error)
  type: string;            // 告警类型
  video_id: number;        // 视频源ID
  video_name: string;      // 视频源名称
  algorithm_id: number;    // 算法ID
  algorithm_name: string;  // 算法名称
  content: string;         // 告警内容
  original_image: string;  // 原图地址
  image_path: string;      // 告警图地址
  status: string;          // 处理状态 (new/processed/ignored)
  created_at: string;      // 创建时间
  updated_at: string;      // 更新时间
}
```

### 查询参数 (AlertQueryParams)
```typescript
interface AlertQueryParams {
  page?: number;           // 页码
  limit?: number;          // 每页数量
  videoId?: number;        // 视频源ID
  algorithmId?: number;    // 算法ID
  videoName?: string;      // 视频源名称
  level?: string;          // 危险等级
  type?: string;           // 告警类型
  status?: string;         // 处理状态
  startTime?: string;      // 开始时间
  endTime?: string;        // 结束时间
}
```

## API 接口

### AlertAPI 类方法
- `getAlerts(params)` - 查询告警记录列表
- `getAlert(id)` - 获取单个告警记录详情
- `updateAlertStatus(id, status)` - 更新告警状态
- `deleteAlert(id)` - 删除告警记录
- `uploadAlertImages(id, formData)` - 上传告警图片

## 使用说明

### 1. 查询告警记录
- 在搜索表单中输入查询条件
- 支持按视频源名称模糊查询
- 支持按危险等级和处理状态精确查询
- 点击"搜索"按钮执行查询

### 2. 查看告警详情
- 点击表格中的"查看"按钮
- 弹出详情对话框显示完整信息
- 支持查看原图和告警图

### 3. 处理告警
- 对于新告警，可点击"处理"按钮标记为已处理
- 在详情对话框中也可以进行状态更新
- 状态更新后会自动刷新列表

### 4. 删除告警
- 点击"删除"按钮可删除告警记录
- 删除前会弹出确认对话框
- 删除操作不可恢复，请谨慎操作

## 样式说明

### 危险等级标签颜色
- **信息 (info)**: 灰色标签
- **警告 (warning)**: 橙色标签  
- **错误 (error)**: 红色标签

### 处理状态标签颜色
- **新告警 (new)**: 红色标签
- **已处理 (processed)**: 绿色标签
- **已忽略 (ignored)**: 灰色标签

## 注意事项

1. **权限控制**: 页面需要用户登录后才能访问
2. **数据刷新**: 执行操作后会自动刷新列表数据
3. **图片显示**: 图片路径需要是可访问的URL地址
4. **分页限制**: 每页最多显示100条记录
5. **时间格式**: 时间显示使用本地化格式

## 扩展功能

### 可扩展的功能点
- 批量状态更新
- 告警统计图表
- 导出告警报表
- 告警规则配置
- 实时告警推送
- 告警图片标注编辑

### 性能优化建议
- 使用虚拟滚动处理大量数据
- 图片懒加载优化加载速度
- 添加缓存机制减少API调用
- 使用防抖优化搜索功能
