<template>
  <div class="app-container">
    <!-- 操作栏 -->
    <div class="filter-container">
      <div class="filter-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><i-ep-plus /></el-icon>
          添加路由
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><i-ep-refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 路由状态概览 -->
    <el-row :gutter="20" class="status-section">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>路由表概览</span>
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="status-item">
                <h4>总路由数</h4>
                <span class="status-value">{{ routeList.length }} 条</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="status-item">
                <h4>默认路由</h4>
                <span class="status-value">{{ defaultRoutes.length }} 条</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="status-item">
                <h4>静态路由</h4>
                <span class="status-value">{{ staticRoutes.length }} 条</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="status-item">
                <h4>活跃接口</h4>
                <span class="status-value">{{ activeInterfaces.length }} 个</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 路由表 -->
    <el-row :gutter="20" class="routes-section">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>路由表</span>
          </template>
          
          <el-table 
            :data="routeList" 
            v-loading="loading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="destination" label="目标网络" width="160">
              <template #default="{ row }">
                <el-tag :type="row.type === 'default' ? 'success' : 'info'" size="small">
                  {{ row.destination }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="gateway" label="网关" width="140" />
            
            <el-table-column prop="interface" label="接口" width="120">
              <template #default="{ row }">
                <el-tag type="primary" size="small">
                  {{ row.interface }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="metric" label="优先级" width="100">
              <template #default="{ row }">
                <el-tag :type="getMetricColor(row.metric)" size="small">
                  {{ row.metric || 0 }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="row.type === 'default' ? 'success' : 'warning'" size="small">
                  {{ row.type === 'default' ? '默认' : '静态' }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="handleEdit(row)"
                >
                  编辑
                </el-button>
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="handleDelete(row)"
                  :disabled="row.type === 'default'"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加/编辑路由对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑路由' : '添加路由'"
      width="600px"
    >
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules" 
        label-width="100px"
      >
        <el-form-item label="目标网络" prop="destination">
          <el-input 
            v-model="formData.destination" 
            placeholder="请输入目标网络，如：***********/24 或 default"
          />
          <div class="form-tip">
            输入 "default" 表示默认路由，或输入具体网段如 "***********/24"
          </div>
        </el-form-item>
        
        <el-form-item label="网关" prop="gateway">
          <el-input 
            v-model="formData.gateway" 
            placeholder="请输入网关IP地址"
          />
        </el-form-item>
        
        <el-form-item label="接口" prop="interface">
          <el-select v-model="formData.interface" placeholder="请选择网络接口">
            <el-option 
              v-for="iface in availableInterfaces" 
              :key="iface.name" 
              :label="iface.name" 
              :value="iface.name"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级" prop="metric">
          <el-input-number 
            v-model="formData.metric" 
            :min="0" 
            :max="9999"
            placeholder="路由优先级（数值越小优先级越高）"
          />
          <div class="form-tip">
            数值越小优先级越高，0为最高优先级
          </div>
        </el-form-item>
        
        <el-form-item label="路由类型" prop="type">
          <el-radio-group v-model="formData.type">
            <el-radio label="static">静态路由</el-radio>
            <el-radio label="default">默认路由</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import NetworkAPI from '@/api/network'

// 响应式数据
const loading = ref(false)
const routeList = ref<any[]>([])
const availableInterfaces = ref<any[]>([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  destination: '',
  gateway: '',
  interface: '',
  metric: 0,
  type: 'static'
})

// 计算属性
const defaultRoutes = computed(() => 
  routeList.value.filter((route: any) => route.type === 'default')
)

const staticRoutes = computed(() => 
  routeList.value.filter((route: any) => route.type === 'static')
)

const activeInterfaces = computed(() => {
  const interfaces = new Set()
  routeList.value.forEach((route: any) => {
    if (route.interface) {
      interfaces.add(route.interface)
    }
  })
  return Array.from(interfaces)
})

// 表单验证规则
const formRules = {
  destination: [
    { required: true, message: '请输入目标网络', trigger: 'blur' }
  ],
  gateway: [
    { required: true, message: '请输入网关地址', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: 'IP地址格式不正确', trigger: 'blur' }
  ],
  interface: [
    { required: true, message: '请选择网络接口', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择路由类型', trigger: 'change' }
  ]
}

// 生命周期
onMounted(() => {
  fetchRouteList()
  fetchInterfaces()
})

// 获取路由列表
const fetchRouteList = async () => {
  loading.value = true
  try {
    const response = await NetworkAPI.getRoutes() as any
    if (response.success) {
      routeList.value = response.data || []
    } else {
      ElMessage.error(response.message || '获取路由列表失败')
    }
  } catch (error) {
    console.error('获取路由列表失败:', error)
    ElMessage.error('获取路由列表失败')
  } finally {
    loading.value = false
  }
}

// 获取网络接口列表
const fetchInterfaces = async () => {
  try {
    const response = await NetworkAPI.getInterfaces() as any
    if (response.success) {
      availableInterfaces.value = response.data || []
    }
  } catch (error) {
    console.error('获取接口列表失败:', error)
  }
}

// 获取优先级颜色
const getMetricColor = (metric: number) => {
  if (metric === 0) return 'success'
  if (metric <= 100) return 'primary'
  if (metric <= 500) return 'warning'
  return 'danger'
}

// 处理添加
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row: any) => {
  isEdit.value = true
  Object.assign(formData, {
    destination: row.destination,
    gateway: row.gateway,
    interface: row.interface,
    metric: row.metric || 0,
    type: row.type || 'static'
  })
  dialogVisible.value = true
}

// 处理删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除路由 ${row.destination} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await NetworkAPI.deleteRoute(row) as any
    if (response.success) {
      ElMessage.success('删除成功')
      fetchRouteList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除路由失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 处理刷新
const handleRefresh = () => {
  fetchRouteList()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    const response = isEdit.value 
      ? await NetworkAPI.updateRoute(formData) as any
      : await NetworkAPI.addRoute(formData) as any
    
    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '添加成功')
      dialogVisible.value = false
      fetchRouteList()
    } else {
      ElMessage.error(response.message || (isEdit.value ? '更新失败' : '添加失败'))
    }
  } catch (error) {
    console.error('提交表单失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    destination: '',
    gateway: '',
    interface: '',
    metric: 0,
    type: 'static'
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20px;
}

.filter-actions {
  display: flex;
  gap: 10px;
}

.status-section {
  margin-bottom: 20px;
}

.routes-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-item {
  text-align: center;
  
  h4 {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
    font-weight: normal;
  }
  
  .status-value {
    font-size: 24px;
    font-weight: bold;
    color: #409eff;
  }
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
