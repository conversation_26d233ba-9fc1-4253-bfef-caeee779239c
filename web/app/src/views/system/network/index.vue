<template>
  <div class="app-container">
    <div class="page-header">
      <p class="page-description">管理系统网络配置、监控网络状态、配置防火墙规则和诊断网络问题</p>
    </div>

    <!-- Tab导航 -->
    <el-tabs v-model="activeTab" class="" @tab-change="handleTabChange">
      <!-- 网络接口 -->
      <el-tab-pane label="网络接口" name="interface">
        <template #label>
          <span class="tab-label">
            <el-icon><i-ep-connection /></el-icon>
            网络接口
          </span>
        </template>
        <NetworkInterface />
      </el-tab-pane>

      <!-- 网络监控 -->
      <el-tab-pane label="网络监控" name="monitor">
        <template #label>
          <span class="tab-label">
            <el-icon><i-ep-monitor /></el-icon>
            网络监控
          </span>
        </template>
        <NetworkMonitor />
      </el-tab-pane>

      <!-- 路由管理 -->
      <el-tab-pane label="路由管理" name="route">
        <template #label>
          <span class="tab-label">
            <el-icon><i-ep-guide /></el-icon>
            路由管理
          </span>
        </template>
        <NetworkRoute />
      </el-tab-pane>

      <!-- 防火墙 -->
      <el-tab-pane label="防火墙" name="firewall">
        <template #label>
          <span class="tab-label">
            <el-icon><i-ep-lock /></el-icon>
            防火墙
          </span>
        </template>
        <NetworkFirewall />
      </el-tab-pane>

      <!-- 网络诊断 -->
      <el-tab-pane label="网络诊断" name="diagnostic">
        <template #label>
          <span class="tab-label">
            <el-icon><i-ep-search /></el-icon>
            网络诊断
          </span>
        </template>
        <NetworkDiagnostic />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NetworkInterface from './interface.vue'
import NetworkMonitor from './monitor.vue'
import NetworkRoute from './route.vue'
import NetworkFirewall from './firewall.vue'
import NetworkDiagnostic from './diagnostic.vue'

// 当前激活的Tab
const activeTab = ref('interface')

// Tab切换处理
const handleTabChange = (tabName: string | number) => {
  console.log('切换到Tab:', tabName)
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;

  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .page-description {
    margin: 0;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }
}

:deep(.el-tabs__content) {
  padding: 0;
}
</style>