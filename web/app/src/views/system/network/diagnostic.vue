<template>
  <div class="app-container">
    <div class="header">
      <p class="description">选择不同的诊断工具来测试网络连接状态</p>
    </div>

    <div class="filter-container">
      <el-select v-model="currentDiagnostic" placeholder="请选择诊断类型" size="large" class="diagnostic-selector">
        <el-option label="Ping 测试" value="ping">
          <span class="select-option">
            <el-icon><i-ep-position /></el-icon>
            <span>Ping 测试</span>
          </span>
        </el-option>
        <el-option label="路由跟踪" value="traceroute">
          <span class="select-option">
            <el-icon><i-ep-guide /></el-icon>
            <span>路由跟踪</span>
          </span>
        </el-option>
        <el-option label="端口扫描" value="portScan">
          <span class="select-option">
            <el-icon><i-ep-connection /></el-icon>
            <span>端口扫描</span>
          </span>
        </el-option>
      </el-select>
    </div>

    <!-- 诊断工具 -->
    <el-row :gutter="20" class="diagnostic-tools">
      <el-col :span="24">
        <el-card class="diagnostic-card">
          <template #header>
            <div class="card-header">
              <el-icon v-if="currentDiagnostic === 'ping'"><i-ep-position /></el-icon>
              <el-icon v-else-if="currentDiagnostic === 'traceroute'"><i-ep-guide /></el-icon>
              <el-icon v-else-if="currentDiagnostic === 'portScan'"><i-ep-connection /></el-icon>
              <span>{{ diagnosticTitles[currentDiagnostic] }}</span>
            </div>
          </template>
          
          <!-- Ping 测试 -->
          <el-form 
            v-if="currentDiagnostic === 'ping'" 
            @submit.prevent="runPingTest" 
            label-position="top" 
            class="diagnostic-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="目标地址">
                  <el-input v-model="pingForm.target" placeholder="请输入IP地址或域名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="测试次数">
                  <el-input-number v-model="pingForm.count" :min="1" :max="10" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="runPingTest" 
                :loading="pingLoading"
                size="large"
              >
                {{ pingLoading ? '测试中...' : '开始测试' }}
              </el-button>
              <el-button @click="clearPingResults" :disabled="pingLoading">清空结果</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="currentDiagnostic === 'ping' && (pingOutput.length > 0 || pingResult)" class="test-result">
            <h4>测试结果</h4>
            <div class="output-container">
              <div v-for="(line, index) in pingOutput" :key="index" class="output-line">
                {{ line }}
              </div>
              <div v-if="pingLoading" class="loading-indicator">
                <el-icon class="is-loading"><i-ep-loading /></el-icon>
                正在测试中...
              </div>
            </div>
            <div v-if="pingResult" class="summary">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="summary-item">
                    <span class="summary-label">目标:</span>
                    <span class="summary-value">{{ pingResult.target }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">发送:</span>
                    <span class="summary-value">{{ pingResult.packets_sent }} 包</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">接收:</span>
                    <span class="summary-value">{{ pingResult.packets_recv }} 包</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="summary-item">
                    <span class="summary-label">丢包率:</span>
                    <span class="summary-value">{{ pingResult.packet_loss }}%</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">平均延迟:</span>
                    <span class="summary-value">{{ Math.round(pingResult.avg_latency / 1000000) }}ms</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">最小延迟:</span>
                    <span class="summary-value">{{ Math.round(pingResult.min_latency / 1000000) }}ms</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">最大延迟:</span>
                    <span class="summary-value">{{ Math.round(pingResult.max_latency / 1000000) }}ms</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
          
          <!-- 路由跟踪 -->
          <el-form 
            v-else-if="currentDiagnostic === 'traceroute'" 
            @submit.prevent="runTracerouteTest" 
            label-position="top" 
            class="diagnostic-form"
          >
            <el-form-item label="目标地址">
              <el-input v-model="tracerouteForm.target" placeholder="请输入IP地址或域名" />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="runTracerouteTest" 
                :loading="tracerouteLoading"
                size="large"
              >
                {{ tracerouteLoading ? '跟踪中...' : '开始跟踪' }}
              </el-button>
              <el-button @click="clearTracerouteResults" :disabled="tracerouteLoading">清空结果</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="currentDiagnostic === 'traceroute' && (tracerouteOutput.length > 0 || tracerouteResult)" class="test-result">
            <h4>跟踪结果</h4>
            <div class="output-container">
              <div v-for="(line, index) in tracerouteOutput" :key="index" class="output-line">
                {{ line }}
              </div>
              <div v-if="tracerouteLoading" class="loading-indicator">
                <el-icon class="is-loading"><i-ep-loading /></el-icon>
                正在跟踪中...
              </div>
            </div>
            <div v-if="tracerouteResult" class="summary">
              <h5>跟踪摘要</h5>
              <el-table :data="tracerouteResult.hops" style="width: 100%" size="small" border>
                <el-table-column prop="hop" label="跳数" width="80" />
                <el-table-column prop="ip" label="IP地址" />
                <el-table-column prop="host" label="主机名" />
                <el-table-column label="延迟">
                  <template #default="scope">
                    {{ Math.round(scope.row.latency / 1000000) }}ms
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          
          <!-- 端口扫描 -->
          <el-form 
            v-else-if="currentDiagnostic === 'portScan'" 
            @submit.prevent="runPortScan" 
            label-position="top" 
            class="diagnostic-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="目标地址">
                  <el-input v-model="portScanForm.target" placeholder="请输入IP地址" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="端口范围">
                  <el-input v-model="portScanForm.ports" placeholder="如：80,443,8080-8090" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="runPortScan" 
                :loading="portScanLoading"
                size="large"
              >
                {{ portScanLoading ? '扫描中...' : '开始扫描' }}
              </el-button>
              <el-button @click="clearPortScanResults" :disabled="portScanLoading">清空结果</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="currentDiagnostic === 'portScan' && portScanResult" class="test-result">
            <h4>扫描结果</h4>
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="目标">{{ portScanResult.target }}</el-descriptions-item>
              <el-descriptions-item label="扫描端口数">{{ portScanResult.total_ports }}</el-descriptions-item>
              <el-descriptions-item label="开放端口数">{{ portScanResult.open_ports.length }}</el-descriptions-item>
            </el-descriptions>
            
            <div v-if="portScanResult.open_ports.length > 0" class="port-results">
              <h5>开放端口</h5>
              <div class="port-tags">
                <el-tag 
                  v-for="port in portScanResult.open_ports" 
                  :key="port" 
                  type="success" 
                  class="port-tag"
                >
                  {{ port }}
                </el-tag>
              </div>
            </div>
            <el-alert 
              v-else 
              title="未发现开放端口" 
              type="info" 
              show-icon 
              class="no-ports-alert"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 使用说明 -->
    <el-card class="instructions-card">
      <template #header>
        <div class="card-header">
          <el-icon><i-ep-info-filled /></el-icon>
          <span>使用说明</span>
        </div>
      </template>
      <div class="instructions-content">
        <ul>
          <li><strong>Ping 测试</strong>：用于测试与目标主机的连通性，测量网络延迟</li>
          <li><strong>路由跟踪</strong>：显示数据包从本地到目标主机经过的路由路径</li>
          <li><strong>端口扫描</strong>：检测目标主机上指定端口的开放状态</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import NetworkAPI from '@/api/network'
import { TOKEN_KEY } from '@/enums/CacheEnum'

// 当前选择的诊断类型
const currentDiagnostic = ref('ping')

// 诊断类型标题映射
const diagnosticTitles = {
  ping: 'Ping 测试',
  traceroute: '路由跟踪',
  portScan: '端口扫描'
}

// 响应式数据
const pingLoading = ref(false)
const tracerouteLoading = ref(false)
const portScanLoading = ref(false)

const pingResult = ref<any>(null)
const tracerouteResult = ref<any>(null)
const portScanResult = ref<any>(null)

// 流式输出数据
const pingOutput = ref<string[]>([])
const tracerouteOutput = ref<string[]>([])

// 流式连接控制
let pingAbortController: AbortController | null = null
let tracerouteAbortController: AbortController | null = null

// 表单数据
const pingForm = reactive({
  target: '*******',
  count: 4
})

const tracerouteForm = reactive({
  target: 'baidu.com'
})

const portScanForm = reactive({
  target: '127.0.0.1',
  ports: '22,80,443,8080'
})

// 诊断类型切换处理
const onDiagnosticChange = () => {
  // 清理之前的请求
  if (pingAbortController) {
    pingAbortController.abort()
    pingAbortController = null
  }
  if (tracerouteAbortController) {
    tracerouteAbortController.abort()
    tracerouteAbortController = null
  }
  
  // 清空结果
  pingResult.value = null
  tracerouteResult.value = null
  portScanResult.value = null
  pingOutput.value = []
  tracerouteOutput.value = []
  
  // 重置加载状态
  pingLoading.value = false
  tracerouteLoading.value = false
  portScanLoading.value = false
}

// 清空Ping测试结果
const clearPingResults = () => {
  pingResult.value = null
  pingOutput.value = []
  ElMessage.info('已清空Ping测试结果')
}

// 清空路由跟踪结果
const clearTracerouteResults = () => {
  tracerouteResult.value = null
  tracerouteOutput.value = []
  ElMessage.info('已清空路由跟踪结果')
}

// 清空端口扫描结果
const clearPortScanResults = () => {
  portScanResult.value = null
  ElMessage.info('已清空端口扫描结果')
}

// 生命周期
onMounted(() => {
  // 组件初始化
})

// Ping测试 - 使用fetch + ReadableStream流式输出
const runPingTest = async () => {
  if (!pingForm.target) {
    ElMessage.warning('请输入目标地址')
    return
  }

  try {
    pingLoading.value = true
    pingResult.value = null
    pingOutput.value = []

    // 取消之前的请求
    if (pingAbortController) {
      pingAbortController.abort()
    }
    pingAbortController = new AbortController()

    // 获取认证令牌
    const token = localStorage.getItem(TOKEN_KEY)
    if (!token) {
      ElMessage.error('请先登录')
      pingLoading.value = false
      return
    }

    // 创建fetch请求
    const url = `/api/network/diagnostic/ping/stream?target=${encodeURIComponent(pingForm.target)}&count=${pingForm.count}`
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': token, // token已经包含"Bearer "前缀
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      signal: pingAbortController.signal
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    if (!response.body) {
      throw new Error('响应体为空')
    }

    // 创建读取器
    const reader = response.body.getReader()
    const decoder = new TextDecoder('utf-8')
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          break
        }

        // 解码数据
        buffer += decoder.decode(value, { stream: true })

        // 处理SSE消息
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // 保留不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.substring(6)
            if (dataStr.trim() === '') continue

            try {
              const data = JSON.parse(dataStr)

              if (data.type === 'output') {
                // 实时输出
                pingOutput.value.push(data.content)
              } else if (data.type === 'result') {
                // 最终结果
                pingResult.value = data.content
                ElMessage.success('Ping测试完成')
              } else if (data.type === 'error') {
                ElMessage.error(data.content || 'Ping测试失败')
              } else if (data.type === 'close') {
                // 连接关闭
                return
              }
            } catch (parseError) {
              console.warn('解析SSE消息失败:', parseError, dataStr)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
      pingLoading.value = false
    }

  } catch (error) {
    console.error('Ping测试失败:', error)
    ElMessage.error(`Ping测试失败: ${(error as Error).message}`)
    pingLoading.value = false
  }
}

// 路由跟踪测试 - 使用fetch + ReadableStream流式输出
const runTracerouteTest = async () => {
  if (!tracerouteForm.target) {
    ElMessage.warning('请输入目标地址')
    return
  }

  try {
    tracerouteLoading.value = true
    tracerouteResult.value = null
    tracerouteOutput.value = []

    // 取消之前的请求
    if (tracerouteAbortController) {
      tracerouteAbortController.abort()
    }
    tracerouteAbortController = new AbortController()

    // 获取认证令牌
    const token = localStorage.getItem(TOKEN_KEY)
    if (!token) {
      ElMessage.error('请先登录')
      tracerouteLoading.value = false
      return
    }

    // 创建fetch请求
    const url = `/api/network/diagnostic/traceroute/stream?target=${encodeURIComponent(tracerouteForm.target)}`
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': token, // token已经包含"Bearer "前缀
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      signal: tracerouteAbortController.signal
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    if (!response.body) {
      throw new Error('响应体为空')
    }

    // 创建读取器
    const reader = response.body.getReader()
    const decoder = new TextDecoder('utf-8')
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          break
        }

        // 解码数据
        buffer += decoder.decode(value, { stream: true })

        // 处理SSE消息
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // 保留不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.substring(6)
            if (dataStr.trim() === '') continue

            try {
              const data = JSON.parse(dataStr)

              if (data.type === 'output') {
                // 实时输出
                tracerouteOutput.value.push(data.content)
              } else if (data.type === 'result') {
                // 最终结果
                tracerouteResult.value = data.content
                ElMessage.success('路由跟踪完成')
              } else if (data.type === 'error') {
                ElMessage.error(data.content || '路由跟踪失败')
              } else if (data.type === 'close') {
                // 连接关闭
                return
              }
            } catch (parseError) {
              console.warn('解析SSE消息失败:', parseError, dataStr)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
      tracerouteLoading.value = false
    }

  } catch (error) {
    console.error('路由跟踪失败:', error)
    ElMessage.error(`路由跟踪失败: ${(error as Error).message}`)
    tracerouteLoading.value = false
  }
}

// 端口扫描
const runPortScan = async () => {
  if (!portScanForm.target || !portScanForm.ports) {
    ElMessage.warning('请输入目标地址和端口范围')
    return
  }
  
  portScanLoading.value = true
  try {
    const ports = parsePortRange(portScanForm.ports)
    const res = await NetworkAPI.portScan(portScanForm.target, ports) as any
    if (res.success) {
      portScanResult.value = res.data
      ElMessage.success('端口扫描完成')
    } else {
      ElMessage.error(res.message || '端口扫描失败')
    }
  } catch (error) {
    console.error('端口扫描出错:', error)
    ElMessage.error('端口扫描失败')
  } finally {
    portScanLoading.value = false
  }
}

// 组件卸载时清理连接
onUnmounted(() => {
  if (pingAbortController) {
    pingAbortController.abort()
    pingAbortController = null
  }
  if (tracerouteAbortController) {
    tracerouteAbortController.abort()
    tracerouteAbortController = null
  }
})

// 解析端口范围
const parsePortRange = (portStr: string): number[] => {
  const ports: number[] = []
  const parts = portStr.split(',')
  
  parts.forEach(part => {
    part = part.trim()
    if (part.includes('-')) {
      const [start, end] = part.split('-').map(p => parseInt(p.trim()))
      for (let i = start; i <= end; i++) {
        ports.push(i)
      }
    } else {
      ports.push(parseInt(part))
    }
  })
  
  return ports
}
</script>

<style scoped>

.header {
  margin-bottom: 20px;
}

.header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
}

.diagnostic-selector {
  width: 300px;
}

.select-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.diagnostic-tools {
  margin-bottom: 20px;
}

.diagnostic-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
}

.diagnostic-form {
  padding: 20px 0;
}

.test-result {
  margin-top: 15px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.test-result h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.test-result h5 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
}

.summary {
  margin-top: 20px;
  padding: 15px;
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.summary-item {
  display: flex;
  margin-bottom: 8px;
}

.summary-label {
  width: 100px;
  color: #606266;
}

.summary-value {
  flex: 1;
  color: #303133;
  font-weight: 500;
}

.port-results {
  margin-top: 15px;
}

.port-results h5 {
  margin: 0 0 10px 0;
  color: #303133;
}

.port-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.port-tag {
  margin: 0;
}

.no-ports-alert {
  margin-top: 15px;
}

.traceroute-hops {
  max-height: 300px;
  overflow-y: auto;
}

.hop-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.hop-number {
  font-weight: bold;
  width: 30px;
}

.hop-ip {
  flex: 1;
  text-align: center;
}

.hop-latency {
  width: 60px;
  text-align: right;
  color: #67C23A;
}

.diagnostic-history {
  margin-bottom: 20px;
}

.network-issues {
  margin-bottom: 20px;
}

.issues-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.issue-item {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 15px;
}

.suggestions {
  margin-top: 10px;
}

.suggestions h5 {
  margin: 0 0 5px 0;
  color: #303133;
}

.suggestions ul {
  margin: 0;
  padding-left: 20px;
}

.suggestions li {
  margin: 3px 0;
  font-size: 14px;
  color: #666;
}

/* 流式输出样式 */
.output-container {
  background: #1e1e1e;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  padding: 16px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
  margin: 16px 0;
  border: 1px solid #333;
}

.output-line {
  margin: 2px 0;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.loading-indicator {
  color: #409eff;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.instructions-card {
  margin-top: 20px;
  border-radius: 8px;
}

.instructions-content ul {
  margin: 0;
  padding-left: 20px;
}

.instructions-content li {
  margin: 8px 0;
  line-height: 1.6;
}
</style>
