<template>
  <div class="app-container">
    <!-- 操作栏 -->
    <div class="filter-container">
      <el-button type="primary" @click="handleAdd">
        <el-icon><i-ep-plus /></el-icon>
        新增接口
      </el-button>
      <el-button @click="handleRefresh">
        <el-icon><i-ep-refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 接口列表 -->
    <el-table 
      :data="interfaceList" 
      v-loading="loading"
      stripe
      style="width: 100%"
    >
      <el-table-column prop="name" label="接口名称" width="120">
        <template #default="{ row }">
          <el-tag :type="row.status === 'up' ? 'success' : 'danger'" size="small">
            {{ row.name }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="type" label="类型" width="80">
        <template #default="{ row }">
          <el-tag :type="getTypeColor(row.type)" size="small">
            {{ row.type }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.status === 'up' ? 'success' : 'danger'" size="small">
            {{ row.status === 'up' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="ip_address" label="IP地址" width="140" />
      <el-table-column prop="netmask" label="子网掩码" width="140" />
      <el-table-column prop="gateway" label="网关" width="140" />
      <el-table-column prop="mac_address" label="MAC地址" width="150" />
      <el-table-column prop="speed" label="速率" width="100">
        <template #default="{ row }">
          {{ row.speed || 'unknown' }}
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="120" />
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button 
            type="primary" 
            size="small" 
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button 
            :type="row.status === 'up' ? 'warning' : 'success'" 
            size="small" 
            @click="handleToggleStatus(row)"
          >
            {{ row.status === 'up' ? '禁用' : '启用' }}
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑接口' : '新增接口'"
      width="600px"
    >
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules" 
        label-width="100px"
      >
        <el-form-item label="接口名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入接口名称" />
        </el-form-item>
        
        <el-form-item label="接口类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择接口类型">
            <el-option label="以太网" value="ethernet" />
            <el-option label="无线" value="wireless" />
            <el-option label="虚拟" value="virtual" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="IP地址" prop="ip_address">
          <el-input v-model="formData.ip_address" placeholder="请输入IP地址" />
        </el-form-item>
        
        <el-form-item label="子网掩码" prop="netmask">
          <el-input v-model="formData.netmask" placeholder="请输入子网掩码" />
        </el-form-item>
        
        <el-form-item label="网关" prop="gateway">
          <el-input v-model="formData.gateway" placeholder="请输入网关地址" />
        </el-form-item>
        
        <el-form-item label="MAC地址" prop="mac_address">
          <el-input v-model="formData.mac_address" placeholder="请输入MAC地址" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="formData.description" 
            type="textarea" 
            placeholder="请输入接口描述"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import NetworkAPI from '@/api/network'

// 响应式数据
const loading = ref(false)
const interfaceList = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  type: '',
  ip_address: '',
  netmask: '',
  gateway: '',
  mac_address: '',
  description: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入接口名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择接口类型', trigger: 'change' }
  ],
  ip_address: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: 'IP地址格式不正确', trigger: 'blur' }
  ],
  netmask: [
    { required: true, message: '请输入子网掩码', trigger: 'blur' }
  ]
}

// 生命周期
onMounted(() => {
  fetchInterfaceList()
})

// 获取接口列表
const fetchInterfaceList = async () => {
  loading.value = true
  try {
    const response = await NetworkAPI.getInterfaces() as any
    if (response.success) {
      interfaceList.value = response.data || []
    } else {
      ElMessage.error(response.message || '获取接口列表失败')
    }
  } catch (error) {
    console.error('获取接口列表失败:', error)
    ElMessage.error('获取接口列表失败')
  } finally {
    loading.value = false
  }
}

// 获取类型颜色
const getTypeColor = (type: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
  const colorMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    'ethernet': 'primary',
    'wireless': 'success',
    'virtual': 'warning'
  }
  return colorMap[type] || 'info'
}

// 新增接口
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑接口
const handleEdit = (row: any) => {
  isEdit.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

// 切换状态
const handleToggleStatus = async (row: any) => {
  const action = row.status === 'up' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}接口 ${row.name} 吗？`, '确认操作')
    
    const response = await NetworkAPI.toggleInterfaceStatus(row.id, row.status === 'down') as any
    if (response.success) {
      ElMessage.success(`${action}成功`)
      fetchInterfaceList()
    } else {
      ElMessage.error(response.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}接口失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除接口
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除接口 ${row.name} 吗？`, '确认删除', {
      type: 'warning'
    })
    
    const response = await NetworkAPI.deleteInterface(row.id) as any
    if (response.success) {
      ElMessage.success('删除成功')
      fetchInterfaceList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除接口失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 刷新列表
const handleRefresh = () => {
  fetchInterfaceList()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    const response = isEdit.value 
      ? await NetworkAPI.updateInterface(formData.id, formData) as any
      : await NetworkAPI.createInterface(formData) as any
    
    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      dialogVisible.value = false
      fetchInterfaceList()
    } else {
      ElMessage.error(response.message || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error('提交表单失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    name: '',
    type: '',
    ip_address: '',
    netmask: '',
    gateway: '',
    mac_address: '',
    description: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20px;
}
</style>
