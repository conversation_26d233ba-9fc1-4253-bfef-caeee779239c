<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="connection-status">
        <el-tag :type="isConnected ? 'success' : 'danger'" size="small">
          <el-icon><i-ep-connection /></el-icon>
          {{ isConnected ? '实时连接' : '连接断开' }}
        </el-tag>
        <span v-if="lastUpdateTime" class="last-update">
          最后更新: {{ formatTime(lastUpdateTime) }}
        </span>
      </div>
      <div class="actions">
        <el-button
          type="primary"
          @click="isConnected ? stopNetworkMonitoring() : startNetworkMonitoring()"
          :loading="!isConnected && !!monitorAbortController"
        >
          <el-icon><i-ep-connection v-if="!isConnected" /><i-ep-close v-else /></el-icon>
          {{ isConnected ? '断开连接' : '开始监控' }}
        </el-button>
        <el-button @click="refreshData">
          <el-icon><i-ep-refresh /></el-icon>
          手动刷新
        </el-button>
      </div>
    </div>

    <!-- 网络状态卡片 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card>
          <div class="status-card">
            <div class="status-icon">
              <el-icon class="icon-large success-icon">
                <i-ep-connection />
              </el-icon>
            </div>
            <div class="status-info">
              <h3>网络连接</h3>
              <p class="status-value success">正常</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card>
          <div class="status-card">
            <div class="status-icon">
              <el-icon class="icon-large warning-icon">
                <i-ep-upload />
              </el-icon>
            </div>
            <div class="status-info">
              <h3>上传速度</h3>
              <p class="status-value">{{ formatSpeed(uploadSpeed) }}</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card>
          <div class="status-card">
            <div class="status-icon">
              <el-icon class="icon-large primary-icon">
                <i-ep-download />
              </el-icon>
            </div>
            <div class="status-info">
              <h3>下载速度</h3>
              <p class="status-value">{{ formatSpeed(downloadSpeed) }}</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card>
          <div class="status-card">
            <div class="status-icon">
              <el-icon class="icon-large info-icon">
                <i-ep-timer />
              </el-icon>
            </div>
            <div class="status-info">
              <h3>网络延迟</h3>
              <p class="status-value">{{ latency }}ms</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 流量统计图表 -->
    <!-- <el-row :gutter="20" class="charts-section">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>网络流量统计</span>
              <el-select v-model="timeRange" @change="refreshData">
                <el-option label="最近1小时" value="1h" />
                <el-option label="最近6小时" value="6h" />
                <el-option label="最近24小时" value="24h" />
                <el-option label="最近7天" value="7d" />
              </el-select>
            </div>
          </template>
          
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon class="chart-icon">
                <i-ep-trend-charts />
              </el-icon>
              <p>流量统计图表</p>
              <p class="chart-desc">显示网络接口的实时流量数据</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row> -->

    <!-- 网络接口详情 -->
    <el-row :gutter="20" class="interface-details">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>网络接口详情</span>
          </template>
          
          <el-table :data="interfaceStats" style="width: 100%">
            <el-table-column prop="interface" label="接口名称" width="180">
              <template #default="{ row }">
                <div>
                  <div style="font-weight: 500;">{{ row.display_name || row.interface }}</div>
                  <div style="font-size: 12px; color: #999;">{{ row.interface }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="ip_address" label="IP地址" width="140">
              <template #default="{ row }">
                <span v-if="row.ip_address">{{ row.ip_address }}</span>
                <span v-else style="color: #999;">-</span>
              </template>
            </el-table-column>
            <el-table-column label="实时速度" width="160">
              <template #default="{ row }">
                <div>
                  <div style="color: #67C23A;">↓ {{ formatSpeed(row.rx_speed) }}</div>
                  <div style="color: #E6A23C;">↑ {{ formatSpeed(row.tx_speed) }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="rx_bytes" label="接收字节" width="120">
              <template #default="{ row }">
                {{ formatBytes(row.rx_bytes) }}
              </template>
            </el-table-column>
            <el-table-column prop="tx_bytes" label="发送字节" width="120">
              <template #default="{ row }">
                {{ formatBytes(row.tx_bytes) }}
              </template>
            </el-table-column>
            <el-table-column prop="rx_packets" label="接收包数" width="120" />
            <el-table-column prop="tx_packets" label="发送包数" width="120" />
            <el-table-column label="错误统计" width="120">
              <template #default="{ row }">
                <div v-if="row.rx_errors > 0 || row.tx_errors > 0" style="color: #F56C6C;">
                  <div v-if="row.rx_errors > 0">RX: {{ row.rx_errors }}</div>
                  <div v-if="row.tx_errors > 0">TX: {{ row.tx_errors }}</div>
                </div>
                <span v-else style="color: #67C23A;">正常</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import NetworkAPI from '@/api/network'
import { TOKEN_KEY } from '@/enums/CacheEnum'

// 响应式数据
const uploadSpeed = ref(0)
const downloadSpeed = ref(0)
const latency = ref(0)
const interfaceStats = ref<any[]>([])
const isConnected = ref(false)
const lastUpdateTime = ref<Date | null>(null)

// SSE连接管理
let monitorAbortController: AbortController | null = null

// 生命周期
onMounted(() => {
  startNetworkMonitoring()
})

onUnmounted(() => {
  stopNetworkMonitoring()
})

// 启动网络监控SSE连接
const startNetworkMonitoring = async () => {
  try {
    // 停止之前的连接
    stopNetworkMonitoring()

    // 获取认证令牌
    const token = localStorage.getItem(TOKEN_KEY)
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    // 创建AbortController
    monitorAbortController = new AbortController()

    // 创建fetch请求
    const url = '/api/network/monitor/stream'
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': token,
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      signal: monitorAbortController.signal
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    if (!response.body) {
      throw new Error('响应体为空')
    }

    console.log('SSE响应接收成功，开始处理流数据')

    // 创建读取器
    const reader = response.body.getReader()
    const decoder = new TextDecoder('utf-8')
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          break
        }

        // 解码数据
        buffer += decoder.decode(value, { stream: true })

        // 处理SSE消息
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.substring(6)
            if (dataStr.trim() === '') continue

            try {
              const message = JSON.parse(dataStr)

              if (message.type === 'connected') {
                // 连接确认
                console.log('SSE连接已建立:', message.content)
                isConnected.value = true
              } else if (message.type === 'data') {
                // 检查是否是心跳消息
                if (message.content && message.content.type === 'heartbeat') {
                  console.log('收到心跳消息:', message.content.message)
                  lastUpdateTime.value = new Date()
                } else {
                  // 更新监控数据
                  console.log('收到监控数据:', message.content)
                  updateMonitorData(message.content)
                }
              } else if (message.type === 'error') {
                console.error('监控数据错误:', message.content)
                ElMessage.error(`监控数据错误: ${message.content}`)
              } else if (message.type === 'close') {
                // 连接关闭
                console.log('SSE连接关闭')
                isConnected.value = false
                return
              }
            } catch (parseError) {
              console.warn('解析SSE消息失败:', parseError, dataStr)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
      isConnected.value = false
    }

  } catch (error) {
    console.error('网络监控连接失败:', error)
    isConnected.value = false

    // 如果不是主动取消的请求，显示错误信息
    if ((error as Error).name !== 'AbortError') {
      ElMessage.error(`网络监控连接失败: ${(error as Error).message}`)
    }
  }
}

// 停止网络监控连接
const stopNetworkMonitoring = () => {
  if (monitorAbortController) {
    monitorAbortController.abort()
    monitorAbortController = null
  }
  isConnected.value = false
}

// 更新监控数据
const updateMonitorData = (data: any) => {
  uploadSpeed.value = data.upload_speed || 0
  downloadSpeed.value = data.download_speed || 0
  latency.value = data.latency || 0
  interfaceStats.value = data.interface_stats || []
  lastUpdateTime.value = new Date()
}

// 手动刷新数据（保留原有功能作为备用）
const refreshData = async () => {
  try {
    // 同时获取网络状态和流量统计
    const [statusRes, trafficRes, qualityRes] = await Promise.all([
      NetworkAPI.getNetworkStatus(),
      NetworkAPI.getTrafficStats(),
      NetworkAPI.getNetworkQuality('*******')
    ])

    // 处理网络状态数据
    let statusMap = new Map()
    if ((statusRes as any).success) {
      const interfaces = (statusRes as any).data?.interfaces || []
      interfaces.forEach((iface: any) => {
        statusMap.set(iface.name, {
          status: iface.status,
          display_name: iface.display_name,
          type: iface.type,
          ip_address: iface.ip_address,
          mac_address: iface.mac_address,
          method: iface.method
        })
      })
    }

    // 处理流量统计数据并合并状态信息
    if ((trafficRes as any).success) {
      const stats = (trafficRes as any).data || []
      interfaceStats.value = stats.map((stat: any) => {
        const statusInfo = statusMap.get(stat.interface) || {}
        return {
          ...stat,
          status: statusInfo.status || 'unknown',
          display_name: statusInfo.display_name || stat.interface,
          type: statusInfo.type || 'unknown',
          ip_address: statusInfo.ip_address || '',
          mac_address: statusInfo.mac_address || '',
          method: statusInfo.method || 'unknown'
        }
      })

      // 计算总流量
      let totalRxSpeed = 0
      let totalTxSpeed = 0
      stats.forEach((stat: any) => {
        totalRxSpeed += stat.rx_speed || 0
        totalTxSpeed += stat.tx_speed || 0
      })

      downloadSpeed.value = totalRxSpeed
      uploadSpeed.value = totalTxSpeed
    }

    // 处理网络质量数据
    if ((qualityRes as any).success) {
      const quality = (qualityRes as any).data
      latency.value = Math.round(quality.latency / 1000000) // 转换为毫秒
    }

  } catch (error) {
    console.error('获取网络监控数据出错:', error)
    ElMessage.error('获取监控数据失败')
  }
}

// 格式化速度
const formatSpeed = (bytesPerSecond: number) => {
  if (bytesPerSecond === 0) return '0 B/s'
  const k = 1024
  const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化字节数
const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取状态类型
const getStatusType = (status: string): 'success' | 'danger' | 'warning' | 'info' => {
  const typeMap: { [key: string]: 'success' | 'danger' | 'warning' | 'info' } = {
    'up': 'success',
    'down': 'danger',
    'no-ip': 'warning',
    'unknown': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: { [key: string]: string } = {
    'up': '在线',
    'down': '离线',
    'no-ip': '无IP',
    'unknown': '未知'
  }
  return textMap[status] || '未知'
}

// 格式化时间
const formatTime = (time: Date) => {
  return time.toLocaleTimeString()
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.last-update {
  font-size: 12px;
  color: #999;
}

.actions {
  display: flex;
  gap: 8px;
}

.status-cards {
  margin-bottom: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  padding: 10px;
}

.status-icon {
  margin-right: 15px;
}

.icon-large {
  font-size: 32px;
}

.success-icon {
  color: #67C23A;
}

.warning-icon {
  color: #E6A23C;
}

.primary-icon {
  color: #409EFF;
}

.info-icon {
  color: #909399;
}

.status-info h3 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #666;
}

.status-value {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.status-value.success {
  color: #67C23A;
}

.charts-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #909399;
}

.chart-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.chart-placeholder p {
  margin: 5px 0;
}

.chart-desc {
  font-size: 12px;
}

.interface-details {
  margin-bottom: 20px;
}
</style>
