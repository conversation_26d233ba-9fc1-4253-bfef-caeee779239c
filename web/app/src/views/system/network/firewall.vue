<template>
  <div class="app-container">
    <div class="filter-container">
      <div></div>
      <div class="filter-actions">
        <el-button type="primary" @click="addRule">
          <el-icon><i-ep-plus /></el-icon>
          添加规则
        </el-button>
        <el-button type="success" @click="refreshRules">
          <el-icon><i-ep-refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 防火墙状态 -->
    <el-row :gutter="20" class="status-section">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>防火墙状态</span>
              <el-switch
                v-model="firewallEnabled"
                active-text="启用"
                inactive-text="禁用"
                @change="toggleFirewall"
              />
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="status-item">
                <h4>状态</h4>
                <el-tag :type="firewallEnabled ? 'success' : 'danger'" size="large">
                  {{ firewallEnabled ? '已启用' : '已禁用' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <h4>规则数量</h4>
                <span class="status-value">{{ rules.length }} 条</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <h4>默认策略</h4>
                <el-tag type="warning">{{ defaultPolicy }}</el-tag>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 防火墙规则列表 -->
    <el-row :gutter="20" class="rules-section">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>防火墙规则</span>
          </template>
          
          <el-table
            v-loading="loading"
            :data="rules"
            style="width: 100%"
          >
            <el-table-column prop="id" label="规则ID" width="100" />
            <el-table-column prop="chain" label="链" width="100">
              <template #default="{ row }">
                <el-tag :type="getChainTagType(row.chain)">
                  {{ row.chain }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="target" label="动作" width="100">
              <template #default="{ row }">
                <el-tag :type="getTargetTagType(row.target)">
                  {{ row.target }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="protocol" label="协议" width="80" />
            <el-table-column prop="source" label="源地址" width="140" />
            <el-table-column prop="destination" label="目标地址" width="140" />
            <el-table-column prop="dest_port" label="目标端口" width="100" />
            <el-table-column prop="comment" label="注释" min-width="150" />
            <el-table-column prop="enabled" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.enabled ? 'success' : 'info'">
                  {{ row.enabled ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="editRule(row)">
                  编辑
                </el-button>
                <el-button 
                  :type="row.enabled ? 'warning' : 'success'" 
                  size="small" 
                  @click="toggleRule(row)"
                >
                  {{ row.enabled ? '禁用' : '启用' }}
                </el-button>
                <el-button type="danger" size="small" @click="deleteRule(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加/编辑规则对话框 -->
    <el-dialog
      v-model="ruleDialogVisible"
      :title="dialogType === 'add' ? '添加防火墙规则' : '编辑防火墙规则'"
      width="600px"
    >
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="ruleRules"
        label-width="100px"
      >
        <el-form-item label="链" prop="chain">
          <el-select v-model="ruleForm.chain" placeholder="请选择链">
            <el-option label="INPUT" value="INPUT" />
            <el-option label="OUTPUT" value="OUTPUT" />
            <el-option label="FORWARD" value="FORWARD" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="动作" prop="target">
          <el-select v-model="ruleForm.target" placeholder="请选择动作">
            <el-option label="ACCEPT" value="ACCEPT" />
            <el-option label="DROP" value="DROP" />
            <el-option label="REJECT" value="REJECT" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="协议" prop="protocol">
          <el-select v-model="ruleForm.protocol" placeholder="请选择协议">
            <el-option label="TCP" value="tcp" />
            <el-option label="UDP" value="udp" />
            <el-option label="ICMP" value="icmp" />
            <el-option label="ALL" value="all" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="源地址" prop="source">
          <el-input v-model="ruleForm.source" placeholder="请输入源地址，如：192.168.1.0/24" />
        </el-form-item>
        
        <el-form-item label="目标地址" prop="destination">
          <el-input v-model="ruleForm.destination" placeholder="请输入目标地址，如：0.0.0.0/0" />
        </el-form-item>
        
        <el-form-item label="目标端口" prop="dest_port">
          <el-input v-model="ruleForm.dest_port" placeholder="请输入端口，如：80,443 或 8000-9000" />
        </el-form-item>
        
        <el-form-item label="注释" prop="comment">
          <el-input v-model="ruleForm.comment" placeholder="请输入规则注释" />
        </el-form-item>
        
        <el-form-item label="启用">
          <el-switch v-model="ruleForm.enabled" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRule" :loading="saving">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import NetworkAPI from '@/api/network'

// 响应式数据
const loading = ref(false)
const rules = ref<any[]>([])
const firewallEnabled = ref(false)
const defaultPolicy = ref('DROP')
const ruleDialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const saving = ref(false)

// 表单引用
const ruleFormRef = ref<FormInstance>()

// 规则表单
const ruleForm = reactive({
  id: '',
  chain: 'INPUT',
  target: 'ACCEPT',
  protocol: 'tcp',
  source: '0.0.0.0/0',
  destination: '0.0.0.0/0',
  dest_port: '',
  comment: '',
  enabled: true
})

// 表单验证规则
const ruleRules = {
  chain: [
    { required: true, message: '请选择链', trigger: 'change' }
  ],
  target: [
    { required: true, message: '请选择动作', trigger: 'change' }
  ],
  protocol: [
    { required: true, message: '请选择协议', trigger: 'change' }
  ]
}

// 生命周期
onMounted(() => {
  fetchFirewallStatus()
  fetchRules()
})

// 获取防火墙状态
const fetchFirewallStatus = async () => {
  try {
    const res = await NetworkAPI.getFirewallStatus() as any
    if (res.success) {
      const status = res.data
      firewallEnabled.value = status.enabled || false
      defaultPolicy.value = status.default_policy || 'DROP'
    }
  } catch (error) {
    console.error('获取防火墙状态出错:', error)
  }
}

// 获取防火墙规则
const fetchRules = async () => {
  loading.value = true
  try {
    const res = await NetworkAPI.getFirewallRules() as any
    if (res.success) {
      rules.value = res.data || []
    } else {
      ElMessage.error(res.message || '获取防火墙规则失败')
    }
  } catch (error) {
    console.error('获取防火墙规则出错:', error)
    ElMessage.error('获取防火墙规则失败')
  } finally {
    loading.value = false
  }
}

// 刷新规则
const refreshRules = () => {
  fetchRules()
}

// 切换防火墙状态
const toggleFirewall = async (enabled: boolean) => {
  try {
    ElMessage.success(`防火墙已${enabled ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('切换防火墙状态出错:', error)
    ElMessage.error('操作失败')
    // 恢复原状态
    firewallEnabled.value = !enabled
  }
}

// 添加规则
const addRule = () => {
  dialogType.value = 'add'
  resetRuleForm()
  ruleDialogVisible.value = true
}

// 编辑规则
const editRule = (row: any) => {
  dialogType.value = 'edit'
  Object.assign(ruleForm, row)
  ruleDialogVisible.value = true
}

// 保存规则
const saveRule = async () => {
  if (!ruleFormRef.value) return
  
  await ruleFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    saving.value = true
    try {
      const res = await NetworkAPI.addFirewallRule(ruleForm) as any
      if (res.success) {
        ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
        ruleDialogVisible.value = false
        fetchRules()
      } else {
        ElMessage.error(res.message || '保存失败')
      }
    } catch (error) {
      console.error('保存防火墙规则出错:', error)
      ElMessage.error('保存失败')
    } finally {
      saving.value = false
    }
  })
}

// 切换规则状态
const toggleRule = async (row: any) => {
  const action = row.enabled ? '禁用' : '启用'
  try {
    ElMessage.success(`规则已${action}`)
    row.enabled = !row.enabled
  } catch (error) {
    console.error('切换规则状态出错:', error)
    ElMessage.error('操作失败')
  }
}

// 删除规则
const deleteRule = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除规则 ${row.id} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const res = await NetworkAPI.deleteFirewallRule(row.id) as any
    if (res.success) {
      ElMessage.success('删除成功')
      fetchRules()
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除防火墙规则出错:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 重置表单
const resetRuleForm = () => {
  Object.assign(ruleForm, {
    id: '',
    chain: 'INPUT',
    target: 'ACCEPT',
    protocol: 'tcp',
    source: '0.0.0.0/0',
    destination: '0.0.0.0/0',
    dest_port: '',
    comment: '',
    enabled: true
  })
}

// 辅助函数
const getChainTagType = (chain: string) => {
  switch (chain) {
    case 'INPUT':
      return 'success'
    case 'OUTPUT':
      return 'warning'
    case 'FORWARD':
      return 'info'
    default:
      return 'default'
  }
}

const getTargetTagType = (target: string) => {
  switch (target) {
    case 'ACCEPT':
      return 'success'
    case 'DROP':
      return 'danger'
    case 'REJECT':
      return 'warning'
    default:
      return 'default'
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-actions {
  display: flex;
  gap: 10px;
}

.status-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-item {
  text-align: center;
  padding: 10px;
}

.status-item h4 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}

.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.rules-section {
  margin-bottom: 20px;
}
</style>
