<template>
  <div class="time-container">
    <!-- 当前时间显示 -->
    <div class="time-display-section">
      <div class="time-info">
        <span class="time-label">系统时间</span>
        <span class="current-time">{{ formatDateTime(currentTime) }}</span>
        <span class="timezone-info">{{ currentTimezone }}</span>
      </div>
    </div>

    <!-- 手动校时区域 -->
    <div class="form-block">
      <div class="block-header">
        <h4 class="block-title">
          <el-icon class="block-icon"><Edit /></el-icon>
          手动校时
        </h4>
      </div>
      <div class="block-content">
        <div class="manual-time-setting">
          <el-form :model="manualTimeForm" label-width="100px">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="日期时间">
                  <el-date-picker
                    v-model="manualTimeForm.datetime"
                    type="datetime"
                    placeholder="选择日期时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DDTHH:mm:ss"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="时区">
                  <el-select v-model="manualTimeForm.timezone" placeholder="选择时区" style="width: 100%">
                    <el-option
                      v-for="tz in availableTimezones"
                      :key="tz.name"
                      :label="tz.display_name"
                      :value="tz.name"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item>
              <el-button type="primary" @click="setManualTime" :loading="settingTime">
                设置时间
              </el-button>
              <el-button @click="setCurrentTime">
                设为当前时间
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- NTP校时区域 -->
    <div class="form-block">
      <div class="block-header">
        <h4 class="block-title">
          <el-icon class="block-icon"><Connection /></el-icon>
          NTP校时
        </h4>
      </div>
      <div class="block-content">
        <div class="ntp-setting">
          <el-form :model="ntpForm" label-width="100px">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="启用NTP">
                  <el-switch v-model="ntpForm.enabled" />
                </el-form-item>
              </el-col>

              <el-col :span="8" v-if="ntpForm.enabled">
                <el-form-item label="同步间隔">
                  <el-input-number
                    v-model="ntpForm.sync_interval"
                    :min="300"
                    :max="86400"
                    :step="300"
                    style="width: 120px"
                  />
                  <span class="input-suffix">秒</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="NTP服务器" v-if="ntpForm.enabled">
              <div class="ntp-servers">
                <div
                  v-for="(server, index) in ntpForm.servers"
                  :key="index"
                  class="server-item"
                >
                  <el-input
                    v-model="ntpForm.servers[index]"
                    placeholder="输入NTP服务器地址"
                  />
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeNTPServer(index)"
                    :disabled="ntpForm.servers.length <= 1"
                  >
                    删除
                  </el-button>
                </div>

                <el-button
                  type="primary"
                  size="small"
                  @click="addNTPServer"
                  :disabled="ntpForm.servers.length >= 5"
                  :class="{ 'is-disabled': ntpForm.servers.length >= 5 }"
                >
                  添加服务器
                </el-button>
              </div>
            </el-form-item>

            <!-- 将保存配置和立即同步按钮移到底部 -->
            <div class="ntp-actions" v-if="ntpForm.enabled">
              <el-button type="primary" @click="setNTPConfig" :loading="settingNTP">
                保存配置
              </el-button>
              <el-button
                @click="syncNTPNow"
                :loading="syncing"
                :disabled="!ntpForm.enabled"
              >
                立即同步
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Clock, Edit, Connection } from '@element-plus/icons-vue'
import SystemAPI from '@/api/system'

// 响应式数据
const loading = ref(false)
const settingTime = ref(false)
const settingNTP = ref(false)
const syncing = ref(false)
const currentTime = ref(new Date())
const systemTimeInfo = ref<any>({})
const ntpConfig = ref<any>({})
const ntpServers = ref<any[]>([])
const availableTimezones = ref<any[]>([])

// 定时器
let timeUpdateTimer: NodeJS.Timeout | null = null

// 表单数据
const manualTimeForm = reactive({
  datetime: '',
  timezone: ''
})

const ntpForm = reactive({
  enabled: false,
  servers: ['ntp.aliyun.com'],
  sync_interval: 3600
})

// 计算属性

const currentTimezone = computed(() => {
  return systemTimeInfo.value.timezone || 'Unknown'
})

// 生命周期
onMounted(() => {
  fetchTimeStatus()
  fetchAvailableTimezones()
  startTimeUpdate()
})

onUnmounted(() => {
  stopTimeUpdate()
})

// 启动时间更新定时器
const startTimeUpdate = () => {
  timeUpdateTimer = setInterval(() => {
    currentTime.value = new Date()
  }, 1000)
}

// 停止时间更新定时器
const stopTimeUpdate = () => {
  if (timeUpdateTimer) {
    clearInterval(timeUpdateTimer)
    timeUpdateTimer = null
  }
}

// 获取时间状态
const fetchTimeStatus = async () => {
  loading.value = true
  try {
    const response = await SystemAPI.getTimeStatus() as any
    if (response.success) {
      systemTimeInfo.value = response.data.system_time || {}
      ntpConfig.value = response.data.ntp_config || {}
      ntpServers.value = response.data.ntp_servers || []
      
      // 更新当前时间
      if (systemTimeInfo.value.current_time) {
        currentTime.value = new Date(systemTimeInfo.value.current_time)
      }
      
      // 更新NTP表单
      ntpForm.enabled = ntpConfig.value.enabled || false
      ntpForm.servers = ntpConfig.value.servers || ['ntp.aliyun.com']
      ntpForm.sync_interval = ntpConfig.value.sync_interval || 3600
      
      // 更新手动时间表单的时区
      if (systemTimeInfo.value.timezone) {
        manualTimeForm.timezone = systemTimeInfo.value.timezone
      }
    } else {
      ElMessage.error(response.message || '获取时间状态失败')
    }
  } catch (error) {
    console.error('获取时间状态失败:', error)
    ElMessage.error('获取时间状态失败')
  } finally {
    loading.value = false
  }
}

// 获取可用时区列表
const fetchAvailableTimezones = async () => {
  try {
    const response = await SystemAPI.getAvailableTimezones() as any
    if (response.success) {
      availableTimezones.value = response.data || []
    }
  } catch (error) {
    console.error('获取时区列表失败:', error)
  }
}



// 设置为当前时间
const setCurrentTime = () => {
  const now = new Date()
  manualTimeForm.datetime = now.toISOString().slice(0, 19)
}

// 设置手动时间
const setManualTime = async () => {
  if (!manualTimeForm.datetime) {
    ElMessage.warning('请选择要设置的时间')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      '确定要设置系统时间吗？这将影响整个系统的时间。',
      '确认设置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    settingTime.value = true
    const response = await SystemAPI.setSystemTime({
      datetime: manualTimeForm.datetime + '.000Z',
      timezone: manualTimeForm.timezone
    }) as any
    
    if (response.success) {
      ElMessage.success('系统时间设置成功')
      fetchTimeStatus()
    } else {
      ElMessage.error(response.message || '设置系统时间失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设置系统时间失败:', error)
      ElMessage.error('设置系统时间失败')
    }
  } finally {
    settingTime.value = false
  }
}

// 添加NTP服务器
const addNTPServer = () => {
  ntpForm.servers.push('')
}

// 删除NTP服务器
const removeNTPServer = (index: number) => {
  ntpForm.servers.splice(index, 1)
}

// 设置NTP配置
const setNTPConfig = async () => {
  // 验证NTP服务器地址
  if (ntpForm.enabled) {
    const validServers = ntpForm.servers.filter(server => server.trim() !== '')
    if (validServers.length === 0) {
      ElMessage.warning('请至少配置一个NTP服务器')
      return
    }
    ntpForm.servers = validServers
  }
  
  settingNTP.value = true
  try {
    const response = await SystemAPI.setNTPConfig(ntpForm) as any
    if (response.success) {
      ElMessage.success('NTP配置保存成功')
      fetchTimeStatus()
    } else {
      ElMessage.error(response.message || 'NTP配置保存失败')
    }
  } catch (error) {
    console.error('NTP配置保存失败:', error)
    ElMessage.error('NTP配置保存失败')
  } finally {
    settingNTP.value = false
  }
}

// 立即同步NTP
const syncNTPNow = async () => {
  syncing.value = true
  try {
    const response = await SystemAPI.syncNTP({ force: true }) as any
    if (response.success) {
      ElMessage.success('NTP同步成功')
      fetchTimeStatus()
    } else {
      ElMessage.error(response.message || 'NTP同步失败')
    }
  } catch (error) {
    console.error('NTP同步失败:', error)
    ElMessage.error('NTP同步失败')
  } finally {
    syncing.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string | Date) => {
  if (!dateTime) return '-'
  const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}


</script>

<style lang="scss" scoped>
.time-container {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.time-display-section {
  margin-bottom: 24px;
  padding: 0;
  background: transparent;
}

.time-info {
  display: flex;
  align-items: center;
  padding: 0 0 0 10px;
  border-left: 4px solid #1265bb;
  background: #f8f9fa;

  .time-label {
    font-size: 14px;
    color: #909399;
    margin-right: 20px;
    min-width: 80px;
  }

  .current-time {
    font-size: 16px;
    color: #909399;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    margin-right: 20px;
  }

  .timezone-info {
    font-size: 14px;
    color: #909399;
  }
}

.time-block {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.block-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;

  .block-icon {
    font-size: 18px;
    color: #3b82f6;
  }
}

.block-content {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.manual-time-setting {
  .el-form-item {
    margin-bottom: 16px;
  }
}

.ntp-setting {
  .ntp-servers {
    .server-item {
      display: flex;
      gap: 12px;
      margin-bottom: 12px;
      align-items: center;

      .el-input {
        flex: 1;
      }
    }
  }

  .input-suffix {
    margin-left: 8px;
    color: #64748b;
    font-size: 13px;
  }

  // NTP操作按钮区域样式
  .ntp-actions {
    margin-top: 24px;
    padding-top: 16px;
    display: flex;
    gap: 12px;
    justify-content: center;
  }
}

// 全局表单样式优化
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.2s ease;
  min-height: 32px;

  &:hover {
    border-color: #3b82f6;
  }
}

:deep(.el-select .el-input__wrapper) {
  min-height: 32px;

  &:hover {
    border-color: #3b82f6;
  }
}

:deep(.el-date-editor.el-input__wrapper) {
  min-height: 32px;

  &:hover {
    border-color: #3b82f6;
  }
}

:deep(.el-input-number) {
  .el-input__wrapper {
    min-height: 32px;
  }
}

:deep(.el-switch) {
  &.is-checked .el-switch__core {
    background-color: #3b82f6;
  }
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  font-size: 13px;
  padding: 8px 16px;
  height: auto;
  transition: all 0.2s ease;

  &:hover:not(.is-disabled):not(:disabled) {
    transform: translateY(-1px);
  }

  // 添加服务器按钮禁用时的样式
  &.is-disabled {
    background-color: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    color: #bfbfbf !important;
    cursor: not-allowed !important;

    &:hover {
      transform: none !important;
      background-color: #f5f5f5 !important;
      border-color: #d9d9d9 !important;
      color: #bfbfbf !important;
    }
  }
}

:deep(.el-button--primary) {
  background: #3b82f6;
  border-color: #3b82f6;
  
  &:hover {
    background: #2563eb;
    border-color: #2563eb;
  }
}

:deep(.el-button--danger) {
  background: #ef4444;
  border-color: #ef4444;
  
  &:hover {
    background: #dc2626;
    border-color: #dc2626;
  }
}

:deep(.el-input__inner) {
  font-size: 13px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

@media (max-width: 640px) {
  .time-container {
    padding: 16px;
  }
  
  .block-content {
    padding: 16px;
  }
}

.form-block {
  margin-bottom: 32px;
}
</style>
