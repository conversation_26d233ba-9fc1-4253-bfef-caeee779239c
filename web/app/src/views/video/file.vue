<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-card class="mb-4">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="文件名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入文件名称" clearable />
        </el-form-item>
        <el-form-item label="文件ID" prop="camera_id">
          <el-input v-model="queryParams.camera_id" placeholder="请输入文件ID" clearable />
        </el-form-item>
        <el-form-item label="IP地址" prop="camera_ip">
          <el-input v-model="queryParams.camera_ip" placeholder="请输入IP地址" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon class="mr-1"><i-ep-search /></el-icon>查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon class="mr-1"><i-ep-refresh /></el-icon>重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="mb-4 flex justify-between">
      <el-button type="primary" @click="handleAddFile">
        <el-icon class="mr-1"><i-ep-plus /></el-icon>添加视频文件
      </el-button>
    </div>

    <!-- 视频文件列表 -->
    <el-table v-loading="loading" :data="fileList" border style="width: 100%">
      <el-table-column prop="name" label="文件名称" min-width="120" />
      <el-table-column prop="url" label="流地址" min-width="240" show-overflow-tooltip />
      <el-table-column prop="codec" label="编码格式" width="100" />
      <el-table-column label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'online' ? 'success' : 'danger'">
            {{ scope.row.status === 'online' ? '在线' : '离线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="150" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <div class="flex space-x-2">
            <el-tooltip content="编辑" placement="top">
              <el-button type="primary" circle @click="handleEdit(scope.row)">
                <el-icon><i-ep-edit /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button type="danger" circle @click="handleDelete(scope.row)">
                <el-icon><i-ep-delete /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="检测状态" placement="top">
              <el-button type="info" circle @click="handleCheckStatus(scope.row)">
                <el-icon><i-ep-connection /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑视频文件对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加视频文件' : '编辑视频文件'"
      width="800px"
    >
      <el-form
        ref="fileFormRef"
        :model="fileForm"
        :rules="rules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文件名称" prop="name">
              <el-input v-model="fileForm.name" placeholder="请输入文件名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件ID" prop="camera_id">
              <el-input v-model="fileForm.camera_id" placeholder="请输入文件ID" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="上传文件" prop="file">
          <el-upload
            class="upload-demo"
            action="#"
            :http-request="handleFileUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="uploadFileList"
            :on-remove="handleRemove"
            :on-change="handleFileChange"
            :auto-upload="false"
            accept=".mp4,.avi,.mov,.mkv,.flv"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                仅支持 mp4/avi/mov/mkv/flv 格式视频文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="flex justify-end">
              <el-button type="primary" @click="handleGenerateUrl" :disabled="!hasSelectedFile">
                生成视频流地址
              </el-button>
            </div>
          </el-col>
        </el-row>
        
        <el-form-item label="视频流地址" prop="url">
          <el-input v-model="fileForm.url" placeholder="视频流地址" readonly />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="fileForm.description"
            type="textarea"
            placeholder="请输入描述信息"
          />
        </el-form-item>

        <!-- 算法模型 -->
        <el-divider content-position="left">算法模型</el-divider>
        <el-row :gutter="20">
          <el-col :span="24" style="padding-left: 25px;">
            <div class="flex mb-4">
              <el-check-tag
                v-for="algorithm in algorithmList"
                :key="algorithm.id"
                :checked="fileForm.selectedAlgorithms.includes(algorithm.id.toString())"
                class="algorithm-tag mr-4"
                type="primary"
                @change="(checked) => handleAlgorithmChange(algorithm.id.toString(), checked)"
              >
                {{ algorithm.name }}
              </el-check-tag>
            </div>
            <div v-if="algorithmList.length === 0" class="text-center text-gray-400 py-4">
              暂无可用算法
            </div>
          </el-col>
        </el-row>

        <!-- 算法参数 -->
        <el-divider content-position="left">算法参数</el-divider>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="algorithm-params-container p-4 rounded mb-4">
              <div v-if="fileForm.selectedAlgorithms && fileForm.selectedAlgorithms.length > 0">
                <el-tabs v-model="activeAlgorithmTab">
                  <el-tab-pane
                    v-for="algorithmId in fileForm.selectedAlgorithms"
                    :key="algorithmId"
                    :label="getAlgorithmName(algorithmId)"
                    :name="algorithmId"
                  >
                    <div class="algorithm-params">
                      <div class="flex justify-between mb-4">
                        <div class="flex">
                          <el-button type="primary" size="small" class="mr-2" @click="handleDrawArea(algorithmId)">绘制区域</el-button>
                          <el-button type="info" size="small" @click="handleViewArea(algorithmId)">查看区域</el-button>
                        </div>
                        <el-button type="default" size="small" circle @click="showAlgorithmSettings(algorithmId)">
                          <el-icon><i-ep-setting /></el-icon>
                        </el-button>
                      </div>
                      <el-row :gutter="20">
                        <el-col :span="10">
                          <el-form-item :label="'告警间隔(秒)'" :prop="'algorithm_params.' + algorithmId + '.alert_interval'" label-width="120px">
                            <el-input-number
                              :model-value="getAlgorithmParam(algorithmId, 'alert_interval')"
                              @update:model-value="(val) => setAlgorithmParam(algorithmId, 'alert_interval', val)"
                              :min="1"
                              :max="3600"
                              :controls="false"
                              style="width: 100%;"
                            />
                          </el-form-item>
                          <el-form-item :label="'告警窗口长度(秒)'" :prop="'algorithm_params.' + algorithmId + '.alert_window'" label-width="120px">
                            <el-input-number
                              :model-value="getAlgorithmParam(algorithmId, 'alert_window')"
                              @update:model-value="(val) => setAlgorithmParam(algorithmId, 'alert_window', val)"
                              :min="1"
                              :max="3600"
                              :controls="false"
                              style="width: 100%;"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="10">
                          <el-form-item :label="'告警阈值'" :prop="'algorithm_params.' + algorithmId + '.alert_threshold'" label-width="120px">
                            <el-input-number
                              :model-value="getAlgorithmParam(algorithmId, 'alert_threshold')"
                              @update:model-value="(val) => setAlgorithmParam(algorithmId, 'alert_threshold', val)"
                              :min="0"
                              :max="1"
                              :step="0.01"
                              :precision="2"
                              :controls="false"
                              style="width: 100%;"
                            />
                          </el-form-item>
                          <el-form-item :label="'危险等级'" :prop="'algorithm_params.' + algorithmId + '.danger_level'" label-width="120px">
                            <el-select
                              :model-value="getAlgorithmParam(algorithmId, 'danger_level')"
                              @update:model-value="(val) => setAlgorithmParam(algorithmId, 'danger_level', val)"
                              placeholder="请选择危险等级"
                              class="w-full"
                            >
                              <el-option label="低" value="low" />
                              <el-option label="中" value="medium" />
                              <el-option label="高" value="high" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
              <div v-else class="text-center py-8 text-gray-400">
                请先选择算法模型
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitFileForm" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 算法设置对话框 -->
    <el-dialog v-model="settingsDialogVisible" :title="`算法${currentSettingAlgorithm}高级设置`" width="600px">
      <div class="p-4">
        <p class="text-center text-gray-400 py-8">此处为算法高级设置预留区域</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="settingsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="settingsDialogVisible = false">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 绘制区域对话框 -->
    <el-dialog v-model="drawAreaDialogVisible" title="绘制检测区域" width="800px">
      <div class="p-4">
        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-2">请在下方画布中绘制检测区域：</p>
          <div class="flex space-x-2">
            <el-button type="primary" size="small" @click="startDrawing">开始绘制</el-button>
            <el-button type="warning" size="small" @click="clearDrawArea">清空</el-button>
            <el-button type="success" size="small" @click="saveDrawArea">保存</el-button>
          </div>
        </div>
        <div class="draw-area-container">
          <canvas
            ref="drawCanvas"
            width="600"
            height="400"
            class="border border-gray-300 cursor-crosshair"
            @click="handleCanvasClick"
          ></canvas>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="drawAreaDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDrawArea">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看区域对话框 -->
    <el-dialog v-model="viewAreaDialogVisible" title="查看检测区域" width="800px">
      <div class="p-4">
        <div class="view-area-container">
          <canvas
            ref="viewCanvas"
            width="600"
            height="400"
            class="border border-gray-300"
          ></canvas>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewAreaDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import VideoAPI from '@/api/video';
import AlgorithmAPI from '@/api/algorithm';
import BindingAPI from '@/api/binding';
import type { AxiosResponse } from 'axios';

// 查询参数
const queryParams = reactive({
  page: 1,
  limit: 10,
  type: 3, // 视频文件类型
  name: '', // 文件名称
  camera_id: '', // 文件ID
  camera_ip: '' // IP地址
});

// 列表数据
const fileList = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);

// 表单相关
const dialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const fileFormRef = ref<FormInstance>();
const queryFormRef = ref<FormInstance>();
const submitting = ref(false);
const uploadFileList = ref<any[]>([]);
const fileForm = ref({
  id: undefined as number | undefined,
  name: '',
  type: 3, // 默认为视频文件类型
  protocol: 'file',
  description: '',
  camera_id: '',
  url: '',
  codec: 'H.264',
  status: 'online',
  file: null as File | null,
  selectedAlgorithms: [] as string[], // 新增多选算法模型字段
  activeAlgorithmTab: 'aaa', // 新增激活的算法参数tab
  algorithm_params: {} as Record<string, any> // 新增算法参数对象
});

// 是否已选择文件
const hasSelectedFile = computed(() => {
  return fileForm.value.file !== null || (dialogType.value === 'edit' && fileForm.value.url);
});

// 算法相关
const showDrawArea = ref(false);
const showViewArea = ref(false);
const activeAlgorithmTab = ref('aaa');
const settingsDialogVisible = ref(false);
const currentSettingAlgorithm = ref('');

// 绘制区域相关
const drawAreaDialogVisible = ref(false);
const viewAreaDialogVisible = ref(false);
const drawCanvas = ref<HTMLCanvasElement>();
const viewCanvas = ref<HTMLCanvasElement>();
const drawAreaData = ref<any[]>([]);
const isDrawing = ref(false);

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入文件名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述信息', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请生成视频流地址', trigger: 'blur' }
  ],
  selectedAlgorithms: [
    { required: false, message: '请至少选择一个算法模型', trigger: 'change' }
  ]
};

// 初始化
onMounted(() => {
  fetchFileList();
  fetchAlgorithmList(); // 获取算法列表
});

// 获取算法列表
const algorithmList = ref<any[]>([]);
const fetchAlgorithmList = async () => {
  try {
    const res = await AlgorithmAPI.getAlgorithms() as any;
    if (res.success) {
      // API返回的数据结构是 { data: { results: [...] } }
      algorithmList.value = res.data.results || [];
    } else {
      ElMessage.error(res.message || '获取算法列表失败');
    }
  } catch (error) {
    console.error('获取算法列表出错:', error);
    ElMessage.error('获取算法列表失败');
  }
};

// 显示算法设置对话框
const showAlgorithmSettings = (algorithm: string) => {
  currentSettingAlgorithm.value = algorithm;
  settingsDialogVisible.value = true;
};

// 选择算法
const handleAlgorithmChange = (algorithmId: string, checked: boolean) => {
  if (checked) {
    // 如果选中，则添加到选中列表
    if (!fileForm.value.selectedAlgorithms.includes(algorithmId)) {
      fileForm.value.selectedAlgorithms.push(algorithmId);
      // 初始化算法参数
      if (!fileForm.value.algorithm_params[algorithmId]) {
        fileForm.value.algorithm_params[algorithmId] = {
          alert_interval: 10,
          alert_window: 60,
          alert_threshold: 0.5,
          danger_level: 'medium',
          detection_area: '[]',
          extension_fields: '{}'
        };
      }
      // 如果是第一个选中的算法，设置为活动tab
      if (fileForm.value.selectedAlgorithms.length === 1) {
        activeAlgorithmTab.value = algorithmId;
      }
    }
  } else {
    // 如果取消选中，则从选中列表中移除
    const index = fileForm.value.selectedAlgorithms.indexOf(algorithmId);
    if (index !== -1) {
      fileForm.value.selectedAlgorithms.splice(index, 1);
      // 如果移除的是当前活动tab，则需要更新活动tab
      if (activeAlgorithmTab.value === algorithmId && fileForm.value.selectedAlgorithms.length > 0) {
        activeAlgorithmTab.value = fileForm.value.selectedAlgorithms[0];
      }
    }
  }
};

// 获取算法名称
const getAlgorithmName = (algorithmId: string) => {
  const algorithm = algorithmList.value.find(item => item.id.toString() === algorithmId);
  return algorithm ? algorithm.name : `算法${algorithmId}`;
};

// 获取算法参数
const getAlgorithmParam = (algorithmId: string, key: string) => {
  if (!fileForm.value.algorithm_params[algorithmId]) {
    fileForm.value.algorithm_params[algorithmId] = {
      alert_interval: 10,
      alert_window: 60,
      alert_threshold: 0.5,
      danger_level: 'medium',
      detection_area: '[]',
      extension_fields: '{}'
    };
  }
  return fileForm.value.algorithm_params[algorithmId][key];
};

// 设置算法参数
const setAlgorithmParam = (algorithmId: string, key: string, value: any) => {
  if (!fileForm.value.algorithm_params[algorithmId]) {
    fileForm.value.algorithm_params[algorithmId] = {
      alert_interval: 10,
      alert_window: 60,
      alert_threshold: 0.5,
      danger_level: 'medium',
      detection_area: '[]',
      extension_fields: '{}'
    };
  }
  fileForm.value.algorithm_params[algorithmId][key] = value;
};

// 获取视频文件列表
const fetchFileList = async () => {
  loading.value = true;
  try {
    const res = await VideoAPI.getVideos({
      page: queryParams.page,
      limit: queryParams.limit,
      type: queryParams.type, // 确保始终传递视频文件类型
      name: queryParams.name,
      camera_id: queryParams.camera_id,
      camera_ip: queryParams.camera_ip
    }) as any;
    if (res.success) {
      fileList.value = res.data.results || [];
      total.value = res.data.page?.total || 0;
    } else {
      ElMessage.error(res.message || '获取视频文件列表失败');
    }
  } catch (error) {
    console.error('获取视频文件列表出错:', error);
    ElMessage.error('获取视频文件列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  queryParams.limit = val;
  queryParams.page = 1;
  fetchFileList();
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val;
  fetchFileList();
};

// 添加视频文件
const handleAddFile = () => {
  dialogType.value = 'add';
  dialogVisible.value = true;
  uploadFileList.value = [];
  fileForm.value = {
    id: undefined,
    name: '',
    type: 3, // 默认为视频文件类型
    protocol: 'file',
    description: '',
    camera_id: '',
    url: '',
    codec: 'H.264',
    status: 'online',
    file: null,
    selectedAlgorithms: [], // 新增多选算法模型字段
    activeAlgorithmTab: 'aaa', // 新增激活的算法参数tab
    algorithm_params: {} // 新增算法参数对象
  };
};

// 编辑视频文件
const handleEdit = async (row: any) => {
  dialogType.value = 'edit';
  dialogVisible.value = true;
  uploadFileList.value = [];
  fileForm.value = {
    ...row,
    file: null,
    selectedAlgorithms: [],
    algorithm_params: {}
  };

  // 获取视频文件绑定的算法
  await fetchFileBindings(row.id);
};

// 获取视频文件绑定的算法
const fetchFileBindings = async (videoId: number) => {
  try {
    // 查询该视频文件绑定的所有算法
    const res = await VideoAPI.getVideoBindings(videoId) as any;
    if (res.success && res.data) {
      const bindings = Array.isArray(res.data) ? res.data : [];

      // 设置已选择的算法
      const selectedIds = bindings.map((binding: any) => binding.algorithm_id.toString());
      fileForm.value.selectedAlgorithms = selectedIds;

      // 设置算法参数
      bindings.forEach((binding: any) => {
        const algorithmId = binding.algorithm_id.toString();
        fileForm.value.algorithm_params[algorithmId] = {
          alert_interval: binding.alert_interval,
          alert_window: binding.alert_window,
          alert_threshold: binding.alert_threshold,
          danger_level: binding.danger_level,
          detection_area: binding.detection_area || '[]',
          extension_fields: binding.extension_fields || '{}'
        };
      });

      // 设置活动tab
      if (selectedIds.length > 0) {
        activeAlgorithmTab.value = selectedIds[0];
      }
    }
  } catch (error) {
    console.error('获取视频文件绑定算法出错:', error);
    ElMessage.error('获取视频文件绑定算法失败');
  }
};

// 删除视频文件
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该视频文件吗？删除后不可恢复', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await VideoAPI.deleteVideo(row.id) as any;
      if (res.success) {
        ElMessage.success('删除成功');
        fetchFileList();
      } else {
        ElMessage.error(res.message || '删除失败');
      }
    } catch (error) {
      console.error('删除视频文件出错:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    // 取消删除操作
  });
};

// 检测视频文件状态
const handleCheckStatus = async (row: any) => {
  try {
    const res = await VideoAPI.checkVideoStatus(row.id) as any;
    if (res.success) {
      const status = res.data?.status;
      ElMessage.success(`视频文件状态: ${status === 'online' ? '在线' : '离线'}`);
      // 更新列表中的状态
      const index = fileList.value.findIndex(item => item.id === row.id);
      if (index !== -1) {
        fileList.value[index].status = status;
      }
    } else {
      ElMessage.error(res.message || '检测失败');
    }
  } catch (error) {
    console.error('检测视频文件状态出错:', error);
    ElMessage.error('检测失败');
  }
};

// 处理文件上传
const handleFileUpload = async (options: any) => {
  const file = options.file;
  fileForm.value.file = file;
  
  // 如果文件名为空，则使用上传文件名
  if (!fileForm.value.name) {
    fileForm.value.name = file.name.split('.')[0];
  }
  
  // 自动生成视频流地址
  handleGenerateUrl();
};

// 处理文件超出限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个视频文件');
};

// 处理文件移除
const handleRemove = () => {
  fileForm.value.file = null;
  fileForm.value.url = '';
  uploadFileList.value = [];
};

// 处理文件变更
const handleFileChange = (file: any) => {
  if (file && file.raw) {
    fileForm.value.file = file.raw;
  }
};

// 生成视频流地址
const handleGenerateUrl = () => {
  if (!fileForm.value.file && dialogType.value === 'add') {
    ElMessage.warning('请先选择视频文件');
    return;
  }
  
  // 生成一个随机ID作为文件标识
  const fileId = fileForm.value.camera_id || `video_${new Date().getTime()}`;
  
  // 更新fileForm的camera_id
  if (!fileForm.value.camera_id) {
    fileForm.value.camera_id = fileId;
  }
  
  // 模拟生成视频流地址
  fileForm.value.url = `file://videos/${fileId}/${fileForm.value.file ? fileForm.value.file.name : 'video.mp4'}`;
  
  ElMessage.success('视频流地址生成成功');
};

// 提交视频文件表单
const submitFileForm = async () => {
  if (!fileFormRef.value) return;
  
  await fileFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    // 检查是否有文件或已有URL
    if (!fileForm.value.file && !fileForm.value.url && dialogType.value === 'add') {
      ElMessage.warning('请上传视频文件或生成视频流地址');
      return;
    }
    
    submitting.value = true;
    try {
      // 实际项目中，这里应该先上传文件，然后再提交表单
      // 模拟文件上传过程
      const formData = { ...fileForm.value };
      // 删除文件对象和算法参数，不需要发送到后端
      const { file, algorithm_params, selectedAlgorithms, activeAlgorithmTab, ...dataToSend } = formData;

      let res;
      let videoId;

      if (dialogType.value === 'add') {
        res = await VideoAPI.addVideo(dataToSend) as any;
        videoId = res.data?.id;
      } else {
        videoId = dataToSend.id;
        const { id: _, ...dataToUpdate } = dataToSend;
        res = await VideoAPI.updateVideo(videoId!, dataToUpdate) as any;
      }

      if (res.success) {
        // 无论是否有选择算法，都需要调用handleAlgorithmBindings
        // 如果selectedAlgorithms为空，则会清空所有绑定关系
        await handleAlgorithmBindings(videoId);

        ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功');
        dialogVisible.value = false;
        fetchFileList();
      } else {
        ElMessage.error(res.message || (dialogType.value === 'add' ? '添加失败' : '更新失败'));
      }
    } catch (error) {
      console.error(dialogType.value === 'add' ? '添加视频文件出错:' : '更新视频文件出错:', error);
      ElMessage.error(dialogType.value === 'add' ? '添加失败' : '更新失败');
    } finally {
      submitting.value = false;
    }
  });
};

// 处理算法绑定
const handleAlgorithmBindings = async (videoId: number) => {
  try {
    // 构建绑定数据
    const bindingData = fileForm.value.selectedAlgorithms.map(algorithmId => {
      const params = fileForm.value.algorithm_params[algorithmId];
      return {
        video_id: videoId,
        algorithm_id: parseInt(algorithmId),
        detection_area: params.detection_area || '[]',
        alert_interval: params.alert_interval || 10,
        alert_window: params.alert_window || 60,
        alert_threshold: params.alert_threshold || 0.5,
        voice_content: '检测到异常情况',
        danger_level: params.danger_level || 'medium',
        extension_fields: params.extension_fields || '{}'
      };
    });

    // 即使bindingData为空数组，也要调用API
    // 这样后端会清除所有现有的绑定关系
    const res = await VideoAPI.updateVideoBindings(videoId, bindingData) as any;
    if (!res.success) {
      ElMessage.warning('算法绑定部分失败，请在算法管理中检查');
    }
  } catch (error) {
    console.error('算法绑定出错:', error);
    ElMessage.warning('算法绑定失败，请在算法管理中手动绑定');
  }
};

// 绘制区域相关函数
const startDrawing = () => {
  isDrawing.value = true;
  drawAreaData.value = [];
};

const clearDrawArea = () => {
  drawAreaData.value = [];
  if (drawCanvas.value) {
    const ctx = drawCanvas.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, drawCanvas.value.width, drawCanvas.value.height);
    }
  }
};

const handleCanvasClick = (event: MouseEvent) => {
  if (!isDrawing.value || !drawCanvas.value) return;

  const rect = drawCanvas.value.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  drawAreaData.value.push({ x, y });

  const ctx = drawCanvas.value.getContext('2d');
  if (ctx) {
    ctx.fillStyle = 'red';
    ctx.beginPath();
    ctx.arc(x, y, 3, 0, 2 * Math.PI);
    ctx.fill();
  }
};

const saveDrawArea = () => {
  // 将绘制区域数据保存到算法参数中
  const currentAlgorithmId = activeAlgorithmTab.value;
  if (currentAlgorithmId) {
    setAlgorithmParam(currentAlgorithmId, 'detection_area', JSON.stringify(drawAreaData.value));
  }
  drawAreaDialogVisible.value = false;
  ElMessage.success('绘制区域已保存');
};

const handleDrawArea = (algorithmId: string) => {
  // 设置当前操作的算法ID
  activeAlgorithmTab.value = algorithmId;
  // 打开绘制区域对话框
  drawAreaDialogVisible.value = true;
};

const handleViewArea = (algorithmId: string) => {
  // 设置当前操作的算法ID
  activeAlgorithmTab.value = algorithmId;
  // 打开查看区域对话框
  viewAreaDialogVisible.value = true;
};

// 查询按钮
const handleQuery = () => {
  queryParams.page = 1;
  fetchFileList();
};

// 重置按钮
const handleReset = () => {
  queryFormRef.value?.resetFields();
  // 确保重置后仍保留视频文件类型
  queryParams.type = 3;
  handleQuery();
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.upload-demo {
  width: 100%;
}

.algorithm-params-container {
  min-height: 200px;
  background-color: #f8f9fa;
}

/* 自定义算法标签样式 */
.algorithm-tag {
  padding: 8px 16px;
  font-size: 14px;
  margin-right: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.algorithm-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.draw-area-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.view-area-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}
</style>