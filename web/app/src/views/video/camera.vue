<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-card class="mb-4">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="摄像头名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入摄像头名称" clearable />
        </el-form-item>
        <el-form-item label="摄像头ID" prop="camera_id">
          <el-input v-model="queryParams.camera_id" placeholder="请输入摄像头ID" clearable />
        </el-form-item>
        <el-form-item label="IP地址" prop="camera_ip">
          <el-input v-model="queryParams.camera_ip" placeholder="请输入IP地址" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon class="mr-1"><i-ep-search /></el-icon>查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon class="mr-1"><i-ep-refresh /></el-icon>重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="mb-4 flex justify-between">
      <el-button type="primary" @click="handleAddCamera">
        <el-icon class="mr-1"><i-ep-plus /></el-icon>添加摄像头
      </el-button>
    </div>

    <!-- 摄像头列表 -->
    <el-table v-loading="loading" :data="cameraList" border style="width: 100%">
      <el-table-column prop="name" label="名称" min-width="120" />
      <el-table-column prop="description" label="描述" min-width="150" />
      <el-table-column prop="camera_ip" label="IP地址" min-width="120" />
      <el-table-column prop="url" label="流地址" min-width="240" show-overflow-tooltip />
      <el-table-column prop="protocol" label="协议" width="80" />
      <el-table-column label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'online' ? 'success' : 'danger'">
            {{ scope.row.status === 'online' ? '在线' : '离线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <div class="flex space-x-2">
            <el-tooltip content="编辑" placement="top">
              <el-button type="primary" circle @click="handleEdit(scope.row)">
                <el-icon><i-ep-edit /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button type="danger" circle @click="handleDelete(scope.row)">
                <el-icon><i-ep-delete /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="检测状态" placement="top">
              <el-button type="info" circle @click="handleCheckStatus(scope.row)">
                <el-icon><i-ep-connection /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="抓拍" placement="top">
              <el-button type="success" circle @click="handleSnapshot(scope.row)">
                <el-icon><i-ep-camera /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑摄像头对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加摄像头' : '编辑摄像头'"
      width="800px"
    >
      <el-form
        ref="cameraFormRef"
        :model="cameraForm"
        :rules="rules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="协议" prop="protocol">
              <el-select v-model="cameraForm.protocol" placeholder="请选择协议" class="w-full">
                <el-option value="rtsp" label="RTSP" />
                <el-option value="onvif" label="ONVIF" />
                <el-option value="gb28181" label="GB28181" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="摄像头名称" prop="name">
              <el-input v-model="cameraForm.name" placeholder="请输入摄像头名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="摄像头ID" prop="camera_id">
              <el-input v-model="cameraForm.camera_id" placeholder="请输入摄像头ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="cameraForm.protocol === 'rtsp'">
            <el-form-item label="取流方式" prop="stream_type">
              <el-select v-model="cameraForm.stream_type" placeholder="请选择取流方式" class="w-full">
                <el-option value="自动生成" label="自动生成" />
                <el-option value="手动输入" label="手动输入" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 所有协议都需要的字段 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="IP地址" prop="camera_ip">
              <el-input v-model="cameraForm.camera_ip" placeholder="请输入IP地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="cameraForm.protocol === 'onvif'">
            <el-form-item label="端口" prop="port">
              <el-input-number v-model="cameraForm.port" :min="1" :max="65535" class="w-full" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- RTSP和ONVIF协议需要的账号密码 -->
        <template v-if="['rtsp', 'onvif'].includes(cameraForm.protocol)">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="cameraForm.username" placeholder="请输入用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="cameraForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>
        </template>
        
        <!-- RTSP协议特有的品牌和码流 -->
        <template v-if="cameraForm.protocol === 'rtsp'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-select v-model="cameraForm.brand" placeholder="请选择品牌" class="w-full">
                <el-option value="海康威视" label="海康威视" />
                <el-option value="大华" label="大华" />
                <el-option value="宇视" label="宇视" />
                <el-option value="华为" label="华为" />
                <el-option value="其他" label="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="选择码流" prop="stream_mode">
                <el-select v-model="cameraForm.stream_mode" placeholder="请选择码流" class="w-full">
                <el-option value="主码流" label="主码流" />
                <el-option value="子码流" label="子码流" />
              </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="视频流地址" prop="url">
              <el-input 
                v-model="cameraForm.url" 
                placeholder="请输入视频流地址" 
                :readonly="cameraForm.stream_type === '自动生成'"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="描述" prop="description">
              <el-input
                v-model="cameraForm.description"
                type="textarea"
                placeholder="请输入描述信息"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 算法模型 -->
        <el-divider content-position="left">算法模型</el-divider>
        <el-row :gutter="20">
          <el-col :span="24" style="padding-left: 25px;">
            <div class="flex mb-4">
              <el-check-tag
                v-for="algorithm in algorithmList"
                :key="algorithm.id"
                :checked="cameraForm.selectedAlgorithms.includes(algorithm.id.toString())"
                class="algorithm-tag mr-4"
                type="primary"
                @change="(checked) => handleAlgorithmChange(algorithm.id.toString(), checked)"
              >
                {{ algorithm.name }}
              </el-check-tag>
            </div>
            <div v-if="algorithmList.length === 0" class="text-center text-gray-400 py-4">
              暂无可用算法
            </div>
          </el-col>
        </el-row>

        <!-- 算法参数 -->
        <el-divider content-position="left">算法参数</el-divider>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="algorithm-params-container p-4 rounded mb-4">
              <div v-if="cameraForm.selectedAlgorithms && cameraForm.selectedAlgorithms.length > 0">
                <el-tabs v-model="activeAlgorithmTab">
                  <el-tab-pane 
                    v-for="algorithmId in cameraForm.selectedAlgorithms" 
                    :key="algorithmId" 
                    :label="getAlgorithmName(algorithmId)" 
                    :name="algorithmId"
                  >
                    <div class="algorithm-params">
                      <div class="flex justify-between mb-4">
                        <div class="flex">
                          <el-button type="primary" size="small" class="mr-2" @click="handleDrawArea(algorithmId)">绘制区域</el-button>
                          <el-button type="info" size="small" @click="handleViewArea(algorithmId)">查看区域</el-button>
                        </div>
                        <el-button type="default" size="small" circle @click="showAlgorithmSettings(algorithmId)">
                          <el-icon><i-ep-setting /></el-icon>
                        </el-button>
                      </div>
                      <el-row :gutter="20">
                        <el-col :span="10">
                          <el-form-item :label="'告警间隔(秒)'" :prop="'algorithm_params.' + algorithmId + '.alert_interval'" label-width="120px">
                            <el-input-number 
                              :model-value="getAlgorithmParam(algorithmId, 'alert_interval')" 
                              @update:model-value="(val) => setAlgorithmParam(algorithmId, 'alert_interval', val)"
                              :min="1" 
                              :max="3600" 
                              :controls="false" 
                              style="width: 100%;"
                            />
                          </el-form-item>
                          <el-form-item :label="'告警窗口长度(秒)'" :prop="'algorithm_params.' + algorithmId + '.alert_window'" label-width="120px">
                            <el-input-number 
                              :model-value="getAlgorithmParam(algorithmId, 'alert_window')" 
                              @update:model-value="(val) => setAlgorithmParam(algorithmId, 'alert_window', val)"
                              :min="1" 
                              :max="3600" 
                              :controls="false" 
                              style="width: 100%;"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="10">
                          <el-form-item :label="'告警阈值'" :prop="'algorithm_params.' + algorithmId + '.alert_threshold'" label-width="120px">
                            <el-input-number 
                              :model-value="getAlgorithmParam(algorithmId, 'alert_threshold')" 
                              @update:model-value="(val) => setAlgorithmParam(algorithmId, 'alert_threshold', val)"
                              :min="0" 
                              :max="1" 
                              :step="0.01" 
                              :precision="2" 
                              :controls="false" 
                              style="width: 100%;"
                            />
                          </el-form-item>
                          <el-form-item :label="'危险等级'" :prop="'algorithm_params.' + algorithmId + '.danger_level'" label-width="120px">
                            <el-select 
                              :model-value="getAlgorithmParam(algorithmId, 'danger_level')" 
                              @update:model-value="(val) => setAlgorithmParam(algorithmId, 'danger_level', val)"
                              placeholder="请选择危险等级" 
                              class="w-full"
                            >
                              <el-option label="低" value="low" />
                              <el-option label="中" value="medium" />
                              <el-option label="高" value="high" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
              <div v-else class="text-center py-8 text-gray-400">
                请先选择算法模型
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCameraForm" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 抓拍结果对话框 -->
    <el-dialog v-model="snapshotDialogVisible" title="抓拍结果" width="600px">
      <div class="flex justify-center">
        <el-image v-if="snapshotUrl" :src="snapshotUrl" fit="contain" style="max-height: 400px;" />
        <div v-else class="text-center">
          <el-icon class="text-6xl text-gray-400"><i-ep-picture /></el-icon>
          <p class="mt-2">暂无抓拍图片</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="snapshotDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="downloadSnapshot" v-if="snapshotUrl">
            下载
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 算法设置对话框 -->
    <el-dialog v-model="settingsDialogVisible" :title="`算法${currentSettingAlgorithm}高级设置`" width="600px">
      <div class="p-4">
        <p class="text-center text-gray-400 py-8">此处为算法高级设置预留区域</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="settingsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="settingsDialogVisible = false">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 绘制区域对话框 -->
    <el-dialog v-model="drawAreaDialogVisible" title="绘制检测区域" width="800px">
      <div class="p-4">
        <div class="relative mb-4" style="height: 450px; background-color: #f0f0f0;">
          <!-- 这里将来放置视频流画面 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <p class="text-gray-400">视频预览区域</p>
          </div>
          <!-- 绘制区域的控件将来放在这里 -->
        </div>
        <div class="mt-4">
          <p class="text-sm text-gray-500 mb-2">提示：点击视频区域添加点，形成多边形区域。双击结束绘制。</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="drawAreaDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDrawArea">
            保存区域
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看区域对话框 -->
    <el-dialog v-model="viewAreaDialogVisible" title="查看检测区域" width="800px">
      <div class="p-4">
        <div class="relative mb-4" style="height: 450px; background-color: #f0f0f0;">
          <!-- 这里将来放置视频流画面和已绘制的区域 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <p class="text-gray-400">视频预览区域</p>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewAreaDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import VideoAPI from '@/api/video';
import AlgorithmAPI from '@/api/algorithm';
import BindingAPI from '@/api/binding';

// 查询参数
const queryParams = reactive({
  page: 1,
  limit: 10,
  type: 1, // 摄像头类型
  name: '',
  camera_id: '',
  camera_ip: ''
});

// 列表数据
const cameraList = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);

// 表单相关
const dialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const cameraFormRef = ref<FormInstance>();
const queryFormRef = ref<FormInstance>();
const submitting = ref(false);
const cameraForm = ref({
  id: undefined as number | undefined,
  name: '',
  type: 1,
  protocol: 'rtsp',
  description: '',
  stream_type: '自动生成',
  camera_id: '', // 新增摄像头ID字段
  camera_ip: '',
  port: 80,
  username: '',
  password: '',
  brand: '海康威视',
  stream_mode: '主码流',
  url: '',
  status: 'online',
  selectedAlgorithms: [] as string[], // 新增多选算法模型字段
  activeAlgorithmTab: 'aaa', // 新增激活的算法参数tab
  algorithm_params: {} as Record<string, any> // 新增算法参数对象
});

// 抓拍相关
const snapshotDialogVisible = ref(false);
const snapshotUrl = ref('');

// 算法相关
const showDrawArea = ref(false);
const showViewArea = ref(false);
const activeAlgorithmTab = ref('aaa');
const settingsDialogVisible = ref(false);
const currentSettingAlgorithm = ref('');

// 绘制区域相关
const drawAreaDialogVisible = ref(false);
const viewAreaDialogVisible = ref(false);

// 显示算法设置对话框
const showAlgorithmSettings = (algorithm: string) => {
  currentSettingAlgorithm.value = algorithm;
  settingsDialogVisible.value = true;
};

// 选择算法
const handleAlgorithmChange = (algorithmId: string, checked: boolean) => {
  if (checked) {
    // 如果选中，则添加到选中列表
    if (!cameraForm.value.selectedAlgorithms.includes(algorithmId)) {
      cameraForm.value.selectedAlgorithms.push(algorithmId);
      // 初始化算法参数
      if (!cameraForm.value.algorithm_params[algorithmId]) {
        cameraForm.value.algorithm_params[algorithmId] = {
          alert_interval: 10,
          alert_window: 60,
          alert_threshold: 0.5,
          danger_level: 'medium',
          detection_area: '[]',
          extension_fields: '{}'
        };
      }
      // 如果是第一个选中的算法，设置为活动tab
      if (cameraForm.value.selectedAlgorithms.length === 1) {
        activeAlgorithmTab.value = algorithmId;
      }
    }
  } else {
    // 如果取消选中，则从选中列表中移除
    const index = cameraForm.value.selectedAlgorithms.indexOf(algorithmId);
    if (index !== -1) {
      cameraForm.value.selectedAlgorithms.splice(index, 1);
      // 如果移除的是当前活动tab，则需要更新活动tab
      if (activeAlgorithmTab.value === algorithmId && cameraForm.value.selectedAlgorithms.length > 0) {
        activeAlgorithmTab.value = cameraForm.value.selectedAlgorithms[0];
      }
    }
  }
};

// 获取算法名称
const getAlgorithmName = (algorithmId: string) => {
  const algorithm = algorithmList.value.find(item => item.id.toString() === algorithmId);
  return algorithm ? algorithm.name : `算法${algorithmId}`;
};

// 获取算法参数
const getAlgorithmParam = (algorithmId: string, key: string) => {
  if (!cameraForm.value.algorithm_params[algorithmId]) {
    cameraForm.value.algorithm_params[algorithmId] = {
      alert_interval: 10,
      alert_window: 60,
      alert_threshold: 0.5,
      danger_level: 'medium',
      detection_area: '[]',
      extension_fields: '{}'
    };
  }
  return cameraForm.value.algorithm_params[algorithmId][key];
};

// 设置算法参数
const setAlgorithmParam = (algorithmId: string, key: string, value: any) => {
  if (!cameraForm.value.algorithm_params[algorithmId]) {
    cameraForm.value.algorithm_params[algorithmId] = {
      alert_interval: 10,
      alert_window: 60,
      alert_threshold: 0.5,
      danger_level: 'medium',
      detection_area: '[]',
      extension_fields: '{}'
    };
  }
  cameraForm.value.algorithm_params[algorithmId][key] = value;
};

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入摄像头名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  protocol: [
    { required: true, message: '请选择协议', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入描述信息', trigger: 'blur' }
  ],
  camera_id: [
    { required: true, message: '请输入摄像头ID', trigger: 'blur' }
  ],
  selectedAlgorithms: [
    { required: false, message: '请至少选择一个算法模型', trigger: 'change' }
  ],
  stream_type: [
    { validator: (rule: any, value: any, callback: any) => {
      if (cameraForm.value.protocol === 'rtsp' && !value) {
        callback(new Error('请选择取流方式'));
      } else {
        callback();
      }
    }, trigger: 'change' }
  ],
  camera_ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  brand: [
    { required: true, message: '请选择品牌', trigger: 'change' }
  ],
  stream_mode: [
    { required: true, message: '请选择视频流模式', trigger: 'change' }
  ],
  url: [
    { required: true, message: '请输入视频流地址', trigger: 'blur' },
    { validator: (rule: any, value: any, callback: any) => {
      if (cameraForm.value.stream_type === '自动生成' && !value) {
        // 自动生成模式下，尝试重新生成URL
        generateUrl();
        if (!cameraForm.value.url) {
          callback(new Error('无法自动生成视频流地址，请检查相关字段'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ]
};

// 初始化
onMounted(() => {
  fetchCameraList();
  fetchAlgorithmList(); // 获取算法列表
});

// 监听流地址类型变化
watch(() => cameraForm.value.stream_type, (newType) => {
  if (newType === '自动生成') {
    generateUrl();
  } else if (newType === '手动输入') {
    // 清空URL，让用户手动输入
    cameraForm.value.url = '';
  }
});

// 监听相关字段变化，自动更新URL
watch([
  () => cameraForm.value.protocol,
  () => cameraForm.value.camera_ip,
  () => cameraForm.value.username,
  () => cameraForm.value.password,
  () => cameraForm.value.brand,
  () => cameraForm.value.stream_mode
], () => {
  if (cameraForm.value.stream_type === '自动生成') {
    generateUrl();
  }
});

// 生成URL
const generateUrl = () => {
  const { protocol, camera_ip, port, username, password, brand, stream_mode, camera_id } = cameraForm.value;
  if (protocol === 'rtsp') {
    // 根据不同品牌生成不同的RTSP地址
    if (brand === '海康威视') {
      const streamType = stream_mode === '主码流' ? '1' : '2';
      cameraForm.value.url = `rtsp://${username}:${password}@${camera_ip}:554/h264/ch1/${streamType}/av_stream`;
    } else if (brand === '大华') {
      const streamType = stream_mode === '主码流' ? '0' : '1';
      cameraForm.value.url = `rtsp://${username}:${password}@${camera_ip}:554/cam/realmonitor?channel=1&subtype=${streamType}`;
    } else {
      // 通用RTSP地址，添加摄像头ID作为标识
      cameraForm.value.url = `rtsp://${username}:${password}@${camera_ip}:554/${camera_id || 'stream'}`;
    }
  } else if (protocol === 'onvif') {
    // ONVIF地址
    cameraForm.value.url = `onvif://${username}:${password}@${camera_ip}:${port}/${camera_id || ''}`;
  } else if (protocol === 'gb28181') {
    // GB28181地址
    cameraForm.value.url = `gb28181://${camera_ip}/${camera_id || ''}`;
  }
};

// 获取摄像头列表
const fetchCameraList = async () => {
  loading.value = true;
  try {
    const res = await VideoAPI.getVideos({
      page: queryParams.page,
      limit: queryParams.limit,
      type: queryParams.type, // 确保始终传递类型字段
      name: queryParams.name,
      camera_id: queryParams.camera_id,
      camera_ip: queryParams.camera_ip
    }) as any;
    if (res.success) {
      cameraList.value = res.data.results || [];
      total.value = res.data.page?.total || 0;
    } else {
      ElMessage.error(res.message || '获取摄像头列表失败');
    }
  } catch (error) {
    console.error('获取摄像头列表出错:', error);
    ElMessage.error('获取摄像头列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取算法列表
const algorithmList = ref<any[]>([]);
const fetchAlgorithmList = async () => {
  try {
    const res = await AlgorithmAPI.getAlgorithms() as any;
    if (res.success) {
      // API返回的数据结构是 { data: { results: [...] } }
      algorithmList.value = res.data.results || [];
    } else {
      ElMessage.error(res.message || '获取算法列表失败');
    }
  } catch (error) {
    console.error('获取算法列表出错:', error);
    ElMessage.error('获取算法列表失败');
  }
};

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  queryParams.limit = val;
  queryParams.page = 1;
  fetchCameraList();
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val;
  fetchCameraList();
};

// 添加摄像头
const handleAddCamera = () => {
  dialogType.value = 'add';
  dialogVisible.value = true;
  cameraForm.value = {
    id: undefined,
    name: '',
    type: 1, // 默认为摄像头类型
    protocol: 'rtsp',
    description: '',
    stream_type: '自动生成',
    camera_id: '', // 摄像头ID
    camera_ip: '',
    port: 80,
    username: '',
    password: '',
    brand: '海康威视',
    stream_mode: '主码流',
    url: '',
    status: 'online',
    selectedAlgorithms: [], // 新增多选算法模型字段
    activeAlgorithmTab: 'aaa', // 新增激活的算法参数tab
    algorithm_params: {} // 新增算法参数对象
  };
  
  // 初始化时生成URL
  setTimeout(() => {
    generateUrl();
  }, 0);
};

// 编辑摄像头
const handleEdit = async (row: any) => {
  dialogType.value = 'edit';
  dialogVisible.value = true;
  cameraForm.value = { 
    ...row,
    selectedAlgorithms: [],
    algorithm_params: {}
  };
  
  // 获取摄像头绑定的算法
  await fetchCameraBindings(row.id);
  
  // 如果是自动生成模式，确保URL是最新的
  if (cameraForm.value.stream_type === '自动生成') {
    setTimeout(() => {
      generateUrl();
    }, 0);
  }
};

// 获取摄像头绑定的算法
const fetchCameraBindings = async (videoId: number) => {
  try {
    // 查询该摄像头绑定的所有算法
    const res = await VideoAPI.getVideoBindings(videoId) as any;
    if (res.success && res.data) {
      const bindings = Array.isArray(res.data) ? res.data : [];
      
      // 设置已选择的算法
      const selectedIds = bindings.map((binding: any) => binding.algorithm_id.toString());
      cameraForm.value.selectedAlgorithms = selectedIds;
      
      // 设置算法参数
      bindings.forEach((binding: any) => {
        const algorithmId = binding.algorithm_id.toString();
        cameraForm.value.algorithm_params[algorithmId] = {
          alert_interval: binding.alert_interval,
          alert_window: binding.alert_window,
          alert_threshold: binding.alert_threshold,
          danger_level: binding.danger_level,
          detection_area: binding.detection_area || '[]',
          extension_fields: binding.extension_fields || '{}'
        };
      });
      
      // 设置活动tab
      if (selectedIds.length > 0) {
        activeAlgorithmTab.value = selectedIds[0];
      }
    }
  } catch (error) {
    console.error('获取摄像头绑定算法出错:', error);
    ElMessage.error('获取摄像头绑定算法失败');
  }
};

// 删除摄像头
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该摄像头吗？删除后不可恢复', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await VideoAPI.deleteVideo(row.id) as any;
      if (res.success) {
        ElMessage.success('删除成功');
        fetchCameraList();
      } else {
        ElMessage.error(res.message || '删除失败');
      }
    } catch (error) {
      console.error('删除摄像头出错:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    // 取消删除操作
  });
};

// 检测摄像头状态
const handleCheckStatus = async (row: any) => {
  try {
    const res = await VideoAPI.checkVideoStatus(row.id) as any;
    if (res.success) {
      const status = res.data?.status;
      ElMessage.success(`摄像头状态: ${status === 'online' ? '在线' : '离线'}`);
      // 更新列表中的状态
      const index = cameraList.value.findIndex(item => item.id === row.id);
      if (index !== -1) {
        cameraList.value[index].status = status;
      }
    } else {
      ElMessage.error(res.message || '检测失败');
    }
  } catch (error) {
    console.error('检测摄像头状态出错:', error);
    ElMessage.error('检测失败');
  }
};

// 抓拍
const handleSnapshot = async (row: any) => {
  try {
    const res = await VideoAPI.captureSnapshot(row.id) as any;
    if (res.success && res.data) {
      snapshotUrl.value = res.data.image_url || '';
      snapshotDialogVisible.value = true;
    } else {
      ElMessage.error(res.message || '抓拍失败');
    }
  } catch (error) {
    console.error('抓拍出错:', error);
    ElMessage.error('抓拍失败');
  }
};

// 下载抓拍图片
const downloadSnapshot = () => {
  if (snapshotUrl.value) {
    const link = document.createElement('a');
    link.href = snapshotUrl.value;
    link.download = `snapshot_${new Date().getTime()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// 提交摄像头表单
const submitCameraForm = async () => {
  if (!cameraFormRef.value) return;
  
  await cameraFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    submitting.value = true;
    try {
      const formData: any = { ...cameraForm.value };
      delete formData.algorithm_params; // 不需要将算法参数一起提交到摄像头接口
      
      let res;
      let videoId;
      
      if (dialogType.value === 'add') {
        res = await VideoAPI.addVideo(formData) as any;
        if (res.success) {
          videoId = res.data.id;
        }
      } else {
        videoId = formData.id;
        delete formData.id;
        res = await VideoAPI.updateVideo(videoId!, formData) as any;
      }
      
      if (res.success) {
        // 无论是否有选择算法，都需要调用handleAlgorithmBindings
        // 如果selectedAlgorithms为空，则会清空所有绑定关系
        await handleAlgorithmBindings(videoId);
        
        ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功');
        dialogVisible.value = false;
        fetchCameraList();
      } else {
        ElMessage.error(res.message || (dialogType.value === 'add' ? '添加失败' : '更新失败'));
      }
    } catch (error) {
      console.error(dialogType.value === 'add' ? '添加摄像头出错:' : '更新摄像头出错:', error);
      ElMessage.error(dialogType.value === 'add' ? '添加失败' : '更新失败');
    } finally {
      submitting.value = false;
    }
  });
};

// 处理算法绑定
const handleAlgorithmBindings = async (videoId: number) => {
  try {
    // 构建绑定数据
    const bindingData = cameraForm.value.selectedAlgorithms.map(algorithmId => {
      const params = cameraForm.value.algorithm_params[algorithmId];
      return {
        video_id: videoId,
        algorithm_id: parseInt(algorithmId),
        detection_area: params.detection_area || '[]',
        alert_interval: params.alert_interval || 10,
        alert_window: params.alert_window || 60,
        alert_threshold: params.alert_threshold || 0.5,
        voice_content: '检测到异常情况',
        danger_level: params.danger_level || 'medium',
        extension_fields: params.extension_fields || '{}'
      };
    });
    
    // 即使bindingData为空数组，也要调用API
    // 这样后端会清除所有现有的绑定关系
    const res = await VideoAPI.updateVideoBindings(videoId, bindingData) as any;
    if (!res.success) {
      ElMessage.warning('算法绑定部分失败，请在算法管理中检查');
    }
  } catch (error) {
    console.error('算法绑定出错:', error);
    ElMessage.warning('算法绑定失败，请在算法管理中手动绑定');
  }
};

// 查询按钮
const handleQuery = () => {
  queryParams.page = 1;
  fetchCameraList();
};

// 重置按钮
const handleReset = () => {
  queryFormRef.value?.resetFields();
  // 确保重置后仍保留摄像头类型
  queryParams.type = 1;
  handleQuery();
};

// 绘制区域保存
const saveDrawArea = () => {
  // 将绘制区域数据保存到算法参数中
  const currentAlgorithmId = activeAlgorithmTab.value;
  if (currentAlgorithmId) {
    setAlgorithmParam(currentAlgorithmId, 'detection_area', JSON.stringify(drawAreaData.value));
  }
  drawAreaDialogVisible.value = false;
  ElMessage.success('绘制区域已保存');
};

// 绘制区域数据
const drawAreaData = ref<any[]>([]);

// 绘制区域对话框打开时，清空数据
watch(drawAreaDialogVisible, (newVal) => {
  if (newVal) {
    drawAreaData.value = [];
  }
});

// 绘制区域对话框关闭时，保存数据
watch(drawAreaDialogVisible, (newVal) => {
  if (!newVal) {
    saveDrawArea();
  }
});

// 绘制区域点击事件
const handleDrawArea = (algorithmId: string) => {
  // 设置当前操作的算法ID
  activeAlgorithmTab.value = algorithmId;
  // 打开绘制区域对话框
  drawAreaDialogVisible.value = true;
};

// 查看区域点击事件
const handleViewArea = (algorithmId: string) => {
  // 设置当前操作的算法ID
  activeAlgorithmTab.value = algorithmId;
  // 打开查看区域对话框
  viewAreaDialogVisible.value = true;
};

// 绘制区域画布点击事件
const handleDrawAreaClick = (event: MouseEvent) => {
  const rect = (event.target as HTMLElement).getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;
  drawAreaData.value.push({ x, y });
};

// 绘制区域双击事件
const handleDrawAreaDblClick = () => {
  // 双击结束绘制，将多边形闭合
  if (drawAreaData.value.length > 0) {
    drawAreaData.value.push(drawAreaData.value[0]); // 将最后一个点连接到第一个点
  }
};

// 查看区域对话框打开时，清空数据
watch(viewAreaDialogVisible, (newVal) => {
  if (newVal) {
    // 从算法参数中获取已绘制的区域
    const currentAlgorithmId = activeAlgorithmTab.value;
    if (currentAlgorithmId) {
      const params = getAlgorithmParam(currentAlgorithmId, 'detection_area');
      if (params && params !== '[]') {
        try {
          drawAreaData.value = JSON.parse(params);
        } catch (e) {
          console.error('解析检测区域数据失败:', e);
          drawAreaData.value = [];
        }
      } else {
        drawAreaData.value = [];
      }
    }
  }
});

// 查看区域对话框关闭时，保存数据
watch(viewAreaDialogVisible, (newVal) => {
  if (!newVal) {
    // 在关闭时，如果数据有变化，则保存
    const currentAlgorithmId = activeAlgorithmTab.value;
    if (currentAlgorithmId) {
      const params = getAlgorithmParam(currentAlgorithmId, 'detection_area');
      if (params && params !== '[]') {
        try {
          const currentDrawAreaData = JSON.parse(params);
          if (JSON.stringify(currentDrawAreaData) !== JSON.stringify(drawAreaData.value)) {
            saveDrawArea();
          }
        } catch (e) {
          console.error('解析检测区域数据失败:', e);
        }
      }
    }
  }
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.algorithm-params-container {
  min-height: 200px;
  background-color: #f8f9fa;
}

/* 自定义算法标签样式 */
.algorithm-tag {
  padding: 8px 16px;
  font-size: 14px;
  margin-right: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

/* 自定义Tab页签样式 */
:deep(.el-tabs--card > .el-tabs__header) {
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  border: 1px solid #e4e7ed;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  margin-right: 4px;
  transition: all 0.3s;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}
</style> 