# 流程图与配置文件不符问题修复

## 🔍 **问题描述**

展示的管道流程图和管道配置文件中描述的不符合：

### **实际配置文件包含**：
1. **video_src_1** (vp_rtsp_src_node) - 视频源
2. **detector_2** (vp_generic_detector_node) - 通用检测器
3. **osd_display** (vp_osd_node) - OSD显示
4. **alert_handler** (vp_alert_handler_node) - 告警处理
5. **screen_output** (vp_screen_des_node) - 屏幕输出

### **但流程图显示的是**：
1. **rtsp_src_0** (vp_rtsp_src_node) - 固定模拟数据
2. **face_detector_0** (vp_yunet_face_detector_node) - 固定模拟数据
3. **osd_0** (vp_face_osd_node_v2) - 固定模拟数据

## 🔧 **根本原因**

流程图组件显示的是 `generateMockNodes()` 生成的固定模拟数据，而不是从实际的管道配置文件中读取的节点信息。

## 🛠️ **解决方案**

### 1. **修改本地客户端，从配置文件生成节点信息**

#### 新增 `generateNodesFromConfig` 方法
```go
// generateNodesFromConfig 从配置文件生成节点数据
func (c *LocalPipelineClient) generateNodesFromConfig(config *CompletePipelineConfig) string {
    if config == nil || len(config.Nodes) == 0 {
        return c.generateMockNodes()
    }

    nodes := make([]map[string]interface{}, 0, len(config.Nodes))
    
    for _, node := range config.Nodes {
        nodeData := map[string]interface{}{
            "id":      node.ID,
            "type":    node.Type,
            "state":   "created", // 初始状态
            "params":  node.Params,
            "metrics": c.generateNodeMetrics(node.Type),
        }
        nodes = append(nodes, nodeData)
    }

    nodesData, _ := json.Marshal(nodes)
    return string(nodesData)
}
```

#### 新增 `generateRunningNodesFromConfig` 方法
```go
// generateRunningNodesFromConfig 从配置文件生成运行中的节点数据
func (c *LocalPipelineClient) generateRunningNodesFromConfig(config *CompletePipelineConfig) string {
    // 类似上面，但状态设为 "running"
}
```

#### 新增 `generateNodeMetrics` 方法
```go
// generateNodeMetrics 根据节点类型生成相应的指标数据
func (c *LocalPipelineClient) generateNodeMetrics(nodeType string) map[string]interface{} {
    switch nodeType {
    case "vp_rtsp_src_node":
        return map[string]interface{}{
            "fps": 25.3,
            "frames_processed": 91825,
            "queue_size": 2,
        }
    case "vp_generic_detector_node":
        return map[string]interface{}{
            "detection_time_ms": 12.5,
            "detections_count": 3,
            "inference_fps": 24.8,
        }
    case "vp_osd_node":
        return map[string]interface{}{
            "render_time_ms": 3.1,
        }
    case "vp_alert_handler_node":
        return map[string]interface{}{
            "alerts_sent": 5,
            "last_alert": "2025-07-28T22:00:00Z",
        }
    case "vp_screen_des_node":
        return map[string]interface{}{
            "display_fps": 30.0,
            "resolution": "1920x1080",
        }
    default:
        return map[string]interface{}{
            "status": "active",
        }
    }
}
```

### 2. **修改 CreatePipeline 方法**

```go
// 修改前：使用固定模拟数据
Nodes: "[]",

// 修改后：从配置文件生成节点信息
Nodes: c.generateNodesFromConfig(config),
```

### 3. **修改 StartPipeline 方法**

```go
// 修改前：使用固定模拟数据
Nodes: c.generateMockNodes(),

// 修改后：从配置文件生成运行中的节点信息
// 1. 读取配置文件
configData, err := ioutil.ReadFile(configPath)
var config CompletePipelineConfig
json.Unmarshal(configData, &config)

// 2. 生成运行中的节点
nodes := c.generateRunningNodesFromConfig(&config)
```

## 🧪 **测试验证**

### 测试结果
```
=== 测试真实配置文件的节点生成 ===
✅ 管道状态: created
✅ 解析后的节点数量: 5

📊 节点 1: ID=video_src_1, Type=vp_rtsp_src_node, State=created
📊 节点 2: ID=detector_2, Type=vp_generic_detector_node, State=created  
📊 节点 3: ID=osd_display, Type=vp_osd_node, State=created
📊 节点 4: ID=alert_handler, Type=vp_alert_handler_node, State=created
📊 节点 5: ID=screen_output, Type=vp_screen_des_node, State=created

🚀 启动管道...
✅ 启动后节点数量: 5
📊 节点 1: ID=video_src_1, Type=vp_rtsp_src_node, State=running
📊 节点 2: ID=detector_2, Type=vp_generic_detector_node, State=running  
📊 节点 3: ID=osd_display, Type=vp_osd_node, State=running
📊 节点 4: ID=alert_handler, Type=vp_alert_handler_node, State=running  
📊 节点 5: ID=screen_output, Type=vp_screen_des_node, State=running
```

## 📊 **修复前后对比**

### 修复前
```json
{
  "nodes": "[{\"id\":\"rtsp_src_0\",\"type\":\"vp_rtsp_src_node\",...},{\"id\":\"face_detector_0\",\"type\":\"vp_yunet_face_detector_node\",...},{\"id\":\"osd_0\",\"type\":\"vp_face_osd_node_v2\",...}]"
}
```

### 修复后
```json
{
  "nodes": "[{\"id\":\"video_src_1\",\"type\":\"vp_rtsp_src_node\",...},{\"id\":\"detector_2\",\"type\":\"vp_generic_detector_node\",...},{\"id\":\"osd_display\",\"type\":\"vp_osd_node\",...},{\"id\":\"alert_handler\",\"type\":\"vp_alert_handler_node\",...},{\"id\":\"screen_output\",\"type\":\"vp_screen_des_node\",...}]"
}
```

## 🎯 **预期效果**

修复后，前端流程图应该显示：

1. **video_src_1** - 视频源节点
   - 类型：vp_rtsp_src_node
   - 参数：RTSP URL、用户名、密码等

2. **detector_2** - 通用检测器节点
   - 类型：vp_generic_detector_node
   - 参数：置信度阈值、模型路径等

3. **osd_display** - OSD显示节点
   - 类型：vp_osd_node
   - 参数：字体颜色、字体大小等

4. **alert_handler** - 告警处理节点
   - 类型：vp_alert_handler_node
   - 参数：告警端点、危险级别等

5. **screen_output** - 屏幕输出节点
   - 类型：vp_screen_des_node
   - 参数：通道索引等

## 🔗 **连接关系**

根据配置文件，节点间的连接关系应该是：
```
video_src_1 → detector_2 → osd_display → alert_handler
                                      → screen_output
```

## ✅ **修复的文件**

1. **`internal/app/pipeline/local_client.go`**
   - 新增 `generateNodesFromConfig` 方法
   - 新增 `generateRunningNodesFromConfig` 方法
   - 新增 `generateNodeMetrics` 方法
   - 修改 `CreatePipeline` 方法使用配置文件节点
   - 修改 `StartPipeline` 方法读取配置文件

## 🚀 **下一步**

1. **重新部署管道**：通过前端重新部署综合管道
2. **验证流程图**：检查前端是否显示正确的5个节点
3. **测试连接关系**：验证节点间的连接是否正确
4. **节点交互**：测试点击节点查看详情功能

这次修复确保了流程图显示的节点信息与实际的管道配置文件完全一致，解决了数据不匹配的问题。
