# 管道连接线缺失问题修复

## 🔍 **问题描述**

流程图显示了正确的5个节点，但是缺少节点间的连接线。根据配置文件，应该有以下连接关系：

```
video_src_1 → detector_2 → osd_display → alert_handler
                                       → screen_output
```

## 🔧 **根本原因**

1. **后端状态数据缺少连接信息**：管道状态文件中只包含节点信息，没有连接信息
2. **前端硬编码空连接**：`extractPipelineData` 函数中连接关系被硬编码为空数组
3. **数据格式不匹配**：前端转换函数期望复杂的连接格式，但配置文件使用简单格式

## 🛠️ **解决方案**

### 1. **后端修复：添加连接信息到状态数据**

#### 新增 `generateConnectionsFromConfig` 方法
```go
// generateConnectionsFromConfig 从配置文件生成连接信息
func (c *LocalPipelineClient) generateConnectionsFromConfig(config *CompletePipelineConfig) string {
    if config == nil || len(config.Connections) == 0 {
        return "[]"
    }

    connections := make([]map[string]interface{}, 0, len(config.Connections))
    
    for _, conn := range config.Connections {
        connectionData := map[string]interface{}{
            "id":   conn.ID,
            "from": conn.From,
            "to":   conn.To,
        }
        connections = append(connections, connectionData)
    }

    connectionsData, _ := json.Marshal(connections)
    return string(connectionsData)
}
```

#### 修改 `CreatePipeline` 和 `StartPipeline` 方法
```go
// 在 GlobalMetrics 中添加连接信息
GlobalMetrics: map[string]interface{}{
    "config_mode": "local",
    "connections": c.generateConnectionsFromConfig(config), // 添加连接信息
    // ... 其他指标
},
```

### 2. **前端修复：从状态数据中提取连接信息**

#### 修改 `extractPipelineData` 函数
```typescript
// 修改前：硬编码空连接
connections = []

// 修改后：从全局指标中提取连接信息
if (statusData.global_metrics && statusData.global_metrics.connections) {
    try {
        if (typeof statusData.global_metrics.connections === 'string') {
            connections = JSON.parse(statusData.global_metrics.connections)
            console.log('✅ 解析连接数据成功:', connections)
        } else {
            connections = statusData.global_metrics.connections
        }
    } catch (e) {
        console.error('❌ 解析连接数据失败:', e)
        connections = []
    }
} else {
    connections = []
}
```

### 3. **前端修复：支持简单连接格式**

#### 修改 `convertToVueFlowEdges` 函数
```typescript
export function convertToVueFlowEdges(apiConnections: any[]): VueFlowEdge[] {
    const edges: VueFlowEdge[] = []
    
    apiConnections.forEach(conn => {
        // 处理简单连接格式（从配置文件生成的格式）
        if (typeof conn.from === 'string' && typeof conn.to === 'string') {
            edges.push({
                id: `edge-${conn.id}`,
                source: conn.from,
                target: conn.to,
                animated: true,
                style: {
                    stroke: '#6366f1',
                    strokeWidth: 2,
                    strokeDasharray: '8,4'
                },
                label: `连接 ${conn.id}`,
                type: 'default'
            })
        }
        // ... 处理复杂格式的代码保持不变
    })
    
    return edges
}
```

## 🧪 **测试验证**

### 后端连接数据生成测试
```
✅ 连接数量: 4
📋 连接详情:
  1. ID: 1, From: video_src_1 → To: detector_2
  2. ID: 2, From: detector_2 → To: osd_display
  3. ID: 3, From: osd_display → To: alert_handler
  4. ID: 4, From: osd_display → To: screen_output
```

### 状态文件验证
```json
{
  "global_metrics": {
    "config_mode": "local",
    "connections": "[{\"from\":\"video_src_1\",\"id\":1,\"to\":\"detector_2\"},{\"from\":\"detector_2\",\"id\":2,\"to\":\"osd_display\"},{\"from\":\"osd_display\",\"id\":3,\"to\":\"alert_handler\"},{\"from\":\"osd_display\",\"id\":4,\"to\":\"screen_output\"}]",
    "cpu_usage_percent": 18.5,
    "memory_usage_mb": 285.6,
    "total_fps": 24.8
  }
}
```

### API响应验证
```json
{
  "errorCode": 0,
  "success": true,
  "data": {
    "pipeline_id": "comprehensive_pipeline",
    "state": "running",
    "global_metrics": {
      "connections": "[{\"from\":\"video_src_1\",\"id\":1,\"to\":\"detector_2\"}...]"
    }
  }
}
```

## 📊 **连接关系映射**

### 配置文件中的连接
```json
[
  {"id": 1, "from": "video_src_1", "to": "detector_2"},
  {"id": 2, "from": "detector_2", "to": "osd_display"},
  {"id": 3, "from": "osd_display", "to": "alert_handler"},
  {"id": 4, "from": "osd_display", "to": "screen_output"}
]
```

### Vue Flow 边格式
```javascript
[
  {
    id: "edge-1",
    source: "video_src_1",
    target: "detector_2",
    animated: true,
    style: { stroke: '#6366f1', strokeWidth: 2, strokeDasharray: '8,4' },
    label: "连接 1",
    type: "default"
  },
  // ... 其他连接
]
```

## ✅ **修复的文件**

### 后端文件
1. **`internal/app/pipeline/local_client.go`**
   - 新增 `generateConnectionsFromConfig` 方法
   - 修改 `CreatePipeline` 方法添加连接信息
   - 修改 `StartPipeline` 方法添加连接信息

### 前端文件
2. **`web/app/src/components/flow/PipelineFlowChart.vue`**
   - 修改 `extractPipelineData` 函数提取连接信息
   - 添加调试日志

3. **`web/app/src/utils/flowDataTransformer.ts`**
   - 修改 `convertToVueFlowEdges` 函数支持简单连接格式
   - 保持向后兼容性

## 🎯 **预期效果**

修复后，前端流程图应该显示：

### 节点（已正确显示）
1. **video_src_1** - 视频源节点
2. **detector_2** - 通用检测器节点
3. **osd_display** - OSD显示节点
4. **alert_handler** - 告警处理节点
5. **screen_output** - 屏幕输出节点

### 连接线（新增）
1. **video_src_1 → detector_2** - 视频流传输
2. **detector_2 → osd_display** - 检测结果传输
3. **osd_display → alert_handler** - 告警信息传输
4. **osd_display → screen_output** - 显示输出传输

### 视觉效果
- **动画连接线**：带有流动动画效果
- **蓝色虚线**：`#6366f1` 颜色，虚线样式
- **连接标签**：显示"连接 1"、"连接 2"等标识
- **正确布局**：节点按照数据流方向排列

## 🔄 **使用说明**

1. **刷新前端页面**（强制刷新：Ctrl+F5）
2. **检查浏览器控制台**：查看连接数据解析日志
3. **验证连接线显示**：确认4条连接线正确显示
4. **测试交互功能**：点击连接线查看详情

## 🚀 **下一步优化**

1. **连接线样式优化**：根据数据类型使用不同颜色
2. **布局算法优化**：自动排列节点位置
3. **性能优化**：大型管道的渲染性能
4. **交互增强**：连接线悬停效果、点击事件

这次修复完整解决了连接线缺失的问题，确保流程图能够完整展示管道的数据流向。
