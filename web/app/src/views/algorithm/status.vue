<!--
  算法运行状态页面

  功能说明：
  1. 显示算法执行管道的实时运行状态
  2. 支持查看管道流程图和节点详情
  3. 支持部署综合管道功能

  主要特性：
  - 固定显示"默认管道"选项
  - 部署按钮：根据VideoAlgorithmBinding表创建综合管道
  - 实时状态监控：显示管道运行状态和运行时间
  - 节点详情：点击节点查看详细信息和运行指标
-->
<template>
  <div class="algorithm-status">
    <!-- 综合管道状态 -->
    <el-card class="pipeline-status-card" shadow="never">
      <div class="status-content">
        <div class="status-left">
          <h3>算法部署</h3>
          <p class="description">基于所有激活绑定关系的综合算法执行管道</p>
        </div>
        <div class="status-right">
          <el-tag
            :type="getPipelineStatusType()"
            size="large"
          >
            <i class="el-icon-video-camera" style="margin-right: 4px;" />
            {{ getPipelineStatusText() }}
          </el-tag>
          <span v-if="pipelineStatus?.uptime_seconds" class="uptime">
            运行时间: {{ formatUptime(pipelineStatus.uptime_seconds) }}
          </span>
          <el-button
            @click="handleDeploy"
            :loading="isDeploying"
            type="primary"
            icon="VideoPlay"
            size="default"
          >
            {{ (!pipelineStatus || pipelineStatus.state === 'not_deployed') ? '部署' : '重新部署' }}
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 流程图容器 -->
    <div class="flow-chart-container">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-state">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div>
      
      <!-- 管道未部署状态 -->
      <div v-else-if="!pipelineStatus || pipelineStatus.state === 'not_deployed'" class="empty-state">
        <div class="empty-content">
          <i class="el-icon-cpu" style="font-size: 48px; color: #c0c4cc; margin-bottom: 16px;" />
          <h3>管道尚未部署</h3>
          <p>点击"部署"按钮创建综合管道后即可查看算法执行流程图</p>
          <el-button
            @click="handleDeploy"
            :loading="isDeploying"
            type="primary"
            style="margin-top: 16px;"
          >
            立即部署
          </el-button>
        </div>
      </div>

      <!-- 管道流程图 -->
      <PipelineFlowChart
        v-else
        pipeline-id="comprehensive"
        :auto-refresh="false"
        :refresh-interval="5000"
        @node-click="handleNodeClick"
        @edge-click="handleEdgeClick"
        @error="handleError"
        @data-loaded="handleDataLoaded"
        @status-updated="handleStatusUpdated"
        class="flow-chart"
      />
    </div>

    <!-- 节点详情抽屉 -->
    <el-drawer
      v-model="showNodeDetails"
      title="节点详情"
      size="400px"
      direction="rtl"
    >
      <div v-if="selectedNode" class="node-details">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-item">
            <span class="label">节点ID:</span>
            <span class="value">{{ selectedNode.data.id }}</span>
          </div>
          <div class="detail-item">
            <span class="label">节点类型:</span>
            <span class="value">{{ selectedNode.data.typeName }}</span>
          </div>
          <div class="detail-item">
            <span class="label">状态:</span>
            <el-tag 
              :type="getNodeStatusType(selectedNode.data.state)" 
              size="small"
            >
              {{ getNodeStatusText(selectedNode.data.state) }}
            </el-tag>
          </div>
        </div>

        <div v-if="Object.keys(selectedNode.data.params).length > 0" class="detail-section">
          <h4>参数配置</h4>
          <div 
            v-for="(value, key) in selectedNode.data.params" 
            :key="key"
            class="detail-item"
          >
            <span class="label">{{ key }}:</span>
            <span class="value">{{ formatParamValue(value) }}</span>
          </div>
        </div>

        <div 
          v-if="selectedNode.data.state === 'running' && Object.keys(selectedNode.data.metrics).length > 0" 
          class="detail-section"
        >
          <h4>运行指标</h4>
          <div 
            v-for="(value, key) in selectedNode.data.metrics" 
            :key="key"
            class="detail-item"
          >
            <span class="label">{{ key }}:</span>
            <span class="value metric-value">{{ formatMetricValue(key, value) }}</span>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PipelineFlowChart from '@/components/flow/PipelineFlowChart.vue'
import { createComprehensivePipeline, getComprehensivePipelineStatus, startComprehensivePipeline } from '@/api/pipeline'
import type { VueFlowNode, VueFlowEdge } from '@/utils/flowDataTransformer'

// 响应式数据
const isDeploying = ref(false)
const selectedNode = ref<VueFlowNode | null>(null)
const showNodeDetails = ref(false)
const pipelineStatus = ref<any>(null)
const isLoading = ref(true) // 添加加载状态

// 部署管道
async function handleDeploy() {
  try {
    await ElMessageBox.confirm(
      '确定要部署综合管道吗？系统将根据当前的绑定关系创建一个新的综合管道。',
      '确认部署',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    isDeploying.value = true

    // 调用创建综合管道接口
    const response = await createComprehensivePipeline()

    // 后端返回格式：{ errorCode: 0, message: "", data: {...}, success: true }
    if (response.success && response.errorCode === 0) {
      ElMessage.success(`部署成功！已创建包含 ${response.data.bindings_count} 个绑定关系的综合管道`)

      // 部署成功后自动启动管道
      try {
        await startComprehensivePipeline()
        console.log('综合管道已自动启动')
      } catch (error) {
        console.warn('自动启动管道失败，但部署成功:', error)
      }

      // 刷新管道状态
      await loadPipelineStatus()
    } else {
      ElMessage.error(response.message || '部署失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('部署管道失败:', error)
      ElMessage.error(error.message || '部署管道失败')
    }
  } finally {
    isDeploying.value = false
  }
}

// 加载管道状态
async function loadPipelineStatus() {
  try {
    isLoading.value = true // 开始加载时设置为true
    const response = await getComprehensivePipelineStatus()

    if (response.success && response.errorCode === 0) {
      pipelineStatus.value = response.data
    }
  } catch (error) {
    console.error('获取管道状态失败:', error)
  } finally {
    isLoading.value = false // 加载完成时设置为false
  }
}

// 获取管道状态类型
function getPipelineStatusType(): 'success' | 'danger' | 'warning' | 'info' {
  if (!pipelineStatus.value || pipelineStatus.value.state === 'not_deployed') {
    return 'info'
  }
  switch (pipelineStatus.value.state) {
    case 'running': return 'success'
    case 'error': return 'danger'
    case 'stopped': return 'warning'
    default: return 'info'
  }
}

// 获取管道状态文本
function getPipelineStatusText(): string {
  if (!pipelineStatus.value || pipelineStatus.value.state === 'not_deployed') {
    return '未部署'
  }
  switch (pipelineStatus.value.state) {
    case 'running': return '运行中'
    case 'error': return '错误'
    case 'stopped': return '已停止'
    default: return '未运行'
  }
}

// 节点点击处理
function handleNodeClick(node: VueFlowNode) {
  selectedNode.value = node
  showNodeDetails.value = true
}

// 边点击处理  
function handleEdgeClick(edge: VueFlowEdge) {
  console.log('边被点击:', edge)
}

// 错误处理
function handleError(message: string) {
  ElMessage.error(message)
}

// 数据加载完成处理
function handleDataLoaded(data: { nodes: VueFlowNode[], edges: VueFlowEdge[] }) {
  console.log('管道数据加载完成:', data)
}

// 处理状态更新事件
function handleStatusUpdated(status: any) {
  console.log('管道状态更新:', status)
  // 同步页面级别的状态
  pipelineStatus.value = status
}

// 格式化运行时间
function formatUptime(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

// 获取节点状态类型
function getNodeStatusType(state: string): 'success' | 'danger' | 'warning' | 'info' {
  switch (state) {
    case 'running': return 'success'
    case 'error': return 'danger'
    case 'stopped': return 'warning'
    default: return 'info'
  }
}

// 获取节点状态文本
function getNodeStatusText(state: string): string {
  switch (state) {
    case 'running': return '运行中'
    case 'error': return '错误'
    case 'stopped': return '已停止'
    default: return '空闲'
  }
}

// 格式化参数值
function formatParamValue(value: any): string {
  if (typeof value === 'string' && value.length > 50) {
    return value.substring(0, 50) + '...'
  }
  return String(value)
}

// 格式化指标值
function formatMetricValue(key: string, value: any): string {
  if (key.includes('fps')) {
    return Number(value).toFixed(1) + ' fps'
  }
  if (key.includes('time_ms')) {
    return Number(value).toFixed(1) + ' ms'
  }
  if (key.includes('percent')) {
    return Number(value).toFixed(1) + '%'
  }
  if (key.includes('mb')) {
    return Number(value).toFixed(1) + ' MB'
  }
  if (key.includes('kbps')) {
    return Number(value).toFixed(1) + ' kbps'
  }
  return String(value)
}

// 生命周期钩子
onMounted(() => {
  // 初始化时加载管道状态（作为备用，如果组件加载失败）
  console.log('🚀 算法运行页面已挂载')
  // 延迟一点时间，让组件先尝试加载
  setTimeout(() => {
    if (!pipelineStatus.value || pipelineStatus.value.state === 'not_deployed') {
      console.log('📊 页面级别备用状态加载')
      loadPipelineStatus()
    }
  }, 1000)
})
</script>

<style scoped>
.algorithm-status {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.pipeline-status-card {
  margin-bottom: 20px;
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-left h3 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.status-left .description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.uptime {
  color: #606266;
  font-size: 14px;
}

.flow-chart-container {
  flex: 1;
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.loading-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #fafafa;
}

.loading-state .spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #409eff;
  animation: spin 1s ease infinite;
  margin-bottom: 16px;
}

.loading-state p {
  color: #909399;
  font-size: 14px;
  margin: 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #fafafa;
}

.empty-content {
  text-align: center;
}

.empty-content h3 {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 18px;
  font-weight: 500;
}

.empty-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.flow-chart {
  width: 100%;
  height: 100%;
}

.node-details {
  padding: 0 4px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 8px 0;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  color: #606266;
  font-weight: 500;
  min-width: 80px;
}

.detail-item .value {
  color: #303133;
  word-break: break-all;
  text-align: right;
  flex: 1;
  margin-left: 12px;
}

.metric-value {
  font-weight: 600;
  color: #409eff !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
