<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="算法名称">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入算法名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="已激活" value="active" />
            <el-option label="未激活" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleImportClick">
        <el-icon><Upload /></el-icon>
        算法导入
      </el-button>
      <el-button 
        type="danger" 
        :disabled="!multipleSelection.length"
        @click="handleBatchDelete"
      >
        <el-icon><Delete /></el-icon>
        批量删除
      </el-button>
      <el-button type="success" @click="handleRefreshStatus">
        <el-icon><Refresh /></el-icon>
        刷新状态
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="algorithmList"
      @selection-change="handleSelectionChange"
      stripe
      style="width: 100%;"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column
        prop="name"
        label="算法名称"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        prop="description"
        label="算法描述"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="version"
        label="版本"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag type="info" size="small">{{ row.version }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        label="状态"
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <div class="status-cell">
            <span class="status-dot" :class="getStatusClass(row.status)"></span>
            <span>{{ getStatusText(row.status) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="created_at"
        label="创建时间"
        width="180"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatTime(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
      >
        <template #default="{ row }">
          <el-button
            v-if="row.status !== 'active'"
            type="success"
            size="small"
            @click="handleActivate(row)"
          >
            激活
          </el-button>
          <el-button
            v-else
            type="warning"
            size="small"
            @click="handleDeactivate(row)"
          >
            停用
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 导入算法对话框 -->
    <el-dialog v-model="importDialogVisible" title="导入算法" width="500px">
      <el-form :model="importForm" label-width="80px" :rules="rules" ref="importFormRef">
        <el-form-item label="算法名称" prop="name">
          <el-input v-model="importForm.name" placeholder="请输入算法名称" />
        </el-form-item>
        <el-form-item label="算法描述" prop="description">
          <el-input
            v-model="importForm.description"
            type="textarea"
            placeholder="请输入算法描述"
          />
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="importForm.version" placeholder="请输入版本号，如v1.0.0" />
        </el-form-item>
        <el-form-item label="算法文件" prop="file">
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :on-exceed="handleExceed"
            :on-remove="handleRemove"
            accept=".zip,.tar.gz,.onnx,.pt,.pb"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持.zip、.tar.gz、.onnx、.pt、.pb格式，文件大小不超过100MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitImport" :loading="importing">
            导入
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { Search, Refresh, Upload, Delete, View } from '@element-plus/icons-vue';
import AlgorithmAPI from '@/api/algorithm';
import dayjs from 'dayjs';

// 算法列表数据
const algorithmList = ref<any[]>([]);
const loading = ref(false);
const multipleSelection = ref<any[]>([]);

// 导入对话框相关
const importDialogVisible = ref(false);
const importFormRef = ref<FormInstance>();
const importing = ref(false);
const importForm = ref({
  name: '',
  description: '',
  version: '',
  file: null as File | null
});

// 查询参数
const queryParams = reactive({
  name: '',
  status: '',
  page: 1,
  size: 10,
  sort: 'created_at',
  order: 'desc'
});
const queryFormRef = ref<FormInstance>();
const total = ref(0);

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入算法名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入算法描述', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  file: [
    { required: true, message: '请上传算法文件', trigger: 'change' }
  ]
};

// 初始化时获取算法列表
onMounted(() => {
  fetchAlgorithmList();
});

// 获取算法列表
const fetchAlgorithmList = async () => {
  loading.value = true;
  try {
    const params = {
      name: queryParams.name,
      status: queryParams.status,
      page: queryParams.page,
      size: queryParams.size,
      sort: queryParams.sort,
      order: queryParams.order
    };
    const res = await AlgorithmAPI.getAlgorithms(params) as any;
    if (res.success) {
      algorithmList.value = res.data.results || [];
      total.value = res.data.total || 0;
    } else {
      ElMessage.error(res.message || '获取算法列表失败');
    }
  } catch (error) {
    console.error('获取算法列表出错:', error);
    ElMessage.error('获取算法列表失败');
  } finally {
    loading.value = false;
  }
};

// 查询按钮点击
const handleQuery = () => {
  queryParams.page = 1;
  fetchAlgorithmList();
};

// 重置按钮点击
const handleReset = () => {
  queryFormRef.value?.resetFields();
  queryParams.page = 1;
  fetchAlgorithmList();
};

// 表格选择变化
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection;
};

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 个算法吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    // TODO: 实现批量删除API
    ElMessage.warning('批量删除功能开发中');
    fetchAlgorithmList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败');
    }
  }
};

// 刷新状态
const handleRefreshStatus = async () => {
  loading.value = true;
  try {
    // TODO: 实现刷新状态API
    ElMessage.warning('刷新状态功能开发中');
    fetchAlgorithmList();
  } catch (error) {
    console.error('刷新算法状态失败:', error);
    ElMessage.error('刷新算法状态失败');
  } finally {
    loading.value = false;
  }
};

// 查看算法详情
const handleView = (row: any) => {
  // 这里可以跳转到详情页面或打开详情对话框
  ElMessage.info('查看算法详情功能开发中');
};

// 获取状态样式类
const getStatusClass = (status: string) => {
  return {
    'status-active': status === 'active',
    'status-inactive': status === 'inactive'
  };
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    active: '已激活',
    inactive: '未激活'
  };
  return textMap[status] || '未知';
};

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};

// 打开导入对话框
const handleImportClick = () => {
  importDialogVisible.value = true;
  importForm.value = {
    name: '',
    description: '',
    version: '',
    file: null
  };
};

// 文件变更处理
const handleFileChange = (file: any) => {
  importForm.value.file = file.raw;
};

// 文件超出限制处理
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件');
};

// 移除文件处理
const handleRemove = () => {
  importForm.value.file = null;
};

// 提交导入
const submitImport = async () => {
  if (!importFormRef.value) return;
  
  await importFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    importing.value = true;
    try {
      const formData = new FormData();
      formData.append('name', importForm.value.name);
      formData.append('description', importForm.value.description);
      formData.append('version', importForm.value.version);
      if (importForm.value.file) {
        formData.append('file', importForm.value.file);
      }
      
      const res = await AlgorithmAPI.importAlgorithm(formData) as any;
      if (res.success) {
        ElMessage.success('算法导入成功');
        importDialogVisible.value = false;
        fetchAlgorithmList(); // 刷新列表
      } else {
        ElMessage.error(res.message || '算法导入失败');
      }
    } catch (error) {
      console.error('导入算法出错:', error);
      ElMessage.error('算法导入失败');
    } finally {
      importing.value = false;
    }
  });
};

// 激活算法
const handleActivate = async (row: any) => {
  try {
    const res = await AlgorithmAPI.activateAlgorithm(row.id) as any;
    if (res.success) {
      ElMessage.success('算法激活成功');
      fetchAlgorithmList(); // 刷新列表
    } else {
      ElMessage.error(res.message || '算法激活失败');
    }
  } catch (error) {
    console.error('激活算法出错:', error);
    ElMessage.error('算法激活失败');
  }
};

// 停用算法
const handleDeactivate = async (row: any) => {
  try {
    const res = await AlgorithmAPI.deactivateAlgorithm(row.id) as any;
    if (res.success) {
      ElMessage.success('算法停用成功');
      fetchAlgorithmList(); // 刷新列表
    } else {
      ElMessage.error(res.message || '算法停用失败');
    }
  } catch (error) {
    console.error('停用算法出错:', error);
    ElMessage.error('算法停用失败');
  }
};

// 删除算法
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该算法吗？删除后不可恢复', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // TODO: 实现删除算法API
      ElMessage.warning('删除算法功能开发中');
      fetchAlgorithmList(); // 刷新列表
    } catch (error) {
      console.error('删除算法出错:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    // 取消删除操作
  });
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.toolbar {
  margin-bottom: 20px;
}

.status-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
    
    &.status-active {
      background-color: #67c23a;
    }
    
    &.status-inactive {
      background-color: #909399;
    }
  }
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 500;
    }
  }
}
</style>
