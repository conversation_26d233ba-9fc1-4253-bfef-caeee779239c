/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module "vue" {
  export interface GlobalComponents {
    AppLink: (typeof import("./../components/AppLink/index.vue"))["default"];
    AppMain: (typeof import("./../layout/components/AppMain/index.vue"))["default"];
    Breadcrumb: (typeof import("./../components/Breadcrumb/index.vue"))["default"];
    ElAlert: (typeof import("element-plus/es"))["ElAlert"];
    ElBacktop: (typeof import("element-plus/es"))["ElBacktop"];
    ElBadge: (typeof import("element-plus/es"))["ElBadge"];
    ElBreadcrumb: (typeof import("element-plus/es"))["ElBreadcrumb"];
    ElBreadcrumbItem: (typeof import("element-plus/es"))["ElBreadcrumbItem"];
    ElButton: (typeof import("element-plus/es"))["ElButton"];
    ElCard: (typeof import("element-plus/es"))["ElCard"];
    ElCol: (typeof import("element-plus/es"))["ElCol"];
    ElColorPicker: (typeof import("element-plus/es"))["ElColorPicker"];
    ElConfigProvider: (typeof import("element-plus/es"))["ElConfigProvider"];
    ElDialog: (typeof import("element-plus/es"))["ElDialog"];
    ElDivider: (typeof import("element-plus/es"))["ElDivider"];
    ElDrawer: (typeof import("element-plus/es"))["ElDrawer"];
    ElDropdown: (typeof import("element-plus/es"))["ElDropdown"];
    ElDropdownItem: (typeof import("element-plus/es"))["ElDropdownItem"];
    ElDropdownMenu: (typeof import("element-plus/es"))["ElDropdownMenu"];
    ElForm: (typeof import("element-plus/es"))["ElForm"];
    ElFormItem: (typeof import("element-plus/es"))["ElFormItem"];
    ElIcon: (typeof import("element-plus/es"))["ElIcon"];
    ElImage: (typeof import("element-plus/es"))["ElImage"];
    ElInput: (typeof import("element-plus/es"))["ElInput"];
    ElLink: (typeof import("element-plus/es"))["ElLink"];
    ElMenu: (typeof import("element-plus/es"))["ElMenu"];
    ElMenuItem: (typeof import("element-plus/es"))["ElMenuItem"];
    ElRow: (typeof import("element-plus/es"))["ElRow"];
    ElScrollbar: (typeof import("element-plus/es"))["ElScrollbar"];
    ElSubMenu: (typeof import("element-plus/es"))["ElSubMenu"];
    ElSwitch: (typeof import("element-plus/es"))["ElSwitch"];
    ElTabPane: (typeof import("element-plus/es"))["ElTabPane"];
    ElTabs: (typeof import("element-plus/es"))["ElTabs"];
    ElTag: (typeof import("element-plus/es"))["ElTag"];
    ElText: (typeof import("element-plus/es"))["ElText"];
    ElTooltip: (typeof import("element-plus/es"))["ElTooltip"];
    ElWatermark: (typeof import("element-plus/es"))["ElWatermark"];
    Hamburger: (typeof import("./../components/Hamburger/index.vue"))["default"];
    IEpBell: (typeof import("~icons/ep/bell"))["default"];
    IEpClose: (typeof import("~icons/ep/close"))["default"];
    IEpLock: (typeof import("~icons/ep/lock"))["default"];
    IEpUser: (typeof import("~icons/ep/user"))["default"];
    LangSelect: (typeof import("./../components/LangSelect/index.vue"))["default"];
    LayoutSelect: (typeof import("./../layout/components/Settings/components/LayoutSelect.vue"))["default"];
    NavBar: (typeof import("./../layout/components/NavBar/index.vue"))["default"];
    NavbarAction: (typeof import("./../layout/components/NavBar/components/NavbarAction.vue"))["default"];
    NavbarLeft: (typeof import("./../layout/components/NavBar/components/NavbarLeft.vue"))["default"];
    NavbarRight: (typeof import("./../layout/components/NavBar/components/NavbarRight.vue"))["default"];
    RouterLink: (typeof import("vue-router"))["RouterLink"];
    RouterView: (typeof import("vue-router"))["RouterView"];
    Settings: (typeof import("./../layout/components/Settings/index.vue"))["default"];
    Sidebar: (typeof import("./../layout/components/Sidebar/index.vue"))["default"];
    SidebarLogo: (typeof import("./../layout/components/Sidebar/components/SidebarLogo.vue"))["default"];
    SidebarMenu: (typeof import("./../layout/components/Sidebar/components/SidebarMenu.vue"))["default"];
    SidebarMenuItem: (typeof import("./../layout/components/Sidebar/components/SidebarMenuItem.vue"))["default"];
    SidebarMenuItemTitle: (typeof import("./../layout/components/Sidebar/components/SidebarMenuItemTitle.vue"))["default"];
    SidebarMixTopMenu: (typeof import("./../layout/components/Sidebar/components/SidebarMixTopMenu.vue"))["default"];
    SizeSelect: (typeof import("./../components/SizeSelect/index.vue"))["default"];
    SvgIcon: (typeof import("./../components/SvgIcon/index.vue"))["default"];
    TagsView: (typeof import("./../layout/components/TagsView/index.vue"))["default"];
    ThemeColorPicker: (typeof import("./../layout/components/Settings/components/ThemeColorPicker.vue"))["default"];
  }
}
