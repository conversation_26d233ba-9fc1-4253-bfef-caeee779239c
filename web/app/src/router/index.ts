import type { App } from "vue";
import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router";

export const Layout = () => import("@/layout/index.vue");

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },

  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: { hidden: true },
  },

  {
    path: "/",
    name: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        // 用于 keep-alive 功能，需要与 SFC 中自动推导或显式声明的组件名称一致
        // 参考文档: https://cn.vuejs.org/guide/built-ins/keep-alive.html#include-exclude
        name: "Dashboard",
        meta: {
          title: "dashboard",
          icon: "homepage",
          affix: true,
          keepAlive: true,
        },
      },
      {
        path: "401",
        component: () => import("@/views/error-page/401.vue"),
        meta: { hidden: true },
      },
      {
        path: "404",
        component: () => import("@/views/error-page/404.vue"),
        meta: { hidden: true },
      },
    ],
  },

  // 告警联动路由已移至 menu.ts 中，通过动态路由系统加载

  // 算法仓库路由
  // {
  //   path: "/algorithm",
  //   component: Layout,
  //   redirect: "/algorithm/list",
  //   meta: {
  //     title: "算法仓库",
  //     icon: "cpu",
  //   },
  //   children: [
  //     {
  //       path: "list",
  //       component: () => import("@/views/algorithm/list.vue"),
  //       name: "AlgorithmList",
  //       meta: {
  //         title: "算法列表",
  //         icon: "list",
  //       },
  //     },
  //     {
  //       path: "status",
  //       component: () => import("@/views/algorithm/status.vue"),
  //       name: "AlgorithmStatus",
  //       meta: {
  //         title: "运行状态",
  //         icon: "monitor",
  //       },
  //     },
  //   ],
  // },

  // 测试路由
  // {
  //   path: "/test",
  //   component: Layout,
  //   redirect: "/test/flow-layout",
  //   meta: {
  //     title: "测试页面",
  //     icon: "experiment",
  //   },
  //   children: [
  //     {
  //       path: "flow-layout",
  //       component: () => import("@/views/test/FlowLayoutTest.vue"),
  //       name: "FlowLayoutTest",
  //       meta: {
  //         title: "流程图布局测试",
  //         icon: "flow-chart",
  //       },
  //     },
  //   ],
  // },

  // 视频接入路由
  // {
  //   path: "/video",
  //   component: Layout,
  //   redirect: "/video/camera",
  //   meta: {
  //     title: "视频接入",
  //     icon: "video-camera",
  //   },
  //   children: [
  //     {
  //       path: "camera",
  //       component: () => import("@/views/video/camera.vue"),
  //       name: "VideoCamera",
  //       meta: {
  //         title: "摄像头",
  //         icon: "camera",
  //       },
  //     },
  //     {
  //       path: "stream",
  //       component: () => import("@/views/video/stream.vue"),
  //       name: "VideoStream",
  //       meta: {
  //         title: "视频流",
  //         icon: "video-play",
  //       },
  //     },
  //     {
  //       path: "file",
  //       component: () => import("@/views/video/file.vue"),
  //       name: "VideoFile",
  //       meta: {
  //         title: "视频文件",
  //         icon: "document-video",
  //       },
  //     },
  //   ],
  // },

  // 外部链接
  // {
  //   path: "/external-link",
  //   component: Layout,
  //   children: [ {
  //       component: () => import("@/views/external-link/index.vue"),
  //       path: "https://www.cnblogs.com/haoxianrui/",
  //       meta: { title: "外部链接", icon: "link" },
  //     },
  //   ],
  // },
  // 多级嵌套路由
  /* {
         path: '/nested',
         component: Layout,
         redirect: '/nested/level1/level2',
         name: 'Nested',
         meta: {title: '多级菜单', icon: 'nested'},
         children: [
             {
                 path: 'level1',
                 component: () => import('@/views/nested/level1/index.vue'),
                 name: 'Level1',
                 meta: {title: '菜单一级'},
                 redirect: '/nested/level1/level2',
                 children: [
                     {
                         path: 'level2',
                         component: () => import('@/views/nested/level1/level2/index.vue'),
                         name: 'Level2',
                         meta: {title: '菜单二级'},
                         redirect: '/nested/level1/level2/level3',
                         children: [
                             {
                                 path: 'level3-1',
                                 component: () => import('@/views/nested/level1/level2/level3/index1.vue'),
                                 name: 'Level3-1',
                                 meta: {title: '菜单三级-1'}
                             },
                             {
                                 path: 'level3-2',
                                 component: () => import('@/views/nested/level1/level2/level3/index2.vue'),
                                 name: 'Level3-2',
                                 meta: {title: '菜单三级-2'}
                             }
                         ]
                     }
                 ]
             },
         ]
     }*/
];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// 全局注册 router
export function setupRouter(app: App<Element>) {
  app.use(router);
}

/**
 * 重置路由
 */
export function resetRouter() {
  router.replace({ path: "/login" });
}

export default router;
