# 固定模板配置文件
version: "1.0"
author: "ECP System"

# 全局变量（固定，预留扩展位置）
globals:
  model_dir: "./vp_data/models/"
  video_dir: "./vp_data/videos/"
  output_dir: "./vp_data/output/"
  resize_ratio: 0.6
  log_level: "info"
  api_endpoint: "http://localhost:8080/api/alerts"
  # 预留位置供用户补充其他全局变量

# 固定节点配置
fixed_nodes:
  # 告警处理节点（固定）
  - id: "alert_handler"
    type: "vp_alert_handler_node"
    params:
      alert_endpoint: "${globals.api_endpoint}"
      danger_level: "warning"  # 将被动态覆盖
      
  # 屏幕输出节点（固定）
  - id: "screen_output"
    type: "vp_screen_des_node"
    params:
      channel_index: 0
      
  # OSD显示节点（固定）
  - id: "osd_display"
    type: "vp_osd_node"
    params:
      show_fps: true
      show_timestamp: true
      font_size: 16
      font_color: "white"
