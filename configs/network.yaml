# 网络管理配置文件

# 监控配置
monitor:
  # 监控间隔（秒）
  interval: 5
  # 流量采样率
  traffic_sample_rate: 1
  # 历史数据保留时间（小时）
  history_retention: 24

# 诊断配置
diagnostic:
  # ping超时时间（秒）
  ping_timeout: 5
  # traceroute最大跳数
  traceroute_max_hops: 30
  # 端口扫描超时时间（秒）
  port_scan_timeout: 10
  # 默认测试目标
  default_targets:
    - "*******"
    - "***************"
    - "baidu.com"

# 防火墙配置
firewall:
  # iptables路径
  iptables_path: "/sbin/iptables"
  # 配置备份路径
  backup_path: "/etc/ecp/network/backup"
  # 默认策略
  default_policy: "DROP"
  # 预定义规则
  predefined_rules:
    - name: "允许SSH"
      chain: "INPUT"
      protocol: "tcp"
      dest_port: "22"
      target: "ACCEPT"
      comment: "允许SSH访问"
    - name: "允许HTTP"
      chain: "INPUT"
      protocol: "tcp"
      dest_port: "80"
      target: "ACCEPT"
      comment: "允许HTTP访问"
    - name: "允许HTTPS"
      chain: "INPUT"
      protocol: "tcp"
      dest_port: "443"
      target: "ACCEPT"
      comment: "允许HTTPS访问"

# 系统配置
system:
  # 网络配置路径
  network_config_path: "/etc/netplan"
  # DNS配置路径
  dns_config_path: "/etc/resolv.conf"
  # 路由表路径
  route_table_path: "/proc/net/route"
  # 网络接口信息路径
  interface_path: "/sys/class/net"
  # 支持的网络管理工具
  supported_tools:
    - "netplan"
    - "networkmanager"
    - "ifconfig"

# 安全配置
security:
  # 允许的IP地址范围
  allowed_ip_ranges:
    - "***********/16"
    - "10.0.0.0/8"
    - "**********/12"
  # 禁止的端口
  forbidden_ports:
    - 23  # Telnet
    - 135 # RPC
    - 139 # NetBIOS
    - 445 # SMB
  # 最大连接数限制
  max_connections: 1000
  # 操作审计
  audit:
    enabled: true
    log_path: "/var/log/ecp/network-audit.log"
    max_log_size: "100MB"
    max_log_files: 10

# 性能配置
performance:
  # 网络缓冲区大小
  buffer_size: 65536
  # 最大并发连接数
  max_concurrent_connections: 100
  # 超时配置
  timeouts:
    connect: 10
    read: 30
    write: 30
  # 重试配置
  retry:
    max_attempts: 3
    backoff_factor: 2
    max_delay: 60

# 日志配置
logging:
  # 日志级别
  level: "info"
  # 日志文件路径
  file_path: "/var/log/ecp/network.log"
  # 日志文件大小限制
  max_file_size: "50MB"
  # 日志文件数量限制
  max_files: 5
  # 日志格式
  format: "json"
  # 是否输出到控制台
  console: true

# 通知配置
notification:
  # 是否启用通知
  enabled: true
  # 通知类型
  types:
    - "interface_down"
    - "high_traffic"
    - "connection_limit"
    - "security_alert"
  # 通知阈值
  thresholds:
    # 流量阈值（MB/s）
    traffic_threshold: 100
    # 连接数阈值
    connection_threshold: 800
    # 延迟阈值（ms）
    latency_threshold: 1000
    # 丢包率阈值（%）
    packet_loss_threshold: 5

# 备份配置
backup:
  # 是否启用自动备份
  enabled: true
  # 备份间隔（小时）
  interval: 24
  # 备份保留天数
  retention_days: 30
  # 备份路径
  backup_path: "/etc/ecp/network/backup"
  # 备份文件压缩
  compression: true

# 更新配置
update:
  # 是否启用自动更新
  auto_update: false
  # 更新检查间隔（小时）
  check_interval: 24
  # 更新源
  update_source: "https://updates.ecp.com/network"
  # 更新前备份
  backup_before_update: true

# 集成配置
integration:
  # SNMP配置
  snmp:
    enabled: false
    community: "public"
    port: 161
  # Prometheus配置
  prometheus:
    enabled: false
    port: 9090
    metrics_path: "/metrics"
  # Grafana配置
  grafana:
    enabled: false
    dashboard_url: "http://localhost:3000"

# 开发配置
development:
  # 是否启用调试模式
  debug: false
  # 模拟数据
  mock_data: false
  # 测试模式
  test_mode: false
  # 性能分析
  profiling: false
