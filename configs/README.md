# 配置目录

这个目录包含边缘计算平台的配置文件。

## 配置文件

- `config.yaml`: 主配置文件，包含服务器、数据库、视频接入、算法仓库、告警记录等配置

## 配置项说明

### 服务器配置

- `port`: 服务器监听端口
- `read_timeout`: 读取超时时间
- `write_timeout`: 写入超时时间

### 数据库配置

- `path`: SQLite数据库文件路径

### 视频接入配置

- `storage_path`: 视频文件存储路径
- `thumbnail_path`: 视频缩略图存储路径

### 算法仓库配置

- `storage_path`: 算法文件存储路径

### 告警记录配置

- `storage_path`: 告警记录存储路径
- `image_path`: 告警图片存储路径

### 日志配置

- `level`: 日志级别（debug, info, warn, error）
- `path`: 日志文件存储路径
