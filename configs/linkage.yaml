# 告警联动系统配置

linkage:
  # 是否启用联动功能
  enabled: true
  
  # 最大并发执行数
  max_concurrent_executions: 10
  
  # 执行超时时间
  execution_timeout: 30s
  
  # 重试次数
  retry_count: 3
  
  # 重试间隔
  retry_interval: 5s
  
  # 协议配置
  protocols:
    # MQTT协议配置
    mqtt:
      enabled: true
      default_qos: 1
      keep_alive: 60s
      connect_timeout: 10s
      clean_session: true
    
    # Modbus协议配置
    modbus:
      enabled: true
      timeout: 5s
      retry_count: 3
      slave_id: 1
      
    # RS485协议配置
    rs485:
      enabled: true
      timeout: 3s
      buffer_size: 1024
  
  # 日志配置
  logging:
    level: info
    retention_days: 30
    execution_retention_days: 90
  
  # 监控配置
  monitoring:
    health_check_interval: 30s
    statistics_update_interval: 60s
    device_offline_threshold: 5m

# 设备类型配置模板
device_types:
  # 警报灯配置
  alarm_light:
    name: "警报灯"
    supported_protocols: ["modbus", "mqtt"]
    commands:
      - name: "turn_on"
        description: "开启警报灯"
        parameters:
          - name: "brightness"
            type: "integer"
            range: [0, 100]
            default: 100
          - name: "color"
            type: "string"
            options: ["red", "yellow", "green", "blue"]
            default: "red"
      - name: "turn_off"
        description: "关闭警报灯"
      - name: "blink"
        description: "闪烁警报灯"
        parameters:
          - name: "interval"
            type: "integer"
            range: [100, 5000]
            default: 1000
            unit: "ms"
  
  # 门禁控制器配置
  access_controller:
    name: "门禁控制器"
    supported_protocols: ["rs485", "modbus"]
    commands:
      - name: "open_door"
        description: "开门"
        parameters:
          - name: "duration"
            type: "integer"
            range: [1, 60]
            default: 5
            unit: "seconds"
      - name: "close_door"
        description: "关门"
      - name: "lock_door"
        description: "锁门"
      - name: "unlock_door"
        description: "解锁"
  
  # 报警器配置
  alarm_siren:
    name: "报警器"
    supported_protocols: ["mqtt", "modbus", "rs485"]
    commands:
      - name: "start_alarm"
        description: "启动报警"
        parameters:
          - name: "volume"
            type: "integer"
            range: [0, 100]
            default: 80
          - name: "pattern"
            type: "string"
            options: ["continuous", "intermittent", "pulse"]
            default: "intermittent"
      - name: "stop_alarm"
        description: "停止报警"
      - name: "test_alarm"
        description: "测试报警"
        parameters:
          - name: "duration"
            type: "integer"
            range: [1, 10]
            default: 3
            unit: "seconds"

# 预定义联动规则模板
rule_templates:
  # 人员入侵联动模板
  person_intrusion:
    name: "人员入侵联动"
    description: "检测到人员入侵时开启警报灯并发送通知"
    conditions:
      algorithm_types: ["人员检测"]
      alert_levels: ["warning", "error"]
      time_range:
        start_time: "18:00:00"
        end_time: "06:00:00"
        weekdays: [1, 2, 3, 4, 5, 6, 7]
    actions:
      - device_type: "alarm_light"
        command: "turn_on"
        params:
          brightness: 100
          color: "red"
        delay: 0
      - device_type: "alarm_siren"
        command: "start_alarm"
        params:
          volume: 80
          pattern: "intermittent"
        delay: 2
  
  # 火灾报警联动模板
  fire_alarm:
    name: "火灾报警联动"
    description: "检测到火灾时立即启动所有报警设备"
    conditions:
      algorithm_types: ["火灾检测", "烟雾检测"]
      alert_levels: ["error"]
    actions:
      - device_type: "alarm_light"
        command: "blink"
        params:
          interval: 500
        delay: 0
      - device_type: "alarm_siren"
        command: "start_alarm"
        params:
          volume: 100
          pattern: "continuous"
        delay: 0
      - device_type: "access_controller"
        command: "unlock_door"
        delay: 1
  
  # 车辆违停联动模板
  vehicle_violation:
    name: "车辆违停联动"
    description: "检测到车辆违停时发出警告"
    conditions:
      algorithm_types: ["车辆检测"]
      alert_levels: ["warning"]
      time_range:
        start_time: "08:00:00"
        end_time: "18:00:00"
        weekdays: [1, 2, 3, 4, 5]
    actions:
      - device_type: "alarm_light"
        command: "turn_on"
        params:
          brightness: 60
          color: "yellow"
        delay: 0

# 协议配置示例
protocol_examples:
  mqtt:
    broker_url: "tcp://*************:1883"
    username: "ecp_user"
    password: "ecp_password"
    client_id: "ecp_linkage"
    use_tls: false
    qos: 1
    topics:
      control: "device/control"
      status: "device/status"
  
  modbus:
    address: "*************"
    port: 502
    slave_id: 1
    timeout: 5
    retry_count: 3
    registers:
      control: 0x0001
      status: 0x0002
      brightness: 0x0003
  
  rs485:
    port: "COM3"
    baud_rate: 9600
    data_bits: 8
    stop_bits: 1
    parity: "none"
    timeout: 3
    buffer_size: 1024
    protocol: "custom"
