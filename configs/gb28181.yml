# GB28181服务配置
gb28181:
  # GB28181服务地址
  server_url: "http://localhost:18080"
  
  # 是否启用GB28181功能（设为false时使用Mock适配器）
  enabled: true
  
  # 健康检查间隔（秒）
  health_check_interval: 30
  
  # 设备同步间隔（分钟）
  sync_interval: 5
  
  # 连接超时时间（秒）
  timeout: 30

# 以下是GB28181服务本身的配置（用于独立运行GB28181服务）
gb28181_service:
  # HTTP服务端口
  server:
    port: 18080

  # 数据库配置（GB28181服务使用独立数据库）
  sqlite:
    path: "./data/gb28181"
    file: "gbserver.db"
    username: "root"
    password: "root"
    database: "go-gb28181"
  
  # Redis配置（用于缓存）
  redis:
    host: "127.0.0.1"
    port: 6379
    database: 0
    username: "root"
    max-retries: 3
    pool-size: 50
    min-idle-connections: 50
    max-idle-connections: 100
    conn-max-life-time: 0

  # SIP协议配置
  sip:
    # 本机IP地址（需要根据实际环境修改）
    ip: "*************"
    
    # SIP服务监听端口
    port: 5060
    
    # 域名（根据国标规定）
    domain: "**********"
    
    # 服务器ID
    id: "**********2000000001"
    
    # 默认设备认证密码
    password: "admin123"
    
    # 用户代理
    user-agent: "gb"

  # 流媒体服务配置（ZLMediaKit）
  media:
    # 流媒体服务器唯一ID
    id: "FQ3TF8yT83wh5Wvz"
    
    # 流媒体服务器IP
    ip: "*************"
    
    # HTTP端口
    http-port: 8000
    
    # 密钥
    secret: "035c73f7-bb6b-4889-a715-d9eb2d1925cc"

  # 日志配置
  log:
    level: "info"
    path: "./logs/gb28181"
    file: "gbserver.log"
    maxSize: 10
    maxBackups: 30
    maxAge: 30 