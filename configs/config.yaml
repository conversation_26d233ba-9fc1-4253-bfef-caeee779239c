# 边缘计算平台配置文件

# 服务器配置
server:
  port: 8080
  read_timeout: 300s  # 增加到5分钟，支持SSE长连接
  write_timeout: 300s # 增加到5分钟，支持SSE长连接

# 数据库配置
database:
  path: "./data/ecp.db"

# 视频接入配置
video:
  storage_path: "./data/videos"
  thumbnail_path: "./data/thumbnails"

# 算法仓库配置
algorithm:
  storage_path: "./data/algorithms"

# 告警记录配置
alert:
  storage_path: "./data/alerts"
  image_path: "./data/images"

# GB28181服务配置
gb28181:
  server_url: "http://localhost:18080"
  enabled: true
  health_check_interval: 30
  sync_interval: 5
  timeout: 30

# 日志配置
log:
  level: "info"
  path: "./logs" 

# 认证配置
auth:
  jwt_secret: "ecp_secure_jwt_secret_key_change_in_production"
  token_expiry: 24   # 令牌过期时间（小时）
  refresh_expiry: 7  # 刷新令牌过期时间（天）

# 管道配置
pipeline:
  enabled: true

  # 部署模式：local(本地部署) 或 remote(远程部署)
  deployment_mode: "local"

  # 本地部署配置（仅配置存储，不涉及进程管理）
  local:
    # 管道配置文件存储路径
    config_storage_path: "./data/pipelines"
    # 管道状态文件存储路径
    status_storage_path: "./data/pipeline-status"

  # 远程部署配置
  remote:
    api_endpoint: "http://localhost:8080/api/v1"
    api_key: "${PIPELINE_API_KEY}"
    timeout: 30s
    retry_count: 3

  # 通用配置
  template_path: "./configs/pipeline-template.yaml"

  # 节点类型映射配置
  node_mapping:
    video_source:
      rtsp: "vp_rtsp_src_node"
      file: "vp_file_src_node"
      gb28181: "vp_gb28181_src_node"
      onvif: "vp_onvif_src_node"

    algorithms:
      face_detection: "vp_yunet_face_detector_node"
      object_detection: "vp_yolo_detector_node"
      person_detection: "vp_person_detector_node"
      vehicle_detection: "vp_vehicle_detector_node"
      helmet_detection: "vp_helmet_detector_node"
      safety_detection: "vp_safety_detector_node"