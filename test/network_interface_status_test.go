package test

import (
	"encoding/json"
	"testing"
	"time"

	"ecp/internal/app/network/monitor"
)

// TestNetworkInterfaceStatus 测试网络接口状态字段
func TestNetworkInterfaceStatus(t *testing.T) {
	manager := monitor.NewManager(5*time.Second, 100)
	
	// 获取网络状态
	status, err := manager.GetNetworkStatus()
	if err != nil {
		t.Fatalf("获取网络状态失败: %v", err)
	}
	
	t.Logf("=== 网络接口状态测试 ===")
	t.<PERSON>gf("发现 %d 个网络接口", len(status.Interfaces))
	
	// 验证每个接口都有状态字段
	for i, iface := range status.Interfaces {
		t.Logf("\n接口 %d:", i+1)
		t.Logf("  名称: %s", iface.Name)
		t.Logf("  显示名称: %s", iface.DisplayName)
		t.Logf("  类型: %s", iface.Type)
		t.Logf("  状态: %s", iface.Status)
		t.Logf("  IP地址: %s", iface.IPAddress)
		t.Logf("  MAC地址: %s", iface.MACAddress)
		t.Logf("  MTU: %d", iface.MTU)
		t.Logf("  配置方法: %s", iface.Method)
		t.Logf("  DNS服务器: %v", iface.DNSServers)
		
		// 验证状态字段不为空
		if iface.Status == "" {
			t.Errorf("接口 %s 的状态字段为空", iface.Name)
		}
		
		// 验证状态字段的有效值
		validStatuses := map[string]bool{
			"up":      true,
			"down":    true,
			"no-ip":   true,
			"unknown": true,
		}
		
		if !validStatuses[iface.Status] {
			t.Errorf("接口 %s 的状态值无效: %s", iface.Name, iface.Status)
		}
		
		// 验证其他必要字段
		if iface.Name == "" {
			t.Errorf("接口名称不能为空")
		}
		
		if iface.Type == "" {
			t.Errorf("接口 %s 的类型字段为空", iface.Name)
		}
		
		// 验证显示名称
		if iface.DisplayName == "" {
			t.Errorf("接口 %s 的显示名称为空", iface.Name)
		}
	}
	
	// 验证至少有一个接口
	if len(status.Interfaces) == 0 {
		t.Error("没有发现任何网络接口")
	}
	
	// 验证JSON序列化包含status字段
	jsonData, err := json.Marshal(status)
	if err != nil {
		t.Fatalf("JSON序列化失败: %v", err)
	}
	
	jsonStr := string(jsonData)
	if !contains(jsonStr, "\"status\"") {
		t.Error("JSON输出中缺少status字段")
	}
	
	t.Logf("\nJSON输出示例 (前500字符):")
	if len(jsonStr) > 500 {
		t.Logf("%s...", jsonStr[:500])
	} else {
		t.Logf("%s", jsonStr)
	}
}

// TestInterfaceStatusTypes 测试不同状态类型
func TestInterfaceStatusTypes(t *testing.T) {
	manager := monitor.NewManager(5*time.Second, 100)
	
	status, err := manager.GetNetworkStatus()
	if err != nil {
		t.Fatalf("获取网络状态失败: %v", err)
	}
	
	t.Logf("=== 接口状态类型统计 ===")
	
	statusCount := make(map[string]int)
	for _, iface := range status.Interfaces {
		statusCount[iface.Status]++
	}
	
	for status, count := range statusCount {
		t.Logf("状态 '%s': %d 个接口", status, count)
	}
	
	// 验证至少有一个up状态的接口（通常应该有）
	if statusCount["up"] == 0 {
		t.Log("警告: 没有发现状态为'up'的接口")
	}
}

// TestInterfaceDisplayNames 测试接口显示名称
func TestInterfaceDisplayNames(t *testing.T) {
	manager := monitor.NewManager(5*time.Second, 100)
	
	status, err := manager.GetNetworkStatus()
	if err != nil {
		t.Fatalf("获取网络状态失败: %v", err)
	}
	
	t.Logf("=== 接口显示名称测试 ===")
	
	for _, iface := range status.Interfaces {
		t.Logf("接口 %s -> 显示名称: %s", iface.Name, iface.DisplayName)
		
		// 验证显示名称不等于接口名称（除非是特殊情况）
		if iface.DisplayName == iface.Name && 
		   !isSpecialInterface(iface.Name) {
			t.Logf("注意: 接口 %s 的显示名称与接口名称相同", iface.Name)
		}
	}
}

// TestInterfaceConfigMethod 测试接口配置方法
func TestInterfaceConfigMethod(t *testing.T) {
	manager := monitor.NewManager(5*time.Second, 100)
	
	status, err := manager.GetNetworkStatus()
	if err != nil {
		t.Fatalf("获取网络状态失败: %v", err)
	}
	
	t.Logf("=== 接口配置方法测试 ===")
	
	methodCount := make(map[string]int)
	for _, iface := range status.Interfaces {
		methodCount[iface.Method]++
		t.Logf("接口 %s: IP=%s, 方法=%s", iface.Name, iface.IPAddress, iface.Method)
	}
	
	for method, count := range methodCount {
		t.Logf("配置方法 '%s': %d 个接口", method, count)
	}
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    contains(s[1:], substr) || 
		    (len(s) > 0 && s[:len(substr)] == substr))
}

func isSpecialInterface(name string) bool {
	specialInterfaces := []string{"lo", "localhost"}
	for _, special := range specialInterfaces {
		if name == special {
			return true
		}
	}
	return false
}
