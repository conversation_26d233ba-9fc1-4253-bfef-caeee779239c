package test

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"ecp/internal/app/network"
	"ecp/internal/app/network/monitor"

	"github.com/gin-gonic/gin"
)

// TestNetworkMonitorIntegration 测试网络监控集成
func TestNetworkMonitorIntegration(t *testing.T) {
	// 创建网络服务配置
	cfg := network.NetworkConfig{
		MonitorInterval:   5 * time.Second,
		TrafficSampleRate: 100,
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   2 * time.Second,
		IptablesPath:      "/sbin/iptables",
		BackupPath:        "/tmp/firewall_backup",
	}

	// 创建网络服务
	service := network.NewNetworkService(cfg)

	// 创建API
	api := network.NewAPI(service)

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	router := gin.New()
	networkGroup := router.Group("/api/network")
	{
		networkGroup.GET("/status", api.GetNetworkStatus)
		networkGroup.GET("/traffic", api.GetTrafficStats)
	}

	t.Run("测试网络状态API", func(t *testing.T) {
		// 创建请求
		req, _ := http.NewRequest("GET", "/api/network/status", nil)
		w := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(w, req)

		// 验证响应
		if w.Code != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", w.Code)
		}

		// 解析响应
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		if err != nil {
			t.Fatalf("解析响应失败: %v", err)
		}

		// 验证响应结构
		if !response["success"].(bool) {
			t.Error("响应success字段应为true")
		}

		data := response["data"].(map[string]interface{})
		interfaces := data["interfaces"].([]interface{})

		t.Logf("网络状态API返回 %d 个接口", len(interfaces))

		// 验证每个接口都有status字段
		for i, iface := range interfaces {
			ifaceMap := iface.(map[string]interface{})

			// 验证必要字段
			requiredFields := []string{"name", "status", "type", "display_name"}
			for _, field := range requiredFields {
				if _, exists := ifaceMap[field]; !exists {
					t.Errorf("接口 %d 缺少字段: %s", i, field)
				}
			}

			// 验证status字段值
			status := ifaceMap["status"].(string)
			validStatuses := map[string]bool{
				"up": true, "down": true, "no-ip": true, "unknown": true,
			}
			if !validStatuses[status] {
				t.Errorf("接口 %d 状态值无效: %s", i, status)
			}

			t.Logf("接口 %d: %s - 状态: %s", i+1, ifaceMap["name"], status)
		}
	})

	t.Run("测试流量统计API", func(t *testing.T) {
		// 创建请求
		req, _ := http.NewRequest("GET", "/api/network/traffic", nil)
		w := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(w, req)

		// 验证响应
		if w.Code != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", w.Code)
		}

		// 解析响应
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		if err != nil {
			t.Fatalf("解析响应失败: %v", err)
		}

		// 验证响应结构
		if !response["success"].(bool) {
			t.Error("响应success字段应为true")
		}

		data := response["data"].([]interface{})
		t.Logf("流量统计API返回 %d 个接口", len(data))

		// 验证每个接口的流量数据
		for i, stat := range data {
			statMap := stat.(map[string]interface{})

			// 验证必要字段
			requiredFields := []string{"interface", "rx_bytes", "tx_bytes", "rx_packets", "tx_packets"}
			for _, field := range requiredFields {
				if _, exists := statMap[field]; !exists {
					t.Errorf("流量统计 %d 缺少字段: %s", i, field)
				}
			}

			// 注意：流量统计API不包含status字段，这是正常的
			t.Logf("流量统计 %d: %s - RX: %.0f bytes, TX: %.0f bytes",
				i+1, statMap["interface"], statMap["rx_bytes"], statMap["tx_bytes"])
		}
	})

	t.Run("验证前端数据合并逻辑", func(t *testing.T) {
		// 模拟前端的数据合并逻辑

		// 1. 获取网络状态
		statusReq, _ := http.NewRequest("GET", "/api/network/status", nil)
		statusW := httptest.NewRecorder()
		router.ServeHTTP(statusW, statusReq)

		var statusResponse map[string]interface{}
		json.Unmarshal(statusW.Body.Bytes(), &statusResponse)

		// 2. 获取流量统计
		trafficReq, _ := http.NewRequest("GET", "/api/network/traffic", nil)
		trafficW := httptest.NewRecorder()
		router.ServeHTTP(trafficW, trafficReq)

		var trafficResponse map[string]interface{}
		json.Unmarshal(trafficW.Body.Bytes(), &trafficResponse)

		// 3. 模拟前端合并逻辑
		statusMap := make(map[string]map[string]interface{})
		if statusResponse["success"].(bool) {
			data := statusResponse["data"].(map[string]interface{})
			interfaces := data["interfaces"].([]interface{})

			for _, iface := range interfaces {
				ifaceMap := iface.(map[string]interface{})
				name := ifaceMap["name"].(string)
				statusMap[name] = ifaceMap
			}
		}

		// 4. 合并数据
		var mergedData []map[string]interface{}
		if trafficResponse["success"].(bool) {
			stats := trafficResponse["data"].([]interface{})

			for _, stat := range stats {
				statMap := stat.(map[string]interface{})
				interfaceName := statMap["interface"].(string)

				// 合并状态信息
				merged := make(map[string]interface{})
				for k, v := range statMap {
					merged[k] = v
				}

				if statusInfo, exists := statusMap[interfaceName]; exists {
					merged["status"] = statusInfo["status"]
					merged["display_name"] = statusInfo["display_name"]
					merged["type"] = statusInfo["type"]
					merged["ip_address"] = statusInfo["ip_address"]
				} else {
					merged["status"] = "unknown"
					merged["display_name"] = interfaceName
					merged["type"] = "unknown"
					merged["ip_address"] = ""
				}

				mergedData = append(mergedData, merged)
			}
		}

		// 5. 验证合并结果
		t.Logf("合并后的数据包含 %d 个接口", len(mergedData))

		hasOnlineInterface := false
		for i, data := range mergedData {
			status := data["status"].(string)
			t.Logf("合并数据 %d: %s - 状态: %s", i+1, data["interface"], status)

			// 验证每个合并后的数据都有status字段
			if status == "" {
				t.Errorf("合并数据 %d 的status字段为空", i)
			}

			if status == "up" {
				hasOnlineInterface = true
			}
		}

		// 验证至少有一个在线接口（通常应该有）
		if !hasOnlineInterface {
			t.Log("注意: 没有发现状态为'up'的接口")
		}

		t.Log("✅ 前端数据合并逻辑验证通过")
	})
}

// TestNetworkStatusFields 测试网络状态字段完整性
func TestNetworkStatusFields(t *testing.T) {
	manager := monitor.NewManager(5*time.Second, 100)

	status, err := manager.GetNetworkStatus()
	if err != nil {
		t.Fatalf("获取网络状态失败: %v", err)
	}

	t.Log("=== 网络状态字段完整性测试 ===")

	for i, iface := range status.Interfaces {
		t.Logf("接口 %d:", i+1)
		t.Logf("  名称: %s", iface.Name)
		t.Logf("  显示名称: %s", iface.DisplayName)
		t.Logf("  类型: %s", iface.Type)
		t.Logf("  状态: %s", iface.Status)
		t.Logf("  IP地址: %s", iface.IPAddress)
		t.Logf("  MAC地址: %s", iface.MACAddress)
		t.Logf("  配置方法: %s", iface.Method)

		// 验证关键字段不为空
		if iface.Name == "" {
			t.Errorf("接口 %d 名称为空", i+1)
		}
		if iface.Status == "" {
			t.Errorf("接口 %d 状态为空", i+1)
		}
		if iface.DisplayName == "" {
			t.Errorf("接口 %d 显示名称为空", i+1)
		}
	}

	t.Logf("总计: %d 个网络接口", len(status.Interfaces))
}
