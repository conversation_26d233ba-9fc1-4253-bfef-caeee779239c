package test

import (
	"context"
	"ecp/internal/app/gb28181"
	"ecp/internal/app/video"
	"ecp/internal/pkg/database"
	"testing"
	"time"
)

func TestGB28181Integration(t *testing.T) {
	// 跳过集成测试，除非明确指定
	if testing.Short() {
		t.<PERSON><PERSON>("跳过GB28181集成测试")
	}

	// 创建测试数据库
	db, err := database.NewDB(&database.Config{
		Path: ":memory:", // 使用内存数据库进行测试
	})
	if err != nil {
		t.Fatalf("创建测试数据库失败: %v", err)
	}
	defer db.Close()

	// 初始化数据库表结构
	if err := db.InitSchema(); err != nil {
		t.Fatalf("初始化数据库表结构失败: %v", err)
	}

	// 创建Mock适配器进行测试
	mockAdapter := gb28181.NewMockAdapter()

	// 创建带GB28181支持的视频服务
	videoService := video.NewVideoServiceWithGB28181(db.DB, mockAdapter)

	t.Run("测试GB28181适配器基本功能", func(t *testing.T) {
		// 测试获取设备列表
		devices, err := videoService.GetGB28181Devices()
		if err != nil {
			t.Errorf("获取GB28181设备列表失败: %v", err)
		}

		// Mock适配器应该返回空列表
		if len(devices) != 0 {
			t.Errorf("期望设备列表为空，实际得到: %d", len(devices))
		}
	})

	t.Run("测试GB28181视频源创建", func(t *testing.T) {
		// 创建GB28181视频源
		testVideo := &database.Video{
			Name:        "测试GB28181摄像头",
			Protocol:    "gb28181",
			CameraID:    "44010200491180000001",
			Description: "GB28181测试设备",
			StreamType:  "自动生成",
			CameraIP:    "*************",
			Brand:       "海康威视",
			StreamMode:  "主码流",
			Status:      "offline",
		}

		err := videoService.Create(testVideo)
		if err != nil {
			t.Fatalf("创建GB28181视频源失败: %v", err)
		}

		// 验证URL是否正确生成
		expectedURL := "gb28181://44010200491180000001"
		if testVideo.URL != expectedURL {
			t.Errorf("期望URL: %s, 实际URL: %s", expectedURL, testVideo.URL)
		}
	})

	t.Run("测试GB28181连接测试", func(t *testing.T) {
		testVideo := &database.Video{
			Protocol: "gb28181",
			CameraID: "44010200491180000001",
		}

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// 使用Mock适配器，连接测试应该通过但服务不健康
		err := videoService.TestConnection(ctx, testVideo)
		if err == nil {
			t.Error("期望Mock适配器返回错误，但测试通过了")
		}
	})

	t.Run("测试GB28181服务健康检查", func(t *testing.T) {
		// Mock适配器的健康检查应该返回false
		isHealthy := videoService.IsGB28181ServiceHealthy()
		if isHealthy {
			t.Error("期望Mock适配器返回不健康状态")
		}
	})

	t.Run("测试同步GB28181设备", func(t *testing.T) {
		// 测试设备同步
		err := videoService.SyncGB28181Devices()
		if err != nil {
			t.Errorf("同步GB28181设备失败: %v", err)
		}
	})

	t.Run("测试云台控制", func(t *testing.T) {
		testVideo := &database.Video{
			Protocol: "gb28181",
			CameraID: "44010200491180000001",
		}

		// 测试云台控制（Mock适配器应该成功）
		err := videoService.PTZControl(testVideo, "up", 50, 50, 0)
		if err != nil {
			t.Errorf("云台控制失败: %v", err)
		}

		// 测试非GB28181设备的云台控制
		rtspVideo := &database.Video{
			Protocol: "rtsp",
		}

		err = videoService.PTZControl(rtspVideo, "up", 50, 50, 0)
		if err == nil {
			t.Error("期望非GB28181设备的云台控制失败")
		}
	})
}

func TestGB28181RealService(t *testing.T) {
	// 这个测试需要真实的GB28181服务运行
	// 通常在CI/CD中会跳过
	if testing.Short() {
		t.Skip("跳过需要真实GB28181服务的测试")
	}

	// 检查环境变量决定是否运行真实服务测试
	gb28181URL := "http://localhost:18080"

	// 创建测试数据库
	db, err := database.NewDB(&database.Config{
		Path: ":memory:",
	})
	if err != nil {
		t.Fatalf("创建测试数据库失败: %v", err)
	}
	defer db.Close()

	// 初始化数据库表结构
	if err := db.InitSchema(); err != nil {
		t.Fatalf("初始化数据库表结构失败: %v", err)
	}

	// 创建真实的GB28181适配器
	adapter := gb28181.NewAdapter(gb28181URL, db)

	// 首先检查服务是否可用
	if !adapter.IsHealthy() {
		t.Skip("GB28181服务不可用，跳过真实服务测试")
	}

	videoService := video.NewVideoServiceWithGB28181(db.DB, adapter)

	t.Run("测试真实GB28181服务连接", func(t *testing.T) {
		// 测试获取设备列表
		devices, err := videoService.GetGB28181Devices()
		if err != nil {
			t.Logf("获取设备列表失败（可能是正常的）: %v", err)
		} else {
			t.Logf("获取到 %d 个GB28181设备", len(devices))
		}
	})

	t.Run("测试设备同步", func(t *testing.T) {
		err := videoService.SyncGB28181Devices()
		if err != nil {
			t.Logf("设备同步失败（可能是正常的）: %v", err)
		}

		// 检查同步后的视频源数量
		videos, err := videoService.GetAll()
		if err != nil {
			t.Fatalf("获取视频源列表失败: %v", err)
		}

		gb28181Count := 0
		for _, v := range videos {
			if v.Protocol == "gb28181" {
				gb28181Count++
			}
		}

		t.Logf("同步后有 %d 个GB28181视频源", gb28181Count)
	})
}

// 性能测试
func BenchmarkGB28181Operations(b *testing.B) {
	// 创建测试数据库
	db, err := database.NewDB(&database.Config{
		Path: ":memory:",
	})
	if err != nil {
		b.Fatalf("创建测试数据库失败: %v", err)
	}
	defer db.Close()

	if err := db.InitSchema(); err != nil {
		b.Fatalf("初始化数据库表结构失败: %v", err)
	}

	mockAdapter := gb28181.NewMockAdapter()
	videoService := video.NewVideoServiceWithGB28181(db.DB, mockAdapter)

	b.Run("GetGB28181Devices", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, _ = videoService.GetGB28181Devices()
		}
	})

	b.Run("IsGB28181ServiceHealthy", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = videoService.IsGB28181ServiceHealthy()
		}
	})

	b.Run("SyncGB28181Devices", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = videoService.SyncGB28181Devices()
		}
	})
}
