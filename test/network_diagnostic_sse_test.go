package test

import (
	"bufio"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"ecp/internal/app/network"

	"github.com/gin-gonic/gin"
)

// TestPingSSE 测试Ping SSE流式输出
func TestPingSSE(t *testing.T) {
	// 创建网络服务
	cfg := network.NetworkConfig{
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   2 * time.Second,
	}
	service := network.NewNetworkService(cfg)
	api := network.NewAPI(service)

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	router := gin.New()
	router.GET("/api/network/diagnostic/ping/stream", api.PingTestStream)

	t.Run("测试Ping SSE流式输出", func(t *testing.T) {
		// 创建请求
		req, _ := http.NewRequest("GET", "/api/network/diagnostic/ping/stream?target=127.0.0.1&count=3", nil)
		w := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(w, req)

		// 验证响应头
		if w.Code != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", w.Code)
		}

		contentType := w.Header().Get("Content-Type")
		if contentType != "text/event-stream" {
			t.Errorf("期望Content-Type为text/event-stream, 得到 %s", contentType)
		}

		// 解析SSE响应
		body := w.Body.String()
		t.Logf("SSE响应长度: %d bytes", len(body))

		// 验证SSE格式
		lines := strings.Split(body, "\n")
		var outputCount, resultCount, closeCount int

		for _, line := range lines {
			if strings.HasPrefix(line, "data: ") {
				dataStr := strings.TrimPrefix(line, "data: ")
				if dataStr == "" {
					continue
				}

				var message map[string]interface{}
				if err := json.Unmarshal([]byte(dataStr), &message); err != nil {
					t.Logf("解析SSE消息失败: %v, 数据: %s", err, dataStr)
					continue
				}

				msgType, ok := message["type"].(string)
				if !ok {
					t.Errorf("SSE消息缺少type字段")
					continue
				}

				switch msgType {
				case "output":
					outputCount++
					content := message["content"].(string)
					t.Logf("输出: %s", content)
				case "result":
					resultCount++
					t.Logf("结果: %v", message["content"])
				case "close":
					closeCount++
					t.Logf("连接关闭")
				case "error":
					t.Logf("错误: %v", message["content"])
				}
			}
		}

		t.Logf("SSE消息统计: 输出=%d, 结果=%d, 关闭=%d", outputCount, resultCount, closeCount)

		// 验证至少有输出和结果
		if outputCount == 0 {
			t.Error("没有收到ping输出消息")
		}
		if resultCount == 0 {
			t.Error("没有收到ping结果消息")
		}
		if closeCount == 0 {
			t.Error("没有收到连接关闭消息")
		}
	})
}

// TestTracerouteSSE 测试Traceroute SSE流式输出
func TestTracerouteSSE(t *testing.T) {
	// 创建网络服务
	cfg := network.NetworkConfig{
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 10, // 减少跳数以加快测试
		PortScanTimeout:   2 * time.Second,
	}
	service := network.NewNetworkService(cfg)
	api := network.NewAPI(service)

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	router := gin.New()
	router.GET("/api/network/diagnostic/traceroute/stream", api.TracerouteTestStream)

	t.Run("测试Traceroute SSE流式输出", func(t *testing.T) {
		// 创建请求
		req, _ := http.NewRequest("GET", "/api/network/diagnostic/traceroute/stream?target=127.0.0.1", nil)
		w := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(w, req)

		// 验证响应头
		if w.Code != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", w.Code)
		}

		contentType := w.Header().Get("Content-Type")
		if contentType != "text/event-stream" {
			t.Errorf("期望Content-Type为text/event-stream, 得到 %s", contentType)
		}

		// 解析SSE响应
		body := w.Body.String()
		t.Logf("SSE响应长度: %d bytes", len(body))

		// 验证SSE格式
		lines := strings.Split(body, "\n")
		var outputCount, resultCount, closeCount int

		for _, line := range lines {
			if strings.HasPrefix(line, "data: ") {
				dataStr := strings.TrimPrefix(line, "data: ")
				if dataStr == "" {
					continue
				}

				var message map[string]interface{}
				if err := json.Unmarshal([]byte(dataStr), &message); err != nil {
					t.Logf("解析SSE消息失败: %v, 数据: %s", err, dataStr)
					continue
				}

				msgType, ok := message["type"].(string)
				if !ok {
					t.Errorf("SSE消息缺少type字段")
					continue
				}

				switch msgType {
				case "output":
					outputCount++
					content := message["content"].(string)
					t.Logf("输出: %s", content)
				case "result":
					resultCount++
					t.Logf("结果: %v", message["content"])
				case "close":
					closeCount++
					t.Logf("连接关闭")
				case "error":
					t.Logf("错误: %v", message["content"])
				}
			}
		}

		t.Logf("SSE消息统计: 输出=%d, 结果=%d, 关闭=%d", outputCount, resultCount, closeCount)

		// 验证至少有输出和结果
		if outputCount == 0 {
			t.Error("没有收到traceroute输出消息")
		}
		if resultCount == 0 {
			t.Error("没有收到traceroute结果消息")
		}
		if closeCount == 0 {
			t.Error("没有收到连接关闭消息")
		}
	})
}

// TestSSEMessageFormat 测试SSE消息格式
func TestSSEMessageFormat(t *testing.T) {
	// 创建网络服务
	cfg := network.NetworkConfig{
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   2 * time.Second,
	}
	service := network.NewNetworkService(cfg)
	api := network.NewAPI(service)

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	router := gin.New()
	router.GET("/api/network/diagnostic/ping/stream", api.PingTestStream)

	t.Run("验证SSE消息格式", func(t *testing.T) {
		// 创建请求
		req, _ := http.NewRequest("GET", "/api/network/diagnostic/ping/stream?target=127.0.0.1&count=1", nil)
		w := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(w, req)

		// 解析响应
		body := w.Body.String()
		scanner := bufio.NewScanner(strings.NewReader(body))

		var validMessages int
		for scanner.Scan() {
			line := scanner.Text()
			if strings.HasPrefix(line, "data: ") {
				dataStr := strings.TrimPrefix(line, "data: ")
				if dataStr == "" {
					continue
				}

				// 验证JSON格式
				var message map[string]interface{}
				if err := json.Unmarshal([]byte(dataStr), &message); err != nil {
					t.Errorf("无效的JSON格式: %v, 数据: %s", err, dataStr)
					continue
				}

				// 验证必需字段
				if _, ok := message["type"]; !ok {
					t.Errorf("消息缺少type字段: %s", dataStr)
					continue
				}

				if _, ok := message["content"]; !ok {
					t.Errorf("消息缺少content字段: %s", dataStr)
					continue
				}

				validMessages++
				t.Logf("有效消息: type=%s", message["type"])
			}
		}

		if validMessages == 0 {
			t.Error("没有收到有效的SSE消息")
		} else {
			t.Logf("收到 %d 条有效SSE消息", validMessages)
		}
	})
}

// TestSSEErrorHandling 测试SSE错误处理
func TestSSEErrorHandling(t *testing.T) {
	// 创建网络服务
	cfg := network.NetworkConfig{
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   2 * time.Second,
	}
	service := network.NewNetworkService(cfg)
	api := network.NewAPI(service)

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	router := gin.New()
	router.GET("/api/network/diagnostic/ping/stream", api.PingTestStream)

	t.Run("测试无效目标地址", func(t *testing.T) {
		// 创建请求 - 无效的目标地址
		req, _ := http.NewRequest("GET", "/api/network/diagnostic/ping/stream?target=invalid.invalid.invalid&count=1", nil)
		w := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(w, req)

		// 验证响应
		if w.Code != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", w.Code)
		}

		// 解析响应，应该包含错误消息
		body := w.Body.String()
		t.Logf("错误响应: %s", body)

		// 验证包含错误消息
		if !strings.Contains(body, "error") {
			t.Error("响应中应该包含错误消息")
		}
	})

	t.Run("测试空目标地址", func(t *testing.T) {
		// 创建请求 - 空目标地址
		req, _ := http.NewRequest("GET", "/api/network/diagnostic/ping/stream?target=&count=1", nil)
		w := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(w, req)

		// 验证响应
		if w.Code != http.StatusBadRequest {
			t.Errorf("期望状态码 400, 得到 %d", w.Code)
		}
	})
}
