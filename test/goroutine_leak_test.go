package test

import (
	"runtime"
	"testing"
	"time"

	"ecp/internal/app/network"
)

// TestNetworkMonitorGoroutineLeak 测试网络监控goroutine是否正确退出
func TestNetworkMonitorGoroutineLeak(t *testing.T) {
	// 创建网络服务
	cfg := network.NetworkConfig{
		MonitorInterval:   5 * time.Second,
		TrafficSampleRate: 100,
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   2 * time.Second,
		IptablesPath:      "/sbin/iptables",
		BackupPath:        "/tmp/firewall_backup",
	}
	service := network.NewNetworkService(cfg)

	// 记录初始goroutine数量
	initialGoroutines := runtime.NumGoroutine()
	t.Logf("初始goroutine数量: %d", initialGoroutines)

	// 创建通道
	dataChan := make(chan interface{}, 100)
	errorChan := make(chan error, 10)
	done := make(chan bool, 1)

	// 启动监控
	go service.NetworkMonitorStream(dataChan, errorChan, done)

	// 等待一段时间，让监控运行
	time.Sleep(3 * time.Second)

	// 检查goroutine数量是否增加
	runningGoroutines := runtime.NumGoroutine()
	t.Logf("运行中goroutine数量: %d", runningGoroutines)

	if runningGoroutines <= initialGoroutines {
		t.Error("监控goroutine似乎没有启动")
	}

	// 发送停止信号
	t.Log("发送停止信号")
	done <- true

	// 等待goroutine退出
	time.Sleep(2 * time.Second)

	// 检查goroutine是否正确退出
	finalGoroutines := runtime.NumGoroutine()
	t.Logf("最终goroutine数量: %d", finalGoroutines)

	// 允许一些误差，但不应该有明显的泄漏
	if finalGoroutines > initialGoroutines+2 {
		t.Errorf("可能存在goroutine泄漏，初始: %d, 最终: %d", initialGoroutines, finalGoroutines)
	}

	// 强制垃圾回收
	runtime.GC()
	time.Sleep(1 * time.Second)

	// 再次检查
	afterGCGoroutines := runtime.NumGoroutine()
	t.Logf("GC后goroutine数量: %d", afterGCGoroutines)

	if afterGCGoroutines > initialGoroutines+1 {
		t.Errorf("GC后仍有goroutine泄漏，初始: %d, GC后: %d", initialGoroutines, afterGCGoroutines)
	}
}

// TestMultipleNetworkMonitorSessions 测试多次启动和停止监控
func TestMultipleNetworkMonitorSessions(t *testing.T) {
	// 创建网络服务
	cfg := network.NetworkConfig{
		MonitorInterval:   5 * time.Second,
		TrafficSampleRate: 100,
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   2 * time.Second,
		IptablesPath:      "/sbin/iptables",
		BackupPath:        "/tmp/firewall_backup",
	}
	service := network.NewNetworkService(cfg)

	// 记录初始goroutine数量
	initialGoroutines := runtime.NumGoroutine()
	t.Logf("初始goroutine数量: %d", initialGoroutines)

	// 多次启动和停止监控
	for i := 0; i < 5; i++ {
		t.Logf("第 %d 次监控会话", i+1)

		// 创建通道
		dataChan := make(chan interface{}, 100)
		errorChan := make(chan error, 10)
		done := make(chan bool, 1)

		// 启动监控
		go service.NetworkMonitorStream(dataChan, errorChan, done)

		// 运行一段时间
		time.Sleep(1 * time.Second)

		// 停止监控
		done <- true

		// 等待退出
		time.Sleep(500 * time.Millisecond)

		// 检查goroutine数量
		currentGoroutines := runtime.NumGoroutine()
		t.Logf("第 %d 次会话后goroutine数量: %d", i+1, currentGoroutines)
	}

	// 最终检查
	time.Sleep(2 * time.Second)
	runtime.GC()
	time.Sleep(1 * time.Second)

	finalGoroutines := runtime.NumGoroutine()
	t.Logf("所有会话结束后goroutine数量: %d", finalGoroutines)

	if finalGoroutines > initialGoroutines+2 {
		t.Errorf("多次会话后存在goroutine泄漏，初始: %d, 最终: %d", initialGoroutines, finalGoroutines)
	}
}

// TestNetworkMonitorDataFlow 测试数据流是否正常
func TestNetworkMonitorDataFlow(t *testing.T) {
	// 创建网络服务
	cfg := network.NetworkConfig{
		MonitorInterval:   5 * time.Second,
		TrafficSampleRate: 100,
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   2 * time.Second,
		IptablesPath:      "/sbin/iptables",
		BackupPath:        "/tmp/firewall_backup",
	}
	service := network.NewNetworkService(cfg)

	// 创建通道
	dataChan := make(chan interface{}, 100)
	errorChan := make(chan error, 10)
	done := make(chan bool, 1)

	// 启动监控
	go service.NetworkMonitorStream(dataChan, errorChan, done)

	// 收集数据
	var dataCount, errorCount int
	timeout := time.After(5 * time.Second)

	for {
		select {
		case data := <-dataChan:
			dataCount++
			t.Logf("收到数据 #%d: %T", dataCount, data)
			
			// 检查数据格式
			if dataMap, ok := data.(map[string]interface{}); ok {
				if dataType, exists := dataMap["type"]; exists && dataType == "heartbeat" {
					t.Logf("收到心跳消息")
				} else {
					t.Logf("收到监控数据")
				}
			}

		case err := <-errorChan:
			errorCount++
			t.Logf("收到错误 #%d: %v", errorCount, err)

		case <-timeout:
			t.Log("测试超时，发送停止信号")
			done <- true
			goto cleanup
		}
	}

cleanup:
	// 等待清理
	time.Sleep(1 * time.Second)

	t.Logf("测试结果: 数据消息=%d, 错误消息=%d", dataCount, errorCount)

	// 验证至少收到了一些数据
	if dataCount == 0 {
		t.Error("没有收到任何数据消息")
	}

	// 验证没有太多错误
	if errorCount > dataCount {
		t.Errorf("错误消息过多: 数据=%d, 错误=%d", dataCount, errorCount)
	}
}

// TestNetworkMonitorStopSignal 测试停止信号的响应
func TestNetworkMonitorStopSignal(t *testing.T) {
	// 创建网络服务
	cfg := network.NetworkConfig{
		MonitorInterval:   5 * time.Second,
		TrafficSampleRate: 100,
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   2 * time.Second,
		IptablesPath:      "/sbin/iptables",
		BackupPath:        "/tmp/firewall_backup",
	}
	service := network.NewNetworkService(cfg)

	// 创建通道
	dataChan := make(chan interface{}, 100)
	errorChan := make(chan error, 10)
	done := make(chan bool, 1)

	// 启动监控
	go service.NetworkMonitorStream(dataChan, errorChan, done)

	// 等待监控启动
	time.Sleep(1 * time.Second)

	// 发送停止信号
	stopTime := time.Now()
	done <- true

	// 监控是否还在发送数据
	timeout := time.After(3 * time.Second)
	var dataAfterStop int

	for {
		select {
		case data := <-dataChan:
			if time.Since(stopTime) > 500*time.Millisecond {
				dataAfterStop++
				t.Logf("停止信号发送后仍收到数据: %T", data)
			}

		case <-timeout:
			goto check
		}
	}

check:
	if dataAfterStop > 1 {
		t.Errorf("停止信号发送后仍收到 %d 条数据，可能goroutine没有正确退出", dataAfterStop)
	} else {
		t.Log("停止信号响应正常")
	}
}
