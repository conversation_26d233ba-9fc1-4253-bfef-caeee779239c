package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"ecp/internal/app/onvif"
)

func main() {
	fmt.Println("=== 智能认证系统测试 ===")

	// 创建ONVIF客户端
	deviceURL := "http://1.116.255.13:58203/onvif/device_service"
	client := onvif.NewONVIFClient(deviceURL, "admin", "fnic1234")

	fmt.Println("1. 默认认证配置（WS-Security优先，允许降级）")

	// 测试获取设备信息
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	deviceInfo, err := client.GetDeviceInformation(ctx)
	if err != nil {
		log.Printf("获取设备信息失败: %v", err)
	} else {
		fmt.Printf("设备信息获取成功:\n")
		fmt.Printf("  制造商: '%s'\n", deviceInfo.Manufacturer)
		fmt.Printf("  型号: '%s'\n", deviceInfo.Model)
		fmt.Printf("  固件版本: '%s'\n", deviceInfo.FirmwareVersion)
	}

	// 等待5秒后进行流地址测试
	time.Sleep(5 * time.Second)

	fmt.Println("\n=== RTSP流地址测试 ===")

	// 1. 获取设备能力
	fmt.Println("1. 获取设备能力...")
	err = client.GetCapabilities(ctx)
	if err != nil {
		log.Printf("获取设备能力失败: %v", err)
		return
	}
	fmt.Println("设备能力获取成功")

	// 2. 获取媒体配置
	fmt.Println("\n2. 获取媒体配置...")
	profiles, err := client.GetProfiles(ctx)
	if err != nil {
		log.Printf("获取媒体配置失败: %v", err)
		fmt.Println("继续其他测试...")
		return // 暂时停止这里看调试信息
	}
	fmt.Printf("找到 %d 个媒体配置\n", len(profiles))

	// 3. 获取每个配置的流地址
	fmt.Println("\n3. 获取流地址...")
	for i, profile := range profiles {
		fmt.Printf("\n配置 %d:\n", i+1)
		fmt.Printf("  名称: %s\n", profile.Name)
		fmt.Printf("  Token: %s\n", profile.Token)

		if profile.VideoEncoderConfiguration.Encoding != "" {
			fmt.Printf("  编码: %s\n", profile.VideoEncoderConfiguration.Encoding)
			fmt.Printf("  分辨率: %dx%d\n",
				profile.VideoEncoderConfiguration.Resolution.Width,
				profile.VideoEncoderConfiguration.Resolution.Height)
		}

		// 获取RTSP流地址
		streamURI, err := client.GetStreamURI(ctx, profile.Token)
		if err != nil {
			log.Printf("  获取流地址失败: %v", err)
			continue
		}
		fmt.Printf("  RTSP地址: %s\n", streamURI)

		// 获取快照地址
		snapshotURI, err := client.GetSnapshotURI(ctx, profile.Token)
		if err != nil {
			log.Printf("  获取快照地址失败: %v", err)
		} else {
			fmt.Printf("  快照地址: %s\n", snapshotURI)
		}
	}

	fmt.Println("\n=== 测试完成 ===")
}
