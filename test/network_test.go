package test

import (
	"testing"
	"time"

	"ecp/internal/app/network/monitor"
	"ecp/internal/app/network/firewall"
	"ecp/internal/app/network/diagnostic"
)

func TestNetworkMonitor(t *testing.T) {
	// 创建网络监控管理器
	manager := monitor.NewManager(5*time.Second, 100)
	
	// 测试获取网络状态
	status, err := manager.GetNetworkStatus()
	if err != nil {
		t.<PERSON>rrorf("获取网络状态失败: %v", err)
		return
	}
	
	t.Logf("网络状态: 接口数量=%d, 默认网关=%s, DNS服务器数量=%d, 互联网访问=%v", 
		len(status.Interfaces), status.DefaultGateway, len(status.DNSServers), status.InternetAccess)
	
	// 测试获取流量统计
	stats, err := manager.GetTrafficStats()
	if err != nil {
		t.Errorf("获取流量统计失败: %v", err)
		return
	}
	
	t.Logf("流量统计: 接口数量=%d", len(stats))
	for _, stat := range stats {
		t.Logf("接口 %s: RX=%d bytes, TX=%d bytes", stat.Interface, stat.RxBytes, stat.TxBytes)
	}
	
	// 测试网络质量检测
	quality, err := manager.GetNetworkQuality("*******")
	if err != nil {
		t.Errorf("获取网络质量失败: %v", err)
		return
	}
	
	t.Logf("网络质量: 目标=%s, 延迟=%v, 丢包率=%.2f%%", 
		quality.Target, quality.Latency, quality.PacketLoss)
}

func TestFirewallManager(t *testing.T) {
	// 创建防火墙管理器
	manager := firewall.NewManager("/sbin/iptables", "/tmp/firewall_backup")
	
	// 测试获取防火墙状态
	status, err := manager.GetStatus()
	if err != nil {
		t.Logf("获取防火墙状态失败 (可能是权限问题): %v", err)
		return
	}
	
	t.Logf("防火墙状态: 启用=%v, 默认策略=%s, 规则数量=%d", 
		status.Enabled, status.DefaultPolicy, status.RuleCount)
	
	// 测试获取防火墙规则
	rules, err := manager.GetRules()
	if err != nil {
		t.Logf("获取防火墙规则失败 (可能是权限问题): %v", err)
		return
	}
	
	t.Logf("防火墙规则数量: %d", len(rules))
}

func TestDiagnosticManager(t *testing.T) {
	// 创建诊断管理器
	manager := diagnostic.NewManager(5*time.Second, 30, 3*time.Second)
	
	// 测试ping功能
	pingResult, err := manager.Ping("*******", 3)
	if err != nil {
		t.Errorf("Ping测试失败: %v", err)
		return
	}
	
	t.Logf("Ping结果: 目标=%s, 发送=%d, 接收=%d, 丢包率=%.2f%%, 平均延迟=%v", 
		pingResult.Target, pingResult.PacketsSent, pingResult.PacketsRecv, 
		pingResult.PacketLoss, pingResult.AvgLatency)
	
	// 测试端口扫描
	ports := []int{80, 443, 22}
	scanResult, err := manager.PortScan("*******", ports)
	if err != nil {
		t.Errorf("端口扫描失败: %v", err)
		return
	}
	
	t.Logf("端口扫描结果: 目标=%s, 开放端口=%v", scanResult.Target, scanResult.OpenPorts)
	
	// 测试网络诊断
	diagResult, err := manager.DiagnoseNetwork()
	if err != nil {
		t.Errorf("网络诊断失败: %v", err)
		return
	}
	
	t.Logf("网络诊断结果: 问题数量=%d, 建议数量=%d", len(diagResult.Issues), len(diagResult.Suggestions))
	for i, issue := range diagResult.Issues {
		t.Logf("问题 %d: %s", i+1, issue)
	}
	for i, suggestion := range diagResult.Suggestions {
		t.Logf("建议 %d: %s", i+1, suggestion)
	}
}

func TestNetworkMonitoringLoop(t *testing.T) {
	// 创建网络监控管理器
	manager := monitor.NewManager(1*time.Second, 100)
	
	// 启动监控
	manager.StartMonitoring()
	t.Log("网络监控已启动")
	
	// 等待几秒钟让监控运行
	time.Sleep(3 * time.Second)
	
	// 获取流量统计（应该包含速度信息）
	stats, err := manager.GetTrafficStats()
	if err != nil {
		t.Errorf("获取流量统计失败: %v", err)
	} else {
		t.Logf("监控运行后的流量统计: 接口数量=%d", len(stats))
		for _, stat := range stats {
			t.Logf("接口 %s: RX速度=%.2f bytes/s, TX速度=%.2f bytes/s", 
				stat.Interface, stat.RxSpeed, stat.TxSpeed)
		}
	}
	
	// 停止监控
	manager.StopMonitoring()
	t.Log("网络监控已停止")
}
