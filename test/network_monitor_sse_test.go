package test

import (
	"bufio"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"ecp/internal/app/network"
	"github.com/gin-gonic/gin"
)

// TestNetworkMonitorSSE 测试网络监控SSE流式输出
func TestNetworkMonitorSSE(t *testing.T) {
	// 创建网络服务
	cfg := network.NetworkConfig{
		MonitorInterval:   5 * time.Second,
		TrafficSampleRate: 100,
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   2 * time.Second,
		IptablesPath:      "/sbin/iptables",
		BackupPath:        "/tmp/firewall_backup",
	}
	service := network.NewNetworkService(cfg)
	api := network.NewAPI(service)

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	router := gin.New()
	router.GET("/api/network/monitor/stream", api.NetworkMonitorStream)

	t.Run("测试网络监控SSE连接", func(t *testing.T) {
		// 创建请求
		req, _ := http.NewRequest("GET", "/api/network/monitor/stream", nil)
		w := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(w, req)

		// 验证响应头
		if w.Code != http.StatusOK {
			t.Errorf("期望状态码 200, 得到 %d", w.Code)
		}

		contentType := w.Header().Get("Content-Type")
		if !strings.Contains(contentType, "text/event-stream") {
			t.Errorf("期望Content-Type包含text/event-stream, 得到 %s", contentType)
		}

		// 解析SSE响应
		body := w.Body.String()
		t.Logf("SSE响应长度: %d bytes", len(body))
		t.Logf("SSE响应内容:\n%s", body)

		// 验证SSE格式
		lines := strings.Split(body, "\n")
		var connectedCount, dataCount, errorCount int

		for _, line := range lines {
			if strings.HasPrefix(line, "data: ") {
				dataStr := strings.TrimPrefix(line, "data: ")
				if dataStr == "" {
					continue
				}

				var message map[string]interface{}
				if err := json.Unmarshal([]byte(dataStr), &message); err != nil {
					t.Logf("解析SSE消息失败: %v, 数据: %s", err, dataStr)
					continue
				}

				msgType, ok := message["type"].(string)
				if !ok {
					t.Errorf("SSE消息缺少type字段")
					continue
				}

				switch msgType {
				case "connected":
					connectedCount++
					t.Logf("连接确认: %v", message["content"])
				case "data":
					dataCount++
					content := message["content"].(map[string]interface{})
					t.Logf("监控数据: timestamp=%v, upload_speed=%v, download_speed=%v", 
						content["timestamp"], content["upload_speed"], content["download_speed"])
				case "error":
					errorCount++
					t.Logf("错误: %v", message["content"])
				}
			}
		}

		t.Logf("SSE消息统计: 连接确认=%d, 数据=%d, 错误=%d", connectedCount, dataCount, errorCount)

		// 验证至少有连接确认消息
		if connectedCount == 0 {
			t.Error("没有收到连接确认消息")
		}

		// 验证至少有一条数据消息
		if dataCount == 0 {
			t.Error("没有收到监控数据消息")
		}
	})
}

// TestNetworkMonitorSSEDataFormat 测试监控数据格式
func TestNetworkMonitorSSEDataFormat(t *testing.T) {
	// 创建网络服务
	cfg := network.NetworkConfig{
		MonitorInterval:   5 * time.Second,
		TrafficSampleRate: 100,
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   2 * time.Second,
		IptablesPath:      "/sbin/iptables",
		BackupPath:        "/tmp/firewall_backup",
	}
	service := network.NewNetworkService(cfg)
	api := network.NewAPI(service)

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	router := gin.New()
	router.GET("/api/network/monitor/stream", api.NetworkMonitorStream)

	t.Run("验证监控数据格式", func(t *testing.T) {
		// 创建请求
		req, _ := http.NewRequest("GET", "/api/network/monitor/stream", nil)
		w := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(w, req)

		// 解析响应
		body := w.Body.String()
		scanner := bufio.NewScanner(strings.NewReader(body))

		var foundDataMessage bool
		for scanner.Scan() {
			line := scanner.Text()
			if strings.HasPrefix(line, "data: ") {
				dataStr := strings.TrimPrefix(line, "data: ")
				if dataStr == "" {
					continue
				}

				// 验证JSON格式
				var message map[string]interface{}
				if err := json.Unmarshal([]byte(dataStr), &message); err != nil {
					continue
				}

				if message["type"] == "data" {
					foundDataMessage = true
					content := message["content"].(map[string]interface{})

					// 验证必需字段
					requiredFields := []string{"timestamp", "upload_speed", "download_speed", "latency", "interface_stats"}
					for _, field := range requiredFields {
						if _, ok := content[field]; !ok {
							t.Errorf("监控数据缺少字段: %s", field)
						}
					}

					// 验证接口统计数据格式
					if interfaceStats, ok := content["interface_stats"].([]interface{}); ok {
						t.Logf("接口统计数据包含 %d 个接口", len(interfaceStats))
						
						for i, stat := range interfaceStats {
							statMap := stat.(map[string]interface{})
							
							// 验证接口数据必需字段
							interfaceFields := []string{"interface", "status", "rx_bytes", "tx_bytes"}
							for _, field := range interfaceFields {
								if _, exists := statMap[field]; !exists {
									t.Errorf("接口统计 %d 缺少字段: %s", i, field)
								}
							}
						}
					}

					t.Logf("监控数据格式验证通过")
					break
				}
			}
		}

		if !foundDataMessage {
			t.Error("没有找到有效的监控数据消息")
		}
	})
}

// TestNetworkMonitorSSEContinuous 测试连续数据推送
func TestNetworkMonitorSSEContinuous(t *testing.T) {
	// 这个测试需要更长的时间来验证连续推送
	if testing.Short() {
		t.Skip("跳过长时间测试")
	}

	// 创建网络服务
	cfg := network.NetworkConfig{
		MonitorInterval:   5 * time.Second,
		TrafficSampleRate: 100,
		PingTimeout:       3 * time.Second,
		TracerouteMaxHops: 30,
		PortScanTimeout:   2 * time.Second,
		IptablesPath:      "/sbin/iptables",
		BackupPath:        "/tmp/firewall_backup",
	}
	service := network.NewNetworkService(cfg)
	api := network.NewAPI(service)

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	router := gin.New()
	router.GET("/api/network/monitor/stream", api.NetworkMonitorStream)

	t.Run("测试连续数据推送", func(t *testing.T) {
		// 创建请求
		req, _ := http.NewRequest("GET", "/api/network/monitor/stream", nil)
		w := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(w, req)

		// 解析响应
		body := w.Body.String()
		lines := strings.Split(body, "\n")
		
		var dataMessages []map[string]interface{}
		for _, line := range lines {
			if strings.HasPrefix(line, "data: ") {
				dataStr := strings.TrimPrefix(line, "data: ")
				if dataStr == "" {
					continue
				}

				var message map[string]interface{}
				if err := json.Unmarshal([]byte(dataStr), &message); err != nil {
					continue
				}

				if message["type"] == "data" {
					dataMessages = append(dataMessages, message)
				}
			}
		}

		t.Logf("收到 %d 条数据消息", len(dataMessages))

		// 验证至少收到多条数据消息（说明是连续推送的）
		if len(dataMessages) < 2 {
			t.Errorf("期望至少收到2条数据消息，实际收到 %d 条", len(dataMessages))
		}

		// 验证时间戳是递增的
		if len(dataMessages) >= 2 {
			firstTimestamp := dataMessages[0]["content"].(map[string]interface{})["timestamp"].(float64)
			lastTimestamp := dataMessages[len(dataMessages)-1]["content"].(map[string]interface{})["timestamp"].(float64)
			
			if lastTimestamp <= firstTimestamp {
				t.Error("时间戳应该是递增的")
			}
			
			t.Logf("时间戳范围: %v - %v", firstTimestamp, lastTimestamp)
		}
	})
}
