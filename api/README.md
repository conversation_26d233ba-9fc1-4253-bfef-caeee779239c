# API目录

这个目录包含边缘计算平台的API定义和文档。

## API接口

### 视频接入API

- `GET /api/videos`: 获取所有视频源列表
- `GET /api/videos/{id}`: 获取指定ID的视频源详情
- `POST /api/videos`: 添加新的视频源
- `PUT /api/videos/{id}`: 更新指定ID的视频源
- `DELETE /api/videos/{id}`: 删除指定ID的视频源

### 算法仓库API

- `GET /api/algorithms`: 获取所有算法列表
- `GET /api/algorithms/{id}`: 获取指定ID的算法详情
- `POST /api/algorithms`: 导入新的算法
- `DELETE /api/algorithms/{id}`: 删除指定ID的算法
- `PUT /api/algorithms/{id}/activate`: 激活指定ID的算法
- `PUT /api/algorithms/{id}/deactivate`: 停用指定ID的算法

### 告警记录API

- `GET /api/alerts`: 获取所有告警记录列表
- `GET /api/alerts/{id}`: 获取指定ID的告警记录详情
- `PUT /api/alerts/{id}/status`: 更新指定ID的告警记录状态
- `DELETE /api/alerts/{id}`: 删除指定ID的告警记录
- `GET /api/alerts/query`: 按条件查询告警记录

## API文档

详细的API文档请参考Swagger文档：

- 开发环境：`http://localhost:8080/swagger/index.html`
