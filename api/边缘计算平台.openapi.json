{"openapi": "3.0.1", "info": {"title": "边缘计算", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/api/videos": {"get": {"summary": "视频源-查询", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "object", "properties": {"page": {"type": "object", "properties": {"page": {"type": "integer", "title": "当前页码"}, "limit": {"type": "integer", "title": "每页数量"}, "total": {"type": "integer", "title": "总记录数"}}, "title": "分页信息", "x-apifox-orders": ["page", "limit", "total"], "x-apifox-ignore-properties": []}, "results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "title": "视频源ID"}, "name": {"type": "string", "title": "视频源名称"}, "type": {"type": "integer", "title": "视频源类型", "description": "1-摄像头、2-视频流、3-视频文件"}, "protocol": {"type": "string", "title": "协议", "description": "rtsp、onvif、gb28181"}, "camera_id": {"type": "string", "title": "相机国标ID"}, "camera_type": {"type": "string", "title": "相机类型", "description": "1-枪机、2-球机"}, "description": {"type": "string", "title": "描述信息"}, "stream_type": {"type": "string", "title": "流地址类型", "description": "自动生成、手动输入"}, "camera_ip": {"type": "string", "title": "相机IP地址"}, "username": {"type": "string", "title": "用户名"}, "password": {"type": "string", "title": "密码"}, "brand": {"type": "string", "title": "品牌", "description": "海康威视、大华、宇视、华为"}, "stream_mode": {"type": "string", "title": "视频流模式", "description": "主码流、子码流"}, "url": {"type": "string", "title": "视频流地址"}, "port": {"type": "integer", "title": "端口"}, "status": {"type": "string", "title": "状态", "description": "online、offline"}, "created_at": {"type": "string", "title": "创建时间"}, "updated_at": {"type": "string", "title": "更新时间"}}, "x-apifox-orders": ["id", "name", "type", "protocol", "camera_id", "camera_type", "description", "stream_type", "camera_ip", "username", "password", "brand", "stream_mode", "url", "port", "status", "created_at", "updated_at"], "x-apifox-ignore-properties": []}, "title": "视频源列表"}}, "title": "数据对象", "x-apifox-orders": ["page", "results"], "x-apifox-ignore-properties": []}, "success": {"type": "boolean", "title": "成功标识"}}, "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943612-run", "security": []}, "post": {"summary": "视频源-新增", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "title": "视频源名称"}, "type": {"type": "integer", "title": "视频源类型", "description": "1-摄像头、2-视频流、3-视频文件"}, "protocol": {"type": "string", "title": "协议", "description": "rtsp、onvif、gb28181"}, "description": {"type": "string", "title": "描述"}, "stream_type": {"type": "string", "title": "流地址类型", "description": "自动生成，手动输入"}, "camera_ip": {"type": "string", "title": "相机IP地址"}, "port": {"type": "integer", "title": "相机端口", "description": "ONVIF端口"}, "username": {"type": "string", "title": "用户名", "description": "ONVIF用户名"}, "password": {"type": "string", "title": "密码", "description": "ONVIF口令"}, "url": {"type": "string", "title": "视频流地址"}, "brand": {"type": "string", "title": "品牌", "description": "海康威视、大华、宇视、华为"}, "stream_mode": {"type": "string", "title": "视频流模式", "description": "主码流、子码流"}, "status": {"type": "string", "title": "状态", "description": "online、offline"}}, "required": ["name", "type", "protocol", "description", "stream_type", "camera_ip", "port", "username", "password", "brand", "stream_mode", "status", "url"], "x-apifox-orders": ["name", "type", "protocol", "description", "stream_type", "camera_ip", "port", "username", "password", "url", "brand", "stream_mode", "status"], "x-apifox-ignore-properties": []}, "example": {"name": "摄像头05", "type": 1, "protocol": "rtsp", "description": "大厅入口摄像头", "stream_type": "自动生成", "camera_ip": "*************", "port": 80, "username": "admin", "password": "123456", "brand": "海康威视", "stream_mode": "主码流", "status": "online"}}}}, "responses": {"201": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "title": "视频源ID"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "integer", "title": "类型", "description": "1-摄像头、2-视频流、3-视频文件"}, "protocol": {"type": "string", "title": "协议", "description": "rtsp、onvif、gb28181"}, "camera_id": {"type": "string", "title": "相机ID", "description": "相机的国标ID"}, "camera_type": {"type": "string", "title": "相机类型", "description": "1-枪机、2-球机"}, "description": {"type": "string", "title": "描述"}, "stream_type": {"type": "string", "title": "流地址类型", "description": "自动生成、手动输入"}, "camera_ip": {"type": "string", "title": "相机IP地址"}, "username": {"type": "string", "title": "ONVIF用户名"}, "password": {"type": "string", "title": "ONVIF密码"}, "brand": {"type": "string", "title": "品牌", "description": "海康威视、大华、宇视、华为"}, "stream_mode": {"type": "string", "title": "视频流模式", "description": "主码流、子码流"}, "url": {"type": "string", "title": "视频流地址"}, "port": {"type": "integer", "title": "ONVIF端口"}, "status": {"type": "string", "title": "相机状态", "description": "online, offline"}, "created_at": {"type": "string", "title": "创建时间"}, "updated_at": {"type": "string", "title": "更新时间"}}, "required": ["id", "name", "type", "protocol", "camera_id", "camera_type", "description", "stream_type", "camera_ip", "username", "password", "brand", "stream_mode", "url", "port", "status", "created_at", "updated_at"], "x-apifox-orders": ["id", "name", "type", "protocol", "camera_id", "camera_type", "description", "stream_type", "camera_ip", "username", "password", "brand", "stream_mode", "url", "port", "status", "created_at", "updated_at"], "title": "数据对象", "x-apifox-ignore-properties": []}, "success": {"type": "boolean", "title": "成功标识"}}, "required": ["errorCode", "message", "data", "success"], "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}, "examples": {"1": {"summary": "成功示例", "value": {"errorCode": 0, "message": "", "data": {"id": 4, "name": "摄像头05", "type": 1, "protocol": "rtsp", "camera_id": "", "camera_type": "", "description": "大厅入口摄像头", "stream_type": "自动生成", "camera_ip": "*************", "username": "admin", "password": "123456", "brand": "海康威视", "stream_mode": "主码流", "url": "rtsp://admin:123456@*************:554/h264/ch1/1/av_stream", "port": 80, "status": "online", "created_at": "2025-07-21T16:14:01.6113179+08:00", "updated_at": "2025-07-21T16:14:01.6113179+08:00"}, "success": true}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943613-run", "security": []}}, "/api/videos/1": {"put": {"summary": "视频源-修改", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "title": "视频源名称"}, "type": {"type": "integer", "title": "视频源类型", "description": "1-摄像头、2-视频流、3-视频文件"}, "protocol": {"type": "string", "title": "协议", "description": "rtsp、onvif、gb28181"}, "description": {"type": "string", "title": "描述"}, "stream_type": {"type": "string", "title": "视频流生成类型", "description": "自动生成、手动输入"}, "camera_ip": {"type": "string", "title": "相机IP地址"}, "port": {"type": "integer", "title": "ONVIF端口"}, "username": {"type": "string", "title": "ONVIF用户名"}, "password": {"type": "string", "title": "ONVIF密码"}, "brand": {"type": "string", "title": "品牌", "description": "海康威视、大华、宇视、华为"}, "stream_mode": {"type": "string", "title": "视频流模式", "description": "主码流、子码流"}, "status": {"type": "string", "title": "相机状态", "description": "online, offline"}}, "required": ["name", "type", "protocol", "description", "stream_type", "camera_ip", "port", "username", "password", "brand", "stream_mode", "status"], "x-apifox-orders": ["name", "type", "protocol", "description", "stream_type", "camera_ip", "port", "username", "password", "brand", "stream_mode", "status"], "x-apifox-ignore-properties": []}, "example": {"name": "摄像头01", "type": 1, "protocol": "rtsp", "description": "大厅入口摄像头", "stream_type": "自动生成", "camera_ip": "*************", "port": 80, "username": "admin", "password": "1234567", "brand": "海康威视", "stream_mode": "主码流", "status": "online"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "title": "视频源ID"}, "name": {"type": "string", "title": "视频源名称"}, "type": {"type": "integer", "title": "视频源类型", "description": "摄像头-1、视频流-2、视频文件-3"}, "protocol": {"type": "string", "title": "协议", "description": "rtsp、onvif、gb28181"}, "camera_id": {"type": "string", "title": "相机国标ID"}, "camera_type": {"type": "string", "title": "相机类型", "description": "1-枪机、2-球机"}, "description": {"type": "string", "title": "描述"}, "stream_type": {"type": "string", "title": "流地址生成类型", "description": "自动生成、手动输入"}, "camera_ip": {"type": "string", "title": "相机IP地址"}, "username": {"type": "string", "title": "ONVIF用户名"}, "password": {"type": "string", "title": "ONVIF密码"}, "brand": {"type": "string", "title": "相机品牌", "description": "海康威视、大华、宇视、华为"}, "stream_mode": {"type": "string", "title": "视频流模式", "description": "主码流、子码流"}, "url": {"type": "string", "title": "视频流地址"}, "port": {"type": "integer", "title": "ONVIF端口"}, "status": {"type": "string", "title": "相机状态"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "type", "protocol", "camera_id", "camera_type", "description", "stream_type", "camera_ip", "username", "password", "brand", "stream_mode", "url", "port", "status", "created_at", "updated_at"], "x-apifox-orders": ["id", "name", "type", "protocol", "camera_id", "camera_type", "description", "stream_type", "camera_ip", "username", "password", "brand", "stream_mode", "url", "port", "status", "created_at", "updated_at"], "title": "数据对象", "x-apifox-ignore-properties": []}, "success": {"type": "boolean", "title": "成功标识"}}, "required": ["errorCode", "message", "data", "success"], "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}, "examples": {"1": {"summary": "成功示例", "value": {"errorCode": 0, "message": "", "data": {"id": 1, "name": "摄像头01", "type": 1, "protocol": "rtsp", "camera_id": "", "camera_type": "", "description": "大厅入口摄像头", "stream_type": "自动生成", "camera_ip": "*************", "username": "admin", "password": "1234567", "brand": "海康威视", "stream_mode": "主码流", "url": "rtsp://admin:1234567@*************:554/h264/ch1/1/av_stream", "port": 80, "status": "online", "created_at": "2025-07-21T16:29:36.3854658+08:00", "updated_at": "2025-07-21T16:29:36.3852722+08:00"}, "success": true}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943614-run", "security": []}, "delete": {"summary": "视频源-删除", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "null", "title": "数据对象"}, "success": {"type": "boolean", "title": "成功标识"}}, "required": ["errorCode", "message", "data", "success"], "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}, "examples": {"1": {"summary": "成功示例", "value": {"errorCode": 0, "message": "", "data": null, "success": true}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943615-run", "security": []}}, "/api/videos/{id}/bindings": {"get": {"summary": "视频源算法绑定-查询", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "视频源ID", "required": true, "example": "4", "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "video_id": {"type": "integer"}, "algorithm_id": {"type": "integer"}, "detection_area": {"type": "string"}, "alert_interval": {"type": "integer"}, "alert_window": {"type": "integer"}, "alert_threshold": {"type": "number"}, "voice_content": {"type": "string"}, "danger_level": {"type": "string"}, "extension_fields": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}, "video": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "type": {"type": "integer"}, "protocol": {"type": "string"}, "camera_id": {"type": "string"}, "camera_type": {"type": "string"}, "description": {"type": "string"}, "stream_type": {"type": "string"}, "camera_ip": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "brand": {"type": "string"}, "stream_mode": {"type": "string"}, "url": {"type": "string"}, "port": {"type": "integer"}, "status": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "type", "protocol", "camera_id", "camera_type", "description", "stream_type", "camera_ip", "username", "password", "brand", "stream_mode", "url", "port", "status", "created_at", "updated_at"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["id", "name", "type", "protocol", "camera_id", "camera_type", "description", "stream_type", "camera_ip", "username", "password", "brand", "stream_mode", "url", "port", "status", "created_at", "updated_at"]}, "algorithm": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "description": {"type": "string"}, "version": {"type": "string"}, "path": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "description", "version", "path", "status", "created_at", "updated_at"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["id", "name", "description", "version", "path", "status", "created_at", "updated_at"]}}, "x-apifox-ignore-properties": [], "x-apifox-orders": ["id", "video_id", "algorithm_id", "detection_area", "alert_interval", "alert_window", "alert_threshold", "voice_content", "danger_level", "extension_fields", "status", "created_at", "updated_at", "video", "algorithm"]}}, "success": {"type": "boolean", "title": "成功标识"}}, "required": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["errorCode", "message", "data", "success"]}, "examples": {"1": {"summary": "成功示例", "value": {"errorCode": 0, "message": "", "data": [{"id": 4, "video_id": 4, "algorithm_id": 1, "detection_area": "[{\"x\":100,\"y\":100},{\"x\":200,\"y\":100},{\"x\":200,\"y\":200},{\"x\":100,\"y\":200}]", "alert_interval": 60, "alert_window": 300, "alert_threshold": 0.8, "voice_content": "检测到异常情况", "danger_level": "warning", "extension_fields": "{\"sensitivity\":0.7,\"min_size\":50}", "status": "active", "created_at": "2025-07-21T17:05:12.7191978+08:00", "updated_at": "2025-07-21T17:05:12.7191978+08:00", "video": {"id": 4, "name": "摄像头05", "type": 1, "protocol": "rtsp", "camera_id": "", "camera_type": "", "description": "大厅入口摄像头", "stream_type": "自动生成", "camera_ip": "*************", "username": "admin", "password": "123456", "brand": "海康威视", "stream_mode": "主码流", "url": "rtsp://admin:123456@*************:554/h264/ch1/1/av_stream", "port": 80, "status": "online", "created_at": "2025-07-21T16:14:01.6113179+08:00", "updated_at": "2025-07-21T16:14:01.6113179+08:00"}, "algorithm": {"id": 1, "name": "算法名称", "description": "这是描述", "version": "V1.0", "path": "data\\algorithms\\algorithm_1752462344114585300", "status": "inactive", "created_at": "2025-07-14T11:05:44.1161778+08:00", "updated_at": "2025-07-21T16:54:57.1323531+08:00"}}], "success": true}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943616-run", "security": []}, "post": {"summary": "视频源算法绑定-新增/修改", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"video_id": {"type": "integer"}, "algorithm_id": {"type": "integer"}, "detection_area": {"type": "string"}, "alert_interval": {"type": "integer"}, "alert_window": {"type": "integer"}, "alert_threshold": {"type": "number"}, "voice_content": {"type": "string"}, "danger_level": {"type": "string"}, "extension_fields": {"type": "string"}}, "x-apifox-orders": ["video_id", "algorithm_id", "detection_area", "alert_interval", "alert_window", "alert_threshold", "voice_content", "danger_level", "extension_fields"], "x-apifox-ignore-properties": []}}, "example": [{"video_id": 4, "algorithm_id": 1, "detection_area": "[{\"x\":100,\"y\":100},{\"x\":200,\"y\":100},{\"x\":200,\"y\":200},{\"x\":100,\"y\":200}]", "alert_interval": 60, "alert_window": 300, "alert_threshold": 0.8, "voice_content": "检测到异常情况", "danger_level": "warning", "extension_fields": "{\"sensitivity\":0.7,\"min_size\":50}"}]}}}, "responses": {"201": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "video_id": {"type": "integer"}, "algorithm_id": {"type": "integer"}, "detection_area": {"type": "string"}, "alert_interval": {"type": "integer"}, "alert_window": {"type": "integer"}, "alert_threshold": {"type": "number"}, "voice_content": {"type": "string"}, "danger_level": {"type": "string"}, "extension_fields": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}, "video": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "type": {"type": "integer"}, "protocol": {"type": "string"}, "camera_id": {"type": "string"}, "camera_type": {"type": "string"}, "description": {"type": "string"}, "stream_type": {"type": "string"}, "camera_ip": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "brand": {"type": "string"}, "stream_mode": {"type": "string"}, "url": {"type": "string"}, "port": {"type": "integer"}, "status": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "type", "protocol", "camera_id", "camera_type", "description", "stream_type", "camera_ip", "username", "password", "brand", "stream_mode", "url", "port", "status", "created_at", "updated_at"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["id", "name", "type", "protocol", "camera_id", "camera_type", "description", "stream_type", "camera_ip", "username", "password", "brand", "stream_mode", "url", "port", "status", "created_at", "updated_at"]}, "algorithm": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "description": {"type": "string"}, "version": {"type": "string"}, "path": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "description", "version", "path", "status", "created_at", "updated_at"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["id", "name", "description", "version", "path", "status", "created_at", "updated_at"]}}, "x-apifox-ignore-properties": [], "x-apifox-orders": ["id", "video_id", "algorithm_id", "detection_area", "alert_interval", "alert_window", "alert_threshold", "voice_content", "danger_level", "extension_fields", "status", "created_at", "updated_at", "video", "algorithm"]}}, "success": {"type": "boolean"}}, "required": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["errorCode", "message", "data", "success"]}, "examples": {"1": {"summary": "异常示例", "value": {"errorCode": 0, "message": "该视频源和算法的绑定关系已存在", "data": null, "success": false}}, "2": {"summary": "成功示例", "value": {"errorCode": 0, "message": "", "data": {"id": 4, "video_id": 4, "algorithm_id": 1, "detection_area": "[{\"x\":100,\"y\":100},{\"x\":200,\"y\":100},{\"x\":200,\"y\":200},{\"x\":100,\"y\":200}]", "alert_interval": 60, "alert_window": 300, "alert_threshold": 0.8, "voice_content": "检测到异常情况", "danger_level": "warning", "extension_fields": "{\"sensitivity\":0.7,\"min_size\":50}", "status": "active", "created_at": "2025-07-21T17:05:12.7191978+08:00", "updated_at": "2025-07-21T17:05:12.7191978+08:00", "video": {"id": 0, "name": "", "type": 0, "protocol": "", "camera_id": "", "camera_type": "", "description": "", "stream_type": "", "camera_ip": "", "username": "", "password": "", "brand": "", "stream_mode": "", "url": "", "port": 0, "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "algorithm": {"id": 0, "name": "", "description": "", "version": "", "path": "", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}}, "success": true}}, "3": {"summary": "成功示例", "value": {"errorCode": 0, "message": "", "data": [{"id": 11, "video_id": 4, "algorithm_id": 1, "detection_area": "[{\"x\":100,\"y\":100},{\"x\":200,\"y\":100},{\"x\":200,\"y\":200},{\"x\":100,\"y\":200}]", "alert_interval": 60, "alert_window": 300, "alert_threshold": 0.8, "voice_content": "检测到异常情况", "danger_level": "warning", "extension_fields": "{\"sensitivity\":0.7,\"min_size\":50}", "status": "active", "created_at": "2025-07-23T15:58:10.3612558+08:00", "updated_at": "2025-07-23T15:58:10.3612558+08:00", "video": {"id": 0, "name": "", "type": 0, "protocol": "", "camera_id": "", "camera_type": "", "description": "", "stream_type": "", "camera_ip": "", "username": "", "password": "", "brand": "", "stream_mode": "", "url": "", "port": 0, "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "algorithm": {"id": 0, "name": "", "description": "", "version": "", "path": "", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}}], "success": true}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943617-run", "security": []}}, "/api/algorithms": {"get": {"summary": "算法-查询", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}, "x-apifox-orders": [], "x-apifox-ignore-properties": []}, "example": ""}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "object", "properties": {"page": {"type": "object", "properties": {"page": {"type": "integer", "title": "当前页"}, "limit": {"type": "integer", "title": "每页大小"}, "total": {"type": "integer", "title": "数据条数"}}, "required": ["page", "limit", "total"], "x-apifox-orders": ["page", "limit", "total"], "title": "分页对象", "x-apifox-ignore-properties": []}, "results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "title": "算法ID"}, "name": {"type": "string", "title": "算法名称"}, "description": {"type": "string", "title": "算法描述"}, "version": {"type": "string", "title": "算法版本"}, "path": {"type": "string", "title": "模型文件路径"}, "status": {"type": "string", "title": "状态", "description": "active, inactive"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "description", "version", "path", "status", "created_at", "updated_at"], "x-apifox-orders": ["id", "name", "description", "version", "path", "status", "created_at", "updated_at"], "x-apifox-ignore-properties": []}, "title": "查询结果"}}, "required": ["page", "results"], "x-apifox-orders": ["page", "results"], "title": "数据对象", "x-apifox-ignore-properties": []}, "success": {"type": "boolean"}}, "required": ["errorCode", "message", "data", "success"], "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}, "examples": {"1": {"summary": "成功示例", "value": {"errorCode": 0, "message": "", "data": {"page": {"page": 1, "limit": 20, "total": 2}, "results": [{"id": 2, "name": "算法名称2", "description": "这是描述2", "version": "V1.0", "path": "data\\algorithms\\algorithm_1752465007177483700", "status": "", "created_at": "2025-07-14T11:50:07.1791016+08:00", "updated_at": "2025-07-14T11:50:07.1791016+08:00"}, {"id": 1, "name": "算法名称", "description": "这是描述", "version": "V1.0", "path": "data\\algorithms\\algorithm_1752462344114585300", "status": "inactive", "created_at": "2025-07-14T11:05:44.1161778+08:00", "updated_at": "2025-07-14T11:27:40.5532343+08:00"}]}, "success": true}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943618-run", "security": []}, "post": {"summary": "算法-导入", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Content-Type", "in": "header", "description": "", "required": false, "example": "multipart/form-data", "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "算法名称", "example": "算法名称2"}, "description": {"type": "string", "description": "描述信息", "example": "这是描述2"}, "version": {"type": "string", "description": "算法版本", "example": "V1.0"}, "file": {"type": "string", "description": "算法模型文件", "example": "file://C:\\Users\\<USER>\\Downloads\\fproxy.zip", "format": "binary"}}}}}}, "responses": {"201": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "title": "算法ID"}, "name": {"type": "string", "title": "算法名称"}, "description": {"type": "string", "title": "算法描述"}, "version": {"type": "string", "title": "版本"}, "path": {"type": "string", "title": "模型文件存储路径"}, "status": {"type": "string", "title": "状态", "description": "active, inactive"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "description", "version", "path", "status", "created_at", "updated_at"], "x-apifox-orders": ["id", "name", "description", "version", "path", "status", "created_at", "updated_at"], "title": "数据对象", "x-apifox-ignore-properties": []}, "success": {"type": "boolean", "title": "成功标识"}}, "required": ["errorCode", "message", "data", "success"], "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943619-run", "security": []}}, "/api/algorithms/1/activate": {"put": {"summary": "算法-激活", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "null", "title": "数据对象"}, "success": {"type": "boolean", "title": "成功标识"}}, "required": ["errorCode", "message", "data", "success"], "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}, "examples": {"1": {"summary": "成功示例", "value": {"errorCode": 0, "message": "", "data": null, "success": true}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943620-run", "security": []}}, "/api/algorithms/1/deactivate": {"put": {"summary": "算法-停用", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "null", "title": "数据对象"}, "success": {"type": "boolean", "title": "成功标识"}}, "required": ["errorCode", "message", "data", "success"], "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}, "examples": {"1": {"summary": "成功示例", "value": {"errorCode": 0, "message": "", "data": null, "success": true}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943621-run", "security": []}}, "/api/algorithms/2/bindings": {"get": {"summary": "算法-视频源", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "array", "title": "绑定列表", "items": {"type": "object", "properties": {"id": {"type": "integer", "title": "绑定ID"}, "video_id": {"type": "integer"}, "algorithm_id": {"type": "integer"}, "detection_area": {"type": "string"}, "alert_interval": {"type": "integer"}, "alert_window": {"type": "integer"}, "alert_threshold": {"type": "number"}, "voice_content": {"type": "string"}, "danger_level": {"type": "string"}, "extension_fields": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}, "video": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "type": {"type": "integer"}, "protocol": {"type": "string"}, "camera_id": {"type": "string"}, "camera_type": {"type": "string"}, "description": {"type": "string"}, "stream_type": {"type": "string"}, "camera_ip": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "brand": {"type": "string"}, "stream_mode": {"type": "string"}, "url": {"type": "string"}, "port": {"type": "integer"}, "status": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "type", "protocol", "camera_id", "camera_type", "description", "stream_type", "camera_ip", "username", "password", "brand", "stream_mode", "url", "port", "status", "created_at", "updated_at"], "x-apifox-orders": ["id", "name", "type", "protocol", "camera_id", "camera_type", "description", "stream_type", "camera_ip", "username", "password", "brand", "stream_mode", "url", "port", "status", "created_at", "updated_at"], "x-apifox-ignore-properties": []}, "algorithm": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "description": {"type": "string"}, "version": {"type": "string"}, "path": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "description", "version", "path", "status", "created_at", "updated_at"], "x-apifox-orders": ["id", "name", "description", "version", "path", "status", "created_at", "updated_at"], "x-apifox-ignore-properties": []}}, "x-apifox-orders": ["id", "video_id", "algorithm_id", "detection_area", "alert_interval", "alert_window", "alert_threshold", "voice_content", "danger_level", "extension_fields", "status", "created_at", "updated_at", "video", "algorithm"], "x-apifox-ignore-properties": []}}, "success": {"type": "boolean"}}, "required": ["errorCode", "message", "data", "success"], "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}, "examples": {"1": {"summary": "成功示例", "value": {"errorCode": 0, "message": "", "data": [{"id": 3, "video_id": 3, "algorithm_id": 2, "detection_area": "[{\"x\":100,\"y\":100},{\"x\":200,\"y\":100},{\"x\":200,\"y\":200},{\"x\":100,\"y\":300}]", "alert_interval": 30, "alert_window": 180, "alert_threshold": 0.9, "voice_content": "检测到危险情况", "danger_level": "error", "extension_fields": "{\"sensitivity\":0.8}", "status": "active", "created_at": "2025-07-14T11:57:27.4472139+08:00", "updated_at": "2025-07-14T11:57:27.4472139+08:00", "video": {"id": 3, "name": "摄像头03", "type": 1, "protocol": "rtsp", "camera_id": "", "camera_type": "", "description": "大厅入口摄像头", "stream_type": "自动生成", "camera_ip": "*************", "username": "admin", "password": "123456", "brand": "海康威视", "stream_mode": "主码流", "url": "rtsp://admin:123456@*************:554/h264/ch1/1/av_stream", "port": 80, "status": "online", "created_at": "2025-07-14T10:31:41.3940983+08:00", "updated_at": "2025-07-14T10:31:41.3940983+08:00"}, "algorithm": {"id": 2, "name": "算法名称2", "description": "这是描述2", "version": "V1.0", "path": "data\\algorithms\\algorithm_1752465007177483700", "status": "", "created_at": "2025-07-14T11:50:07.1791016+08:00", "updated_at": "2025-07-14T11:50:07.1791016+08:00"}}], "success": true}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943622-run", "security": []}}, "/api/users/me": {"get": {"summary": "用户-用户信息", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "title": "用户ID"}, "username": {"type": "string", "title": "用户名"}, "name": {"type": "string", "title": "显示名称"}, "email": {"type": "string", "title": "电子邮箱"}, "mobile": {"type": "string", "title": "手机号码"}, "role": {"type": "string", "title": "角色", "description": "admin、user"}, "status": {"type": "string", "title": "状态", "description": "active、inactive"}, "last_login_at": {"type": "string", "title": "最后登录时间"}, "last_login_ip": {"type": "string", "title": "最后登录IP"}, "created_at": {"type": "string", "title": "创建时间"}, "updated_at": {"type": "string", "title": "更新时间"}}, "title": "用户信息", "x-apifox-orders": ["id", "username", "name", "email", "mobile", "role", "status", "last_login_at", "last_login_ip", "created_at", "updated_at"], "x-apifox-ignore-properties": []}, "success": {"type": "boolean", "title": "成功标识"}}, "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943627-run", "security": []}, "put": {"summary": "用户-更新", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMSIsImV4cCI6MTc1MjU3MTQ2OCwibmJmIjoxNzUyNDg1MDY4LCJpYXQiOjE3NTI0ODUwNjh9.8_jLiyCeXPzZleNh_ohSqvx8AtVTO0zqOE3rinE3uqQ", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "title": "显示名称"}, "email": {"type": "string", "title": "电子邮箱"}, "mobile": {"type": "string", "title": "手机号码"}}, "x-apifox-orders": ["name", "email", "mobile"], "x-apifox-ignore-properties": []}, "example": {"name": "新名称", "email": "<EMAIL>", "mobile": "13800138001"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述信息"}, "data": {"type": "object", "title": "用户信息", "properties": {"id": {"type": "integer", "title": "用户ID"}, "username": {"type": "string", "title": "用户名"}, "name": {"type": "string", "title": "显示名称"}, "email": {"type": "string", "title": "电子邮箱"}, "mobile": {"type": "string", "title": "手机号码"}, "role": {"type": "string", "title": "角色", "description": "admin、user"}, "status": {"type": "string", "title": "状态", "description": "active、inactive"}, "created_at": {"type": "string", "title": "创建时间"}, "updated_at": {"type": "string", "title": "更新时间"}}, "x-apifox-orders": ["id", "username", "name", "email", "mobile", "role", "status", "created_at", "updated_at"], "x-apifox-ignore-properties": []}, "success": {"type": "boolean", "title": "成功标识"}}, "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943628-run", "security": []}}, "/api/auth/login": {"post": {"summary": "用户-登录", "x-apifox-folder": "", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string", "title": "用户名"}, "password": {"type": "string", "title": "口令"}}, "required": ["username", "password"], "x-apifox-orders": ["username", "password"], "x-apifox-ignore-properties": []}, "example": {"username": "admin", "password": "admin123"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"errorCode": {"type": "integer", "title": "错误码"}, "message": {"type": "string", "title": "描述"}, "data": {"type": "object", "properties": {"access_token": {"type": "string", "title": "访问token"}, "refresh_token": {"type": "string", "title": "刷新token"}, "expires_at": {"type": "string", "title": "过期时间"}, "token_type": {"type": "string", "title": "token类型"}, "user_id": {"type": "integer", "title": "用户id"}, "username": {"type": "string", "title": "用户名"}, "name": {"type": "string", "title": "显示名称"}, "role": {"type": "string", "title": "角色"}}, "required": ["access_token", "refresh_token", "expires_at", "token_type", "user_id", "username", "name", "role"], "x-apifox-orders": ["access_token", "refresh_token", "expires_at", "token_type", "user_id", "username", "name", "role"], "title": "响应数据", "x-apifox-ignore-properties": []}, "success": {"type": "boolean", "title": "成功标识"}}, "required": ["errorCode", "message", "data", "success"], "x-apifox-orders": ["errorCode", "message", "data", "success"], "x-apifox-ignore-properties": []}, "examples": {"1": {"summary": "成功示例", "value": {"errorCode": 0, "message": "", "data": {"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoiZWNwIiwic3ViIjoiMiIsImV4cCI6MTc1MzE3MTA4OCwibmJmIjoxNzUzMDg0Njg4LCJpYXQiOjE3NTMwODQ2ODh9.Vo83tbCcbTAjnWLsH6UyCaOjVWd6wUfxQe1A6W6qIaA", "refresh_token": "55ab9955-ed24-4507-a2bb-56d7068eab75", "expires_at": "2025-07-22T15:58:08.9401408+08:00", "token_type": "Bearer", "user_id": 2, "username": "admin", "name": "系统管理员", "role": "admin"}, "success": true}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/6824629/apis/api-325943629-run", "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": []}