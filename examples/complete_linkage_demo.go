package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"ecp/internal/app/linkage"
	"ecp/internal/pkg/database"
	"github.com/google/uuid"
)

func main() {
	fmt.Println("=== 完整告警联动功能演示 ===")

	// 1. 创建数据库连接
	db, err := database.NewDB(&database.Config{Path: "complete_demo.db"})
	if err != nil {
		log.Fatalf("创建数据库失败: %v", err)
	}
	defer db.Close()

	// 初始化数据库表结构
	if err := db.InitSchema(); err != nil {
		log.Fatalf("初始化数据库表结构失败: %v", err)
	}

	// 2. 创建联动引擎配置
	config := &linkage.LinkageConfig{
		Enabled:                 true,
		MaxConcurrentExecutions: 10,
		ExecutionTimeout:        30 * time.Second,
		RetryCount:              3,
		RetryInterval:           5 * time.Second,
		Protocols: linkage.ProtocolsConfig{
			MQTT: linkage.MQTTConfig{
				Enabled:        true,
				DefaultQoS:     1,
				KeepAlive:      60 * time.Second,
				ConnectTimeout: 10 * time.Second,
				CleanSession:   true,
			},
			Modbus: linkage.ModbusConfig{
				Enabled:    true,
				Timeout:    5 * time.Second,
				RetryCount: 3,
				SlaveID:    1,
			},
			RS485: linkage.RS485Config{
				Enabled:    true,
				Timeout:    3 * time.Second,
				BufferSize: 1024,
			},
		},
	}

	// 3. 创建并启动联动引擎
	engine := linkage.NewLinkageEngine(db, config)
	if err := engine.Start(); err != nil {
		log.Fatalf("启动联动引擎失败: %v", err)
	}
	defer engine.Stop()

	fmt.Println("✓ 联动引擎启动成功")

	// 4. 创建多种协议的设备
	devices := createDemoDevices(engine)
	fmt.Printf("✓ 创建了 %d 个演示设备\n", len(devices))

	// 5. 创建联动规则
	rules := createDemoRules(engine, devices)
	fmt.Printf("✓ 创建了 %d 个联动规则\n", len(rules))

	// 6. 模拟不同类型的告警
	alerts := createDemoAlerts(db)
	fmt.Printf("✓ 创建了 %d 个模拟告警\n", len(alerts))

	// 7. 处理告警并触发联动
	fmt.Println("\n--- 开始处理告警联动 ---")
	for i, alert := range alerts {
		fmt.Printf("处理告警 %d: %s\n", i+1, alert.Content)
		
		if err := engine.ProcessAlert(alert); err != nil {
			fmt.Printf("❌ 处理告警失败: %v\n", err)
		} else {
			fmt.Printf("✓ 告警处理完成\n")
		}
		
		// 等待一段时间让任务执行
		time.Sleep(2 * time.Second)
	}

	// 8. 等待所有联动任务完成
	fmt.Println("\n⏳ 等待联动任务执行...")
	time.Sleep(10 * time.Second)

	// 9. 显示执行结果
	showExecutionResults(db, engine)

	// 10. 演示手动设备控制
	fmt.Println("\n--- 手动设备控制演示 ---")
	demonstrateManualControl(engine, devices)

	// 11. 显示系统状态
	showSystemStatus(engine)

	fmt.Println("\n=== 演示完成 ===")
}

// createDemoDevices 创建演示设备
func createDemoDevices(engine *linkage.LinkageEngine) []*database.LinkageDevice {
	var devices []*database.LinkageDevice

	// MQTT警报灯
	mqttConfig := map[string]interface{}{
		"broker_url": "tcp://localhost:1883",
		"username":   "ecp_user",
		"password":   "ecp_password",
		"client_id":  "ecp_alarm_light",
		"qos":        1,
	}
	configJSON, _ := json.Marshal(mqttConfig)

	mqttDevice := &database.LinkageDevice{
		ID:        uuid.New().String(),
		Name:      "入口MQTT警报灯",
		Type:      "警报灯",
		Protocol:  linkage.ProtocolMQTT,
		Address:   "localhost",
		Port:      1883,
		Config:    string(configJSON),
		Status:    linkage.DeviceStatusOffline,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	engine.deviceManager.CreateDevice(mqttDevice)
	devices = append(devices, mqttDevice)

	// Modbus门禁控制器
	modbusConfig := map[string]interface{}{
		"slave_id":    1,
		"timeout":     5,
		"retry_count": 3,
		"mode":        "tcp",
	}
	configJSON, _ = json.Marshal(modbusConfig)

	modbusDevice := &database.LinkageDevice{
		ID:        uuid.New().String(),
		Name:      "主门Modbus控制器",
		Type:      "门禁控制器",
		Protocol:  linkage.ProtocolModbus,
		Address:   "*************",
		Port:      502,
		Config:    string(configJSON),
		Status:    linkage.DeviceStatusOffline,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	engine.deviceManager.CreateDevice(modbusDevice)
	devices = append(devices, modbusDevice)

	// RS485报警器
	rs485Config := map[string]interface{}{
		"serial_port": "COM3",
		"baud_rate":   9600,
		"data_bits":   8,
		"stop_bits":   1,
		"parity":      "none",
		"timeout":     3,
		"protocol":    "custom",
		"device_addr": 1,
	}
	configJSON, _ = json.Marshal(rs485Config)

	rs485Device := &database.LinkageDevice{
		ID:        uuid.New().String(),
		Name:      "紧急RS485报警器",
		Type:      "报警器",
		Protocol:  linkage.ProtocolRS485,
		Address:   "COM3",
		Port:      0,
		Config:    string(configJSON),
		Status:    linkage.DeviceStatusOffline,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	engine.deviceManager.CreateDevice(rs485Device)
	devices = append(devices, rs485Device)

	return devices
}

// createDemoRules 创建演示规则
func createDemoRules(engine *linkage.LinkageEngine, devices []*database.LinkageDevice) []*database.LinkageRule {
	var rules []*database.LinkageRule

	// 人员入侵联动规则
	conditions := linkage.RuleConditions{
		VideoID:     []int64{1, 2},
		AlgorithmID: []int64{1},
		Level:       []string{"warning", "error"},
		Type:        []string{"人员检测"},
		TimeRange: &linkage.TimeRange{
			StartTime: "18:00:00",
			EndTime:   "06:00:00",
			Weekdays:  []int{1, 2, 3, 4, 5, 6, 7},
		},
	}
	conditionsJSON, _ := json.Marshal(conditions)

	actions := []linkage.LinkageAction{
		{
			DeviceID: devices[0].ID, // MQTT警报灯
			Command:  "turn_on",
			Params: map[string]interface{}{
				"topic":      "alarm/light/control",
				"brightness": 100,
				"color":      "red",
			},
			Delay: 0,
		},
		{
			DeviceID: devices[2].ID, // RS485报警器
			Command:  "raw_hex",
			Params: map[string]interface{}{
				"data": "010600010001D90A",
			},
			Delay: 2,
		},
	}
	actionsJSON, _ := json.Marshal(actions)

	rule1 := &database.LinkageRule{
		ID:          uuid.New().String(),
		Name:        "人员入侵联动",
		Description: "检测到人员入侵时开启警报灯和报警器",
		Enabled:     true,
		Priority:    10,
		Conditions:  string(conditionsJSON),
		Actions:     string(actionsJSON),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	engine.ruleManager.CreateRule(rule1)
	rules = append(rules, rule1)

	// 火灾报警联动规则
	conditions2 := linkage.RuleConditions{
		AlgorithmID: []int64{2},
		Level:       []string{"error"},
		Type:        []string{"火灾检测"},
	}
	conditionsJSON2, _ := json.Marshal(conditions2)

	actions2 := []linkage.LinkageAction{
		{
			DeviceID: devices[1].ID, // Modbus门禁
			Command:  "write_single_coil",
			Params: map[string]interface{}{
				"address": float64(0),
				"value":   true,
			},
			Delay: 0,
		},
		{
			DeviceID: devices[0].ID, // MQTT警报灯
			Command:  "blink",
			Params: map[string]interface{}{
				"topic":    "alarm/light/control",
				"interval": 500,
			},
			Delay: 1,
		},
	}
	actionsJSON2, _ := json.Marshal(actions2)

	rule2 := &database.LinkageRule{
		ID:          uuid.New().String(),
		Name:        "火灾报警联动",
		Description: "检测到火灾时立即开门并闪烁警报灯",
		Enabled:     true,
		Priority:    20,
		Conditions:  string(conditionsJSON2),
		Actions:     string(actionsJSON2),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	engine.ruleManager.CreateRule(rule2)
	rules = append(rules, rule2)

	return rules
}

// createDemoAlerts 创建演示告警
func createDemoAlerts(db *database.DB) []*database.Alert {
	var alerts []*database.Alert

	// 人员入侵告警
	alert1 := &database.Alert{
		ID:            1,
		Source:        "前门摄像头",
		IP:            "************",
		Level:         "warning",
		Type:          "人员检测",
		VideoID:       1,
		VideoName:     "前门摄像头",
		AlgorithmID:   1,
		AlgorithmName: "人员检测算法",
		Content:       "检测到未授权人员进入",
		Status:        "new",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	db.Create(alert1)
	alerts = append(alerts, alert1)

	// 火灾报警
	alert2 := &database.Alert{
		ID:            2,
		Source:        "仓库摄像头",
		IP:            "************",
		Level:         "error",
		Type:          "火灾检测",
		VideoID:       2,
		VideoName:     "仓库摄像头",
		AlgorithmID:   2,
		AlgorithmName: "火灾检测算法",
		Content:       "检测到火灾迹象",
		Status:        "new",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	db.Create(alert2)
	alerts = append(alerts, alert2)

	return alerts
}

// demonstrateManualControl 演示手动设备控制
func demonstrateManualControl(engine *linkage.LinkageEngine, devices []*database.LinkageDevice) {
	for _, device := range devices {
		fmt.Printf("手动控制设备: %s (%s)\n", device.Name, device.Protocol)
		
		var params map[string]interface{}
		var command string
		
		switch device.Protocol {
		case linkage.ProtocolMQTT:
			command = "turn_off"
			params = map[string]interface{}{
				"topic": "alarm/light/control",
			}
		case linkage.ProtocolModbus:
			command = "write_single_coil"
			params = map[string]interface{}{
				"address": float64(1),
				"value":   false,
			}
		case linkage.ProtocolRS485:
			command = "raw_hex"
			params = map[string]interface{}{
				"data": "010600010000180A",
			}
		}
		
		err := engine.adapterManager.SendCommand(device.ID, command, params)
		if err != nil {
			fmt.Printf("❌ 手动控制失败: %v\n", err)
		} else {
			fmt.Printf("✓ 手动控制成功\n")
		}
	}
}

// showExecutionResults 显示执行结果
func showExecutionResults(db *database.DB, engine *linkage.LinkageEngine) {
	fmt.Println("\n--- 联动执行结果 ---")
	
	// 查询执行记录
	var executions []database.LinkageExecution
	if err := db.Order("executed_at DESC").Limit(10).Find(&executions).Error; err != nil {
		fmt.Printf("❌ 查询执行记录失败: %v\n", err)
		return
	}
	
	if len(executions) == 0 {
		fmt.Println("暂无执行记录")
		return
	}
	
	fmt.Printf("最近 %d 条执行记录:\n", len(executions))
	for i, exec := range executions {
		fmt.Printf("%d. 时间: %s | 设备: %s | 动作: %s | 状态: %s | 耗时: %dms\n",
			i+1,
			exec.ExecutedAt.Format("15:04:05"),
			exec.DeviceID[:8]+"...",
			exec.Action,
			exec.Status,
			exec.Duration)
		
		if exec.ErrorMessage != "" {
			fmt.Printf("   错误: %s\n", exec.ErrorMessage)
		}
	}
	
	// 显示统计信息
	stats := engine.GetStatistics()
	fmt.Printf("\n统计信息:\n")
	fmt.Printf("- 总执行次数: %d\n", stats.TotalExecutions)
	fmt.Printf("- 成功次数: %d\n", stats.SuccessExecutions)
	fmt.Printf("- 失败次数: %d\n", stats.FailedExecutions)
	fmt.Printf("- 成功率: %.1f%%\n", stats.SuccessRate)
	fmt.Printf("- 平均响应时间: %dms\n", stats.AverageResponseTime)
}

// showSystemStatus 显示系统状态
func showSystemStatus(engine *linkage.LinkageEngine) {
	fmt.Println("\n--- 系统状态 ---")
	
	health := engine.GetHealthStatus()
	fmt.Printf("整体状态: %s\n", health.Status)
	fmt.Printf("引擎状态: %s\n", health.EngineStatus)
	fmt.Printf("队列长度: %d\n", health.QueueLength)
	fmt.Printf("活跃规则数: %d\n", health.ActiveRules)
	
	fmt.Println("\n协议适配器状态:")
	for protocol, status := range health.ProtocolAdapters {
		fmt.Printf("- %s: %s\n", protocol, status)
	}
	
	// 显示设备状态
	devices := engine.deviceManager.GetAllDevices()
	fmt.Printf("\n设备状态 (共%d个):\n", len(devices))
	for _, device := range devices {
		status, _ := engine.deviceManager.GetDeviceStatus(device.ID)
		fmt.Printf("- %s (%s): %s\n", device.Name, device.Protocol, status.Status)
	}
}
