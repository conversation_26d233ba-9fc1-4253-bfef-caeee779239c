package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"ecp/internal/app/linkage"
	"ecp/internal/pkg/database"
	"github.com/google/uuid"
)

func main() {
	fmt.Println("=== 协议适配器功能演示 ===")

	// 1. 创建适配器管理器
	manager := linkage.NewAdapterManager()

	// 2. 创建和注册MQTT适配器
	mqttConfig := linkage.MQTTConfig{
		Enabled:        true,
		DefaultQoS:     1,
		KeepAlive:      60 * time.Second,
		ConnectTimeout: 10 * time.Second,
		CleanSession:   true,
	}
	mqttAdapter := linkage.NewMQTTAdapter(mqttConfig)
	manager.RegisterAdapter(linkage.ProtocolMQTT, mqttAdapter)
	fmt.Println("✓ MQTT适配器注册成功")

	// 3. 创建和注册Modbus适配器
	modbusConfig := linkage.ModbusConfig{
		Enabled:    true,
		Timeout:    5 * time.Second,
		RetryCount: 3,
		SlaveID:    1,
	}
	modbusAdapter := linkage.NewModbusAdapter(modbusConfig)
	manager.RegisterAdapter(linkage.ProtocolModbus, modbusAdapter)
	fmt.Println("✓ Modbus适配器注册成功")

	// 4. 创建和注册RS485适配器
	rs485Config := linkage.RS485Config{
		Enabled:    true,
		Timeout:    3 * time.Second,
		BufferSize: 1024,
	}
	rs485Adapter := linkage.NewRS485Adapter(rs485Config)
	manager.RegisterAdapter(linkage.ProtocolRS485, rs485Adapter)
	fmt.Println("✓ RS485适配器注册成功")

	// 5. 演示MQTT设备
	fmt.Println("\n--- MQTT设备演示 ---")
	mqttDeviceConfig := map[string]interface{}{
		"broker_url": "tcp://localhost:1883",
		"username":   "ecp_user",
		"password":   "ecp_password",
		"client_id":  "ecp_demo",
		"qos":        1,
		"use_tls":    false,
	}
	configJSON, _ := json.Marshal(mqttDeviceConfig)

	mqttDevice := &database.LinkageDevice{
		ID:       uuid.New().String(),
		Name:     "演示MQTT警报灯",
		Type:     "警报灯",
		Protocol: linkage.ProtocolMQTT,
		Address:  "localhost",
		Port:     1883,
		Config:   string(configJSON),
	}

	err := manager.ConnectDevice(mqttDevice)
	if err != nil {
		fmt.Printf("❌ MQTT设备连接失败: %v\n", err)
		fmt.Println("   (这是正常的，因为没有运行MQTT服务器)")
	} else {
		fmt.Println("✓ MQTT设备连接成功")
		
		// 发送控制命令
		params := map[string]interface{}{
			"topic":      "device/alarm_light/control",
			"brightness": 100,
			"color":      "red",
			"qos":        1,
		}
		
		err = manager.SendCommand(mqttDevice.ID, "turn_on", params)
		if err != nil {
			fmt.Printf("❌ MQTT命令发送失败: %v\n", err)
		} else {
			fmt.Println("✓ MQTT命令发送成功")
		}
	}

	// 6. 演示Modbus设备
	fmt.Println("\n--- Modbus设备演示 ---")
	modbusDeviceConfig := map[string]interface{}{
		"slave_id":    1,
		"timeout":     5,
		"retry_count": 3,
		"mode":        "tcp",
	}
	configJSON, _ = json.Marshal(modbusDeviceConfig)

	modbusDevice := &database.LinkageDevice{
		ID:       uuid.New().String(),
		Name:     "演示Modbus控制器",
		Type:     "门禁控制器",
		Protocol: linkage.ProtocolModbus,
		Address:  "*************",
		Port:     502,
		Config:   string(configJSON),
	}

	err = manager.ConnectDevice(modbusDevice)
	if err != nil {
		fmt.Printf("❌ Modbus设备连接失败: %v\n", err)
		fmt.Println("   (这是正常的，因为没有Modbus设备)")
	} else {
		fmt.Println("✓ Modbus设备连接成功")
		
		// 发送控制命令
		params := map[string]interface{}{
			"address": float64(0),
			"value":   true,
		}
		
		err = manager.SendCommand(modbusDevice.ID, "write_single_coil", params)
		if err != nil {
			fmt.Printf("❌ Modbus命令发送失败: %v\n", err)
		} else {
			fmt.Println("✓ Modbus命令发送成功")
		}
	}

	// 7. 演示RS485设备
	fmt.Println("\n--- RS485设备演示 ---")
	rs485DeviceConfig := map[string]interface{}{
		"serial_port": "COM3",
		"baud_rate":   9600,
		"data_bits":   8,
		"stop_bits":   1,
		"parity":      "none",
		"timeout":     3,
		"protocol":    "custom",
		"device_addr": 1,
	}
	configJSON, _ = json.Marshal(rs485DeviceConfig)

	rs485Device := &database.LinkageDevice{
		ID:       uuid.New().String(),
		Name:     "演示RS485报警器",
		Type:     "报警器",
		Protocol: linkage.ProtocolRS485,
		Address:  "COM3",
		Port:     0,
		Config:   string(configJSON),
	}

	err = manager.ConnectDevice(rs485Device)
	if err != nil {
		fmt.Printf("❌ RS485设备连接失败: %v\n", err)
		fmt.Println("   (这是正常的，因为没有COM3端口)")
	} else {
		fmt.Println("✓ RS485设备连接成功")
		
		// 发送控制命令
		params := map[string]interface{}{
			"data":          "010300000001840A", // 十六进制命令
			"need_response": true,
			"validate_crc":  true,
		}
		
		err = manager.SendCommand(rs485Device.ID, "raw_hex", params)
		if err != nil {
			fmt.Printf("❌ RS485命令发送失败: %v\n", err)
		} else {
			fmt.Println("✓ RS485命令发送成功")
		}
	}

	// 8. 演示命令构建和CRC计算
	fmt.Println("\n--- 命令构建演示 ---")
	
	// 演示Modbus命令构建
	params := map[string]interface{}{
		"device_addr": float64(1),
		"function":    float64(3),
		"start_addr":  float64(0),
		"quantity":    float64(1),
	}
	
	// 获取RS485适配器进行演示
	rs485AdapterInterface, err := manager.GetAdapter(linkage.ProtocolRS485)
	if err != nil {
		fmt.Printf("❌ 获取RS485适配器失败: %v\n", err)
	} else {
		fmt.Println("✓ 成功获取RS485适配器")
		fmt.Println("✓ 命令构建功能已集成到适配器中")
		fmt.Println("✓ CRC16校验功能已集成到适配器中")
	}

	// 9. 健康检查
	fmt.Println("\n--- 健康检查 ---")
	healthResults := manager.HealthCheck()
	for protocol, err := range healthResults {
		if err != nil {
			fmt.Printf("❌ %s协议健康检查失败: %v\n", protocol, err)
		} else {
			fmt.Printf("✓ %s协议健康检查通过\n", protocol)
		}
	}

	// 10. 获取适配器信息
	fmt.Println("\n--- 适配器信息 ---")
	allAdapters := manager.GetAllAdapters()
	for protocol, adapter := range allAdapters {
		fmt.Printf("协议: %s, 类型: %s\n", protocol, adapter.GetProtocolType())
	}

	// 11. 演示不同的命令类型
	fmt.Println("\n--- 命令类型演示 ---")
	
	// MQTT命令示例
	fmt.Println("MQTT命令示例:")
	fmt.Println("  - turn_on: 开启设备")
	fmt.Println("  - turn_off: 关闭设备")
	fmt.Println("  - set_brightness: 设置亮度")
	fmt.Println("  - blink: 闪烁模式")

	// Modbus命令示例
	fmt.Println("Modbus命令示例:")
	fmt.Println("  - read_coils: 读取线圈")
	fmt.Println("  - read_holding_registers: 读取保持寄存器")
	fmt.Println("  - write_single_coil: 写入单个线圈")
	fmt.Println("  - write_single_register: 写入单个寄存器")

	// RS485命令示例
	fmt.Println("RS485命令示例:")
	fmt.Println("  - raw_hex: 发送原始十六进制数据")
	fmt.Println("  - modbus_read: Modbus读取命令")
	fmt.Println("  - modbus_write: Modbus写入命令")
	fmt.Println("  - custom: 自定义协议命令")

	// 12. 清理资源
	fmt.Println("\n--- 清理资源 ---")
	manager.Close()
	fmt.Println("✓ 所有适配器已关闭")

	fmt.Println("\n=== 演示完成 ===")
	fmt.Println("注意:")
	fmt.Println("1. MQTT演示需要运行MQTT服务器 (如 Mosquitto)")
	fmt.Println("2. Modbus演示需要Modbus TCP服务器或设备")
	fmt.Println("3. RS485演示需要可用的串口和设备")
	fmt.Println("4. 在实际环境中，请根据具体设备配置正确的参数")
}
