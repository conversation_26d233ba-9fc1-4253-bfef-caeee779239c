package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"ecp/internal/app/linkage"
	"ecp/internal/pkg/database"
	"github.com/google/uuid"
)

func main() {
	fmt.Println("=== 告警联动功能演示 ===")

	// 1. 创建数据库连接
	db, err := database.NewDB(&database.Config{Path: "demo.db"})
	if err != nil {
		log.Fatalf("创建数据库失败: %v", err)
	}
	defer db.Close()

	// 初始化数据库表结构
	if err := db.InitSchema(); err != nil {
		log.Fatalf("初始化数据库表结构失败: %v", err)
	}

	// 2. 创建联动引擎
	config := &linkage.LinkageConfig{
		Enabled:                 true,
		MaxConcurrentExecutions: 5,
		ExecutionTimeout:        30 * time.Second,
		RetryCount:              3,
		RetryInterval:           5 * time.Second,
	}

	engine := linkage.NewLinkageEngine(db, config)

	// 启动联动引擎
	if err := engine.Start(); err != nil {
		log.Fatalf("启动联动引擎失败: %v", err)
	}
	defer engine.Stop()

	fmt.Println("✓ 联动引擎启动成功")

	// 3. 创建示例设备
	deviceConfig := linkage.DeviceConfig{
		SlaveID:    1,
		Timeout:    5,
		RetryCount: 3,
	}
	configJSON, _ := json.Marshal(deviceConfig)

	device := &database.LinkageDevice{
		ID:        uuid.New().String(),
		Name:      "入口警报灯",
		Type:      "警报灯",
		Protocol:  "modbus",
		Address:   "************0",
		Port:      502,
		Config:    string(configJSON),
		Status:    linkage.DeviceStatusOffline,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := engine.deviceManager.CreateDevice(device); err != nil {
		log.Fatalf("创建设备失败: %v", err)
	}

	fmt.Printf("✓ 创建设备成功: %s (%s)\n", device.Name, device.ID)

	// 4. 创建联动规则
	conditions := linkage.RuleConditions{
		VideoID:     []int64{1},
		AlgorithmID: []int64{1},
		Level:       []string{"warning", "error"},
		Type:        []string{"人员检测"},
		TimeRange: &linkage.TimeRange{
			StartTime: "18:00:00",
			EndTime:   "06:00:00",
			Weekdays:  []int{1, 2, 3, 4, 5, 6, 7},
		},
	}
	conditionsJSON, _ := json.Marshal(conditions)

	actions := []linkage.LinkageAction{
		{
			DeviceID: device.ID,
			Command:  "turn_on",
			Params: map[string]interface{}{
				"brightness": 100,
				"color":      "red",
			},
			Delay: 0,
		},
		{
			DeviceID: device.ID,
			Command:  "blink",
			Params: map[string]interface{}{
				"interval": 1000,
			},
			Delay: 2,
		},
	}
	actionsJSON, _ := json.Marshal(actions)

	rule := &database.LinkageRule{
		ID:          uuid.New().String(),
		Name:        "人员入侵联动",
		Description: "检测到人员入侵时开启警报灯",
		Enabled:     true,
		Priority:    10,
		Conditions:  string(conditionsJSON),
		Actions:     string(actionsJSON),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := engine.ruleManager.CreateRule(rule); err != nil {
		log.Fatalf("创建规则失败: %v", err)
	}

	fmt.Printf("✓ 创建联动规则成功: %s (%s)\n", rule.Name, rule.ID)

	// 5. 模拟告警事件
	alert := &database.Alert{
		ID:            1,
		Source:        "前门摄像头",
		IP:            "************",
		Level:         "warning",
		Type:          "人员检测",
		VideoID:       1,
		VideoName:     "前门摄像头",
		AlgorithmID:   1,
		AlgorithmName: "人员检测算法",
		Content:       "检测到未授权人员进入",
		Status:        "new",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 保存告警到数据库
	if err := db.Create(alert).Error; err != nil {
		log.Fatalf("保存告警失败: %v", err)
	}

	fmt.Printf("✓ 创建模拟告警: %s (ID: %d)\n", alert.Content, alert.ID)

	// 6. 处理告警并触发联动
	fmt.Println("\n--- 开始处理告警联动 ---")

	if err := engine.ProcessAlert(alert); err != nil {
		log.Fatalf("处理告警失败: %v", err)
	}

	fmt.Println("✓ 告警联动处理完成")

	// 7. 等待一段时间让联动任务执行
	fmt.Println("⏳ 等待联动任务执行...")
	time.Sleep(5 * time.Second)

	// 8. 查看统计信息
	stats := engine.GetStatistics()
	fmt.Printf("\n--- 联动统计信息 ---\n")
	fmt.Printf("总执行次数: %d\n", stats.TotalExecutions)
	fmt.Printf("成功执行次数: %d\n", stats.SuccessExecutions)
	fmt.Printf("失败执行次数: %d\n", stats.FailedExecutions)
	fmt.Printf("成功率: %.1f%%\n", stats.SuccessRate)
	fmt.Printf("平均响应时间: %dms\n", stats.AverageResponseTime)
	fmt.Printf("在线设备数: %d\n", stats.OnlineDevices)
	fmt.Printf("离线设备数: %d\n", stats.OfflineDevices)
	fmt.Printf("活跃规则数: %d\n", stats.ActiveRules)

	// 9. 查看健康状态
	health := engine.GetHealthStatus()
	fmt.Printf("\n--- 系统健康状态 ---\n")
	fmt.Printf("整体状态: %s\n", health.Status)
	fmt.Printf("引擎状态: %s\n", health.EngineStatus)
	fmt.Printf("队列长度: %d\n", health.QueueLength)
	fmt.Printf("活跃规则数: %d\n", health.ActiveRules)

	// 10. 查看执行记录
	var executions []database.LinkageExecution
	if err := db.Order("executed_at DESC").Limit(10).Find(&executions).Error; err != nil {
		log.Printf("查询执行记录失败: %v", err)
	} else {
		fmt.Printf("\n--- 最近执行记录 ---\n")
		for _, exec := range executions {
			fmt.Printf("时间: %s | 设备: %s | 动作: %s | 状态: %s | 耗时: %dms\n",
				exec.ExecutedAt.Format("15:04:05"),
				exec.DeviceID[:8]+"...",
				exec.Action,
				exec.Status,
				exec.Duration)
		}
	}

	fmt.Println("\n=== 演示完成 ===")
	fmt.Println("注意: 由于没有实际的设备连接，联动命令执行会失败，这是正常现象。")
	fmt.Println("在实际环境中，需要配置真实的设备地址和协议参数。")
}
